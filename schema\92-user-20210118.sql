ALTER TABLE "user" ADD CONSTRAINT fk_user_roles_id FOREIGN KEY ("user_role_id") REFERENCES user_roles(id);
alter table "user" add column "user_role_id" uuid   ;

	
update "user" set user_role_id = (select id from user_roles where value = 'finance') where approval_role = 64;
update "user" set user_role_id = (select id from user_roles where value = 'admin') where approval_role = 63;
update "user" set user_role_id = (select id from user_roles where value = 'datastewardadmin') where approval_role = 401;
update "user" set user_role_id = (select id from user_roles where value = 'clustercomplianceofficer') where approval_role = 49;
update "user" set user_role_id = (select id from user_roles where value = 'sfeadmin') where approval_role = 402;
update "user" set user_role_id = (select id from user_roles where value = 'linesecretary') where approval_role = 403;
update "user" set user_role_id = (select id from user_roles where value = 'salesmanager') where approval_role = 46;
update "user" set user_role_id = (select id from user_roles where value = 'bumanager') where approval_role = 47;
update "user" set user_role_id = (select id from user_roles where value = 'countrymedical') where approval_role = 48;
update "user" set user_role_id = (select id from user_roles where value = 'countrycommercialsolutionhead') where approval_role = 50;
update "user" set user_role_id = (select id from user_roles where value = 'regionalmarketing') where approval_role = 51;
update "user" set user_role_id = (select id from user_roles where value = 'regionalcompliance') where approval_role = 52;
update "user" set user_role_id = (select id from user_roles where value = 'regionalfranchisehead') where approval_role = 54;
update "user" set user_role_id = (select id from user_roles where value = 'regionalmedical') where approval_role = 53;
update "user" set user_role_id = (select id from user_roles where value = 'salesrepresentative') where approval_role = 68;
update "user" set user_role_id = (select id from user_roles where value = 'productmanager') where approval_role = 69;
update "user" set user_role_id = (select id from user_roles where value = 'salesmanager') where approval_role = 70;
update "user" set user_role_id = (select id from user_roles where value = 'marketaccessmanager') where approval_role = 71;
update "user" set user_role_id = (select id from user_roles where value = 'medicalmanager') where approval_role = 72;
update "user" set user_role_id = (select id from user_roles where value = 'manager') where approval_role = 393;