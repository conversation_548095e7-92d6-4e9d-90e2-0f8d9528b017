INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Country Medical' and is_active = true),(select id from user_roles where value = 'countrymedical'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from user_roles where value = 'countrycommercialsolutionhead'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Regional Marketing' and is_active = true),(select id from user_roles where value = 'regionalmarketing'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Regional Compliance' and is_active = true),(select id from user_roles where value = 'regionalcompliance'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Regional Medical' and is_active = true),(select id from user_roles where value = 'regionalmedical'and is_active = true),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Regional Franchise Head' and is_active = true),(select id from user_roles where value = 'regionalfranchisehead'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Admin' and is_active = true),(select id from user_roles where value = 'sfeadmin'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Admin' and is_active = true),(select id from user_roles where value = 'admin'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Admin' and is_active = true),(select id from user_roles where value = 'datastewardadmin'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Finance' and is_active = true),(select id from user_roles where value = 'finance'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Sales Representative' and is_active = true),(select id from user_roles where value = 'salesrepresentative'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Line Secretary' and is_active = true),(select id from user_roles where value = 'linesecretary'),true,false,'00000000-0000-0000-0000-000000000000');
INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Regional Commercial Solutions' and is_active = true),(select id from user_roles where value = 'regionalcommercialsolutions'),true,false,'00000000-0000-0000-0000-000000000000');


INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Compliance' and is_active = true),(select id from user_roles where value = 'clustercomplianceofficer'),true,false,'00000000-0000-0000-0000-000000000000');

INSERT INTO department_roles (department,userrole,is_active,is_deleted,created_by) VALUES
((select id from departments where department = 'Manager' and is_active = true),(select id from user_roles where value = 'medicalmanager'),true,false,'00000000-0000-0000-0000-000000000000'),
((select id from departments where department = 'Manager' and is_active = true),(select id from user_roles where value = 'marketaccessmanager'),true,false,'00000000-0000-0000-0000-000000000000'),
((select id from departments where department = 'Manager' and is_active = true),(select id from user_roles where value = 'productmanager'),true,false,'00000000-0000-0000-0000-000000000000'),
((select id from departments where department = 'Manager' and is_active = true),(select id from user_roles where value = 'bumanager'),true,false,'00000000-0000-0000-0000-000000000000'),
((select id from departments where department = 'Manager' and is_active = true),(select id from user_roles where value = 'salesmanager'),true,false,'00000000-0000-0000-0000-000000000000');




