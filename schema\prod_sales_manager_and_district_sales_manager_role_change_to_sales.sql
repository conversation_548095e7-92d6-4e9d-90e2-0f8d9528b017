UPDATE ezflow_prod.user_roles
SET value='salesmanager', is_active=true, is_deleted=false, created_by='00000000-0000-0000-0000-000000000000'::uuid, date_created='2021-02-04 13:18:41.383', last_modified=NULL, modified_by=NULL, title='Sales', description='SLS'
WHERE id='2b8b13e1-70d4-4690-92e7-f8917fb210a4'::uuid;
UPDATE ezflow_prod.user_roles
SET value='manager', is_active=false, is_deleted=true, created_by='00000000-0000-0000-0000-000000000000'::uuid, date_created='2021-02-04 13:18:41.383', last_modified=NULL, modified_by=NULL, title='District Sales Manager', description='SLS'
WHERE id='9b1a3660-55ae-4147-af60-be41363163f5'::uuid;
UPDATE ezflow_prod.code
SET date_created='2020-11-26 08:47:53.621', is_active=false, is_deleted=true, category='UserRole', sequence_no=NULL, title='District Sales Manager', value='manager', description='SLS', last_modified=NULL
WHERE id=393;
UPDATE ezflow_prod.code
SET date_created='2020-09-29 16:01:14.140', is_active=true, is_deleted=false, category='UserRole', sequence_no=1, title='Sales', value='salesmanager', description='SLS', last_modified=NULL
WHERE id=46;
update approval_log set user_role_id ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where user_role_id ='9b1a3660-55ae-4147-af60-be41363163f5' 
update approvers  set user_role_id  ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where user_role_id  ='9b1a3660-55ae-4147-af60-be41363163f5' 
update department_roles  set userrole  ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where userrole  ='9b1a3660-55ae-4147-af60-be41363163f5'
update user_management_logs  set user_role  ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where user_role ='9b1a3660-55ae-4147-af60-be41363163f5' 
update event_type_access_permission  set user_role_id  ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where user_role_id ='9b1a3660-55ae-4147-af60-be41363163f5' 
update "user"  set user_role_id  ='2b8b13e1-70d4-4690-92e7-f8917fb210a4' where user_role_id ='9b1a3660-55ae-4147-af60-be41363163f5' 

