package postgres

import (
	"context"
	"errors"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
	"github.com/jmoiron/sqlx"

	uuid "github.com/satori/go.uuid"
)

func InsertEventTypeAccessPermissionData(entity *entity.EventTypeAccessPermissionUpsert) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	// byteArr := IntToByteArray(0)
	controlQuerystring := `INSERT INTO event_type_access_permission (user_role,user_role_id, event_type, country, created_by)VALUES($1,$2,$3,$4,$5)`
	_, err = tx.Exec(context.Background(), controlQuerystring, entity.UserRoleForApprover, entity.UserRole, entity.EventType, entity.Country, entity.CreatedBy)
	logengine.GetTelemetryClient().TrackEvent("InsertEventTypeAccessPermissionData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to insert data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit event_type_access_permission data")
	}
	return nil
}

func DeleteEventTypeAccessPermissionData(entity *entity.EventTypeAccessPermissionUpsert) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	querystring := "UPDATE event_type_access_permission SET is_deleted = true, is_active = false, modified_by = $2, last_modified = $3 WHERE id = $1"
	_, err = tx.Exec(context.Background(), querystring, entity.ID, entity.CreatedBy, timenow)
	logengine.GetTelemetryClient().TrackEvent("DeleteEventTypeAccessPermissionData query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New("Failed to delete data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit event_type_access_permission data")
	}
	return nil
}

func UpdateEventTypeAccessPermissionData(inputModel *model.EventTypeAccessPermissionUpsertInput, entity *entity.EventTypeAccessPermissionUpsert) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())

	querystring := `update event_type_access_permission set last_modified = now(),`
	var inputArgs []interface{}
	if inputModel.UserRole != nil {
		querystring += `user_role_id = ? ,`
		inputArgs = append(inputArgs, entity.UserRole)
	}
	if inputModel.EventType != nil {
		querystring += `event_type = ? ,`
		inputArgs = append(inputArgs, entity.EventType)
	}

	querystring += `modified_by= ? `
	inputArgs = append(inputArgs, entity.CreatedBy)

	querystring += `WHERE id= ? and is_active = true`
	inputArgs = append(inputArgs, entity.ID)
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	_, err = tx.Exec(context.Background(), querystring, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("UpdateEventTypeAccessPermissionData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to update data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit event_type_access_permission data")
	}
	return nil
}

func GetEventTypeAccessPermission(inputModel *model.EventTypeAccessPermissionInput, country *int) ([]entity.EventAccessTypeEntity, error) {
	functionName := "GetEventTypeAccessPermission"
	log.Println(functionName)
	var result []entity.EventAccessTypeEntity
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var err error
	queryString = `select etcp.id, etcp.user_role, c1.title as event_title, c1.value as event_value,
	c2.title as countryTitle, c2.value as countryValue , etcp.user_role_id from event_type_access_permission  etcp
	inner join code c1 on etcp.event_type = c1.id 
	left join code c2 on etcp.country = c2.id 
	where etcp.is_active = true and etcp.is_deleted = false`
	var args []interface{}
	if inputModel != nil {
		if inputModel.EventType != nil && *inputModel.EventType != "" {
			queryString += ` AND c1.value = ? `
			args = append(args, inputModel.EventType)
		}

		queryString += ` AND etcp.country = ? `
		args = append(args, country)

	} else {
		queryString += ` AND etcp.country = ? `
		args = append(args, country)
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, args...)
	logengine.GetTelemetryClient().TrackEvent("GetEventTypeAccessPermission query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, err
	}

	for rows.Next() {
		var roleSelection entity.EventAccessTypeEntity
		err = rows.Scan(
			&roleSelection.ID,
			&roleSelection.UserRoles,
			&roleSelection.EventTypeName,
			&roleSelection.EventTypeValue,
			&roleSelection.CountryTitle,
			&roleSelection.CountryValue,
			&roleSelection.UserRoleID,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", "GetEventTypeAccessPermission", err.Error())
			return result, err
		}
		result = append(result, roleSelection)

	}
	return result, nil
}

func GetUserRoleValueTitle(userRole string) (*entity.UserRole, error) {
	functionName := "GetUserRoleValueTitle()"
	var response entity.UserRole
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select ur.title, ur.value from user_roles ur where id = $1 and is_active = true and is_deleted = false`

	var countryTitle string
	var countryValue string
	err := pool.QueryRow(context.Background(), query, userRole).Scan(&countryTitle, &countryValue)
	logengine.GetTelemetryClient().TrackEvent("GetUserRoleValueTitle query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	response.UserRoleTitle = countryTitle
	response.UserRoleValue = countryValue

	return &response, nil
}

func CheckUserApprovalRoleByMasterData(approvalRole string) bool {
	functionName := "CheckUserApprovalRoleByMasterData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	var result bool
	querystring := `select 1 from roles_permission where master_data = true and user_role_id = $1;`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalRole).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func HasEventTypeAccessPermissionID(eventTypeAccessPermissionID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from event_type_access_permission where id = $1 AND is_active = true AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, eventTypeAccessPermissionID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func IsEventTypeAccessPermissionCreatedBy(countryID int, id *string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from event_type_access_permission where id = $2 and country = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, countryID, *id).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

func FetchEventTypeIDByValue(eventType string) (int, error) {
	functionName := "FetchEventTypeIDByValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT id FROM code WHERE value = $1 AND is_active = true AND is_deleted = false"
	var status int
	err := pool.QueryRow(context.Background(), querystring, eventType).Scan(&status)
	logengine.GetTelemetryClient().TrackEvent("FetchEventTypeIDByValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return status, errors.New("Invalid type")
	}

	return status, err
}
