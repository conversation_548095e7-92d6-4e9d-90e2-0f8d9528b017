package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertEmployee is the resolver for the upsertEmployee field.
func (r *mutationResolver) UpsertEmployee(ctx context.Context, input model.EmployeeInput) (*model.UpsertResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertEmployee", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertEmployee :" + string(inputJson))
	response := controller.UpsertEmployeeData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertEmployee", "data_maintenance/UpsertEmployee", time.Since(start), "200")
	return response, nil
}

// Employee is the resolver for the employee field.
func (r *queryResolver) Employee(ctx context.Context, input model.EmployeeRequest) (*model.EmployeeResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "Employee", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Employee :" + string(inputJson))
	response := controller.ExportEmployeeExcel(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("Employee", "data_maintenance/Employee", time.Since(start), "200")
	return response, nil
}

// EmployeeByRole is the resolver for the employeeByRole field.
func (r *queryResolver) EmployeeByRole(ctx context.Context, input model.EmployeeRoleRequest) (*model.EmployeeResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "EmployeeByRole", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/EmployeeByRole :" + string(inputJson))
	response := controller.ExportEmployeeByRole(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("EmployeeByRole", "data_maintenance/EmployeeByRole", time.Since(start), "200")
	return response, nil
}

// EmployeeAuditLogs is the resolver for the EmployeeAuditLogs field.
func (r *queryResolver) EmployeeAuditLogs(ctx context.Context, input model.EmployeeAuditLogsRequest) (*model.EmployeeAuditLogsResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "Employee", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Employee :" + string(inputJson))
	response := controller.EmployeeAuditLogs(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("Employee", "data_maintenance/Employee", time.Since(start), "200")
	return response, nil
}
