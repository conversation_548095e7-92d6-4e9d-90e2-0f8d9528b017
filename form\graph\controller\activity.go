package controller

import (
	"context"
	"strconv"

	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/login/auth"
)

func GetActivitySelections(ctx *context.Context) *model.ActivitySelectionsResponse {
	var response model.ActivitySelectionsResponse
	userId := auth.GetUserID(*ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	activities, err := postgres.GetActivitySelections()
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchActivity := mapper.FetchActivityEntityToModel(activities)
	response.Data = fetchActivity
	return &response
}

func UpsertActivity(ctx *context.Context, input model.ActivityInput) *model.UpsertResponse {
	var response = &model.UpsertResponse{Error: false}
	userId := auth.GetUserID(*ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	activityEntity := mapper.MapActivityModelToEntity(input, ctx, response)
	if !response.Error {
		postgres.UpsertActivity(activityEntity, response)
		if !response.Error {
			if activityEntity.ID != nil {
				response.Message = "Activity successfully updated!"
			} else if activityEntity.IsDeleted != nil && *activityEntity.IsDeleted {
				response.Message = "Activity successfully deleted!"
			} else {
				response.Message = "Activity successfully created!"
			}
		}
	}

	return response
}
func GetActivities(ctx *context.Context, input *model.ActivityFilter) *model.ActivityModelsResponse {
	response := &model.ActivityModelsResponse{}
	userId := auth.GetUserID(*ctx)
	country := auth.GetCountry(*ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	activityEntites := postgres.GetActivities(*country, response)
	response.Data = mapper.MapActivityEntitiesToModels(activityEntites)

	return response
}

func ValidateHcpMeal(ctx *context.Context, input model.HcpMealInput) *model.ValidateHcpMealResponse {
	response := &model.ValidateHcpMealResponse{}
	validationMessages := []*model.ValidationMessage{}
	validationMessage := &model.ValidationMessage{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		validationMessage.Message = "You are not authorized to login please contact your country ezflow admin."
		validationMessages = append(validationMessages, validationMessage)
		response.ValidationMessages = validationMessages
		return response
	}
	var val = input.Input
	var checkMeal bool
	for _, item := range val {
		if item.MealType == "" {
			response.Error = true
			validationMessage.Message = "Meal type cannot be blank."
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response
		}
		if item.ActivityID == 0 {
			response.Error = true
			validationMessage.Message = "ActivityID can not be zero"
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response

		}
		checkMeal = postgres.GetHcpMealCheck(item.ActivityID)
		if checkMeal {
			response.Error = false
			validationMessage.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") is within the limit!"
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response

		}
	}

	mealEntites, _ := postgres.GetHcpMealLimit(*country)
	mapper.HcpMealValidation(mealEntites, &input, response, *country)
	return response
}
