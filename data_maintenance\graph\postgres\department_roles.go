package postgres

import (
	"context"
	"errors"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/jmoiron/sqlx"
)

func GetDepartmentIDByDepartment(department string) (string, error) {
	functionName := "GetDepartmentIDByDepartment()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var departmentID string
	query := `select id from departments where id = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, department).Scan(&departmentID)
	logengine.GetTelemetryClient().TrackEvent("GetDepartmentIDByDepartment query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return departmentID, nil
}

func GetUserRoleIDByUserRole(userRole string) (string, error) {
	functionName := "GetUserRoleIDByUserRole()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var userRoleID string
	query := `select id from user_roles where id = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userRole).Scan(&userRoleID)
	logengine.GetTelemetryClient().TrackEvent("GetUserRoleIDByUserRole query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return userRoleID, nil
}

func InsertDepartmentRoles(item *entity.DepartmentRolesEntity) error {
	functionName := "InsertDepartmentRoles()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New(err.Error())
	}
	defer tx.Rollback(context.Background())
	for value := range item.RoleID {
		query := `INSERT INTO department_roles (department ,userrole ,created_by) 
		VALUES ($1,$2,$3)`
		var inputArgs []interface{}
		inputArgs = append(inputArgs, item.DepartmentID, item.RoleID[value], item.CreatedBy)
		_, queryErr := tx.Exec(context.Background(), query, inputArgs...)
		logengine.GetTelemetryClient().TrackEvent("InsertDepartmentRoles query called")
		if queryErr != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - error: {%s}", functionName, err.Error())
			return err
		}
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New(txErr.Error())
	}
	return err
}

func UpdateDepartmentRoles(inputModel *model.DepartmentRolesInput, entity *entity.DepartmentRolesEntity) error {
	var err error

	err = DeleteDepartmentRoles(entity)
	logengine.GetTelemetryClient().TrackEvent("UpdateDepartmentRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to delete data")
	}

	err = InsertDepartmentRoles(entity)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to insert data")
	}

	return nil
}

func DeleteDepartmentRoles(entity *entity.DepartmentRolesEntity) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New(err.Error())
	}
	defer tx.Rollback(context.Background())
	querystring := `update department_roles set modified_by = $2, last_modified = now(), is_active = false , is_deleted = true 
	where department = $1`
	_, err = tx.Exec(context.Background(), querystring, entity.ID, entity.CreatedBy)
	logengine.GetTelemetryClient().TrackEvent("DeleteDepartmentRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New(err.Error())
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New(txErr.Error())
	}
	return err
}

func GetDepartmentRolesInfo(input *model.DepartmentRoleRequest) ([]entity.DepartmentRolesFetchEntity, error) {
	functionName := "GetDepartmentRolesInfo()"
	if pool == nil {
		pool = GetPool()
	}

	queryString := ` select d.id, d.department,ur.id, ur.value,ur.title ,ur.description from department_roles dr 
	inner join departments d on d.id = dr.department 
	inner join user_roles ur on ur.id = dr.userrole 
	where dr.is_active = true and dr.is_deleted = false `
	log.Printf("%s", functionName)
	var inputArgs []interface{}
	if input != nil {
		if input.Department != nil {
			queryString += ` AND d.department ILIKE ? `
			inputArgs = append(inputArgs, "%"+*input.Department+"%")
		}
		if input.UserRole != nil {
			queryString += ` AND ur.value ILIKE ? `
			inputArgs = append(inputArgs, "%"+*input.UserRole+"%")
		}
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	var departmentRoles []entity.DepartmentRolesFetchEntity
	logengine.GetTelemetryClient().TrackEvent("GetDepartmentRolesInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	} else {
		for rows.Next() {
			var departmentRole entity.DepartmentRolesFetchEntity
			err := rows.Scan(&departmentRole.ID, &departmentRole.Department, &departmentRole.UserRoleID,
				&departmentRole.UserRole, &departmentRole.UserRoleTitle, &departmentRole.UserRoleDesc)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - Error: %s", functionName, err.Error())
				return departmentRoles, err
			}
			departmentRoles = append(departmentRoles, departmentRole)
		}
	}
	return departmentRoles, nil
}

func HasDepartmentID(departmentID string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := ` select distinct 1 from department_roles where department = $1 AND is_deleted = false and is_active = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, departmentID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}
