package postgres

import (
	"context"
	"database/sql"
	"log"
	"strconv"

	"github.com/ihcp/form/graph/postgres/util"
)

func GetStartDateAndEndDateFromMeetingID(formanswerid string, getMeetingId string, BlobUrl string) (string, error) {
	functionName := "GetStartDateAndEndDateFromMeetingID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var eventName sql.NullString
	var startDate sql.NullString
	var endDate sql.NullString
	querystring := `select	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,2,values,0,value}' as activity_name,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}' as activity_start_date,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}' as activity_end_date
    FROM form_answers fa where fa.id = $1`
	pool.QueryRow(context.Background(), querystring, formanswerid).Scan(&eventName, &startDate, &endDate)
	pdfUrl, err := util.AttachmentMailAttendeesForEzClaim(BlobUrl, getMeetingId, eventName.String, startDate.String, endDate.String)
	if err != nil {
		return pdfUrl, err
	}
	return pdfUrl, nil
}
func GetEventIdandcheckAttendanceDB(formanswerId string) (string, int, string, error) {
	functionName := "GetEventIdandcheckAttendanceDB()"
	if pool == nil {
		pool = GetPool()
	}
	var eventSeq sql.NullString
	var eventCode sql.NullString
	var attendanceId sql.NullString
	query := `select fa.event_code ,fa.event_seq ,fa2.id::text from form_answers fa 
	left join form_attendances fa2 on fa2.form_answers_id =fa.id   and fa2.is_veeva =true and  fa2.is_active =true
	where fa.is_deleted =false  and  fa.id =$1 `
	err := pool.QueryRow(context.Background(), query, formanswerId).Scan(&eventCode, &eventSeq, &attendanceId)
	if err != nil {
		log.Println(functionName)
		return "", 0, "", err
	}
	eventSquence, _ := strconv.Atoi(eventSeq.String)
	return eventCode.String, eventSquence, attendanceId.String, nil
}
func GetEventId(formanswerId string) (string, int, error) {
	functionName := "GetBlobUrlForAttendece()"
	if pool == nil {
		pool = GetPool()
	}
	var eventSeq sql.NullString
	var eventCode sql.NullString
	query := `select fa.event_code,fa.event_seq  from form_answers fa where fa.id =$1 `
	err := pool.QueryRow(context.Background(), query, formanswerId).Scan(&eventCode, &eventSeq)
	if err != nil {
		log.Println(functionName)
		return "", 0, err
	}
	eventSquence, _ := strconv.Atoi(eventSeq.String)
	return eventCode.String, eventSquence, nil
}
