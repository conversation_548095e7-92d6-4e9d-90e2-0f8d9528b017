package mapper

import (
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"log"
	"math"
	"reflect"
	"strconv"
)

// #1

func FormAnswerDesignEntityToModel(input *entity.FormAnswerEntity, country string) []*model.FormTabsAnswer {
	log.Println("FormAnswerDesignEntityToModel")
	var response []*model.FormTabsAnswer
	formSection := input.Design.Get()
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormTabsAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &id
						}
					case "title":
						if strct != nil {
							title := strct.(string)
							formTab.Title = &title
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue

						}
					case "sectionAnswer":
						if strct != nil {
							formTab.SectionAnswer = getFormAnswerSectionValueFromMap(strct, country)
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #2

func getFormAnswerSectionValueFromMap(formSection interface{}, country string) []*model.FormSectionAnswer {
	log.Println("getFormAnswerSectionValueFromMap")
	var response []*model.FormSectionAnswer
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormSectionAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &id
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "title":
						if strct != nil {
							title := strct.(string)
							formTab.Title = &title
						}
					case "form":
						if strct != nil {
							formTab.Form = getFormQuestionAnswerFromMap(strct, country)
						}
					case "childForm":
						if strct != nil {
							formTab.ChildForm = getFormQuestionAnswerFromMap(strct, country)
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #3

func getFormQuestionAnswerFromMap(questions interface{}, country string) *model.FormSectionContentAnswer {
	log.Println("getFormQuestionAnswerFromMap")
	var response model.FormSectionContentAnswer
	v := reflect.ValueOf(questions)
	if v.Kind() == reflect.Map {
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			switch key.Interface() {
			case "id":
				if strct != nil {
					id := strct.(string)
					response.ID = &id
				}
			case "sequenceNo":
				if strct != nil {
					seqValue := int(strct.(float64))
					response.SequenceNo = &seqValue
				}
			case "title":
				if strct != nil {
					title := strct.(string)
					response.Title = &title
				}
			case "groupAnswer":
				if strct != nil {
					response.GroupAnswer = getQuestionGroupFromAnswerInterfaceMap(strct, country)
				}
			}
		}
	}
	return &response
}

// #4

func getQuestionGroupFromAnswerInterfaceMap(groups interface{}, country string) []*model.QuestionGroupAnswers {
	log.Println("getQuestionGroupFromAnswerInterfaceMap")
	var response []*model.QuestionGroupAnswers
	v := reflect.ValueOf(groups)
	if v.Kind() == reflect.Slice {
		for _, item := range groups.([]interface{}) {
			var formTab model.QuestionGroupAnswers
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &id
						}
					case "title":
						if strct != nil {
							title := strct.(string)
							formTab.Title = &title
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "questionAnswers":
						if strct != nil {
							formTab.QuestionAnswers = getGroupFromAnswerInterfaceMap(strct, country)
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #5

func getGroupFromAnswerInterfaceMap(inputGroup interface{}, country string) []*model.GroupAnswers {
	log.Println("getGroupFromAnswerInterfaceMap")
	var response []*model.GroupAnswers
	v := reflect.ValueOf(inputGroup)
	if v.Kind() == reflect.Slice {
		for _, item := range inputGroup.([]interface{}) {
			var formTab model.GroupAnswers
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &(id)
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							formTab.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "source":
						if strct != nil {
							source := strct.(string)
							formTab.Source = &source
						}
					case "answers":
						if strct != nil {
							formTab.Answers = getInputsAnswersFromInterfaceMap(strct, country)
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

//#6

func getInputsAnswersFromInterfaceMap(inputs interface{}, country string) []*model.InputAnswers {
	//log.Println("getInputsAnswersFromInterfaceMap")
	var response []*model.InputAnswers
	v := reflect.ValueOf(inputs)
	if v.Kind() == reflect.Slice {
		for _, item := range inputs.([]interface{}) {
			var formTab model.InputAnswers
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &(id)
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							formTab.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "type":
						if strct != nil {
							seqValue := strct.(string)
							formTab.Type = &seqValue
						}
					case "source":
						if strct != nil {
							seqValue := strct.(string)
							formTab.Source = &seqValue
						}
					case "values":
						if strct != nil {
							formTab.Values = getValuesAnswersFromInterfaceMap(strct, country)
						}
					}
				}
			}
			//if formTab.ID != nil {
			//	fmt.Println(*formTab.ID, "=================")
			//}
			response = append(response, &formTab)
		}
	}
	return response
}

// #7

func getValuesAnswersFromInterfaceMap(question interface{}, country string) []*model.ValuesAnswer {
	//log.Println("getValuesAnswersFromInterfaceMap")
	var response []*model.ValuesAnswer
	code := codeController.GetValueKeyCodes()["country"]
	v := reflect.ValueOf(question)
	if v.Kind() == reflect.Slice {
		for _, item := range question.([]interface{}) {
			var formTab model.ValuesAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					if strct == nil {
						continue
					}
					f := strct.(string)
					switch key.Interface() {
					case "id":
						formTab.ID = &f
					case "description":
						formTab.Description = &f
					case "value":
						formTab.Value = &f
					}
				}
			}
			var valueInUsd string
			if formTab.Description != nil && formTab.Value != nil {
				if *formTab.Description == "Total Cost" || *formTab.Description == "Proposed Payable Honorarium" {
					if *formTab.Value != "" {
						if val, err := strconv.ParseFloat(*formTab.Value, 10); err == nil {
							usdValue, err := postgres.ConvertedCurrencyValue(378, code[country].ID, val, "local")
							if err != nil {
								log.Println(err)
							}
							valueInUsd = strconv.Itoa(int(math.Ceil(usdValue)))
						}
					}
				}
			}
			formTab.ValueInUsd = &valueInUsd
			response = append(response, &formTab)
		}
	}
	return response
}
