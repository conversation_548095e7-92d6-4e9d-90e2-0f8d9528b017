package postgres

import (
	"context"
	"errors"
	"log"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/jmoiron/sqlx"
)

func GetAdminAnswerList() ([]entity.FormAnswerForAdmins, error) {
	functionName := "GetAdminFormAnswerList()"
	log.Println(functionName)
	var result []entity.FormAnswerForAdmins
	if pool == nil {
		pool = GetPool()
	}
	codes2 := codeController.GetValueKeyCodes()["typeofemail"]
	completedMailStatusID := codes2["formcompleted"].ID
	queryString := `WITH fa AS 
	(	
	SELECT fa.id, CONCAT(u.first_name ,' ', u.last_name) as "name", u.id as userId,
	u.active_directory,
	fa.date_created,
	fa.event_code as eventCode,
	fa.event_seq as eventSeq,
	c.title as title,
	c.value as value,
	c1.title as countrytitle,c1.value as countryvalue,
	c2.title as currencytitle, 
	c2.value as currencyvalue,
	fa.total_cost, 
	fa.answers,
	fa.status,
	(select max (date_created) from email_log where event_id = fa.id and type_of_email = ?) as completion_date
	from form_answers fa
	inner join "user" u on fa.created_by = u.id
	inner join code c on fa.status  = c.id
	inner join code c1 on fa.country = c1.id 
	inner join code c2 on fa.currency = c2.id 
	and fa.is_deleted = false 
	), groups AS 
	(
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
		arr(item_object)
	), tab_section AS (
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
	), group_questions AS (
		SELECT id, arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT id, arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS 
	(
		SELECT id, arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'id' IN ('type-of-ihcp-activity', 'type-of-event', 'activity-name', 'activity-start-date', 'activity-end-date', 'team', 'product','venue','govt-or-non-govt-hcp',
		'event-organizer','speaker-name','specialty','hco-institute-name','type-of-event-expenses','event-owner','proposed-honorarium') or arr.item_object->>'title' IN ('Expenses')
	),cost_answer as
	(
		SELECT id, arr.item_object as value FROM questions, jsonb_array_elements(qn_values) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'description' IN ('Total Cost')
	) SELECT fa.id, fa.completion_date, fa.name, fa.active_directory, fa.date_created,fa.eventcode, fa.eventseq, fa.title, fa.value, fa.countrytitle, fa.countryvalue, fa.currencytitle, fa.currencyvalue, fa.total_cost,
	(select array(SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('activity-start-date','activity-end-date','activity-name','product','venue','event-organizer','event-owner') AND questions.id = fa.id)) as activity_start_date,
	(select array (SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('govt-or-non-govt-hcp','speaker-name','specialty','hco-institute-name','proposed-honorarium') AND questions.id = fa.id)) as govtnongovthcp,
 	(select array(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'type-of-event-expenses' AND questions.id = fa.id)) as eventExpenses,
	(select array(SELECT value#>>'{value}' FROM  cost_answer WHERE cost_answer.id = fa.id)) as expenseCost,
	(select array (SELECT jsonb_array_elements(qn_values)->>'description'::text FROM  questions WHERE qn_id in ('type-of-event','type-of-ihcp-activity') AND questions.id = fa.id)) as event_type,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'team' AND questions.id = fa.id) as team,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-name' AND questions.id = fa.id) as activity_name,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'product' AND questions.id = fa.id) as product,
	fa.userId FROM fa ORDER BY fa.date_created desc `
	var inputargs []interface{}
	inputargs = append(inputargs, completedMailStatusID)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("GetAdminAnswerList query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var answerEntity entity.FormAnswerForAdmins
		rows.Scan(&answerEntity.FormAnswerID, &answerEntity.CompletionTime,
			&answerEntity.RequestorName, &answerEntity.RequestorActiveDirectory,
			&answerEntity.CreatedDate, &answerEntity.EventCode,
			&answerEntity.EventSeq, &answerEntity.Status,
			&answerEntity.StatusValue, &answerEntity.CountryTitle,
			&answerEntity.CountryValue, &answerEntity.CurrencyTitle,
			&answerEntity.CurrencyValue, &answerEntity.TotalCost,
			&answerEntity.SingleDataArray, &answerEntity.GovtNonGovtHcp,
			&answerEntity.EventExpenses, &answerEntity.ExpenseCost,
			&answerEntity.EventTypes, &answerEntity.Team,
			&answerEntity.ActivityName, &answerEntity.Product,
			&answerEntity.UserID)
		result = append(result, answerEntity)
	}
	return result, nil
}

func InsertReportingDB(input entity.FormAnswerForAdmins) error {
	functionName := "InsertReportingDB()"
	log.Printf("%s", functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	query := `INSERT INTO reporting_db
	(form_answer_id, completion_date, requestor_name, active_directory, date_created, event_code, event_seq, status_title, status_value, country_title, country_value, currency_title, currency_value, total_cost, activity_start_date, govt_non_govt_hcp, event_expenses, expense_cost, event_type, created_by, team, product, activity_name)
	VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23);
	`
	_, err = tx.Exec(context.Background(), query, input.FormAnswerID, input.CompletionTime, input.RequestorName, input.RequestorActiveDirectory, input.CreatedDate, input.EventCode, input.EventSeq, input.Status, input.StatusValue, input.CountryTitle, input.CountryValue, input.CurrencyTitle, input.CurrencyValue, input.TotalCost, input.SingleDataArray, input.GovtNonGovtHcp, input.EventExpenses, input.ExpenseCost, input.EventTypes, input.UserID, input.Team, input.Product, input.ActivityName)
	if err != nil {
		log.Println("%s - Error: %s", err.Error())
		return errors.New("Failed to insert data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit controls data")
	}
	return err
}
