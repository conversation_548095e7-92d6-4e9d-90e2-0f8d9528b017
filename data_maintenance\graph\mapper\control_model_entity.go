package mapper

import (
	"errors"
	"log"
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func ExportControlInfoEntityToModel(input []entity.ExportControls) []*model.Control {
	log.Println("ExportControlInfoEntityToModel()")
	var response []*model.Control
	for _, item := range input {
		res := new(model.Control)
		res.ID = item.ID.String()
		res.MaxLimit = int(item.MaxLimit)
		res.Category = item.Category.String
		res.Type = item.Type.String
		res.Description = item.Description.String
		res.Status = item.Status.String
		res.Country = item.Country.String
		response = append(response, res)
	}
	return response
}

func ExportControlInputModelToEntity(input *model.ControlRequest, userUUID uuid.UUID) (*entity.Control, error) {
	var outEntity entity.Control
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid

	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New("Country does not exists!")
	}
	outEntity.Country = countryID

	if input.Category != nil {
		outEntity.Category = strings.TrimSpace(*(input).Category)
	}
	if input.Type != nil {
		outEntity.Type = strings.TrimSpace(*(input).Type)
	}
	if input.Description != nil {
		outEntity.Description = strings.TrimSpace(*(input).Description)
	}
	if input.Status != nil {
		outEntity.Status = *input.Status
	}

	if input.Limit != nil && *input.Limit > 0 {
		outEntity.Limit = *input.Limit
		if input.PageNo != nil && *input.PageNo > 0 {
			outEntity.PageNo = *input.Limit * (*input.PageNo - 1)
		}
	}

	return &outEntity, nil
}
func MapControlModelToEntity(inputModel *model.ControlInput, userUUID uuid.UUID) (*entity.ControlUpsertInput, error) {
	log.Println("MapControlModelToEntity()")
	var entity entity.ControlUpsertInput

	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			entity.ID = &uuid
			if !postgres.HasControlID(entity.ID) {
				return nil, errors.New("ControlID does not exist!")
			}
		} else {
			return nil, errors.New("ControlID format is invalid!")
		}
	}
	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New("Country does not exists!" + userUUID.String())
	}
	entity.Country = countryID
	if inputModel.MaxLimit != nil {
		entity.MaxLimit = inputModel.MaxLimit
	}
	if inputModel.IsDelete != nil {
		entity.IsDelete = *inputModel.IsDelete
	}

	return &entity, nil
}
