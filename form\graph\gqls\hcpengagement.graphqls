input excelUploadForSponsoredHcpEngagementRequest {
    fileName: String!
    url: String!
    activityId:Int!
}

type excelUploadForSponsoredHcpEngagementResponse {
    error: Boolean!
    message: String!
    data:[sponsoredHcpEngagementData]
}
type sponsoredHcpEngagementData{
    typeofengagement:String	
    individualcategory:String
    govtornongovthcp:String	
    remarks:String	
    speakername:String	
    specialty:String	
    hcoinstitutename:String	
    speakerID:String
    speakernamevalue:String
    preparationtime:String
    servicetime:String
    morethan2lecturesmeetings:String
    expenses:[sponsoredExpenseData]
    doesthehcpinfluenceinpublichealthpolicyorlawsorregulations:String
    doesthehcpinfluenceinpublichealthpolicyorlawsorregulationsremarks:String
    doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilar:String
    doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilarremarks:String
    doesthehcpserveoninternationalnongovernmentalhealthorganization:String
    doesthehcpserveoninternationalnongovernmentalhealthorganizationremarks:String
    tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernment:String
    tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernmentremarks:String
    whatisthehcpsphereofinfluence:String
    levelofinfluence:String
    question1:String
    remarks1:String
}
type sponsoredExpenseData{
    registrationfee:String	
    registrationremark:String	
    groundtransportation:String	
    airfare:String	
    transportremark:String	
    typeofmeal0:String
    nom0:Int	
    costpermeal0:String
    typeofmeal1:String
    nom1:Int	
    costpermeal1:String
    typeofmeal2:String
    nom2:Int	
    costpermeal2:String
    typeofmeal3:String
    nom3:Int	
    costpermeal3:String
    mealExpenses:[sponsoredMealExpenses]	
    mealremark:String	
    nodaccomodation:Int	
    costperdayaccomodation:String	
    accomodationremark:String	
    otherexpenses:[sponsoredOtherexpenses]
    otherremark:String
}
type sponsoredMealExpenses{
    typeofmeal0:String
    nom0:Int	
    costpermeal0:String
    typeofmeal1:String
    nom1:Int	
    costpermeal1:String
    typeofmeal2:String
    nom2:Int	
    costpermeal2:String
    typeofmeal3:String
    nom3:Int	
    costpermeal3:String
}
type sponsoredOtherexpenses{
    nameofexpenses:String	
    totalcostsother:String	
}

type validationResultForSponsoredHcp {
    error: Boolean!
    excelValidationMessages: String!
}

input searchHCP {
    Country: String!
    HCPNameList: [String!]
    TerritoryIdList: [String!]
    HCONameList: [String!]
    ProvinceList: [String!]
    Cities: [String!]
    Specialties: [String!]
    ProductNameList: [String!]
    ClassificationList: [String!]
}

type hcp {
    Country: String
    HCPName: String
    HCPVeevaId: String
    TerritoryId: String
    HCOName: String
    HCOId: String
    Province: String
    City: String
    Specialty: String
    ProductName: String
    Classification: String
}

type searchHCPResponse {
    error: Boolean!
    message: String!
    data: [hcp!]
    total: Int!
}

extend type Query {
    hcpEngagementSponsoredExcel(input: excelUploadForSponsoredHcpEngagementRequest!): excelUploadForSponsoredHcpEngagementResponse!
    searchHCP(input: searchHCP!): searchHCPResponse!
}
