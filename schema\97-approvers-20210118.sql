alter table approvers  add column user_role_id uuid ;

alter table approvers add constraint fk_user_role_id foreign key  (user_role_id) references  user_roles(id) ;

alter table approvers  add column department_id uuid ;
alter table approvers add constraint fk_department_id foreign key (department_id) references departments(id) ;


update approvers set user_role_id = (select id from user_roles where value = 'finance') where approval_role = 64;
update approvers set user_role_id = (select id from user_roles where value = 'admin') where approval_role = 63;
update approvers set user_role_id = (select id from user_roles where value = 'clustercomplianceofficer') where approval_role = 49;
update approvers set user_role_id = (select id from user_roles where value = 'datastewardadmin') where approval_role = 401;
update approvers set user_role_id = (select id from user_roles where value = 'sfeadmin') where approval_role = 402;
update approvers set user_role_id = (select id from user_roles where value = 'linesecretary') where approval_role = 403;
update approvers set user_role_id = (select id from user_roles where value = 'salesmanager') where approval_role = 46;
update approvers set user_role_id = (select id from user_roles where value = 'bumanager') where approval_role = 47;
update approvers set user_role_id = (select id from user_roles where value = 'countrymedical') where approval_role = 48;
update approvers set user_role_id = (select id from user_roles where value = 'countrycommercialsolutionhead') where approval_role = 50;
update approvers set user_role_id = (select id from user_roles where value = 'regionalmarketing') where approval_role = 51;
update approvers set user_role_id = (select id from user_roles where value = 'regionalcompliance') where approval_role = 52;
update approvers set user_role_id = (select id from user_roles where value = 'regionalfranchisehead') where approval_role = 54;
update approvers set user_role_id = (select id from user_roles where value = 'regionalmedical') where approval_role = 53;
update approvers set user_role_id = (select id from user_roles where value = 'salesrepresentative') where approval_role = 68;
update approvers set user_role_id = (select id from user_roles where value = 'productmanager') where approval_role = 69;
update approvers set user_role_id = (select id from user_roles where value = 'salesmanager') where approval_role = 70;
update approvers set user_role_id = (select id from user_roles where value = 'marketaccessmanager') where approval_role = 71;
update approvers set user_role_id = (select id from user_roles where value = 'medicalmanager') where approval_role = 72;
update approvers set user_role_id = (select id from user_roles where value = 'manager') where approval_role = 393;


	
	 UPDATE approvers SET department_id = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active =true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approvers.user_role_id = subquery.r_id