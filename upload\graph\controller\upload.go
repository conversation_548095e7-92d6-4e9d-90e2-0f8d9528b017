package controller

import (
	"context"

	"github.com/ihcp/login/auth"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
)

func getUploadList(ctx *context.Context, input *model.ExcelStatusRequest) *model.ExcelStatusResponse {
	authorID := auth.GetUserID(*ctx)
	var excelType string
	if input.Type != nil {
		excelType = *input.Type
	}
	response := postgres.GetUploadLogs(*authorID, excelType)

	return response
}
