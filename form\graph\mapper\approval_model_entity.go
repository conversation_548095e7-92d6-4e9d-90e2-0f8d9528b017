package mapper

import (
	"errors"
	"sort"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapApprovalRolesEntitiesToModels(entities []entity.ApprovalRoles) []*model.ApprovalRoleData {
	var models []*model.ApprovalRoleData

	for _, approvalEntity := range entities {
		var approvalModel model.ApprovalRoleData

		approvalModel.ID = (approvalEntity.ID).String()
		approvalModel.GroupType = approvalEntity.GroupName
		approvalModel.GroupTitle = approvalEntity.GroupTitle
		approvalModel.HasInternational = approvalEntity.HasInternational
		approvalModel.SequenceNo = approvalEntity.SequenceNo
		approvalModel.Activity = approvalEntity.ActivityName
		approvalModel.ActivityTitle = approvalEntity.ActivityTitle
		approvalModel.CountryTitle = approvalEntity.CountryTitle
		approvalModel.CountryValue = approvalEntity.CountryValue
		approvalModel.HasCondition = approvalEntity.HasCondition
		approvalModel.HasLevelOfInfluence = approvalEntity.HasLevelOfInfluence
		approvalModel.LedBy = approvalEntity.LedBy
		if approvalEntity.DepartmentID != nil {
			departmentId := approvalEntity.DepartmentID.String()
			approvalModel.DepartmentID = &departmentId
		} else {
			approvalModel.DepartmentID = nil
		}
		if approvalEntity.Department != nil {
			department := approvalEntity.Department
			approvalModel.Department = department
		} else {
			approvalModel.Department = nil
		}

		if approvalEntity.MinLimit.Int32 >= 0 {
			minLimit := int(approvalEntity.MinLimit.Int32)
			approvalModel.MinLimit = &minLimit
		} else {
			approvalModel.MinLimit = nil
		}

		if approvalEntity.MaxLimit.Int32 != 0 {
			maxLimit := int(approvalEntity.MaxLimit.Int32)
			approvalModel.MaxLimit = &maxLimit
		} else {
			approvalModel.MaxLimit = nil
		}

		models = append(models, &approvalModel)
	}
	sort.SliceStable(models, func(i, j int) bool {
		var minLimitI int
		var minLimitJ int
		if models[i].MinLimit != nil {
			minLimitI = *(models[i].MinLimit)
		}
		if models[j].MinLimit != nil {
			minLimitJ = *(models[j].MinLimit)
		}

		if minLimitI < minLimitJ {
			return true
		}

		if minLimitI > minLimitJ {
			return false
		}
		if models[i].GroupType < models[j].GroupType {
			return true
		}
		if models[i].GroupType > models[j].GroupType {
			return false
		}
		return models[i].SequenceNo < models[j].SequenceNo
	})
	return models

}

func MapUserRolesForApprovers(entities []entity.UserRole) []*model.GetUserRoles {
	var models []*model.GetUserRoles

	for _, approvalEntity := range entities {
		var approvalModel model.GetUserRoles

		approvalModel.ID = approvalEntity.ID
		approvalModel.Title = approvalEntity.Title
		approvalModel.Value = approvalEntity.Value
		models = append(models, &approvalModel)
	}
	return models
}

func ApprovalEntityToModel(input []entity.Approval) []*model.ApprovalRoles {
	var approvals []*model.ApprovalRoles
	index := 0
	for _, item := range input {
		var groups []*model.Group
		var roles []*model.Roles
		var approval model.ApprovalRoles
		var group model.Group
		var role model.Roles
		if len(approvals) > 0 {
			if approvals[index-1].ActivityName == item.ActivityName {
				for j := 0; j < len(approvals[index-1].Group); j++ {
					if approvals[index-1].Group[j].GroupName == item.GroupName {
						var role model.Roles
						role.Role = item.ApprovalRole
						approvals[index-1].Group[j].Roles = append(approvals[index-1].Group[j].Roles, &role)
					} else {
						var group model.Group
						group.GroupName = item.GroupName
						var roles []*model.Roles
						var role model.Roles
						role.Role = item.ApprovalRole
						roles = append(roles, &role)
						group.Roles = roles
						approvals[index-1].Group = append(approvals[index-1].Group, &group)
					}
				}
				index--
			} else {
				approval.ActivityName = item.ActivityName
				group.GroupName = item.GroupName
				role.Role = item.ApprovalRole
				roles = append(roles, &role)
				group.Roles = roles
				groups = append(groups, &group)
				approval.Group = groups
				approvals = append(approvals, &approval)
			}
		} else {
			approval.ActivityName = item.ActivityName
			group.GroupName = item.GroupName
			role.Role = item.ApprovalRole
			roles = append(roles, &role)
			group.Roles = roles
			groups = append(groups, &group)
			approval.Group = groups
			approvals = append(approvals, &approval)
		}
		index++
	}
	return approvals
}

func MapApprovalRoleManagementModelToEntity(inputModel *model.UpsertApprovalRoleManagementRequest, userId string) (*entity.ApprovalRoleManagementEntity, error) {

	var entity entity.ApprovalRoleManagementEntity

	isDeleted := false
	if inputModel.IsDeleted != nil {
		isDeleted = *(inputModel).IsDeleted
		entity.IsDeleted = isDeleted
	}
	if !entity.IsDeleted {
		countryID, err := postgres.GetUserCountryByID(userId)
		if err != nil {
			return nil, errors.New("Country does not exists!")
		}
		entity.CountryCode = countryID
		if inputModel.ID != nil {
			uuid, err := uuid.FromString(*inputModel.ID)
			if err != nil {
				return nil, errors.New("ID format is invalid!")
			}
			entity.ID = &uuid
			hasArmID, err := postgres.HasApprovalRoleManagementID(entity.ID)
			if err != nil {
				return nil, err
			} else if !hasArmID {
				return nil, errors.New("ID does not exist!")
			}
		}

		if inputModel.Activity == nil {
			return nil, errors.New("Activity cannot be blank")
		}
		if *inputModel.Activity == "" {
			return nil, errors.New("Activity cannot be blank")
		}
		if inputModel.GroupType == nil {
			return nil, errors.New("Group type cannot be blank")
		}
		if *inputModel.GroupType == "" {
			return nil, errors.New("Group type cannot be blank")
		}
		if inputModel.Department == nil {
			return nil, errors.New("Department cannot be blank")
		}
		if *inputModel.Department == "" {
			return nil, errors.New("Department cannot be blank")
		}
		if inputModel.SequenceNo == nil {
			return nil, errors.New("Sequence number cannot be blank")
		}

		if inputModel.Activity != nil {
			activityCodeID, err := postgres.GetActivityCodeIDByActivity(*inputModel.Activity)
			if err != nil {
				return nil, errors.New("Activity is invalid.")
			} else {
				entity.ActivityCodeID = activityCodeID
			}
		}

		if inputModel.MaxLimit != nil {
			maxLimit := *inputModel.MaxLimit
			if maxLimit > 0 {
				entity.MaxLimit = &maxLimit
			}
		}

		if inputModel.MinLimit != nil {
			minLimit := *inputModel.MinLimit
			if minLimit < 0 {
				return nil, errors.New("Minimum limit can't be negative!")
			}
			if minLimit != 0 || entity.MaxLimit != nil {
				entity.MinLimit = inputModel.MinLimit
			}
		}

		if inputModel.HasInternational != nil {
			entity.HasInternational = *inputModel.HasInternational
		}

		if inputModel.HasLevelOfInfluence != nil {
			entity.HasLevelOfInfluence = *inputModel.HasLevelOfInfluence
		}

		if entity.MinLimit != nil && entity.MaxLimit != nil && *entity.MaxLimit < *entity.MinLimit {
			return nil, errors.New("Maximum limit provided cannot be larger than minimum limit!")
		}

		if inputModel.GroupType != nil {
			groupCodeID, err := postgres.GetCodeIDByTitle(*inputModel.GroupType, "Group")
			if err != nil {
				return nil, errors.New("Group type is invalid.")
			} else {
				entity.GroupID = groupCodeID
			}
		}
		if inputModel.SequenceNo != nil {
			if *inputModel.SequenceNo <= 0 {
				return nil, errors.New("Sequence number is invalid!")
			} else {
				entity.SequenceNo = *inputModel.SequenceNo
			}

		}
		if inputModel.HasCondition == nil {
			entity.HasCondition = false
		} else {
			entity.HasCondition = *inputModel.HasCondition
		}

		if inputModel.Department != nil {
			if !postgres.CheckDepartmentPresent(*inputModel.Department) {
				return nil, errors.New("Department is invalid!")
			} else {
				entity.Department = *inputModel.Department
			}
		}

		if inputModel.AlternateGroupType != nil && inputModel.AlternateDepartment != nil {
			alternateGroupCodeID, err := postgres.GetCodeIDByTitle(*inputModel.AlternateGroupType, "Group")
			if err != nil {
				return nil, errors.New("Group type is invalid.")
			} else {
				entity.AlternateGroupID = alternateGroupCodeID
			}
			if !postgres.CheckDepartmentPresent(*inputModel.AlternateDepartment) {
				return nil, errors.New("Department is invalid!")
			} else {
				entity.AlternateDepartment = inputModel.AlternateDepartment
			}
		} else if inputModel.AlternateGroupType != nil && inputModel.AlternateDepartment == nil {
			return nil, errors.New("Alternate Department Cannot be blank")
		} else if inputModel.AlternateGroupType == nil && inputModel.AlternateDepartment != nil {
			return nil, errors.New("Alternate Group Type Cannot be blank")
		}

		entity.UserID = userId

		hasExistingSequenceNo, err := postgres.HasExistingApprovalRoleSequence(entity, entity.CountryCode)
		if err != nil {
			return nil, errors.New("Sequence no validation check error!")
		} else if hasExistingSequenceNo {
			return nil, errors.New("Department sequence number already exist for the given range!")
		}
		hasLimitConflict, err := postgres.HasExistingApprovalRoleManagement(entity, entity.CountryCode)
		if err != nil {
			return nil, errors.New("Limit validation check error!")
		} else if hasLimitConflict {
			return nil, errors.New("Department already set for the given limit or conflicts with other limit range!")

		}
	} else {
		if inputModel.ID != nil {
			uuid, err := uuid.FromString(*inputModel.ID)
			if err != nil {
				return nil, errors.New("ID format is invalid!")
			}
			entity.ID = &uuid
		} else {
			return nil, errors.New("ID is not provided!")
		}
	}

	entity.LedByOptions = inputModel.LedBy
	return &entity, nil
}

func MapDelegateApproverModelToEntity(inputModel model.DelegateApproverInput, userId string) (*entity.DelegateApproverEntity, error) {
	var entity entity.DelegateApproverEntity

	if inputModel.FormAnswerID == "" {
		return nil, errors.New("Form Answer ID cannot be blank")
	} else {
		FormAnswerID, err := uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return nil, errors.New("Invalid UUID Format for Form Answer ID!")
		} else {
			entity.FormAnswerID = FormAnswerID.String()
		}
	}

	if inputModel.ApproverID == "" {
		return nil, errors.New("Form Approver ID cannot be blank")
	} else {
		ApproverID, err := uuid.FromString(inputModel.ApproverID)
		if err != nil {
			return nil, errors.New("Invalid UUID Format for Approver ID!")
		} else {
			entity.ApproverID = ApproverID.String()
		}
	}
	ApproverDetails, err := postgres.GetSequenceNoOfApprover(entity.FormAnswerID, userId)
	if err != nil {
		return nil, errors.New("Approver Already Delegated")
	} else {
		entity.GroupID = ApproverDetails.GroupID
		entity.SequenceNo = ApproverDetails.SequenceNo
		entity.UserRoleID = ApproverDetails.UserRoleID
		entity.DepartmentID = ApproverDetails.DepartmentID
		entity.SetNumber = ApproverDetails.SetNumber
	}

	return &entity, nil
}

func FetchDeligateApproversEntityToModel(input []entity.FetchDelegateApproverEntity) []*model.ApproverData {
	var outEntity []*model.ApproverData
	for _, item := range input {
		user := new(model.ApproverData)
		user.ApproverID = item.ApproverID
		user.ApproverName = item.ApproverName
		user.UserRole = item.UserRole
		outEntity = append(outEntity, user)
	}
	return outEntity
}
