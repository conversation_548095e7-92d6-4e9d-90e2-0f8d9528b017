package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

func InsertOfflineDate(entity *entity.UpsertOfflineDate, responce *model.UpsertOfflineDateResponse, userID *string) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertOfflineDate query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		responce.Message = "Failed to begin transaction"
		responce.Error = true
	}
	defer tx.Rollback(context.Background())
	// var inputArgs []interface{}

	querystring := `insert into offline_date (start_date, end_date, created_by, user_id, is_active, is_deleted)
	 VALUES ($1, $2, $3, $4, true, false)`
	_, err = pool.Exec(context.Background(), querystring, entity.StartDate, entity.EndDate, userID, userID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	return nil
}

func FetchOfflineDatepostgres(userId *string) ([]entity.FetchOfflineDate, error) {
	functionName := "FetchOfflineDatepostgres()"
	log.Println(functionName)
	logengine.GetTelemetryClient().TrackEvent("FetchOfflineDatepostgres query called")
	var response []entity.FetchOfflineDate
	if pool == nil {
		pool = GetPool()
	}
	var err error
	querystring := `select start_date ,end_date ,id from offline_date where created_by = $1 and is_active = true and is_deleted = false`
	rows, err := pool.Query(context.Background(), querystring, userId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var data entity.FetchOfflineDate
		_ = rows.Scan(&data.StartDate, &data.EndDate, &data.ID)
		response = append(response, data)
	}
	return response, nil
}

func UpdateOfflineDate(UserId string) error {
	functionName := "UpdateOfflineDate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := `update offline_date set is_active = false , is_deleted = true where  id = $1`
	_, err := pool.Exec(context.Background(), querystring, UserId)
	logengine.GetTelemetryClient().TrackEvent("UpdateOfflineDate query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return err
	}
	return nil
}

func CheckDateOverlapping(date string, userID *string) bool {
	log.Println("CheckDateOverlapping()")
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `SELECT distinct  1 
	FROM offline_date
	WHERE $1 between start_date and end_date and user_id = $2
	 and is_active = true  and is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, date, userID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}

func CheckUserInDB(userID *string) bool {
	log.Println("CheckUserInDB()")
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `SELECT distinct  1 
	FROM offline_date
	WHERE user_id = $1 and is_active = true  and is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, userID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}
