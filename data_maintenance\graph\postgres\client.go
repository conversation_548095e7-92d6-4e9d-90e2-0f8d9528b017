package postgres

import (
	"context"
	"log"
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func InsertClientData(entity *entity.ClientUpsertInput, userUUID uuid.UUID, response *model.UpsertClientResponse) {
	if pool == nil {
		pool = GetPool()
	}
	querystring := `INSERT INTO product_owner
	( date_created, last_modified, is_active, is_deleted, country, owner_name)
	VALUES( now(), now(), true, false, $1, $2);`
	commandTag, err := pool.Exec(context.Background(), querystring, entity.CountryCode, entity.ClientName)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to roll back Client data"
		response.Error = true
		return
	}
	if commandTag.RowsAffected() != 1 {
		response.Message = "Invalid Client data"
		response.Error = true
		return
	} else {
		response.Message = "Client successfully inserted"
		response.Error = false
	}
}

func ClientExistingCheckInDB(clientName string, countryNo int) bool {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "select 1 from product_owner po where po.country =$1 and lower(po.owner_name) =$2"
	var val int
	err := pool.QueryRow(context.Background(), querystring, countryNo, strings.ToLower(clientName)).Scan(&val)
	if err == nil {
		return false
	}
	return true
}
func ClientCount(country int) (int, error) {
	functionName := "ClientCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `SELECT count(id) FROM product_owner WHERE is_deleted = false and country = $1`
	var result int
	err := pool.QueryRow(context.Background(), query, country).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("ClientCount query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return result, nil
}
func GetClientExcelInfo(ClientInput *model.ClientRequest, input *entity.ExportClientExcel) ([]entity.ClientExcel, error) {
	functionName := "GetClientExcelInfo()"
	log.Printf("%s", functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select po.id,po.owner_name as clientName,po.country  as country  ,po.is_active ,po.is_deleted  from product_owner po  
	where  po.is_deleted=false `

	var inputArgs []interface{}
	if input.ID != nil && *(input).ID != uuid.Nil {
		queryString += ` AND po.id = ? `
		inputArgs = append(inputArgs, input.ID)
	}
	if input.SearchItem != "" {
		queryString += ` AND (po.owner_name ilike ? or (
				case 
				when 'Active' ilike ? then po.is_active = true 
				when 'InActive' ilike ? then po.is_active = false 
				end)) `
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.Status != "" {
			if input.Status == "Active" {
				queryString += ` AND po.is_active = true  `
			} else if input.Status == "InActive" {
				queryString += ` AND po.is_active = false  `
			}
		}
		if input.IsActive != nil {
			queryString += ` AND po.is_active = ?`
			inputArgs = append(inputArgs, input.IsActive)
		}
		if input.ClientName != "" {
			queryString += ` AND po.owner_name ilike ? `
			inputArgs = append(inputArgs, "%"+input.ClientName+"%")
		}
	}

	if input.Country > 0 {
		queryString += ` AND po.country = ? `
		inputArgs = append(inputArgs, input.Country)
	}
	if ClientInput.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY po.date_created desc, id asc`
	}
	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ?
		`
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetClientExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var Clients []entity.ClientExcel
	for rows.Next() {
		var Client entity.ClientExcel
		err := rows.Scan(&Client.ID, &Client.ClientName, &Client.Country, &Client.IsActive, &Client.IsDeleted)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return Clients, err
		}
		Clients = append(Clients, Client)
	}
	return Clients, nil
}
func UpdateClientData(entity *entity.ClientUpsertInput, response *model.UpsertClientResponse, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	var inputArg []interface{}
	query := `update product_owner set date_created=now(),last_modified=now(),is_active=?,is_deleted=?,country=? `
	inputArg = append(inputArg, entity.IsActive, entity.IsDeleted, entity.CountryCode)
	if strings.TrimSpace(entity.ClientName) != "" {
		query += ` ,owner_name=? `
		inputArg = append(inputArg, entity.ClientName)
	}
	query += ` WHERE id= ? `
	inputArg = append(inputArg, entity.ID)
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	_, err := pool.Exec(context.Background(), query, inputArg...)
	if err != nil {
		response.Message = err.Error()
		response.Error = true
		return
	}
	response.Message = "Client successfully updated"
	response.Error = false

}
