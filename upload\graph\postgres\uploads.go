package postgres

import (
	"context"
	"log"
	"sort"
	"strings"
	"time"

	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres/util"
	"github.com/jackc/pgtype"
	uuid "github.com/satori/go.uuid"
)

func InsertUploadLogs(input *model.ExcelUploadRequest, excelType string, author uuid.UUID) string {
	log.Println("InsertUploadLogs()")
	if pool == nil {
		pool = GetPool()
	}
	var u2 pgtype.UUID
	querystring := "INSERT INTO upload (blob_url, blob_name, type, created_by, status) VALUES($1, $2, $3, $4, $5) RETURNING(id)"
	err := pool.QueryRow(context.Background(), querystring, input.URL, input.FileName, excelType, author, "Processing").Scan(&u2)
	if err != nil {
		log.Println(err)
	}
	mybyte := u2.Get().([16]byte)
	uuidstr := util.UUIDV4ToString(mybyte)
	log.Println("InsertUploadLogs(): uuidstr: " + uuidstr)
	return uuidstr
}

func UpdateUploadStatus(uploadId *uuid.UUID, status string, author uuid.UUID) {
	log.Println("UpdateUploadStatus()")
	if pool == nil {
		pool = GetPool()
	}
	updateStmt := "UPDATE upload SET status = $2, modified_by = $3, last_modified = now() WHERE id = $1"
	commandTag, err := pool.Exec(context.Background(), updateStmt, uploadId, status, author)
	if err != nil {
		log.Println(err)
	}
	if commandTag.RowsAffected() != 1 {

		log.Println("UpdateUploadStatus: Fail to update upload status with id: " + uploadId.String())
	}
}

func InsertUploadValidationErrors(input *model.ExcelUploadRequest, validation *model.ValidationResult, uploadId *uuid.UUID, author uuid.UUID) {
	log.Println("InsertUploadValidationErrors()")
	// Update upload status to Fail.
	if pool == nil {
		pool = GetPool()
	}
	if uploadId != nil {

		UpdateUploadStatus(uploadId, "Fail", author)

		querystring := "INSERT INTO upload_log (upload, row, message, created_by) VALUES($1, $2, $3, $4)"
		for _, validationError := range validation.ExcelValidationMessages {
			commandTag, err := pool.Exec(context.Background(), querystring, uploadId, validationError.Row, validationError.Message, author)
			if err != nil {
				log.Println(err)
			}
			if commandTag.RowsAffected() != 1 {
				log.Println("No row inserted into uploads")
			}
		}
	} else {
		log.Println("InsertUploadValidationErrors(): Upload id is empty")
	}

}

func GetUploadLogs(userID string, excelType string) *model.ExcelStatusResponse {
	if pool == nil {
		pool = GetPool()
	}
	response := &model.ExcelStatusResponse{}
	var queryString strings.Builder
	uploadMap := make(map[string]*model.Excel)
	var sqlValues []interface{}

	queryString.WriteString(`
	SELECT upload.id, emp.active_directory, upload.blob_url, upload.blob_name, upload.status, upload.type, upload.date_created, upload.last_modified, logs.row, logs.message
	FROM upload 
	INNER JOIN (SELECT id, active_directory FROM "user" WHERE id = $1) AS emp ON emp.id = upload.created_by
	LEFT JOIN upload_log logs ON logs.upload = upload.id
	WHERE (blob_url ILIKE '%.xlsx' OR blob_url ILIKE '%.xls')`)

	sqlValues = append(sqlValues, userID)
	if excelType != "" {
		queryString.WriteString(` AND upload.type = $2`)
		sqlValues = append(sqlValues, excelType)
	}
	rows, err := pool.Query(context.Background(), queryString.String(), sqlValues...)
	if err != nil {
		log.Println("GetUploadLogs(): Fail to execute query")
	}
	for rows.Next() {
		var userId pgtype.UUID
		var activeDirectory string
		var url string
		var filename string
		var uploadStatus string
		var excelType *string
		var dateCreated time.Time
		//var lastModified pgtype.Timestamptz
		var lastModified *time.Time
		var row *int
		var message *string

		err := rows.Scan(&userId, &activeDirectory, &url, &filename, &uploadStatus, &excelType, &dateCreated, &lastModified, &row, &message)
		if err != nil {
			log.Println(err)
		}
		idByte := userId.Get().([16]byte)
		uuidStr := util.UUIDV4ToString(idByte)
		var excelModel *model.Excel
		var validationTime time.Duration
		if lastModified == nil {
			validationTime = dateCreated.Sub(dateCreated)
		} else {
			validationTime = lastModified.Sub(dateCreated)
		}
		// Using composite key for sorting
		// Format: <timestamp>-<uuistr>
		timeStampString := util.GetTimeUnixTimeStamp(dateCreated)
		compositeKey := timeStampString + "-" + uuidStr

		if _, value := uploadMap[compositeKey]; !value {
			excelModel = &model.Excel{ID: uuidStr, Filename: filename, URL: url, Status: uploadStatus, DateCreated: timeStampString}
			if excelType != nil {
				excelModel.ExcelType = *excelType
			}
			excelModel.ValidationResult = &model.ValidationResult{Error: false, ValidationTimeTaken: validationTime.String()}
			uploadMap[compositeKey] = excelModel
		} else {
			excelModel = uploadMap[compositeKey]
		}

		if row != nil && message != nil {
			if !excelModel.ValidationResult.Error {
				excelModel.ValidationResult.Error = true
			}
			validationMessageModel := &model.ExcelValidationMessage{Row: *row, Message: *message}
			excelModel.ValidationResult.ExcelValidationMessages = append(excelModel.ValidationResult.ExcelValidationMessages, validationMessageModel)
		}
	}

	keys := make([]string, 0, len(uploadMap))
	for k := range uploadMap {
		keys = append(keys, k)
	}
	sort.SliceStable(keys, func(i, j int) bool {
		return keys[j] < keys[i]
	})
	for _, key := range keys {
		response.Excels = append(response.Excels, uploadMap[key])
	}
	if response.Excels == nil {
		response.Message = "No data found"
	}

	return response
}
