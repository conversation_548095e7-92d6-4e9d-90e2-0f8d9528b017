package veeva

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/ihcp/form/graph/entity"
)

func MedicalEventInsertIntoVeeva(veevaReferenceId string, inputEntity entity.InputForInsertion) (entity.InsertionResponse, string, error) {
	log.Println("MedicalEventInsertIntoVeeva()", inputEntity)

	url := GetVeevaBaseURL(os.Getenv("AUTH_VEEVA_URL")) + `/services/data/v54.0/sobjects/Medical_Event_vod__c`
	method := "POST"
	if veevaReferenceId != "" {
		url += "/Id/" + veevaReferenceId
		method = "PATCH"
	}

	body, err := json.Marshal(inputEntity)
	if err != nil {
		panic(err)
	}

	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}

	bearer := "Bearer " + os.Getenv("AUTH_VEEVA_TOKEN")
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	log.Println("--- *Request: \n", request.Header, url)
	log.Println("--- *Payload: \n", string(body))
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()
	log.Println("--- *Response: \n", response)

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	var responseEntity entity.InsertionResponse

	if response.StatusCode != http.StatusCreated && response.StatusCode != http.StatusOK {
		var txnError []entity.VeevaErrResponse
		if err := json.Unmarshal(responseBody, &txnError); err != nil {
			return responseEntity, "", errors.New(fmt.Sprintf(`%v - %v`, response.StatusCode, string(responseBody)))
			panic(err)
		}

		return responseEntity, "", errors.New(txnError[0].Message)
	}

	if err = json.Unmarshal(responseBody, &responseEntity); err != nil {
		panic(err)
	}

	return responseEntity, url, nil
}

func GetVeevaRecordsByEventId(eventId string) ([]entity.VeevaRecords, error) {
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(os.Getenv("AUTH_VEEVA_URL"))
	url := veevaInstanceURL + `/services/data/v54.0/query?q=Select+id+,+ZLG_eZFlow_ID__c+FROM+Medical_Event_vod__c+where+ZLG_eZFlow_ID__c+=+'` + eventId + `'+order+by+CreatedDate+desc+limit+1`
	request, err := http.NewRequest("GET", strings.ReplaceAll(url, " ", ""), bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}

	bearer := "Bearer " + os.Getenv("AUTH_VEEVA_TOKEN")
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	if response.StatusCode != http.StatusOK {
		return nil, errors.New(string(responseBody))
	}

	var responseUrlValues entity.VeevaIdResponse
	if err := json.Unmarshal(responseBody, &responseUrlValues); err != nil {
		panic(err)
	}

	for responseUrlValues.NextRecordsURL != "" {
		res := func() *entity.VeevaIdResponse {
			url = veevaInstanceURL + responseUrlValues.NextRecordsURL
			requestForNextRecord, err := http.NewRequest("GET", url, bytes.NewBuffer(body))
			if err != nil {
				panic(err)
			}
			requestForNextRecord.Header.Add("Authorization", bearer)
			requestForNextRecord.Header.Set("Content-Type", "application/json")

			responseForNextRecord, err := client.Do(requestForNextRecord)
			if err != nil {
				panic(err)
			}
			defer responseForNextRecord.Body.Close()

			responseForNextRecordBody, err := io.ReadAll(responseForNextRecord.Body)
			if err != nil {
				panic(err)
			}

			var res entity.VeevaIdResponse

			if err := json.Unmarshal(responseForNextRecordBody, &res); err != nil {
				panic(err)
			}

			return &res
		}()

		responseUrlValues.Records = append(responseUrlValues.Records, res.Records...)
		responseUrlValues.NextRecordsURL = res.NextRecordsURL
	}

	return responseUrlValues.Records, nil
}
