package ad_service

import (
	"context"
	"fmt"
	"github.com/go-ldap/ldap/v3"
	"log"
)

func (s *Service) reDial() {
	s.m.Lock()
	defer s.m.Unlock()

	s.dial()
	log.Println(`>>> Redialing success`)
}

func (s *Service) authenticateInternal(ctx context.Context, username, password string) error {
	searchReq := ldap.NewSearchRequest(
		s.internalBaseDN,
		ldap.ScopeWholeSubtree,
		ldap.NeverDerefAliases,
		0,
		10,
		false,
		fmt.Sprintf("(sAMAccountName=%v)", username),
		[]string{"userPrincipalName"},
		nil,
	)
	if err := ctx.Err(); err != nil {
		return err
	}
	res, err := s.internalConn.Search(searchReq)
	if err != nil {
		panic(err)
	}

	if len(res.Entries) == 0 {
		return ErrUserNotFound
	}

	email := res.Entries[0].GetAttributeValue("userPrincipalName")
	if s.internalConn.Bind(email, password) != nil {
		return ErrInvalidCredentials
	}
	return nil
}

func (s *Service) authenticateExternal(ctx context.Context, username, password string) error {
	searchReq := ldap.NewSearchRequest(
		s.externalBaseDN,
		ldap.ScopeWholeSubtree,
		ldap.NeverDerefAliases,
		0,
		10,
		false,
		fmt.Sprintf("(sAMAccountName=%v)", username),
		[]string{"userPrincipalName"},
		nil,
	)

	if err := ctx.Err(); err != nil {
		return err
	}
	res, err := s.externalConn.Search(searchReq)
	if err != nil {
		panic(err)
	}

	if len(res.Entries) == 0 {
		return ErrUserNotFound
	}

	email := res.Entries[0].GetAttributeValue("userPrincipalName")
	if s.externalConn.Bind(email, password) != nil {
		return ErrInvalidCredentials
	}

	return nil
}
