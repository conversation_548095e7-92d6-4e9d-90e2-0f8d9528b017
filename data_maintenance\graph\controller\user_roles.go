package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertUserRolesData(ctx *context.Context, inputModel model.UserRolesInput) *model.UpsertResponse {
	var upsertResponse model.UpsertResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
	entity, err := mapper.MapUserRolesModelToEntity(&inputModel, userUUID)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	if entity.ID == nil && !entity.IsDeleted {
		err := postgres.InsertUserRoles(entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "User roles successfully inserted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else if entity.IsDeleted {
		err := postgres.DeleteUserRoles(entity, *userID)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "User roles successfully deleted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else {
		err := postgres.UpdateUserRoles(&inputModel, entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "User roles successfully updated"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
}

func FetchUserRoles(ctx *context.Context, input *model.UserRoleRequest) *model.UserRoleResponse {
	var response model.UserRoleResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var err error
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	if input.UserRole != nil {
		if *input.UserRole == "" {
			response.Error = true
			response.Message = "User role cannot be blank"
			return &response
		}
	}
	values, err := postgres.GetUserRolesInfo(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if values == nil {
		response.Error = true
		response.Message = "No data exists"
		return &response
	}
	fetchUserRolesValue := mapper.FetchUserRolesEntityToModel(values)
	response.Error = false
	response.Data = fetchUserRolesValue
	return &response
}
