package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertEventTypeAccessPermission(ctx *context.Context, inputModel *model.EventTypeAccessPermissionUpsertInput) *model.UpsertEventTypeAccessPermissionResponse {
	upsertResponse := &model.UpsertEventTypeAccessPermissionResponse{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)

	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else if !postgres.CheckUserApprovalRoleByMasterData(*approvalRole) {
		upsertResponse.Error = true
		upsertResponse.Message = "User don't have access!!"
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, err := mapper.MapEventTypeAccessPermissionModelToEntity(inputModel, userUUID)

	if err == nil {
		if entity.ID == nil && !entity.IsDelete {
			err = postgres.InsertEventTypeAccessPermissionData(entity)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully inserted EventTypeAccessPermission data"
			}
		} else if entity.IsDelete {
			err = postgres.DeleteEventTypeAccessPermissionData(entity)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully Deleted EventTypeAccessPermission data"
			}
		} else {
			err = postgres.UpdateEventTypeAccessPermissionData(inputModel, entity)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully Updated EventTypeAccessPermission data"
			}
		}
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
	}
	return upsertResponse
}

func FetchEventTypeAccessPermission(ctx *context.Context, inputModel *model.EventTypeAccessPermissionInput) *model.EventTypeAccessPermissionResponse {
	var response model.EventTypeAccessPermissionResponse
	userId := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	if !postgres.CheckUserApprovalRoleByMasterData(*approvalRole) {
		response.Error = true
		response.Message = "You are not allowed to access!!"
		return &response
	}
	eventTypeAccessPermissionData, _ := postgres.GetEventTypeAccessPermission(inputModel, country)
	responseModel := mapper.FetchEventTypeAccessPermissionEntityToModel(eventTypeAccessPermissionData)
	if responseModel == nil {
		response.Error = false
		response.Message = "No data found"
		return &response
	}
	response.Error = false
	response.Message = "Successfully fetched Event type access Permissions"
	response.Data = responseModel
	return &response
}
