INSERT INTO form
(id, "name", "type", is_active, is_deleted, created_by, modified_by, date_created, last_modified, design, version_no)
VALUES('da22a3d9-4bea-4e04-94b1-4b94839356ed'::uuid, 'Form Design 13', 2, true, false, '00000000-0000-0000-0000-000000000000'::uuid, NULL, '2023-08-08 13:53:31.801', NULL, '[{"id": "activities-selection", "title": "Activities Selection", "sections": [{"id": "activities-selection-sections-1", "form": {"id": "activities-selection-section-1-form-1", "group": [{"id": "activities-selection-section-1-form-1-group1", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "type-of-ihcp-activity", "type": "dropdown", "rules": null, "title": "Type of IHCP activity", "hidden": false, "source": "code|ActivityType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "type-of-event", "type": "dropdown", "rules": null, "title": "Type of Event", "hidden": false, "source": "code|ActivityEventType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}], "groupId": "activities-selection-section-1-form-1-group1-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 1}], "title": null, "sequenceNo": 1}, "title": null, "childForm": null, "sequenceNo": 1}], "sequenceNo": 1}, {"id": "basic-info", "title": "Basic Info", "sections": [{"id": "basic-info-section-1", "form": {"id": "basic-info-section-1-form-1", "group": [{"id": "basic-info-section-1-form-1-group1", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "event-requestor", "type": "text", "rules": null, "title": "Event Requestor", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "event-owner", "type": "dropdown", "rules": null, "title": "Event owner", "hidden": false, "source": "user", "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}, {"id": "activity-name", "type": "text", "rules": null, "title": "Activity Name", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}, {"id": "activity-start-date", "type": "date", "rules": null, "title": "Activity Start Date", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 4, "validations": null, "validatesource": "controls|StartDate"}, {"id": "activity-end-date", "type": "date", "rules": null, "title": "Activity End Date", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 5}, {"id": "duration-of-the-activity", "type": "text", "rules": null, "title": "Duration of the Activity", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 6}, {"id": "product", "type": "dropdown", "rules": null, "title": "Product", "hidden": false, "source": "material", "values": null, "readOnly": false, "operation": null, "sequenceNo": 7}, {"id": "country", "type": "dropdown", "rules": null, "title": "Country", "hidden": false, "source": "code|Country", "values": null, "readOnly": false, "operation": null, "sequenceNo": 8}, {"id": "team", "type": "dropdown", "rules": null, "title": "Team", "hidden": false, "source": "team", "values": null, "readOnly": false, "operation": null, "sequenceNo": 9}, {"id": "therapeutic-area", "type": "text", "rules": null, "title": "Therapeutic Area", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 10}, {"id": "event-organizer", "type": "text", "rules": null, "title": "Event Organizer", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 11}, {"id": "venue", "type": "text", "rules": null, "title": "Venue", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 12}, {"id": "target-audience", "type": "text", "rules": null, "title": "Target Audience (Specialization)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 13}, {"id": "material-code-number", "type": "text", "rules": null, "title": "Material Code Number", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 14}, {"id": "meeting-objective", "type": "text", "rules": null, "title": "Meeting Objective (Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 15}, {"id": "no-of-hcps", "type": "number", "rules": null, "title": "No of HCPs", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 16}, {"id": "no-of-non-hcps", "type": "number", "rules": null, "title": "No of Non HCPs", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 17}, {"id": "employee-no", "type": "number", "rules": null, "title": "No of Employees", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 18}, {"id": "do-you-want-to-add-any-attachment-for-agenda-or-proposal", "type": "upload", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-for-agenda-or-proposal-show", "action": "show"}]}], "title": "Upload attachments for Agenda (Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 19}, {"id": "attendees-attachments", "type": "upload", "rules": [{"value": "true", "actions": [{"id": "attendees-attachments-show", "action": "show"}]}], "title": "Upload attachments for Attendees (Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 20}, {"id": "proposal-attachments", "type": "upload", "rules": [{"value": "true", "actions": [{"id": "attendees-attachments-show", "action": "show"}]}], "title": "Upload attachments for Proposal (Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 21}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 22}, {"id": "promotional-or-non-promotional-event", "type": "dropdown", "rules": null, "title": "Promotional or non-promotional event", "hidden": false, "source": "code|HcpPromotional", "values": null, "readOnly": false, "operation": null, "sequenceNo": 23}, {"id": "meeting-mode", "type": "dropdown", "rules": null, "title": "Meeting Mode", "hidden": true, "source": "code|MeetingMode", "values": null, "readOnly": false, "operation": null, "sequenceNo": 26}, {"id": "details-of-the-virtual-event", "type": "text", "rules": null, "title": "Details of the Virtual Event", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 25}, {"id": "linked-event-id", "type": "dropdown", "rules": null, "title": "Linked Event ID", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 26}, {"id": "name-of-organization", "type": "text", "rules": null, "title": "Name of Organization", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 27}, {"id": "nature-of-hcohci", "type": "text", "rules": null, "title": "Nature of HCO/HCI", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 28}, {"id": "is-the-recipient-owned-or-controlled-by-a-government-or-government-official", "type": "radio", "rules": [{"value": "true", "actions": [{"id": "details-of-the-recipient-owned-or-controlled-by-a-government", "action": "show"}]}], "title": "Is the Recipient owned or controlled by a Government or Government Official?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 29}, {"id": "details-of-the-recipient-owned-or-controlled-by-a-government", "type": "text", "rules": null, "title": "Please Provide Additional Information", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 30}, {"id": "name-of-contact-person", "type": "text", "rules": null, "title": "Name of Contact Person", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 31}, {"id": "purpose-of-grant", "type": "text", "rules": null, "title": "Purpose of grant", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 32}, {"id": "in-return-will-zuellig-pharma-be-benefiting-from-this-grant?", "type": "radio", "rules": [{"value": "true", "actions": [{"id": "details-of-the-zuellig-pharma-be-benefiting-from-this-grant", "action": "show"}]}], "title": "In return, will Zuellig Pharma be benefiting from this grant?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 33}, {"id": "details-of-the-zuellig-pharma-be-benefiting-from-this-grant", "type": "text", "rules": null, "title": "Please provide more details", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 34}, {"id": "any-red-flags?", "type": "radio", "rules": [{"value": "true", "actions": [{"id": "details-of-the-red-flags", "action": "show"}, {"id": "has-the-red-flag-been-resolved?", "action": "show"}]}], "title": "Any Red Flags?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 35}, {"id": "details-of-the-red-flags", "type": "text", "rules": null, "title": "Please provide more details", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 36}, {"id": "has-the-red-flag-been-resolved?", "type": "radio", "rules": [{"value": "true", "actions": [{"id": "has-the-red-flag-been-resolved-provide-more-details", "action": "show"}]}], "title": "Has the Red Fag been resolved?", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 37}, {"id": "has-the-red-flag-been-resolved-provide-more-details", "type": "text", "rules": null, "title": "Please provide more details", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 38}, {"id": "upload-documents-of-ddq", "type": "upload", "rules": null, "title": "Upload Documents Of DDQ(Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 39}, {"id": "upload-documents-of-request-letter", "type": "upload", "rules": null, "title": "Upload Documents Of Request Letter(Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 40}, {"id": "upload-documents-of-screening-report", "type": "upload", "rules": null, "title": "Upload Documents Of Screening Report(Required)", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 41}, {"id": "product-owner", "type": "dropdown", "rules": null, "title": "Client", "hidden": false, "source": "product_owner", "values": null, "readOnly": false, "operation": null, "sequenceNo": 42}, {"id": "sponsorship-to-hco-requesting-hco", "type": "text", "rules": null, "title": "Requesting HCO", "hidden": false, "source": "sponsorship-to-hco-specific-to-hco", "values": null, "readOnly": false, "operation": null, "sequenceNo": 43}, {"id": "sponsorship-to-hco-recipient", "type": "text", "rules": null, "title": "Recipient", "hidden": false, "source": "sponsorship-to-hco-specific-to-hco", "values": null, "readOnly": false, "operation": null, "sequenceNo": 44}, {"id": "general-remarks", "type": "text", "rules": null, "title": "General Remarks", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 45}], "groupId": "basic-info-section-1-form-1-group1-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 1}], "title": null, "sequenceNo": 1}, "title": null, "childForm": null, "sequenceNo": 1}], "sequenceNo": 2}, {"id": "hcp-engagement-info", "title": "HCP Engagement Info", "sections": [{"id": "hcp-engagement-info-section-1", "form": {"id": "hcp-engagement-info-section-1-form-1", "group": [{"id": "hcp-engagement-info-section-1-form-1-group1", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "typeOfEngagement", "type": "dropdown", "rules": null, "title": "Type of Engagement", "hidden": false, "source": "code|HcpTypeEngagement", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-20", "groupRules": null, "sequenceNo": 20}, {"title": null, "hidden": false, "inputs": [{"id": "individualCategory", "type": "dropdown", "rules": null, "title": "Individual Category", "hidden": false, "source": "code|HcpTypeIndividual", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-19", "groupRules": null, "sequenceNo": 19}, {"title": null, "hidden": false, "inputs": [{"id": "type-of-hcp", "type": "dropdown", "rules": null, "title": "Type of HCP", "hidden": false, "source": "code|HcpType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-1", "groupRules": [{"value": "foreign", "actions": [{"action": "hide", "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-3"}, {"action": "show", "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-4"}, {"action": "show", "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-12"}]}], "sequenceNo": 1}, {"title": null, "hidden": false, "inputs": [{"id": "govt-or-non-govt-hcp", "type": "dropdown", "rules": null, "title": "Govt or Non-Govt HCP", "hidden": false, "source": "code|HcpGovtType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-2", "groupRules": null, "sequenceNo": 2}, {"title": null, "hidden": false, "inputs": [{"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-2a", "groupRules": null, "sequenceNo": 2}, {"title": null, "hidden": false, "inputs": [{"id": "speaker-name", "type": "dropdown", "rules": null, "title": "Speaker Name", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-3", "groupRules": null, "sequenceNo": 3}, {"title": null, "hidden": true, "inputs": [{"id": "speaker-name-text", "type": "text", "rules": null, "title": "Speaker Name", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-4", "groupRules": null, "sequenceNo": 4}, {"title": null, "hidden": false, "inputs": [{"id": "base-type", "type": "dropdown", "rules": null, "title": "Base Type", "hidden": false, "source": "code|BaseType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-5", "groupRules": null, "sequenceNo": 5}, {"title": null, "hidden": false, "inputs": [{"id": "hcp-country", "type": "dropdown", "rules": null, "title": "Country", "hidden": false, "source": "code|Country", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-6", "groupRules": null, "sequenceNo": 6}, {"title": null, "hidden": false, "inputs": [{"id": "specialty", "type": "text", "rules": null, "title": "Specialty", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-7", "groupRules": null, "sequenceNo": 7}, {"title": null, "hidden": false, "inputs": [{"id": "hco-institute-name", "type": "text", "rules": null, "title": "HCO/Institute Name", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-8", "groupRules": null, "sequenceNo": 8}, {"title": null, "hidden": false, "inputs": [{"id": "expert-level", "type": "dropdown", "rules": null, "title": "Expert Level", "hidden": false, "source": "code|HCPTier", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-9a", "groupRules": null, "sequenceNo": 9}, {"title": null, "hidden": false, "inputs": [{"id": "expert-level-international-others", "type": "dropdown", "rules": null, "title": "International Expert Level", "hidden": true, "source": "code|ExpertLevelInternational", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-9b", "groupRules": null, "sequenceNo": 9}, {"title": null, "hidden": true, "inputs": [{"id": "expert-level-text", "type": "text", "rules": null, "title": "Expert Level", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-10", "groupRules": null, "sequenceNo": 10}, {"title": null, "hidden": false, "inputs": [{"id": "preparation-time", "type": "text", "rules": null, "title": "Preparation Time", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-11", "groupRules": null, "sequenceNo": 11}, {"title": null, "hidden": false, "inputs": [{"id": "service-time", "type": "text", "rules": null, "title": "Service Time", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-12", "groupRules": null, "sequenceNo": 12}, {"title": null, "hidden": false, "inputs": [{"id": "hourly-rate", "type": "text", "rules": null, "title": "Hourly Rate", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-13", "groupRules": null, "sequenceNo": 13}, {"title": null, "hidden": false, "inputs": [{"id": "more-than-2-lectures-meetings", "type": "checkbox", "rules": null, "title": "More than 2 lectures/meetings", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1, "validations": null, "validatesource": null}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-13a", "groupRules": null, "sequenceNo": 13}, {"title": null, "hidden": false, "inputs": [{"id": "role", "type": "dropdown", "rules": null, "title": "Role", "hidden": false, "source": "code|RoleMaxPaidTime", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-14", "groupRules": null, "sequenceNo": 14}, {"title": null, "hidden": true, "inputs": [{"id": "role-text", "type": "text", "rules": null, "title": "Role", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-15", "groupRules": null, "sequenceNo": 15}, {"title": null, "hidden": false, "inputs": [{"id": "calculated-honorarium", "type": "text", "rules": null, "title": "Calculated/Max Honorarium", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2, "validations": null, "validatesource": null}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-16", "groupRules": null, "sequenceNo": 16}, {"title": null, "hidden": false, "inputs": [{"id": "proposed-honorarium", "type": "text", "rules": null, "title": "Proposed Payable Honorarium", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2, "validations": null, "validatesource": "controls|Honorarium"}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-17", "groupRules": null, "sequenceNo": 17}, {"title": null, "hidden": false, "inputs": [{"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-show", "action": "show"}]}], "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-18", "groupRules": null, "sequenceNo": 18}], "sequenceNo": 1}], "title": null, "sequenceNo": 1}, "title": null, "childForm": {"id": "hcp-engagement-info-section-1-child-form-1", "group": [{"id": "hcp-engagement-info-section-1-child-form-1-group1", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [], "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": null, "inputs": [{"id": "registration-fee", "type": "number", "rules": null, "title": "Registration Fee", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-2", "groupRules": null, "sequenceNo": 2}, {"title": null, "hidden": false, "inputs": [{"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-show", "action": "show"}]}], "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-3", "groupRules": null, "sequenceNo": 3}], "sequenceNo": 1}, {"id": "hcp-engagement-info-section-1-child-form-1-group2", "title": "Transportation", "questions": [{"title": null, "hidden": false, "inputs": [{"id": "ground-transportation", "type": "number", "rules": null, "title": "Ground Transportation", "hidden": false, "source": null, "values": null, "readOnly": null, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": false, "inputs": [{"id": "airfare", "type": "number", "rules": null, "title": "Airfare", "hidden": false, "source": null, "values": null, "readOnly": null, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-2", "groupRules": null, "sequenceNo": 2}, {"title": null, "hidden": false, "inputs": [{"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-show", "action": "show"}]}], "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-3", "groupRules": null, "sequenceNo": 3}], "sequenceNo": 2}, {"id": "hcp-engagement-info-section-1-child-form-1-group3", "title": "Meal Package", "questions": [{"title": null, "hidden": false, "inputs": [{"id": "type-of-meal", "type": "dropdown", "rules": null, "title": "Type of Meal", "hidden": false, "source": "code|ExpenseLimit", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group3-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": false, "inputs": [{"id": "no-of-meal", "type": "number", "rules": null, "title": "No of Meal", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": "", "sequenceNo": 1}, {"id": "cost-per-meal", "type": "number", "rules": null, "title": "Cost per Meal", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": "=", "sequenceNo": 2, "validations": null, "validatesource": "controls|Meal"}, {"id": "total-cost", "type": "number", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}, {"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-show", "action": "show"}]}], "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 4}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 5}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "hcp-engagement-info-section-1-child-form-1-group3-questions-2", "groupRules": null, "sequenceNo": 2}], "sequenceNo": 3}, {"id": "hcp-engagement-info-section-1-child-form-1-group4", "title": "Accomodation", "questions": [{"title": null, "hidden": false, "inputs": [{"id": "no-of-days", "type": "number", "rules": null, "title": "No of Days", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": "", "sequenceNo": 1}, {"id": "cost-per-day", "type": "number", "rules": null, "title": "Cost per Day", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": "=", "sequenceNo": 2}, {"id": "total-cost", "type": "number", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}, {"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": null, "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 4}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 5}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 6}], "groupId": "hcp-engagement-info-section-1-child-form-1-group4-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 4}, {"id": "hcp-engagement-info-section-1-child-form-1-group5", "title": "Others", "questions": [{"title": null, "hidden": false, "inputs": [{"id": "name-of-expense", "type": "text", "rules": null, "title": "Name of Expense", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group5-questions-1", "groupRules": null, "sequenceNo": 1}, {"id": "hcp-engagement-info-section-1-child-form-1-group6", "title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "number", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group6-questions-1", "groupRules": null, "sequenceNo": 2}, {"id": "hcp-engagement-info-section-1-child-form-1-group7", "title": null, "hidden": false, "inputs": [{"id": "do-you-want-to-add-any-attachment", "type": "checkbox", "rules": [{"value": "true", "actions": [{"id": "do-you-want-to-add-any-attachment-show", "action": "show"}]}], "title": "Do you want to add any attachment?", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}, {"id": "upload", "type": "button", "rules": null, "title": "Upload", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 2}, {"id": "remarks", "type": "text", "rules": null, "title": "Remarks", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "hcp-engagement-info-section-1-child-form-1-group7-questions-1", "groupRules": null, "sequenceNo": 3}], "sequenceNo": 5}, {"id": "hcp-engagement-info-section-1-child-form-1-group8", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "hcp-engagement-info-section-1-child-form-1-group8-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 6}], "title": "Expenses", "sequenceNo": 1}, "sequenceNo": 1}], "sequenceNo": 3}, {"id": "event-expenses", "title": "Event Expenses", "sections": [{"id": "event-expenses-section-1", "form": {"id": "event-expenses-section-1-form-1", "group": [{"id": "event-expenses-section-1-form-1-group1", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "type-of-event-expenses", "type": "dropdown", "rules": [{"value": "logisticsmaterials", "actions": [{"id": "event-expenses-section-1-form-1-group3", "action": "show"}]}, {"value": "accomodation", "actions": [{"id": "event-expenses-section-1-form-1-group2", "action": "show"}]}, {"value": "meals", "actions": [{"id": "event-expenses-section-1-form-1-group2", "action": "show"}]}, {"value": "sponsorpackage", "actions": [{"id": "event-expenses-section-1-form-1-group4", "action": "show"}]}, {"value": "others", "actions": [{"id": "event-expenses-section-1-form-1-group5", "action": "show"}]}, {"value": "proposedfundingamount", "actions": [{"id": "event-expenses-section-1-form-1-group6", "action": "show"}]}], "title": "Type of Event Expenses", "hidden": false, "source": "code|EventExpenseType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group1-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": true, "inputs": [{"id": "type-meeting-package", "type": "dropdown", "rules": null, "title": "Type of Meeting Package", "hidden": false, "source": "code|MeetingPackageType", "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group1-questions-2", "groupRules": [{"value": "others", "actions": [{"action": "show", "groupId": "event-expenses-section-1-form-1-group1-questions-3"}]}], "sequenceNo": 2}, {"title": null, "hidden": true, "inputs": [{"id": "meeting-package-others", "type": "text", "rules": null, "title": "Others", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group1-questions-3", "groupRules": null, "sequenceNo": 3}], "sequenceNo": 1}, {"id": "event-expenses-section-1-form-1-group2", "title": null, "questions": [{"title": null, "hidden": true, "inputs": [{"id": "no-of-people", "type": "number", "rules": null, "title": "No of people|No of days", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": "*", "sequenceNo": 1}, {"id": "fare", "type": "number", "rules": null, "title": "Fee", "hidden": true, "source": null, "values": null, "readOnly": false, "operation": "=", "sequenceNo": 2}, {"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 3}], "groupId": "event-expenses-section-1-form-1-group2-questions-1", "groupRules": null, "sequenceNo": 2}], "sequenceNo": 2}, {"id": "event-expenses-section-1-form-1-group3", "title": null, "questions": [{"title": null, "hidden": true, "inputs": [{"id": "type-of-transport", "type": "text", "rules": null, "title": "Type of Transport", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group3-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group3-questions-2", "groupRules": null, "sequenceNo": 2}], "sequenceNo": 3}, {"id": "event-expenses-section-1-form-1-group4", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group4-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 4}, {"id": "event-expenses-section-1-form-1-group5", "title": null, "questions": [{"title": null, "hidden": true, "inputs": [{"id": "type-of-others", "type": "text", "rules": null, "title": "Type of Others", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group5-questions-1", "groupRules": null, "sequenceNo": 1}, {"title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group5-questions-2", "groupRules": null, "sequenceNo": 2}], "sequenceNo": 5}, {"id": "event-expenses-section-1-form-1-group6", "title": null, "questions": [{"title": null, "hidden": false, "inputs": [{"id": "total-cost", "type": "text", "rules": null, "title": "Total Cost", "hidden": false, "source": null, "values": null, "readOnly": false, "operation": null, "sequenceNo": 1}], "groupId": "event-expenses-section-1-form-1-group6-questions-1", "groupRules": null, "sequenceNo": 1}], "sequenceNo": 6}], "title": null, "sequenceNo": 1}, "title": null, "childForm": null, "sequenceNo": 1}], "sequenceNo": 4}]'::jsonb, 15);
