package postgres

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"math"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/jackc/pgx/v4"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/veeva"
)

func CreateEventLog(eventCode string) error {
	pool := GetPool()
	s := strings.Split(eventCode, `-`)
	if len(s) != 4 {
		return errors.New(`err_invalid_event_id`)
	}

	queryString := `select id
	from form_answers 
	where event_code = $1 and event_seq = $2`

	var formAnswerId string
	err := pool.QueryRow(context.Background(), queryString, fmt.Sprintf(`%v-%v-%v`, s[0], s[1], s[2]), s[3]).Scan(&formAnswerId)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return errors.New(`err_event_not_found`)
		}

		panic(err)
	}

	var placeholder int
	err = pool.QueryRow(context.Background(),
		`select 1
	from veeva_event_logs
	where form_answers_id = $1`, formAnswerId).Scan(&placeholder)
	if err != nil && !errors.Is(err, pgx.ErrNoRows) {
		panic(err)

	}

	if placeholder != 0 {
		return nil
	}

	query := ` INSERT INTO veeva_event_logs("form_answers_id", "date_created", "date_retry", "veeva_reference_id") VALUES
			($1, now(), now(), '');`

	_, err = pool.Exec(context.Background(), query, formAnswerId)
	if err != nil {
		panic(err)
	}

	return nil

}

func InsertIntoVeeva(formAnswerId string, status string) (err error) {
	defer func() {
		if r := recover(); r != nil {
			UpsertVeevaEventLogs(context.Background(), formAnswerId,
				false, false, "", "", fmt.Sprintf(`panic_err:%v`, r))
			log.Println(fmt.Sprintf("--- Panic when sync events to Veeva %s", r))
			err = errors.New(`panic when sync events to Veeva`)
		}
	}()

	log.Println(" >>>>>>>>>>>>>>>>>>>>>>>  InsertIntoVeeva() >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")

	loc, _ := time.LoadLocation("UTC")
	ctx := context.Background()

	// ---------------------- Start Transaction
	//pool := GetPool()
	//tx, err := pool.Begin(ctx)
	//if err != nil {
	//	panic(err)
	//}
	//defer tx.Rollback(ctx)

	dataList := GetEventDetails(ctx, formAnswerId)
	if dataList == nil {
		return errors.New("event not found")
	}

	if dataList.OwnerId.String == "" && dataList.RequestorVeevaId.String == "" {
		return errors.New("ezf_err:user_veeva_reference_id_is_null")
	}

	veevaAllowedCountries := strings.Split(GetVeevaEventCountries(ctx), "|")
	if dataList.Country.String != "" {
		var isEventCountryInVeevaAllowedCountries bool
		for _, country := range veevaAllowedCountries {
			if country == dataList.Country.String {
				isEventCountryInVeevaAllowedCountries = true
				break
			}
		}

		if isEventCountryInVeevaAllowedCountries == false {
			return nil
		}
	}

	var veevaReferenceId string
	var insertEntity entity.InputForInsertion

	veevaResponse, err := veeva.GetVeevaRecordsByEventId(dataList.EventId.String)
	if err != nil {
		UpsertVeevaEventLogs(context.Background(), formAnswerId,
			false, false, "", "", fmt.Sprintf(`veeva_err:%v`, err.Error()))
		return err
	}

	for _, v := range veevaResponse {
		if v.ZLGEZFlowIDC == dataList.EventId.String {
			veevaReferenceId = v.ID
		}
		break
	}

	activityStartDate, _ := strconv.Atoi(dataList.ActivityStartDate)
	activityStartDateTime := time.Unix(int64(activityStartDate), 0)
	activityStartDateUTC := activityStartDateTime.In(loc)
	activityStartDateFormatted := activityStartDateUTC.Format("2006-01-02T15:04:05-0700")
	maxWorkingHours, _ := strconv.ParseFloat(os.Getenv("MAXIMUM_WORKING_HOURS"), 64)
	activityEndDate, _ := strconv.Atoi(dataList.ActivityEndDate)
	activityEndDateTime := time.Unix(int64(activityEndDate), 0)
	activityEndDateUTC := activityEndDateTime.In(loc)
	activityEndDateFormatted := activityEndDateUTC.Format("2006-01-02T15:04:05-0700")
	durationHours := activityEndDateTime.Sub(activityStartDateTime).Hours()
	duration := (durationHours * maxWorkingHours) / 24
	durationHoursRounded := math.Ceil(duration*100) / 100
	country := dataList.Country.String

	noOfProducts := len(dataList.ProductList)
	var productVeevaReferenceId []string
	if noOfProducts != 0 {
		productVeevaReferenceId, err = GetProduct(dataList.ProductList, country)
		if err != nil {
			panic(err)
		}

		if len(productVeevaReferenceId) == 1 {
			insertEntity.PrimaryProductId = productVeevaReferenceId[0]
		} else if len(productVeevaReferenceId) == 2 {
			insertEntity.PrimaryProductId = productVeevaReferenceId[0]
			insertEntity.SecondaryProduct = productVeevaReferenceId[1]
		} else if len(productVeevaReferenceId) == 3 {
			insertEntity.PrimaryProductId = productVeevaReferenceId[0]
			insertEntity.SecondaryProduct = productVeevaReferenceId[1]
			insertEntity.TertiaryProduct = productVeevaReferenceId[2]

		}
	}

	insertEntity.RequestorId = dataList.RequestorName.String
	insertEntity.FullVenueLocation = dataList.Venue.String
	insertEntity.FullActivityName = dataList.ActivityName.String
	insertEntity.ActivityName = dataList.ActivityName.String
	insertEntity.ActivityType = dataList.ActivityType.String
	insertEntity.CountryCodeQsC = dataList.Country.String
	insertEntity.ActivityStartDate = activityStartDateFormatted
	insertEntity.ActivityEndDate = activityEndDateFormatted
	insertEntity.EventId = dataList.EventId.String
	insertEntity.EventName = dataList.EventType.String
	insertEntity.Venue = dataList.Venue.String
	insertEntity.Duration = int(durationHoursRounded)
	insertEntity.EZFlowLedBy = dataList.LedBy.String

	if len(dataList.Venue.String) > 40 {
		insertEntity.Venue = dataList.Venue.String[:40]
	}

	if len(dataList.ActivityName.String) > 80 {
		insertEntity.ActivityName = dataList.ActivityName.String[:80]
	}

	insertEntity.OwnerId = dataList.RequestorVeevaId.String
	if dataList.OwnerId.String != "" {
		insertEntity.OwnerId = dataList.OwnerId.String
	}

	// ----------------------------------------- Call to Veeva to check status of the owner user
	var userData []entity.UsersRecords
	userData, err = VeevaGetUsersData(insertEntity.OwnerId, os.Getenv("AUTH_VEEVA_TOKEN"), os.Getenv("AUTH_VEEVA_URL"))
	if err != nil {
		UpsertVeevaEventLogs(context.Background(), formAnswerId,
			false, false, "", "", fmt.Sprintf(`veeva_err:%v`, err.Error()))
		return err
	}

	for _, val := range userData {
		if !val.IsActive {
			err = errors.New(`veeva_err:inactive_event_owner`)
			UpsertVeevaEventLogs(context.Background(), formAnswerId,
				false, false, "", "", err.Error())
			return err
		}
	}
	// ----------------------------------------- Call to Veeva to check status of the owner user

	insertEntity.Status = status

	insertEntity.VirtualEvent = "No"
	if CheckMeetingModeTitleExist(strings.TrimSpace(dataList.MeetingMode.String)) {
		insertEntity.VirtualEvent = "Yes"
		insertEntity.MeetingMode = strings.TrimSpace(dataList.MeetingMode.String)
	}

	insertEntity.VirtualEventDetails = "N/A"
	if strings.TrimSpace(dataList.VirtualEventDetails.String) != "" {
		insertEntity.VirtualEventDetails = strings.TrimSpace(dataList.VirtualEventDetails.String)
	}

	// Send event data to Veeva
	//type InputForInsertion struct {
	//	ActivityName        string `json:"Name"`
	//	ActivityType        string `json:"ZLG_eZFlow_Activity_Type__c"`
	//	CountryCodeQsC      string `json:"Country_Code_QS__c"`
	//	ActivityStartDate   string `json:"Start_Time_vod__c"`
	//	ActivityEndDate     string `json:"End_Time_vod__c"`
	//	Duration            int    `json:"Duration_hrs_QS__c"`
	//	EventId             string `json:"ZLG_eZFlow_ID__c"`
	//	Venue               string `json:"Location__c"`
	//	Status              string `json:"ZLG_eZFlow_Status__c"`
	//	RequestorId         string `json:"ZLG_eZFlow_Event_Requestor__c"`
	//	OwnerId             string `json:"OwnerId"`
	//	PrimaryProductId    string `json:"Product_QS__c"`
	//	SecondaryProduct    string `json:"ZLG_Secondary_Product__c"`
	//	TertiaryProduct     string `json:"ZLG_Tertiary_Product__c"`
	//	EventName           string `json:"ZLG_eZFlow_Event_Type__c"`
	//	FullVenueLocation   string `json:"Venue_Location__c"`
	//	FullActivityName    string `json:"ZLG_eZFlow_Event_Name__c"`
	//	MeetingMode         string `json:"ZLG_eZFlow_Meeting_Mode__c"`
	//	VirtualEvent        string `json:"ZLG_eZFlow_Virtual_Event__c"`
	//	VirtualEventDetails string `json:"ZLG_eZFlow_Details_Event__c"`
	//	EZFlowLedBy         string `json:"-"`
	//}
	fmt.Println("====== Payload to VEEVA ========")
	fmt.Println(fmt.Sprintf(`
		"Activity Type": %s
		"Activity Name": %s,
		"Event Type: %s"
`, insertEntity.ActivityType, insertEntity.ActivityName, insertEntity.EventName))
	fmt.Println("================================")
	response, url, err := veeva.MedicalEventInsertIntoVeeva(veevaReferenceId, insertEntity)
	if err != nil {
		UpsertVeevaEventLogs(context.Background(), formAnswerId,
			false, false, response.Id, url, err.Error())
		return err
	}
	// Update event logs
	UpsertVeevaEventLogs(context.Background(), formAnswerId, response.Success, false, response.Id, url, "")

	return nil
}
func VeevaGetUsersData(ownerId string, Token string, Instance_url string) ([]entity.UsersRecords, error) {
	functionName := "VeevaGetUsersData()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllUsersData
	method := "GET"
	var body []byte
	veevaInstanceURL := veeva.GetVeevaBaseURL(Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Id+,+IsActive+,+Username+,+ZLG_Active_Directory_Name__c+,+Country+FROM+User+WHERE+id+=+'` + ownerId + `'`
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}
	bearer := "Bearer " + Token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}

	defer response.Body.Close()
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	if response.StatusCode != http.StatusOK {
		return []entity.UsersRecords{}, errors.New(string(responseBody))
	}
	fmt.Println("---------------------", string(responseBody), url)
	if err := json.Unmarshal(responseBody, &ResponseUrlValues); err != nil {
		panic(err)
	}

	for ResponseUrlValues.NextRecordsURL != "" {

		rs := func() *entity.VeevaGetAllUsersData {
			url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
			requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
			if err != nil {
				panic(err)
			}

			requestForNextRecord.Header.Add("Authorization", bearer)
			requestForNextRecord.Header.Set("Content-Type", "application/json")

			responseForNextRecord, err := client.Do(requestForNextRecord)
			if err != nil {
				panic(err)
			}
			defer responseForNextRecord.Body.Close()

			responseForNextRecordBody, err := io.ReadAll(responseForNextRecord.Body)
			if err != nil {
				panic(err)
			}

			var responseUrlNextValues entity.VeevaGetAllUsersData
			if err := json.Unmarshal(responseForNextRecordBody, &responseUrlNextValues); err != nil {
				panic(err)
			}

			return &responseUrlNextValues
		}()

		ResponseUrlValues.Records = append(ResponseUrlValues.Records, rs.Records...)
		ResponseUrlValues.NextRecordsURL = rs.NextRecordsURL
	}
	return ResponseUrlValues.Records, nil
}
