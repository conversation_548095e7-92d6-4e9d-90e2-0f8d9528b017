package postgres

import (
	"context"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var pool *pgxpool.Pool
var maxRetry int64

func InitDbPool() {
	host := os.Getenv("IHCP_HOST")
	port := os.Getenv("IHCP_PORT")
	user := os.Getenv("IHCP_USER")
	password := os.Getenv("IHCP_PASSWORD")
	dbname := os.Getenv("IHCP_DB_NAME")
	sslmode := os.Getenv("IHCP_SSLMODE")
	searchPath := os.Getenv("IHCP_SEARCH_PATH")
	databaseUrl := "host=" + host + " port=" + port + " user=" + user + " password=" + password + " dbname=" + dbname + " sslmode=" + sslmode + " search_path=" + searchPath

	maxOpenConnection, err := strconv.Atoi(os.Getenv("IHCP_MAX_CONN"))
	if err != nil {
		panic(err)
	}
	maxIdleTime, err := strconv.Atoi(os.Getenv("IHCP_MAX_IDLE_TIME"))
	if err != nil {
		panic(err)
	}
	maxConnectionLifetime, err := strconv.Atoi(os.Getenv("IHCP_MAX_LIFETIME"))
	if err != nil {
		panic(err)
	}

	healthcheckperiod, err := strconv.Atoi(os.Getenv("IHCP_HEALTHCHECK_PREIOD"))
	if err != nil {
		panic(err)
	}

	config, err := pgxpool.ParseConfig(databaseUrl)
	if err != nil {
		log.Print(err)
		log.Print("Error in config.")
		//return &pgxpool.Pool{}
	}
	config.MaxConns = int32(maxOpenConnection)
	config.MaxConnLifetime = time.Duration(maxConnectionLifetime) * time.Minute
	config.HealthCheckPeriod = time.Duration(healthcheckperiod) * time.Minute
	config.MaxConnIdleTime = time.Duration(maxIdleTime) * time.Minute

	pool, err = pgxpool.ConnectConfig(context.Background(), config)

	if err != nil {
		refreshPoolConnection := os.Getenv("POOL_CONNECTION_INTERVAL")
		poolConnectionRefresh, err := strconv.ParseUint(refreshPoolConnection, 10, 64)
		if err != nil {
			poolConnectionRefresh = 15
		}
		maxRetryVal := os.Getenv("MAX_RETRY")
		retryMaxTime, err := strconv.ParseInt(maxRetryVal, 10, 64)
		if err != nil {
			retryMaxTime = 10
		}
		if maxRetry < retryMaxTime {
			maxRetry++
			log.Print(err)
			log.Print("Could not connect to Postgres.")
			duration := time.Second * time.Duration(poolConnectionRefresh)
			time.Sleep(duration)
			InitDbPool()
		}

	}
	log.Println("Postgres connected!")
}

func GetPool() *pgxpool.Pool {
	if pool == nil {
		InitDbPool()
	}

	connectedPoolSize := pool.AcquireAllIdle(context.Background())
	for connectedPoolSize == nil {
		log.Println("Pg Connection Lost")
		pool.Close()
		time.Sleep(2 * time.Second)
		log.Print("Reconnecting...")
		InitDbPool()
		connectedPoolSize = pool.AcquireAllIdle(context.Background())
	}
	return pool
}
