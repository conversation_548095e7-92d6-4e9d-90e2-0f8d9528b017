package postgres

import (
	"context"
	"log"

	codeEntity "github.com/ihcp/code/entity"
	"github.com/ihcp/upload/graph/entity"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func UpsertTeamMemberCustomerForExcel(tx pgx.Tx, entity *entity.TeamMemberCustomer, userUUID uuid.UUID) error {
	functionName := "UpsertTeamMemberCustomerForExcel()"
	log.Println(functionName)
	queryString := `INSERT INTO team_member_customer (customer,team_member,cmsl_class,is_active,created_by) VALUES ($1,$2,$3,$4,$5)`
	var inputargs []interface{}
	inputargs = append(inputargs, entity.Customer, entity.TeamMember, entity.CMSLClass, true, userUUID)
	_, err := tx.Exec(context.Background(), queryString, inputargs...)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	return err
}

func updateTeamMemberCustomer(tx pgx.Tx, entity entity.CustomerExcelInput, cmslClasses map[string]codeEntity.Code, teamMemberID uuid.UUID) error {
	functionName := "updateTeamMemberCustomer()"
	log.Println(functionName)
	return nil
}
