package teams_service

import (
	"context"
	"fmt"
	teams "github.com/ihcp/teams"
	"log"
)

var teamsWH *teams.WebhookClient

// Make it clear

func InitPackageVariable(t *teams.WebhookClient) {
	teamsWH = t
}

func Send(ctx context.Context, msg string, stackTrace, apiName string) {
	log.Println(msg, stackTrace, apiName)
	if teamsWH == nil {
		return
	}
	go func() {
		if err := teamsWH.SendPanicMessage(ctx, msg, stackTrace, apiName); err != nil {
			log.Println(`--- Error happens when sending teams_service msg`)
		}
	}()
}

func SendMsg(ctx context.Context, title string, details string, apiName string) {
	log.Println(fmt.Sprintf(`%s \n%s \n%s`, title, apiName, details))
	if teamsWH == nil {
		return
	}
	m := map[string]interface{}{
		"@type":      "MessageCard",
		"@context":   "http://schema.org/extensions",
		"themeColor": "0076F7",
		"summary":    title,
		"title":      title,
		"sections": []map[string]interface{}{
			{
				"activityTitle":    apiName,
				"activitySubtitle": details,
			},
		},
	}

	go func() {
		if err := teamsWH.SendMessage(ctx, m); err != nil {
			log.Println(`--- Error happens when sending teams_service msg`)
		}
	}()
}

func Get() *teams.WebhookClient {
	return teamsWH
}
