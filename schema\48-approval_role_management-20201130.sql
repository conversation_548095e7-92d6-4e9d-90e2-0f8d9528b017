UPDATE approval_role_management SET is_active = false WHERE activity_id = 94;
UPDATE approval_role_management SET is_active = false WHERE activity_id = 86;
INSERT INTO approval_role_management (activity, approval_role, group_type, is_active, sequence_no, date_created, min_limit, max_limit, limit_range, activity_id)
VALUES ('4ad3b995-e7e0-4ffa-a474-965956b1abc5', 393, 55, true, 1, now(), 0, 1499, '[0,1499)', 94);
INSERT INTO approval_role_management (activity, approval_role, group_type, is_active, sequence_no, date_created, min_limit, max_limit, limit_range, activity_id)
VALUES ('9b08a248-01d4-470f-96dd-49b70850619a', 393, 55, true, 1, now(), 0, 1499, '[0,1499)', 86);
INSERT INTO approval_role_management (activity, approval_role, group_type, is_active, sequence_no, date_created, min_limit, max_limit, limit_range, activity_id)
VALUES ('9b08a248-01d4-470f-96dd-49b70850619a', 49, 55, true, 2, now(), 0, 1499, '[0,1499]', 86);
INSERT INTO approval_role_management (activity, approval_role, group_type, is_active, sequence_no, date_created, min_limit, max_limit, limit_range, activity_id)
VALUES ('2609b6a5-44f4-42a8-afa6-a7d5b6bb7772', 49, 55, true, 3, now(), 0, 10000, '[0,10000]', 98);


ALTER TABLE approval_role_management
ADD COLUMN has_condition bool;

ALTER TABLE approval_role_management
ALTER COLUMN has_condition SET DEFAULT false;
UPDATE approval_role_management SET has_condition = false WHERE has_condition IS NULL;
ALTER TABLE approval_role_management ALTER COLUMN has_condition SET NOT NULL;

UPDATE approval_role_management SET has_condition = true WHERE activity_id = 86 AND approval_role = 49;

UPDATE approval_role_management SET has_condition = true WHERE activity_id = 98 AND approval_role = 49 AND min_limit = 0 AND is_active = true;