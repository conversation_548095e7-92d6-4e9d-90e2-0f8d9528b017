package entity

import (
	"database/sql"

	uuid "github.com/satori/go.uuid"
)

type DepartmentRolesEntity struct {
	ID           *uuid.UUID
	DepartmentID string
	RoleID       []string
	IsDeleted    bool
	CreatedBy    string
}

type DepartmentRolesFetchEntity struct {
	ID            sql.NullString
	Department    sql.NullString
	UserRole      sql.NullString
	UserRoleID    sql.NullString
	UserRoleTitle sql.NullString
	UserRoleDesc  sql.NullString
}
