package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertCustomer is the resolver for the upsertCustomer field.
func (r *mutationResolver) UpsertCustomer(ctx context.Context, input model.CustomerInput) (*model.UpsertCustomerResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertCustomer", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertCustomer :" + string(inputJson))
	response := controller.UpsertCustomerData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("Controls", "data_maintenance/Controls", time.Since(start), "200")
	return response, nil
}

// CustomerExport is the resolver for the customerExport field.
func (r *queryResolver) CustomerExport(ctx context.Context, input model.CustomerExcelRequest) (*model.CustomerExcelResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "CustomerExport", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/CustomerExport :" + string(inputJson))
	reponse := controller.ExportCustomerExcel(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("CustomerExport", "data_maintenance/CustomerExport", time.Since(start), "200")
	return reponse, nil
}

// GetCustomerDetails is the resolver for the getCustomerDetails field.
func (r *queryResolver) GetCustomerDetails(ctx context.Context, input model.CustomerInfoRequest) (*model.CustomerExcelResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "CustomerExport", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/CustomerExport :" + string(inputJson))
	reponse := controller.ExportCustomerDetails(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("CustomerExport", "data_maintenance/CustomerExport", time.Since(start), "200")
	return reponse, nil
}
