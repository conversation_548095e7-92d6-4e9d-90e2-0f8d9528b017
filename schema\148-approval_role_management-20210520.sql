delete from approval_role_management where activity_id = 86


INSERT INTO approval_role_management
(approval_role, group_type, created_by, is_active, is_deleted, sequence_no, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_group_type, country, has_international, department, alternate_department_id)
VALUES
    (413,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,5,'[0,1499]',0,1499,86,TRUE,NULL,6,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,4,'[0,1499]',0,1499,86,TRUE,NULL,12,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (72,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,400,FALSE,NULL,NULL),
    (72,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,6,FALSE,NULL,NULL),
    (72,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,9,FALSE,NULL,NULL),
    (72,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,12,FALSE,NULL,NULL),
    (69,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,'[0,1499)',0,1499,86,FALSE,NULL,400,FALSE,NULL,NULL),
    (69,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,'[0,1499)',0,1499,86,FALSE,NULL,6,FALSE,NULL,NULL),
    (69,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,'[0,1499)',0,1499,86,FALSE,NULL,9,FALSE,NULL,NULL),
    (69,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,'[0,1499)',0,1499,86,FALSE,NULL,12,FALSE,NULL,NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,3,'[0,1499]',0,1499,86,TRUE,'56',9,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,'56',400,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,400,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,4,'[0,1499]',0,1499,86,TRUE,NULL,9,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,6,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (52,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,400,FALSE,(select id from departments where department = 'Regional Compliance' and is_active = true) ,NULL),
    (52,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,6,FALSE,(select id from departments where department = 'Regional Compliance' and is_active = true) ,NULL),
    (52,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,9,FALSE,(select id from departments where department = 'Regional Compliance' and is_active = true) ,NULL),
    (52,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,12,FALSE,(select id from departments where department = 'Regional Compliance' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,3,'[0,1499]',0,1499,86,TRUE,'56',12,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (49,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,2,'[0,1499]',0,1499,86,TRUE,NULL,400,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,'56',6,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (49,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,2,'[0,1499]',0,1499,86,TRUE,NULL,6,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,2,'[0,1499]',0,1499,86,TRUE,NULL,9,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,2,'[0,1499]',0,1499,86,TRUE,NULL,12,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,5,'[0,1499]',0,1499,86,TRUE,NULL,9,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,5,'[0,1499]',0,1499,86,TRUE,NULL,400,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,8,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,8,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,8,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,8,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,10,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,10,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,10,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,10,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (393,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,1,NULL,0,1499,86,FALSE,NULL,6,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (393,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,1,NULL,0,1499,86,FALSE,NULL,9,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (393,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,1,NULL,0,1499,86,FALSE,NULL,12,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,1,NULL,0,1499,86,FALSE,NULL,400,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,400,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,FALSE,1,NULL,NULL,NULL,86,FALSE,NULL,400,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,6,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,FALSE,1,NULL,NULL,NULL,86,FALSE,NULL,6,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,9,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,FALSE,1,NULL,NULL,NULL,86,FALSE,NULL,9,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,1,NULL,0,1499,86,FALSE,NULL,12,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (46,55,'00000000-0000-0000-0000-000000000000',FALSE,FALSE,1,NULL,NULL,NULL,86,FALSE,NULL,12,FALSE,(select id from departments where department = 'Manager' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',FALSE,TRUE,5,'[0,1499]',0,1499,86,TRUE,NULL,12,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,7,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,7,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,7,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,7,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL);
