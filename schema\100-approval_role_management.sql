
alter table approval_role_management add column alternate_department_id uuid ;


update approval_role_management set alternate_department_id = (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)
where alternate_role = 50 ;

update approval_role_management set alternate_department_id = (select id from departments where department = 'Regional Medical' and is_active = true)
where alternate_role = 53 ;

update approval_role_management set alternate_group_type = 56 , 
alternate_department_id = (select id from departments where department = 'Regional Commercial Solutions' and is_active = true)
where has_international =true and approval_role  = 51

delete from approval_role_management where has_international = true and approval_role = 413