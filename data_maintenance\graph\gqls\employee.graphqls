type Employee {
    id: String!
    country: String!
    team: String
    activeDirectoryName: String!
    firstName: String!
    lastName: String!
    position: String
    employeeCode: String
    email: String
    roleTitle  :String!
    roleValue   :String!
    teamId :   String
    isActive: Boolean!
    veevaReferenceId:String!
}
type EmployeeAuditLogsData {
    id: String!
    userId:String!
    country: String!
    activeDirectoryName: String!
    firstName: String!
    lastName: String!
    employeeCode: String!
    email:String!
    roleTitle:String!
    actionBy:String!
    actionType:String!
    actionDate:String!
}

type EmployeeAuditLogsResponse{
    error: Boolean!
    message: String!
    totalCount:Int!
    url: String!
    data: [EmployeeAuditLogsData]!
}

input EmployeeAuditLogsRequest{
    limit: Int
    pageNo: Int
    actionBy:String
    actionType:String
    actionStartDate:String
    actionEndDate:String
    activeDirectoryName: String
    firstName: String
    lastName: String
    email:String
    userId:String
    searchItem: String
    isExcel: Boolean!
    sort: [SortingInputs]
}

input EmployeeInput {
    id: String
    isDelete: Boolean
    teamID:  String
    vPosition: String
    firstName: String
    lastName: String
    empCode: String
    activeDirectoryName: String
    email: String
    role: String
}

input SortingInputs {
    column: String
    sort: String
}

input EmployeeRequest{
    isExcel: Boolean!
    id:String
    searchItem: String
    team: String
    position: String
    employeeCode: String
    activeDirectoryName: String
    role: String
    firstName: String
    lastName: String
    limit: Int
    pageNo: Int
    isActive: Boolean
    veevaReferenceId:String
    sort: [SortingInputs]
}

type EmployeeResponse{
    error: Boolean!
    message: String!
    url: String!
    totalCount:Int!
    data: [Employee]!
}

input EmployeeRoleRequest{
    role: String!
}

extend type Query {
    employee(input: EmployeeRequest!): EmployeeResponse!
    employeeByRole(input: EmployeeRoleRequest!): EmployeeResponse!
    EmployeeAuditLogs(input:EmployeeAuditLogsRequest!):EmployeeAuditLogsResponse!
}

extend type Mutation {
    upsertEmployee(input: EmployeeInput!): upsertResponse!
}

