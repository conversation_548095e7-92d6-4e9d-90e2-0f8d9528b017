update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'd4a4d24c-1a4a-4cf4-85b7-f24161b29b24') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'd4a4d24c-1a4a-4cf4-85b7-f24161b29b24';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'e69c3347-6876-47b9-879c-6e17fcd21630') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'e69c3347-6876-47b9-879c-6e17fcd21630';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '3824c32f-6d7d-44b9-b25f-b6f4b96a0902') 
and user_roles.is_active = true)
and is_active = true
)) where id = '3824c32f-6d7d-44b9-b25f-b6f4b96a0902';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '849a257d-d25a-4f3b-97fd-dc9243898015') 
and user_roles.is_active = true)
and is_active = true
)) where id = '849a257d-d25a-4f3b-97fd-dc9243898015';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'bc9fb1c3-5820-4062-a703-acccef1fe16b') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'bc9fb1c3-5820-4062-a703-acccef1fe16b';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1ec89a88-6cc2-4954-a2ee-3091f613f949') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1ec89a88-6cc2-4954-a2ee-3091f613f949';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'd5862f64-1562-45d2-adcc-88403cfe345a') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'd5862f64-1562-45d2-adcc-88403cfe345a';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'b8aff908-b6f6-495c-a6f6-35d3ccda3892') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'b8aff908-b6f6-495c-a6f6-35d3ccda3892';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1bc1f718-aab4-4bfc-9141-f658780e526e') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1bc1f718-aab4-4bfc-9141-f658780e526e';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '3b7119e9-8c15-4947-a9ae-96dfff00c27c') 
and user_roles.is_active = true)
and is_active = true
)) where id = '3b7119e9-8c15-4947-a9ae-96dfff00c27c';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'd979e82e-0104-431c-bc8a-94e884e95f54') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'd979e82e-0104-431c-bc8a-94e884e95f54';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1369ccfb-dc75-4b91-b35a-3f832165562a') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1369ccfb-dc75-4b91-b35a-3f832165562a';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '54859d7c-d023-40f4-840f-8713cde11cf1') 
and user_roles.is_active = true)
and is_active = true
)) where id = '54859d7c-d023-40f4-840f-8713cde11cf1';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'c7b5507e-af85-4be9-a610-a69110a74edf') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'c7b5507e-af85-4be9-a610-a69110a74edf';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '69d2d709-d0bd-4d11-8b35-12c9d40a9b34') 
and user_roles.is_active = true)
and is_active = true
)) where id = '69d2d709-d0bd-4d11-8b35-12c9d40a9b34';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '355f0c07-a4d0-40b4-8eed-1938f0540eab') 
and user_roles.is_active = true)
and is_active = true
)) where id = '355f0c07-a4d0-40b4-8eed-1938f0540eab';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '15bfa832-6a37-48fc-83ae-84688f146adf') 
and user_roles.is_active = true)
and is_active = true
)) where id = '15bfa832-6a37-48fc-83ae-84688f146adf';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1695e87a-f8a4-4ad7-ad20-b67f863a41f7') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1695e87a-f8a4-4ad7-ad20-b67f863a41f7';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '33a8e312-7ffb-405e-9cb2-c9768e86e2b2') 
and user_roles.is_active = true)
and is_active = true
)) where id = '33a8e312-7ffb-405e-9cb2-c9768e86e2b2';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'e81eb015-94cc-4534-88bc-3e8da9586790') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'e81eb015-94cc-4534-88bc-3e8da9586790';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '981bb6bc-06f3-4f8f-b13e-2b4a2a8302a4') 
and user_roles.is_active = true)
and is_active = true
)) where id = '981bb6bc-06f3-4f8f-b13e-2b4a2a8302a4';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '83db9ab3-dfa2-4022-953e-f088eee9a716') 
and user_roles.is_active = true)
and is_active = true
)) where id = '83db9ab3-dfa2-4022-953e-f088eee9a716';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'badd33b4-a500-4491-9dad-95fbfbc52df5') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'badd33b4-a500-4491-9dad-95fbfbc52df5';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'bd39b383-f179-481c-8f52-4f24c3424f78') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'bd39b383-f179-481c-8f52-4f24c3424f78';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'a21a7324-eaa3-4f4b-bcab-fcf69d8ce96f') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'a21a7324-eaa3-4f4b-bcab-fcf69d8ce96f';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '42213278-f775-4a83-aaa3-8170868469f6') 
and user_roles.is_active = true)
and is_active = true
)) where id = '42213278-f775-4a83-aaa3-8170868469f6';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'f7aa9572-79d0-4089-81a4-171fb04305ac') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'f7aa9572-79d0-4089-81a4-171fb04305ac';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '18cd9d79-db72-4baa-88a8-dc879b06a1f6') 
and user_roles.is_active = true)
and is_active = true
)) where id = '18cd9d79-db72-4baa-88a8-dc879b06a1f6';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'd5e1c518-18b5-49ed-b466-305355e3b693') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'd5e1c518-18b5-49ed-b466-305355e3b693';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '8cdd8467-d6a4-432d-a3bf-98246f772b9b') 
and user_roles.is_active = true)
and is_active = true
)) where id = '8cdd8467-d6a4-432d-a3bf-98246f772b9b';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '29927266-e25e-4bf5-8df9-a6a072b7b85f') 
and user_roles.is_active = true)
and is_active = true
)) where id = '29927266-e25e-4bf5-8df9-a6a072b7b85f';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'f6602485-152f-4ddb-88b4-cbb38a5d766e') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'f6602485-152f-4ddb-88b4-cbb38a5d766e';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'd266c4cd-1cdb-45d4-8bc9-b62a4ec63274') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'd266c4cd-1cdb-45d4-8bc9-b62a4ec63274';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'b688883d-89d7-43a5-b6f2-25e9d7657a7e') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'b688883d-89d7-43a5-b6f2-25e9d7657a7e';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '0db44b4a-9963-40e0-88a4-0964326ba010') 
and user_roles.is_active = true)
and is_active = true
)) where id = '0db44b4a-9963-40e0-88a4-0964326ba010';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '48816744-f261-4694-8787-c12235c30aa1') 
and user_roles.is_active = true)
and is_active = true
)) where id = '48816744-f261-4694-8787-c12235c30aa1';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1f9c541d-aaef-4d3a-88ff-21efc31e2b76') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1f9c541d-aaef-4d3a-88ff-21efc31e2b76';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '28c20cad-1331-42da-b8a9-8af894995f34') 
and user_roles.is_active = true)
and is_active = true
)) where id = '28c20cad-1331-42da-b8a9-8af894995f34';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '360fe81a-c05e-403e-9b65-e79cf2da3815') 
and user_roles.is_active = true)
and is_active = true
)) where id = '360fe81a-c05e-403e-9b65-e79cf2da3815';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '0d0451b1-73f7-4de2-b146-3f483dbaad89') 
and user_roles.is_active = true)
and is_active = true
)) where id = '0d0451b1-73f7-4de2-b146-3f483dbaad89';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '835e8243-13c2-4a7d-ab41-1c2e18127ca6') 
and user_roles.is_active = true)
and is_active = true
)) where id = '835e8243-13c2-4a7d-ab41-1c2e18127ca6';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '57f4ffe6-69df-4a6c-9c05-3d905b8af398') 
and user_roles.is_active = true)
and is_active = true
)) where id = '57f4ffe6-69df-4a6c-9c05-3d905b8af398';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '1d8b5cf1-8dc8-452a-8291-961c7056a54f') 
and user_roles.is_active = true)
and is_active = true
)) where id = '1d8b5cf1-8dc8-452a-8291-961c7056a54f';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '13f897da-694c-4bcf-88eb-9fa2d40a1e60') 
and user_roles.is_active = true)
and is_active = true
)) where id = '13f897da-694c-4bcf-88eb-9fa2d40a1e60';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = 'ad4da3c4-a2b4-4ac8-bed6-45d87eaf95eb') 
and user_roles.is_active = true)
and is_active = true
)) where id = 'ad4da3c4-a2b4-4ac8-bed6-45d87eaf95eb';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '2f6975a1-7841-4972-88b2-68d4bc303b6b') 
and user_roles.is_active = true)
and is_active = true
)) where id = '2f6975a1-7841-4972-88b2-68d4bc303b6b';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '74ab579b-68ce-45cc-9cec-6bbf45aeb72b') 
and user_roles.is_active = true)
and is_active = true
)) where id = '74ab579b-68ce-45cc-9cec-6bbf45aeb72b';

update event_type_access_permission 
set user_role_id = (select array(select id from user_roles where
value in (select user_roles.value from user_roles inner join code
on code.value=user_roles.value where code.id in 
(select unnest(user_role) from event_type_access_permission etap where etap.id = '182053f6-2249-43a9-9618-8a828638c1f6') 
and user_roles.is_active = true)
and is_active = true
)) where id = '182053f6-2249-43a9-9618-8a828638c1f6';