package util

import (
	"bytes"
	"log"

	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/jasonlvhit/gocron"
)

func ParseExcel(b []byte, sheetname string, totalColumns int) [][]string {
	log.Println("ParseExcel()")
	r := bytes.NewReader(b)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.Error())
	}

	rows := f.GetRows(sheetname)
	if len(rows) == 0 || len(rows[0]) < totalColumns {
		log.Println("Format of excel columns is wrong!")
	} else {

		return rows
	}
	return nil
}

func GetSheetName(data []byte) string {
	r := bytes.NewReader(data)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.<PERSON><PERSON>r())
	}
	return f.GetSheetName(f.GetActiveSheetIndex())
}

func StartCronService() {
	// gocron.Every(45).Seconds().From(gocron.NextTick()).Do(postgres.InsertConvertedCurrencyInDB)
	gocron.Every(1).Day().At("00:01").Do(postgres.InsertConvertedCurrencyInDB)
	<-gocron.Start()
}
