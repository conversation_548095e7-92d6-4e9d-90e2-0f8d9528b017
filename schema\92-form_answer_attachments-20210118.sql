alter table form_answer_attachments add column type_desc text ;
alter table form_answer_attachments add column category_desc text ;

update form_answer_attachments set type_desc = 'Receipts' where type = 73 ;
update form_answer_attachments set type_desc = 'Proof of Attendance' where type = 74 ;
update form_answer_attachments set type_desc = 'Proof of Delivery' where type = 75 ;
update form_answer_attachments set type_desc = 'Invoices' where type = 76 ;
update form_answer_attachments set type_desc = 'Changes' where type = 396 ;
update form_answer_attachments set category_desc = 'Payment Receipt' where category = 77 ;
update form_answer_attachments set category_desc = 'Acknowledge of Payment' where category = 78 ;
update form_answer_attachments set category_desc = 'Signed Attendance list' where category = 79 ;
update form_answer_attachments set category_desc = 'Certification/proof of Attendance' where category = 80 ;
update form_answer_attachments set category_desc = 'Picture of booth' where category = 81 ;
update form_answer_attachments set category_desc = 'Signed confirmation' where category = 82 ;
update form_answer_attachments set category_desc = 'Approve presentation' where category = 83 ;
update form_answer_attachments set category_desc = 'Photograph/video/film' where category = 84 ;
