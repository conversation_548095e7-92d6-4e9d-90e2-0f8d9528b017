package util

import (
	"log"
	"time"

	suuid "github.com/satori/go.uuid"
)

func UUIDV4ToString(id [16]byte) string {
	u, err := suuid.FromBytes(id[:])
	if err != nil {
		log.Println(err.Error())
	}
	return u.String()
}

func BytesToUUIDV4(id [16]byte) suuid.UUID {
	u, err := suuid.FromBytes(id[:])
	if err != nil {
		log.Println("Failed to parse UUID from byte array")
	}
	return u
}

func StringToUUID4(uuidstr string) suuid.UUID {
	uid, err := suuid.FromString(uuidstr)
	if err != nil {
		log.Printf("Something went wrong: %s", err)
	}
	return uid
}

func IsValidDate(date string) error {
	const (
		layoutISO = "01/02/2006"
		layoutUS  = "2 January, 2006"
	)
	_, err := time.Parse(layoutISO, date)
	if err != nil {
		log.Println(err)
	}
	return err
}

func GetCurrentTime() time.Time {
	timeNow := time.Now().UTC().Add(0 * time.Hour)
	return timeNow
}

func GetTimeUnixTimeStamp(date time.Time) string {
	timestamp := int32(date.Unix())
	return String(timestamp)
}

func String(n int32) string {
	buf := [11]byte{}
	pos := len(buf)
	i := int64(n)
	signed := i < 0
	if signed {
		i = -i
	}
	for {
		pos--
		buf[pos], i = '0'+byte(i%10), i/10
		if i == 0 {
			if signed {
				pos--
				buf[pos] = '-'
			}
			return string(buf[pos:])
		}
	}
}
