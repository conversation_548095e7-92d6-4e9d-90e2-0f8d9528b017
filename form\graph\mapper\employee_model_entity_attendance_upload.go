package mapper

import (
	"encoding/json"
	"log"
	"math/rand"
	"strconv"
	"strings"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	uuid "github.com/satori/go.uuid"
)

func MapEmployeeAttendanceExcelToEntities(data [][]string, userUUID uuid.UUID, formAnswerID string) (string, string, *model.ValidationResult) {
	log.Println("MapEmployeeAttendanceExcelToEntities", len(data), data)
	var employees []entity.EmployeeAttendanceExcelInput
	var employeesEmailJson []entity.EmployeeAttendanceExcelForOriginalInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	var concatName string
	for i := 1; i < len(data); i++ {
		var employeesAttendanceEntity entity.EmployeeAttendanceExcelInput
		var employeesOriginalExcelJsonEntity entity.EmployeeAttendanceExcelForOriginalInput
		eventCode, eventSeq, _ := postgres.GetEventId(formAnswerID)
		getMeetingId := util.ConvertToEventID(eventCode, eventSeq)
		firstName := strings.TrimSpace(data[i][0])
		employeesAttendanceEntity.FirstName = getMeetingId
		if firstName != "" {
			concatName = firstName
			employeesAttendanceEntity.FirstNameEzClaim = firstName
		} else {
			errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: "Can't Blank FirstName Field!!"}
			validationMessages = append(validationMessages, errorMessage)
		}
		lastName := strings.TrimSpace(data[i][1])
		if lastName != "" {
			employeesAttendanceEntity.LastName = firstName + " " + lastName
			employeesAttendanceEntity.LastNameEzClaim = lastName
			concatName += lastName
		} else {
			errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: "Can't Blank LastName Field!!"}
			validationMessages = append(validationMessages, errorMessage)
		}
		affiliation := strings.TrimSpace(data[i][2])
		if affiliation != "" {
			employeesOriginalExcelJsonEntity.Affiliation = affiliation
		}
		if strings.TrimSpace(concatName) != "" {
			threeDigitRandomNumber := rand.Intn(899) + 100
			concatName += strconv.Itoa(threeDigitRandomNumber)
			employeesAttendanceEntity.AccountName = strings.ReplaceAll(concatName, " ", "")
		}
		specialty := strings.TrimSpace(data[i][3])
		if specialty != "" {
			employeesAttendanceEntity.Title = specialty
			employeesOriginalExcelJsonEntity.Specialty = specialty
		} else {
			employeesAttendanceEntity.Title = ""
		}
		employeesAttendanceEntity.AccountId = strconv.Itoa(rand.Intn(*********) + *********)
		employeesAttendanceEntity.SystemCode = "eZFlow"
		employeesAttendanceEntity.Status = "ACTIVE"
		employeesAttendanceEntity.ContactType = "CRM"
		uniqueuuid := uuid.NewV4()
		employeesAttendanceEntity.ExternalUniqueId = uniqueuuid.String()

		employeesOriginalExcelJsonEntity.AccountId = employeesAttendanceEntity.AccountId
		employeesOriginalExcelJsonEntity.AccountName = employeesAttendanceEntity.AccountName
		employeesOriginalExcelJsonEntity.ContactType = employeesAttendanceEntity.ContactType
		employeesOriginalExcelJsonEntity.ExternalUniqueId = employeesAttendanceEntity.ExternalUniqueId
		employeesOriginalExcelJsonEntity.FirstName = employeesAttendanceEntity.FirstName
		employeesOriginalExcelJsonEntity.LastName = employeesAttendanceEntity.LastName
		employeesOriginalExcelJsonEntity.FirstNameEzClaim = employeesAttendanceEntity.FirstNameEzClaim
		employeesOriginalExcelJsonEntity.LastNameEzClaim = employeesAttendanceEntity.LastNameEzClaim
		employeesOriginalExcelJsonEntity.Status = employeesAttendanceEntity.Status
		employeesOriginalExcelJsonEntity.SystemCode = employeesAttendanceEntity.SystemCode
		employeesOriginalExcelJsonEntity.Title = employeesAttendanceEntity.Title

		employees = append(employees, employeesAttendanceEntity)
		employeesEmailJson = append(employeesEmailJson, employeesOriginalExcelJsonEntity)
	}
	eventAttendances, err := json.Marshal(employees)
	if err != nil {
		panic(err)
	}
	eventAttendancesForEmail, err := json.Marshal(employeesEmailJson)
	if err != nil {
		panic(err)
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}

	//log.Println("=================================", string(eventAttendances), string(eventAttendancesForEmail), len(employees), len(employeesEmailJson))

	return string(eventAttendances), string(eventAttendancesForEmail), result
}
func MapEmployeeAttendanceExcelToEntitiesForApollo(data [][]string, userUUID uuid.UUID, formanswerID string) (string, string, *model.ValidationResult) {
	log.Println("MapEmployeeAttendanceExcelToEntitiesForApollo")
	var employees []entity.EmployeeAttendanceExcelInput
	var employeesEmailJson []entity.EmployeeAttendanceExcelForOriginalInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	var concatName string
	for i := 1; i < len(data); i++ {
		var employeesAttendanceEntity entity.EmployeeAttendanceExcelInput
		var employeesOriginalExcelJsonEntity entity.EmployeeAttendanceExcelForOriginalInput
		eventCode, eventSeq, _ := postgres.GetEventId(formanswerID)
		getMeetingId := util.ConvertToEventID(eventCode, eventSeq)
		name := strings.TrimSpace(data[i][1])
		employeesAttendanceEntity.FirstName = getMeetingId

		var firstName, lastName string
		if name != "" {
			concatName = name
			employeesAttendanceEntity.FirstNameEzClaim = name
			s := strings.Split(name, " ")
			if len(s) == 1 {
				s = append(s, "")
			}
			firstName, lastName = s[0], strings.Join(s[1:], " ")
		} else {
			errorMessage := &model.ExcelValidationMessage{Row: i + 1, Message: "name can not be blank"}
			validationMessages = append(validationMessages, errorMessage)
		}

		employeesAttendanceEntity.FirstNameEzClaim = firstName
		employeesAttendanceEntity.LastNameEzClaim = lastName
		employeesAttendanceEntity.FirstName = firstName
		employeesAttendanceEntity.LastName = firstName + " " + lastName

		specialty := strings.TrimSpace(data[i][2])
		if specialty != "" {
			employeesAttendanceEntity.Title = specialty
			employeesOriginalExcelJsonEntity.Specialty = specialty
		} else {
			employeesAttendanceEntity.Title = ""
		}

		hcoName := strings.TrimSpace(data[i][3])
		if hcoName != "" {
			employeesAttendanceEntity.HCOName = hcoName
			employeesOriginalExcelJsonEntity.HCOName = hcoName
			employeesOriginalExcelJsonEntity.Affiliation = hcoName
		} else {
			employeesAttendanceEntity.Title = ""
		}

		if strings.TrimSpace(concatName) != "" {
			threeDigitRandomNumber := rand.Intn(899) + 100
			concatName += strconv.Itoa(threeDigitRandomNumber)
			employeesAttendanceEntity.AccountName = strings.ReplaceAll(concatName, " ", "")
		}

		hcpId := strings.TrimSpace(data[i][0])
		if hcpId != "" {
			employeesAttendanceEntity.AccountId = hcpId
			employeesOriginalExcelJsonEntity.AccountId = hcpId
		} else {
			employeesAttendanceEntity.AccountId = ""
			employeesOriginalExcelJsonEntity.AccountId = ""
		}

		isHCP := strings.TrimSpace(data[i][4])
		if strings.ToLower(isHCP) == "true" || strings.ToLower(isHCP) == "yes" || strings.ToLower(isHCP) == "1" {
			employeesAttendanceEntity.IsHcp = true
			employeesOriginalExcelJsonEntity.IsHcp = true
		} else {
			employeesAttendanceEntity.IsHcp = false
		}

		isSpeaker := strings.TrimSpace(data[i][5])
		if strings.ToLower(isSpeaker) == "true" || strings.ToLower(isSpeaker) == "yes" || strings.ToLower(isSpeaker) == "1" {
			employeesAttendanceEntity.IsSpeaker = true
			employeesOriginalExcelJsonEntity.IsSpeaker = true
		} else {
			employeesAttendanceEntity.IsSpeaker = false
		}

		employeesAttendanceEntity.SystemCode = "eZFlow"
		employeesAttendanceEntity.Status = "ACTIVE"
		employeesAttendanceEntity.ContactType = "CRM"
		uniqueuuid := uuid.NewV4()
		employeesAttendanceEntity.ExternalUniqueId = uniqueuuid.String()
		employees = append(employees, employeesAttendanceEntity)
		employeesOriginalExcelJsonEntity.AccountId = employeesAttendanceEntity.AccountId
		employeesOriginalExcelJsonEntity.AccountName = employeesAttendanceEntity.AccountName
		employeesOriginalExcelJsonEntity.ContactType = employeesAttendanceEntity.ContactType
		employeesOriginalExcelJsonEntity.ExternalUniqueId = employeesAttendanceEntity.ExternalUniqueId
		employeesOriginalExcelJsonEntity.FirstName = employeesAttendanceEntity.FirstName
		employeesOriginalExcelJsonEntity.LastName = employeesAttendanceEntity.LastName
		employeesOriginalExcelJsonEntity.Status = employeesAttendanceEntity.Status
		employeesOriginalExcelJsonEntity.SystemCode = employeesAttendanceEntity.SystemCode
		employeesOriginalExcelJsonEntity.Title = employeesAttendanceEntity.Title
		employeesOriginalExcelJsonEntity.IsHcp = employeesAttendanceEntity.IsHcp
		employeesOriginalExcelJsonEntity.FirstNameEzClaim = employeesAttendanceEntity.FirstNameEzClaim
		employeesOriginalExcelJsonEntity.LastNameEzClaim = employeesAttendanceEntity.LastNameEzClaim
		employeesOriginalExcelJsonEntity.IsSpeaker = employeesAttendanceEntity.IsSpeaker
		employeesEmailJson = append(employeesEmailJson, employeesOriginalExcelJsonEntity)
	}
	eventAttendances, err := json.Marshal(employees)
	if err != nil {
		panic(err)
	}
	eventAttendancesForEmail, err := json.Marshal(employeesEmailJson)
	if err != nil {
		panic(err)
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}

	return string(eventAttendances), string(eventAttendancesForEmail), result
}
