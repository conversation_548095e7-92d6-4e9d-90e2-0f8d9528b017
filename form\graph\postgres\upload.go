package postgres

import (
	"context"
	"log"

	"github.com/ihcp/form/graph/model"
	uuid "github.com/satori/go.uuid"
)

func InsertUploadLogsAttendance(attendanceUrl string, FileName string, excelType string, author uuid.UUID) string {
	log.Println("InsertUploadLogsAttendance()")
	if pool == nil {
		pool = GetPool()
	}
	var u2 uuid.UUID
	querystring := "INSERT INTO upload (blob_url, blob_name, type, created_by, status) VALUES($1, $2, $3, $4, $5) RETURNING(id)"
	err := pool.QueryRow(context.Background(), querystring, attendanceUrl, FileName, excelType, author, "Processing").Scan(&u2)
	if err != nil {
		log.Println(err)
	}
	// mybyte := u2.Get().([16]byte
	uuidstr := u2.String()
	// log.Println("InsertUploadLogs(): uuidstr: " + uuidstr)
	return uuidstr
}
func UpdateUploadStatus(uploadId *uuid.UUID, status string, author uuid.UUID) {
	log.Println("UpdateUploadStatus()")
	if pool == nil {
		pool = GetPool()
	}
	updateStmt := "UPDATE upload SET status = $2, modified_by = $3, last_modified = now() WHERE id = $1"
	commandTag, err := pool.Exec(context.Background(), updateStmt, uploadId, status, author)
	if err != nil {
		log.Println(err)
	}
	if commandTag.RowsAffected() != 1 {

		log.Println("UpdateUploadStatus: Fail to update upload status with id: " + uploadId.String())
	}
}
func InsertUploadValidationForFormAttendanceErrors(validation *model.ValidationResult, uploadId *uuid.UUID, author uuid.UUID) {
	log.Println("InsertUploadValidationErrors()")
	// Update upload status to Fail.
	if pool == nil {
		pool = GetPool()
	}
	if uploadId != nil {

		UpdateUploadStatus(uploadId, "Fail", author)

		querystring := "INSERT INTO upload_log (upload, row, message, created_by) VALUES($1, $2, $3, $4)"
		for _, validationError := range validation.ExcelValidationMessages {
			commandTag, err := pool.Exec(context.Background(), querystring, uploadId, validationError.Row, validationError.Message, author)
			if err != nil {
				log.Println(err)
			}
			if commandTag.RowsAffected() != 1 {
				log.Println("No row inserted into uploads")
			}
		}
	} else {
		log.Println("InsertUploadValidationErrors(): Upload id is empty")
	}

}
func InsertUploadLogsSponsoredExcel(input model.ExcelUploadForSponsoredHcpEngagementRequest, excelType string, author uuid.UUID) string {
	log.Println("InsertUploadLogsAttendance()")
	if pool == nil {
		pool = GetPool()
	}
	var u2 uuid.UUID
	querystring := "INSERT INTO upload (blob_url, blob_name, type, created_by, status) VALUES($1, $2, $3, $4, $5) RETURNING(id)"
	err := pool.QueryRow(context.Background(), querystring, input.URL, input.FileName, excelType, author, "Processing").Scan(&u2)
	if err != nil {
		log.Println(err)
	}
	// mybyte := u2.Get().([16]byte
	uuidstr := u2.String()
	// log.Println("InsertUploadLogs(): uuidstr: " + uuidstr)
	return uuidstr
}
