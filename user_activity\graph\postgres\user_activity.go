package postgres

import (
	"context"
	"log"

	"github.com/gofrs/uuid"
	"github.com/ihcp/user_activity/graph/entity"
	"github.com/ihcp/user_activity/graph/logengine"
	"github.com/ihcp/user_activity/graph/model"
	"github.com/jackc/pgtype"
	"github.com/jmoiron/sqlx"
)

func InsertUserTrackingDb(input entity.UserTrackingEntity) error {
	if pool == nil {
		pool = GetPool()
	}
	query := `INSERT INTO user_tracking
	( user_id, "action", page_name, user_ip, accessed_datetime, event_id)
	VALUES( $1, $2, $3, $4, now(), $5)`
	_, err := pool.Exec(context.Background(), query, input.UserId, input.Action, input.PageName, input.UserIp, input.MeetingId)
	if err != nil {
		log.Println("InsertUserTrackingDb : " + err.<PERSON>rror())
		return err
	}
	return nil

}
func FetchUserTrackAPIDb(action string, formAnswerId uuid.UUID, country *int) ([]*model.FetchUserTrackingData, error) {
	if pool == nil {
		pool = GetPool()
	}
	var result []*model.FetchUserTrackingData
	var inputargs []interface{}
	query := `select ut.id	,concat(u.first_name,' ',u.last_name) as user_name 	,ut."action",	ut.page_name,	ut.user_ip	,ut.accessed_datetime::text,	ut.event_id from user_tracking ut
	inner join "user" u on u.id =ut.user_id where u.country=? `
	inputargs = append(inputargs, country)
	if formAnswerId != uuid.Nil {
		query += ` and ut.event_id=?  `
		inputargs = append(inputargs, formAnswerId)
	}
	if action != "" {
		query += ` and  ut."action"=? order by ut.accessed_datetime desc limit 1`
		inputargs = append(inputargs, action)
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("GetAdminAnswerList query called")
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		answerEntity := &model.FetchUserTrackingData{}
		err := rows.Scan(&answerEntity.ID, &answerEntity.UserName, &answerEntity.Action,
			&answerEntity.PageName, &answerEntity.UserIP,
			&answerEntity.AccessedDatetime, &answerEntity.EventID)
		result = append(result, answerEntity)
		if err != nil {
			return nil, err
		}
	}
	return result, nil
}
func GetAllUserActivitiesExceptToday() ([]entity.UserTrackingEntityAllData, error) {
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.UserTrackingEntityAllData
	query := `select 	ut.user_id,	ut."action"	,ut.page_name,concat(u.first_name,' ',u.last_name) as user_name,	ut.user_ip	,ut.accessed_datetime::text	,ut.event_id from user_tracking ut
	inner join "user" u on u.id =ut.user_id 
	where accessed_datetime<(now())::date  `
	rows, err := pool.Query(context.Background(), query)
	logengine.GetTelemetryClient().TrackEvent("GetAdminAnswerList query called")
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		var answerEntity entity.UserTrackingEntityAllData
		err := rows.Scan(&answerEntity.UserId, &answerEntity.Action, &answerEntity.PageName,
			&answerEntity.UserName, &answerEntity.UserIp,
			&answerEntity.AccessedDatetime, &answerEntity.MeetingId)
		result = append(result, answerEntity)
		if err != nil {
			return nil, err
		}
	}
	return result, nil

}
func InsertUploadLogs(URL string, FileName string, excelType string, author string) error {
	log.Println("InsertUploadLogs()")
	if pool == nil {
		pool = GetPool()
	}
	var u2 pgtype.UUID
	querystring := "INSERT INTO upload (blob_url, blob_name, type, created_by, status) VALUES($1, $2, $3, $4, $5) RETURNING(id)"
	err := pool.QueryRow(context.Background(), querystring, URL, FileName, excelType, author, "Write Success").Scan(&u2)
	if err != nil {
		log.Println(err)
		return err
	}
	return err
}
func DeletePreviousData() error {
	log.Println("DeletePreviousData()")
	if pool == nil {
		pool = GetPool()
	}
	query := `DELETE FROM user_tracking
	WHERE accessed_datetime<(now())::date`
	_, err := pool.Exec(context.Background(), query)
	if err != nil {
		log.Println(err)
		return err
	}
	return err

}
