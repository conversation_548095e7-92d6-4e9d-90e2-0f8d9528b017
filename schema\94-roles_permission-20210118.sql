alter table roles_permission add column "user_role_id" uuid ;
alter table roles_permission add constraint fk_user_role_id foreign key (user_role_id) REFERENCES user_roles(id) ;


update roles_permission set user_role_id = (select id from user_roles where value = 'finance') where role_id = 64;
update roles_permission set user_role_id = (select id from user_roles where value = 'admin') where role_id = 63;
update roles_permission set user_role_id = (select id from user_roles where value = 'clustercomplianceofficer') where role_id = 49;
update roles_permission set user_role_id = (select id from user_roles where value = 'datastewardadmin') where role_id = 401;
update roles_permission set user_role_id = (select id from user_roles where value = 'sfeadmin') where role_id = 402;
update roles_permission set user_role_id = (select id from user_roles where value = 'linesecretary') where role_id = 403;
update roles_permission set user_role_id = (select id from user_roles where value = 'salesmanager') where role_id = 46;
update roles_permission set user_role_id = (select id from user_roles where value = 'bumanager') where role_id = 47;
update roles_permission set user_role_id = (select id from user_roles where value = 'countrymedical') where role_id = 48;
update roles_permission set user_role_id = (select id from user_roles where value = 'countrycommercialsolutionhead') where role_id = 50;
update roles_permission set user_role_id = (select id from user_roles where value = 'regionalmarketing') where role_id = 51;
update roles_permission set user_role_id = (select id from user_roles where value = 'regionalcompliance') where role_id = 52;
update roles_permission set user_role_id = (select id from user_roles where value = 'regionalfranchisehead') where role_id = 54;
update roles_permission set user_role_id = (select id from user_roles where value = 'regionalmedical') where role_id = 53;
update roles_permission set user_role_id = (select id from user_roles where value = 'salesrepresentative') where role_id = 68;
update roles_permission set user_role_id = (select id from user_roles where value = 'productmanager') where role_id = 69;
update roles_permission set user_role_id = (select id from user_roles where value = 'salesmanager') where role_id = 70;
update roles_permission set user_role_id = (select id from user_roles where value = 'marketaccessmanager') where role_id = 71;
update roles_permission set user_role_id = (select id from user_roles where value = 'medicalmanager') where role_id = 72;