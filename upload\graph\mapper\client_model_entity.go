package mapper

import (
	"regexp"
	"strings"

	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func ClientExcelToEntities(data [][]string, userUUID uuid.UUID) ([]*entity.ClientExcelInput, *model.ValidationResult) {
	var Clients []*entity.ClientExcelInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	countryID, err := postgres.GetUserCountryByActiveDir(userUUID)
	if err != nil {
		errorMessage := &model.ExcelValidationMessage{Message: err.Error()}
		validationMessages = append(validationMessages, errorMessage)

	}
	country := countryID
	pattern := regexp.MustCompile(`^[A-Za-z ]+$`)
	for i := 1; i < len(data); i++ {
		var uid uuid.UUID
		var err error
		uuidString := strings.TrimSpace(data[i][0])
		if uuidString != "" {
			uid, err = uuid.FromString(uuidString)
			if err != nil {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: err.Error()}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		var isActive bool
		var isDeleted bool
		if data[i][2] != "" {
			if strings.TrimSpace(data[i][3]) == "Y" {
				isActive = true
				isDeleted = false
			} else {
				isActive = false
				isDeleted = false
			}
		} else {
			isActive = true
			isDeleted = false
		}
		if strings.TrimSpace(data[i][2]) != "" {
			match := pattern.MatchString(strings.TrimSpace(data[i][2]))
			if !match {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: "Client Name format is invalid!"}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		m := &entity.ClientExcelInput{
			ID:          &uid,
			ClientName:  strings.TrimSpace(data[i][2]),
			CountryName: strings.TrimSpace(data[i][1]),
			CountryNo:   country,
			IsActive:    isActive,
			IsDeleted:   isDeleted,
		}

		if m.ID != nil && m.CountryName == "" && m.ClientName == "" {
			m.IsActive = false
			m.IsDeleted = false
		}

		m.ValidateClientExcelData(i+1, validationMessages)
		Clients = append(Clients, m)
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}
	return Clients, result

}
