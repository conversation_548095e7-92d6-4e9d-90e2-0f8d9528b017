package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/model"
)

// UpdateEventApprovalFormMaptool is the resolver for the UpdateEventApprovalFormMaptool field.
func (r *mutationResolver) UpdateEventApprovalFormMaptool(ctx context.Context, input model.UpdateEventApprovalFromMaptoolInput) (*model.CommonResponse, error) {
	//if err := r.MapToolAuthentication(ctx); err != nil {
	//	errStr := err.Error()
	//	return &model.CommonResponse{
	//		Success: false,
	//		Message: &errStr,
	//	}, nil
	//}

	return controller.ApproveEventFromMaptool(ctx, &input), nil
}
