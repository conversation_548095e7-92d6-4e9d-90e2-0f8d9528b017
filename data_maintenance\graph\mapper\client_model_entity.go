package mapper

import (
	"errors"
	"regexp"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapClientModelToEntity(inputModel model.ClientInput, userUUID uuid.UUID) (*entity.ClientUpsertInput, *model.UpsertClientResponse) {
	var id *uuid.UUID
	result := &model.UpsertClientResponse{Error: false}
	entity := &entity.ClientUpsertInput{}
	isDeleted := false
	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			id = &uuid
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "Client ID format is invalid!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.ID = id
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			if isDeleted == true {
				entity.IsActive = false
			} else {
				entity.IsActive = true
			}
		} else {
			entity.IsActive = true
		}
		countryID, err := postgres.GetUserCountryByID(userUUID)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Country does not exists!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.CountryCode = countryID

		if inputModel.ClientName != nil && strings.TrimSpace(*inputModel.ClientName) != "" {
			pattern := "^[A-Za-z ]+$"
			match, _ := regexp.MatchString(pattern, strings.TrimSpace(*inputModel.ClientName))
			if match {
				entity.ClientName = strings.TrimSpace(*inputModel.ClientName)
			} else {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Client Name format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
		}

	} else {
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			if isDeleted == true {
				entity.IsActive = false
			} else {
				entity.IsActive = true
			}
		} else {
			entity.IsActive = true
		}

		countryID, err := postgres.GetUserCountryByID(userUUID)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Country does not exists!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.CountryCode = countryID

		if inputModel.ClientName != nil && strings.TrimSpace(*inputModel.ClientName) != "" {
			pattern := "^[A-Za-z ]+$"
			match, _ := regexp.MatchString(pattern, strings.TrimSpace(*inputModel.ClientName))
			if match {
				entity.ClientName = strings.TrimSpace(*inputModel.ClientName)
			} else {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Client Name format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
		} else {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Client name cannot be blank!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}
	entity.ValidateClientUpsertData(result)

	return entity, result

}
func ExportClientInputModelToEntity(input *model.ClientRequest, userUUID uuid.UUID) (*entity.ExportClientExcel, error) {
	var outEntity entity.ExportClientExcel
	sortElements := []entity.SortingElements{}
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid
	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New(err.Error())

	}
	outEntity.Country = countryID
	if input.IsActive != nil {
		outEntity.IsActive = input.IsActive
	}
	if input.ClientName != nil {
		outEntity.ClientName = strings.TrimSpace(*(input).ClientName)
	}
	if input.Status != nil {
		outEntity.Status = strings.TrimSpace(*(input).Status)
	}
	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = *input.SearchItem
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements
	return &outEntity, nil
}
func ExportClientInfoEntityToModel(input []entity.ClientExcel) ([]*model.Client, []*entity.ClientExcelData) {
	code := codeController.GetIdKeyCodes()["country"]
	var response []*model.Client
	var responseExcel []*entity.ClientExcelData
	for _, item := range input {
		res := new(model.Client)
		res.ID = item.ID.String()

		if item.Country.Int32 != 0 {
			country := code[int(item.Country.Int32)].Title.String
			res.Country = &country
		} else {
			res.Country = nil
		}
		if item.ClientName.String != "" {
			clientName := item.ClientName.String
			res.ClientName = clientName
		} else {
			res.ClientName = ""
		}
		res.IsActive = item.IsActive
		if item.IsActive && !item.IsDeleted {
			res.Status = "Active"
		} else {
			res.Status = "InActive"
		}
		response = append(response, res)

		resExcel := new(entity.ClientExcelData)
		resExcel.Data = append(resExcel.Data, item.ID.String())
		resExcel.Data = append(resExcel.Data, code[int(item.Country.Int32)].Title.String)
		resExcel.Data = append(resExcel.Data, item.ClientName.String)
		if item.IsActive {
			resExcel.Data = append(resExcel.Data, "Y")
		} else {
			resExcel.Data = append(resExcel.Data, "N")
		}
		responseExcel = append(responseExcel, resExcel)
	}
	return response, responseExcel

}
