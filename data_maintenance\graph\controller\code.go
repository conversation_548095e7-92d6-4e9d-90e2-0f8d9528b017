package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
)

func FetchCodeValues(ctx *context.Context, input *model.CodeInput) *model.CodeResponse {
	var response model.CodeResponse
	if input.Category == "" {
		response.Error = true
		response.Message = "Category cannot be Blank"
		return &response
	}

	values, err := postgres.GetCodeValues(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchCodeValue := mapper.FetchCodeValuesEntityToModel(values)
	response.Data = fetchCodeValue
	return &response
}
