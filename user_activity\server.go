package main

import (
	"log"
	"net/http"
	"os"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/go-chi/chi"
	"github.com/gorilla/websocket"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/login/auth"
	"github.com/ihcp/user_activity/graph/generated"
	"github.com/ihcp/user_activity/graph/postgres"
	"github.com/ihcp/user_activity/graph/resolvers"
	"github.com/ihcp/user_activity/graph/utils"
	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

const defaultPort = "8080"

func main() {

	err := godotenv.Load(".dev.env")
	if err != nil {
		log.Println("error loading env", err)
	}
	postgres.InitDbPool()
	pool := postgres.GetPool()
	codeController.InitCodes(pool)
	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}
	go utils.ExecuteCronServiceForRemoveBeforeTodayUserActivityList()

	router := chi.NewRouter()
	router.Use(cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowCredentials: true,
		Debug:            false,
	}).Handler)

	srv := handler.NewDefaultServer(generated.NewExecutableSchema(generated.Config{Resolvers: &resolvers.Resolver{}}))
	srv.AddTransport(&transport.Websocket{
		Upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Check against your desired domains here
				return r.Host == "*"
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	})
	router.Use(auth.AuthMiddleware(pool))
	router.Handle("/", playground.Handler("GraphQL playground", "/query"))
	router.Handle("/query", srv)
	http.Handle("/", http.FileServer(http.Dir("/tmp")))
	log.Printf("connect to http://localhost:%s/ for GraphQL playground", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}
