package controller

import (
	"context"
	"errors"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/ihcp/data_maintenance/graph/azure"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertMaterialData(ctx *context.Context, inputModel model.MaterialInput) *model.UpsertMaterialResponse {
	upsertResponse := &model.UpsertMaterialResponse{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, validationResult := mapper.MapMaterialModelToEntity(&inputModel, userUUID)

	if !validationResult.Error {
		if entity.ID == nil && postgres.CheckMaterialDB(entity) {
			postgres.InsertMaterialData(entity, userUUID, upsertResponse)
		} else if entity.ID != nil && (postgres.CheckMaterialDB(entity) || !entity.IsActive) {
			divisionID := postgres.GetDivisionIdFromMaterialId(*entity.ID)
			postgres.UpdateMaterialData(entity, &inputModel, upsertResponse, divisionID, userUUID)
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = "Material Already Exists!!"
			upsertResponse.ValidationErrors = validationResult.ValidationErrors
		}
		// else if entity.ID != nil && entity.IsDeleted {
		// 	divisionID := postgres.GetDivisionIdFromMaterialId(*entity.ID)
		// 	postgres.DeleteMaterialData(entity, upsertResponse, divisionID, userUUID)
		// }
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = "Material validation failed!"
		upsertResponse.ValidationErrors = validationResult.ValidationErrors
	}
	return upsertResponse
}

func ExportMaterialExcel(ctx *context.Context, input *model.MaterialRequest) *model.MaterialResponse {
	var response model.MaterialResponse
	var userUUID uuid.UUID
	var err error
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	if !postgres.UserIsAdmin(*userID) {
		response.Error = true
		response.Message = "User is not authorized to access"
		return &response
	}
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		}
	}
	exportMaterial, err := mapper.ExportMaterialInputModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	materialCount, countErr := postgres.MaterialCount(exportMaterial.Country)
	if countErr != nil {
		response.Error = true
		response.Message = countErr.Error()
		return &response
	}
	materialInfo, err := postgres.GetMaterialExcelInfo(input, exportMaterial)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputModels, outputModelsExcel := mapper.ExportMaterialInfoEntityToModel(materialInfo)
	if input.IsExcel {
		url, err := createExportMaterialExcel(outputModelsExcel)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.URL = url
	} else {
		response.Data = outputModels
	}
	response.TotalCount = materialCount
	return &response
}

func createExportMaterialExcel(input []*entity.MaterialExcelData) (string, error) {
	if len(input) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "material"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)

	// Populate excel 1 row header columns
	typeInfo := reflect.TypeOf(entity.MaterialExcelResponse{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
		headerRow = append(headerRow, strings.ToLower(typeInfo.Field(fieldIndex).Name))
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	for index, item := range input {
		rowNumber := index + 2

		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "materialExcel" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }
	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}

func ExportMaterialInstructionExcel(f *excelize.File) {
	instructions := [10]string{"Avoid editing the column header and the sheet name", "Please do not edit id column generated by the system. Leave the id column blank for new records",
		"To delete records, keep the id intact and delete the other column values", "Please avoid data formatting across rows where there is no data",
		"", ""}

	instructiondata := make([]entity.MaterialInstructionExcel, 0)
	tempInstructrionData := entity.MaterialInstructionExcel{Column: "Column", Mandatory: "Mandatory", Limit: "Limit"}
	instructiondata = append(instructiondata, tempInstructrionData)
	tempInstructrionData1 := entity.MaterialInstructionExcel{Column: "Id", Mandatory: "N", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData1)
	tempInstructrionData2 := entity.MaterialInstructionExcel{Column: "vCountryName", Mandatory: "Y", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData2)
	tempInstructrionData3 := entity.MaterialInstructionExcel{Column: "vDivisionName", Mandatory: "Y", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData3)
	tempInstructrionData4 := entity.MaterialInstructionExcel{Column: "vMatGroupCode", Mandatory: "Y", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData4)
	tempInstructrionData5 := entity.MaterialInstructionExcel{Column: "vMatGroupName", Mandatory: "Y", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData5)
	tempInstructrionData6 := entity.MaterialInstructionExcel{Column: "Status", Mandatory: "Y", Limit: "-"}
	instructiondata = append(instructiondata, tempInstructrionData6)
	sheetName1 := "Instructions"
	f.NewSheet(sheetName1)
	for fieldIndex := 0; fieldIndex < len(instructions); fieldIndex++ {
		f.SetCellValue(sheetName1, "A"+strconv.Itoa(fieldIndex+1), instructions[fieldIndex])
	}

	for index, instruction := range instructiondata {
		rowNumber := index + 8
		rowNumberString := strconv.FormatInt(int64(rowNumber), 10)
		for fieldIndex1 := 0; fieldIndex1 < 3; fieldIndex1++ {
			typeValue := reflect.ValueOf(instruction)
			f.SetCellValue(sheetName1, getColumnName(fieldIndex1+1)+rowNumberString, typeValue.Field(fieldIndex1).String())
		}
	}
}
