package mapper

import (
	"errors"
	"sort"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapExceptionalApprovalManagementInputModelToEntity(inputModel model.ExceptionalApprovalManagementInput) (*entity.UpsertExceptionalApprovalManagementEntity, error) {
	var entity entity.UpsertExceptionalApprovalManagementEntity
	if inputModel.IsDelete != nil {
		entity.IsDelete = *inputModel.IsDelete
	} else {
		entity.IsDelete = false
	}

	if !entity.IsDelete {
		if inputModel.UserID != nil {
			if *inputModel.UserID != "" {
				uuid1, err := uuid.FromString(*inputModel.UserID)
				if err != nil {
					return nil, errors.New("User ID format is invalid!")
				} else {
					entity.UserID = &uuid1
				}
			} else {
				return nil, errors.New("User ID cannot be blank")
			}
		} else {
			return nil, errors.New("User ID cannot be nil")
		}
		if inputModel.ID != nil {
			if *inputModel.ID != "" {
				uuid, err := uuid.FromString(*inputModel.ID)
				if err != nil {
					return nil, errors.New("ID format is invalid!")
				} else {
					entity.ID = &uuid
				}
			} else {
				return nil, errors.New("ID cannot be blank")
			}
		}
	} else {
		if inputModel.ID != nil {
			if *inputModel.ID != "" {
				uuid, err := uuid.FromString(*inputModel.ID)
				if err != nil {
					return nil, errors.New("ID format is invalid!")
				} else {
					entity.ID = &uuid
				}
			} else {
				return nil, errors.New("ID cannot be blank")
			}
		} else {
			return nil, errors.New("ID cannot be nil")
		}
	}

	if !entity.IsDelete {
		exceptionInsertEntity, err := postgres.GetDetailsFromUserId(*entity.UserID)
		if err != nil {
			return nil, err
		}
		entity.UserRole = exceptionInsertEntity.UserRole
		entity.Country = exceptionInsertEntity.Country
		entity.Department = exceptionInsertEntity.Department
	}
	if inputModel.SequencenNo != nil {
		entity.SequenceNo = inputModel.SequencenNo
	} else {
		return nil, errors.New("Sequence Number cannot be nil")
	}
	if inputModel.GroupType != nil {
		codes := codeController.GetValueKeyCodes()["group"]
		groupId := codes[*inputModel.GroupType].ID
		entity.GroupId = &groupId
	}
	return &entity, nil
}

func FetchExceptionalApprovalManagementDataModelToEntity(input model.FetchExceptionalApprovalManagementInput, userCountry *int) (*entity.FetchExceptionDetails, error) {
	var inputentity entity.FetchExceptionDetails
	if input.UserID != nil {
		userUUID, err := uuid.FromString(*input.UserID)
		if err != nil {
			return nil, errors.New("UserID format is invalid!")
		}
		inputentity.UserID = &userUUID
	}
	if userCountry != nil {
		inputentity.UserCountry = userCountry
	}
	if input.IsCompliance != nil {
		inputentity.IsCompliance = input.IsCompliance
	}
	return &inputentity, nil
}

func FetchExceptionalApprovalManagementDataEntityToModel(outputentity []entity.OutputFetchEntity) (model.FetchExceptionalApprovalManagementResponse, error) {
	var resultEntity model.FetchExceptionalApprovalManagementResponse
	roleMap := make(map[int]*model.UserByApprovalRole)
	groupType := codeController.GetIdKeyCodes()["group"]
	for _, userSelection := range outputentity {
		var departmentType string
		if userSelection.GroupType != nil {
			departmentType = groupType[*userSelection.GroupType].Title.String
		}
		if _, ok := roleMap[*userSelection.SequenceNo]; !ok {

			roleModel := &model.UserByApprovalRole{
				DepartmentID:   userSelection.DepartmentId,
				DepartmentName: userSelection.DepartmentName,
				SequenceNo:     userSelection.SequenceNo,
				DepartmentType: &departmentType,
			}
			roleMap[*userSelection.SequenceNo] = roleModel
		}
		// if userSelection.UserId.String == userId {
		// 	continue
		// }
		// if userSelection.UserID.String == "" {
		// 	continue
		// }
		user := &model.UserSelection{
			Description: userSelection.UserName,
			Value:       userSelection.UserId,
			RoleID:      userSelection.UserRole,
		}
		roleMap[*userSelection.SequenceNo].UserSelection = append(roleMap[*userSelection.SequenceNo].UserSelection, user)
	}
	for _, v := range roleMap {
		resultEntity.Data = append(resultEntity.Data, v)
	}
	sort.Slice(resultEntity.Data[:], func(i, j int) bool {
		return *resultEntity.Data[i].SequenceNo < *resultEntity.Data[j].SequenceNo
	})
	return resultEntity, nil
}
