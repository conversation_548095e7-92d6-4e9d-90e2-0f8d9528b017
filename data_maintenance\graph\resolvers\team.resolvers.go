package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertTeam is the resolver for the upsertTeam field.
func (r *mutationResolver) UpsertTeam(ctx context.Context, input model.TeamInput) (*model.UpsertTeamResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertTeam", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertTeam :" + string(inputJson))
	response := controller.UpsertTeamData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertTeam", "data_maintenance/UpsertTeam", time.Since(start), "200")
	return response, nil
}

// GetTeamList is the resolver for the getTeamList field.
func (r *queryResolver) GetTeamList(ctx context.Context) (*model.TeamResponse, error) {
	start := time.Now().UTC()
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetTeamList : No Input")
	response := controller.FetchTeamValues(&ctx)
	logengine.GetTelemetryClient().TrackRequest("GetTeamList", "data_maintenance/GetTeamList", time.Since(start), "200")
	return response, nil
}
