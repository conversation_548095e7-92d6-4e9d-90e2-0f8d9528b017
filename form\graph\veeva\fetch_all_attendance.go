package veeva

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strings"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
)

func VeevaGetAllAttendanceListCreate(veevaId string) ([]entity.AttendanceRecords, error) {
	functionName := "VeevaGetAllAttendanceListCreate()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllAttendanceData
	method := "GET"
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(os.Getenv("AUTH_VEEVA_URL"))
	url := veevaInstanceURL + `/services/data/v54.0/query?q=Select+Id+,+Account_vod__c+,+Status_vod__c+,+Last_Name_vod__c+,+First_Name_vod__c+,+Account_vod__r.ZLG_Local_Specialty__r.name+,+Organization_vod__c+,+Specialty_input__c+,+Account_vod__r.Specialty_1_QS__r.name+,+Account_vod__r.Specialty_2_QS__r.name+,+Account_vod__r.Primary_Parent_vod__r.Name+FROM+Event_Attendee_vod__c+where+Medical_Event_vod__c+=+'` + veevaId + `'`
	request, err := http.NewRequest(method, strings.ReplaceAll(url, " ", ""), bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	bearer := "Bearer " + os.Getenv("AUTH_VEEVA_TOKEN")
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	defer response.Body.Close()

	responseBody, responseErr := io.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, responseErr
	}
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)

	if err2 != nil {
		err4 := GetVeevaErrorResponse(responseBody)
		if err4 != nil {
			return nil, errors.New(err4.Error())
		} else {
			return nil, errors.New(err2.Error())
		}
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllAttendanceData
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			err5 := GetVeevaErrorResponse(responseForNextRecordBody)
			if err5 != nil {
				return nil, errors.New(err5.Error())
			} else {
				return nil, errors.New(err3.Error())
			}
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}

	return ResponseUrlValues.Records, nil

}
