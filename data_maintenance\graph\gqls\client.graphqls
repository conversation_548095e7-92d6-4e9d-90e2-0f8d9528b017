input ClientInput {
    id: String
    isDelete: Boolean
    clientName:String
    isActive: Boolean
}

input clientRequest{
    isExcel: Boolean!
    id:String
    status: String
    searchItem: String
    limit: Int
    pageNo: Int
    isActive: Boolean
    clientName:String
    sort: [SortingInputs]
}

type client {
    id: String!
    country: String
    status: String!
    isActive: Boolean!
    clientName:String!
}

type clientResponse{
    error: Boolean!
    message: String!
    url: String!
    totalCount: Int!
    data: [client]!
}

type UpsertClientResponse {
  error: Boolean!
  message: String!
  validationErrors: [validationMessage]
}

extend type Query {
    clientListExport(input: clientRequest!): clientResponse!
}

extend type Mutation {
    upsertClient(input: ClientInput!): UpsertClientResponse!
}





