
input excelUploadRequestForEmployeeAttendance {
    fileName: String!
    url: [String!]!
    formAnswerId:String!
}

type excelUploadResponse {
    error: Boolean!
    message: String!
}

type excelValidationMessage {
    row: Int!
    message: String!
}

type validationResult {
    error: Boolean!
    validationTimeTaken: String!
    excelValidationMessages: [excelValidationMessage]
}


type Mutation {
    excelUploadForEmployeeAttendance(input:excelUploadRequestForEmployeeAttendance!): excelUploadResponse!
}
