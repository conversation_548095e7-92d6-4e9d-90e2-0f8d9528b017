<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en" xml:lang="en">
<head>

	<meta http-equiv="Content-type" content="text/html; charset=utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="format-detection" content="date=no" />
	<meta name="format-detection" content="address=no" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="x-apple-disable-message-reformatting" />
    <!--[if !mso]><!-->
	<link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;1,500&display=swap" rel="stylesheet">
	<link rel="preconnect" href="https://fonts.gstatic.com">
	<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <!--<![endif]-->
	<title>eZFlow</title>


	<style type="text/css" media="screen">
		/* Linked Styles */
		a { text-decoration:none }
		p { padding:0 !important; margin:0 !important } 
		img { -ms-interpolation-mode: bicubic; /* Allow smoother rendering of resized image in Internet Explorer */ }

				
		/* Mobile styles */
		@media only screen and (max-device-width: 480px), only screen and (max-width: 480px) {
			.mobile-shell { width: 100% !important; min-width: 100% !important; }

			.holder { padding: 0 10px !important; }
			.ribbon { font-size: 18px !important; }
			.center { margin: 0 auto !important; }
			.td { width: 100% !important; min-width: 100% !important; }
			.text-header .link-white { text-shadow: 0 3px 4px rgba(0,0,0,09) !important; }
			.m-br-15 { height: 15px !important; }
			.bg { height: auto !important; } 
			.m-td,
			.m-hide { display: none !important; width: 0 !important; height: 0 !important; font-size: 0 !important; line-height: 0 !important; min-height: 0 !important; }
			.m-block { display: block !important; }
			.p30-15 { padding: 30px 15px !important; }
			.p15-15 { padding: 15px 15px !important; }
			.p30-0 { padding: 30px 0px !important; }
			.p0-0-30 { padding: 0px 0px 30px 0px !important; }
			.p0-15-30 { padding: 0px 15px 30px 15px !important; }
			.p0-15 { padding: 0px 15px 0px 15px !important; }
			.mp0 { padding: 0px !important; }
			.mp20-0-0 { padding: 20px 0px 0px 0px !important }
			.mp30 { padding-bottom: 30px !important; }
			.container { padding: 20px 0px !important; }
			.outer { padding: 0px !important }
			.fluid-img img { width: 100% !important; max-width: 100% !important; height: auto !important; }
		}
	</style>
</head>
<body style="padding:150; margin:150; display: block; -webkit-text-size-adjust:none; font-family: 'Poppins', sans-serif;">
	<table width="100%" border="0" cellspacing="0" cellpadding="0" align="center"> 
		<caption></caption>
		<tr>
			<th></th>
		</tr>
		<tr>
			<td align="center" valign="top"> 
				<table width="634" border="0" cellspacing="0" cellpadding="0" class="mobile-shell" bgcolor="#fff" style="box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16); -webkit-box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16); -moz-box-shadow: 0 3px 6px 0 rgba(0, 0, 0, 0.16); border: 1px solid #efeff1">
					<caption></caption>
					<tr><th></th></tr>
					<tr>
						<td class="td" style="width:634px; min-width:634px; font-size:0pt; line-height:0pt; padding:0; margin:0;">
							<table width="100%" border="0" cellspacing="0" cellpadding="0">
								<caption></caption>
								<tr><th></th></tr>
								<tr>
									<td>
										<!-- Header -->
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<caption></caption>
											<tr><th></th></tr>
											<tr>
												<td align="center" style="padding-top: 0px; padding-bottom: 0px;" bgcolor="#72b860">
													<img src="https://ezflowprodstorage.blob.core.windows.net/public/ezflow/images/footer2.jpg" border="0" alt="" / style="width: 100%; height:auto;">
												</td>
											</tr>
										</table>
										<!-- End Header -->

										<!-- Body Content -->
										<table width="100%" border="0" cellspacing="0" cellpadding="0" style="margin: 100px 0;">
											<caption></caption>
											<tr><th></th></tr>
											<tr>
												<td style="font-size: 16px; font-weight: 300; line-height: 24px; color: #4a4a4a; padding:0 20px; text-align: center; font-family: 'Poppins', sans-serif;">
													The following event has been approved <br/>
													Meeting ID: {{.EventCode}} <br/>
													Activity Name: {{.ActivityName}} <br/>
													{{if ne .ApolloUrl ""}}
													<a href='{{.ApolloUrl}}'>Please click here to login to Apollo bot</a><br/>
													{{else}}
													<a href='{{.SiteUrl}}'>Please click here to login to eZFlow</a><br/>
													{{end}}




													<small>In case you find any discrepancy in the event details or have <br/>
													any other issues please send us an email reporting with a screenshot on <br/>
													<EMAIL></small><br/>
												</td>
											</tr>
										</table>
										<!-- End Body Content -->									

										<!-- Footer -->
										<table width="100%" border="0" cellspacing="0" cellpadding="0">
											<caption></caption>
											<tr><th></th></tr>
											<tr>
												<td align="center" style="padding-top: 0px; padding-bottom: 0px; " bgcolor="#72b860">
													<img src="https://ezflowprodstorage.blob.core.windows.net/public/ezflow/images/footer1.jpg" border="0" alt="" / style="width: 100%; height:auto">
												</td>
											</tr>
										</table>
										<!-- End Footer -->
									</td>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>
	<br/>
</body>
</html>
