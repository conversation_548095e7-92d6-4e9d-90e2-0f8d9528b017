CREATE TABLE email_log (
	id uuid NOT NULL DEFAULT public.uuid_generate_v4(),
	event_id uuid NOT NULL,
	type_of_email int4 NOT NULL,
	email_content text null ,
	created_by uuid not NULL,
	date_created timestamptz NOT NULL DEFAULT now(),
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false
);

ALTER TABLE email_log ADD CONSTRAINT fk_event_id FOREIGN KEY (event_id) REFERENCES form_answers(id);


ALTER TABLE email_log ADD CONSTRAINT fk_type_of_email FOREIGN KEY (type_of_email) REFERENCES code(id);

alter table email_log add column receiver uuid null ;
