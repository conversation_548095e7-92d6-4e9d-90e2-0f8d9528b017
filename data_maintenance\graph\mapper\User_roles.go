package mapper

import (
	"errors"
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapUserRolesModelToEntity(inputModel *model.UserRolesInput, userUUID uuid.UUID) (*entity.UserRolesEntity, error) {
	var entity entity.UserRolesEntity

	if inputModel.IsDelete != nil {
		isDelete := *(inputModel).IsDelete
		entity.IsDeleted = isDelete
	} else {
		entity.IsDeleted = false
	}
	if inputModel.ID != nil {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err != nil {
			return nil, errors.New("ID format is invalid!")
		} else {
			entity.ID = &uuid
		}
	}

	if !entity.IsDeleted {

		if inputModel.ID == nil {
			if inputModel.UserRole == nil {
				return nil, errors.New("User role cannot be blank")
			}
			if inputModel.Description == nil {
				return nil, errors.New("Description cannot be blank")
			}
		}

		if inputModel.UserRole != nil {
			if *inputModel.UserRole == "" {
				return nil, errors.New("User Role cannot be blank")
			} else {
				if postgres.HasUserRole(inputModel) {
					return nil, errors.New("User role already exists")
				} else {
					entity.UserRole = *inputModel.UserRole
					value_first := strings.ToLower(*inputModel.UserRole)
					value := strings.ReplaceAll(value_first, " ", "")
					entity.Value = value
					entity.CreatedBy = userUUID.String()
				}
			}
		}

		if inputModel.Description != nil {
			if *inputModel.Description == "" {
				return nil, errors.New("Description cannot be blank")
			} else {
				DescriptionInUpper := strings.ToUpper(*inputModel.Description)
				entity.Description = DescriptionInUpper
			}
		}

	} else {
		if inputModel.ID == nil {
			return nil, errors.New("ID cannot be blank")
		}
	}

	return &entity, nil
}

func FetchUserRolesEntityToModel(input []entity.UserRolesFetchEntity) []*model.UserRoles {
	var outEntity []*model.UserRoles
	for _, item := range input {
		userRoles := new(model.UserRoles)
		userRoles.ID = item.ID.String
		userRoles.UserRole = item.UserRole.String
		userRoles.Title = item.Title.String
		userRoles.Description = item.Description.String
		outEntity = append(outEntity, userRoles)
	}
	return outEntity
}
