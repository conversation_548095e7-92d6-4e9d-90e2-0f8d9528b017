package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.42

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/user_activity/graph/controller"
	"github.com/ihcp/user_activity/graph/generated"
	"github.com/ihcp/user_activity/graph/logengine"
	"github.com/ihcp/user_activity/graph/model"
)

// UserTrackingAction is the resolver for the userTrackingAction field.
func (r *mutationResolver) UserTrackingAction(ctx context.Context, input model.UserTrackingRequest) (*model.UserTrackingResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UserTrackingAction", err)
	}
	logengine.GetTelemetryClient().TrackEvent("UserTrackingAction :" + string(inputJson))
	response := controller.UserTrackingActionController(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UserTrackingAction", "user_activity/UserTrackingAction", time.Since(start), "200")
	return response, nil
}

// FetchUserTrackAPI is the resolver for the fetchUserTrackApi field.
func (r *queryResolver) FetchUserTrackAPI(ctx context.Context, input model.FetchUserTrackingRequest) (*model.FetchUserTrackingResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "FetchUserTrackAPI", err)
	}
	logengine.GetTelemetryClient().TrackEvent("UserTrackingAction :" + string(inputJson))
	response := controller.FetchUserTrackAPIController(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("FetchUserTrackAPI", "user_activity/FetchUserTrackAPI", time.Since(start), "200")
	return response, nil
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
