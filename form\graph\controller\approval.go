package controller

import (
	"context"
	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
	"slices"
)

func UpsertApproval(ctx context.Context, input *model.UpsertRequest) *model.ApprovalResponse {
	var response model.ApprovalResponse
	userId := auth.GetUserID(ctx)
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	if input.ID != nil {
		uid, err := uuid.FromString(*(input).ID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		if input.IsDeleted != nil && *(input).IsDeleted {
			err := postgres.DeleteApproval(uid)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
		} else if input.IsActive != nil && *(input).IsActive {
			err := postgres.UpdateApproval(uid, *(input).IsActive)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
		}
	} else {
		response.ApprovalRoles, err = FetchApproval()
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}

	return &response
}

func FetchApproval() ([]*model.ApprovalRoles, error) {
	resEntity, err := postgres.GetApprovalRules()
	if err != nil {
		return nil, err
	}
	return mapper.ApprovalEntityToModel(resEntity), nil
}

func FetchApprovalRoles(ctx *context.Context, input *model.ApprovalRolesInput) *model.ApprovalRolesResponse {
	response := &model.ApprovalRolesResponse{}
	userId := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	if !postgres.CheckUserApprovalRoleByMasterData(*approvalRole) {
		response.Error = true
		response.Message = "You are not allowed to access!!"
		return response
	}
	countryID, err := postgres.GetUserCountryByID(*userId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	country := countryID
	activityEntites, err := postgres.FetchApprovalRole(input, country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.ApprovalRoles = mapper.MapApprovalRolesEntitiesToModels(activityEntites)
	return response
}

var LedByOptions []string = []string{
	"Sales (FF)",
	"Marketing",
	"Medical",
}

func UpsertForApprovalRoleManagement(ctx *context.Context, input *model.UpsertApprovalRoleManagementRequest) *model.UpsertApprovalRoleManagementResponse {
	userId := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userId == nil && approvalRole == nil {
		return &model.UpsertApprovalRoleManagementResponse{
			Error:   true,
			Message: "you are not authorized to login please contact your country ezflow admin.",
		}
	}
	if !postgres.CheckUserApprovalRoleByMasterData(*approvalRole) {
		return &model.UpsertApprovalRoleManagementResponse{
			Error:   true,
			Message: "you are not allowed to access",
		}
	}

	for _, v := range input.LedBy {
		if !slices.Contains(LedByOptions, v) {
			return &model.UpsertApprovalRoleManagementResponse{
				Error:   true,
				Message: "invalid 'led by' value",
			}
		}
	}

	entity, err := mapper.MapApprovalRoleManagementModelToEntity(input, *userId)
	if err != nil {
		return &model.UpsertApprovalRoleManagementResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	response := &model.UpsertApprovalRoleManagementResponse{}
	if entity.ID != nil && entity.IsDeleted {
		return postgres.DeleteApprovalRoleManagementData(entity)
	} else if entity.ID != nil {
		return postgres.UpdateApprovalRoleManagementData(input, entity)
	} else if entity.ID == nil {
		return postgres.InsertApprovalRoleManagementData(entity)
	}

	return response
}

func GetUserRolesFromCode(ctx context.Context, input *model.UserRoles) *model.GetUserRolesByapprover {
	var response = &model.GetUserRolesByapprover{Error: false}
	userId := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	if !postgres.CheckUserApprovalRoleByMasterData(*approvalRole) {
		response.Error = true
		response.Message = "You are not allowed to access"
		return response
	}

	activityEntites, err := postgres.GetUserRolesFormCode(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.Data = mapper.MapUserRolesForApprovers(activityEntites)
	return response

}

func DelegateAnotherApprover(ctx *context.Context, input model.DelegateApproverInput) model.DelegateApproverResponse {
	var response = model.DelegateApproverResponse{Error: false}
	userId := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var response1 model.DelegateApproverResponse
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	entity, err := mapper.MapDelegateApproverModelToEntity(input, *userId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	} else {
		response1 = postgres.InsertDeligatedApprover(entity, response, *userId, input.IsApollo)
		if postgres.CheckPreviousApproverActionForAllApprovers(entity.FormAnswerID, entity.ApproverID) {
			emailData, err := postgres.GetFormAnswerDetails(entity.FormAnswerID)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return response
			}
			data := mapper.SetEmailData(emailData)
			code := codeController.GetValueKeyCodes()["approvedstatus"]
			status := code["pending"].ID
			_ = postgres.InsertStatusNotification(entity.FormAnswerID, *userId, status, entity.ApproverID)
			if entity.ApproverID != "" {
				emailCode := codeController.GetValueKeyCodes()["typeofemail"]
				approverStatus := emailCode["requireapproval"].ID
				email, _ := postgres.GetEmailIDFromUserID(entity.ApproverID)
				approverEmailContent := util.SendEmailScenarioOne(email, data)
				newApproverEmailContent := strip.StripTags(approverEmailContent)
				_ = postgres.InsertEmailLog(entity.FormAnswerID, approverStatus, newApproverEmailContent, *userId, entity.ApproverID)
			}
		}
	}

	return response1
}

func FetchDeligateApprovers(ctx *context.Context) model.SameApproverList {
	var response model.SameApproverList
	userId := auth.GetUserID(*ctx)
	country := auth.GetCountry(*ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	userRoleID, err := postgres.GetUserRoleID(*userId)
	if err != nil {
		response.Error = true
		response.Message = "User Role does not exist"
		return response
	}

	values, err := postgres.GetDeligateApprovers(userRoleID, *userId, *country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	fetchValues := mapper.FetchDeligateApproversEntityToModel(values)
	response.Data = fetchValues
	return response
}
