scalar Upload


type excelValidationMessage {
    row: Int!
    message: String!
}

type validationResult {
    error: Boolean!
    validationTimeTaken: String!
    excelValidationMessages: [excelValidationMessage]
}

input excelUploadRequest {
    fileName: String!
    url: String!
    isImport: Boolean
}

type excelUploadResponse {
    error: Boolean!
    message: String!
}

input azureExcelUploadInput {
    file: Upload!
    fileName: String!
}

type azureExcelUploadResponse {
    error: Boolean!
    message: String!
    url: String!
}

input excelTemplateRequest {
    templateType: String!
}

input excelStatusRequest{
  type: String
}


type excel{
  id: String!
  filename: String!
  url: String!
  excelType: String!
  status: String! #received/validating/processed/error
  dateCreated: String!
  validationResult: validationResult!
}

type excelStatusResponse{
    message: String!
    excels:[excel!]!
}

type Query {
   getExcel: String!
    excelTemplate(input: excelTemplateRequest!): azureExcelUploadResponse!
    excelStatus(input: excelStatusRequest!): excelStatusResponse!
}


type Mutation {
    excelUpload(input: excelUploadRequest!): excelUploadResponse
    azureExcelUpload(input: azureExcelUploadInput!): azureExcelUploadResponse!
}
