
input UserCredentialsInput{
    username: String!
    password: String!
}

type UserAuthenticationResponse {
    error: Boolean!
    message: String!
    errorCode: Int!
    data: UserDetails
}

type UserDetails {
    ActiveDirectoryName: String!
    JwtToken: String!
    countryCurrency:[CountryCurrency]!
}
type CountryCurrency {
    id: String!
    ApprovalRole: String
    CountryDescription: String!
    CountryValue: String!
    CurrencyDescription: String!
    CurrencyValue: String!
    ConversionRate: Float
    isChangeRequestAllow: Boolean!
    isMonitoring:Boolean
    AccessControl: Permission
}


type Permission{
isApprover: Boolean
isRequestor: Boolean
readOnlySubmissions: Boolean
masterData: Boolean
isAdmin: Boolean
superAdmin: Boolean
}


input proxyUserCredentialsInput{
    username: String!
}


type proxyUserAuthenticationResponse {
    error: Boolean!
    message: String!
    errorCode: Int!
    data: proxyUserDetails
}

type proxyUserDetails {
    ActiveDirectoryName: String!
    countryCurrency:[proxyCountryCurrency]!
}
type proxyCountryCurrency {
    id: String!
    ApprovalRole: String
    CountryDescription: String!
    JwtToken: String!
    CountryValue: String!
    CurrencyDescription: String!
    CurrencyValue: String!
    ConversionRate: Float
    isChangeRequestAllow: Boolean!
    isMonitoring:Boolean
    AccessControl: proxyPermission
}


type proxyPermission{
isApprover: Boolean
isRequestor: Boolean
readOnlySubmissions: Boolean
masterData: Boolean
isAdmin: Boolean
superAdmin: Boolean
}


input regenerateJwtTokenWithCountryRequest{
   country: String!
}

type regenerateJwtTokenWithCountryResponse{
  error: Boolean!
    message: String!
    data: regenerateToken
}

type regenerateToken{
    ActiveDirectoryName: String!
    JwtToken: String!
    Country: String!
}

extend type Query{
    authenticateUser(input: UserCredentialsInput!): UserAuthenticationResponse!
    regenerateJwtTokenWithCountry(input: regenerateJwtTokenWithCountryRequest!): UserAuthenticationResponse!
    proxyLoginUser(input: proxyUserCredentialsInput!):proxyUserAuthenticationResponse!
}