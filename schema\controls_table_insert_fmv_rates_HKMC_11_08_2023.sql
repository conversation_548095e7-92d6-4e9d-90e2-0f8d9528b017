INSERT INTO controls
(id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('3fbe5c40-c3c7-4a83-82cd-03bd8af4e3ad'::uuid, 'Items', 150, 4, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-10 21:10:16.156', NULL, NULL, 'ExpenseLimit', 'Gift', 'e2b2bfb8-0790-4891-87d2-6b96493cc7af'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO controls
(id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('c74448c8-d27f-4d99-955c-4ce048052fbb'::uuid, 'Breakfast', 400, 4, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-10 15:39:02.029', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 107, true, false, NULL, 37, false);
INSERT INTO controls
(id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('779ce7b8-5e6c-4d31-8a53-00fcfdf1722e'::uuid, 'Lunch', 400, 4, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-10 15:39:14.787', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 108, true, false, NULL, 37, false);
INSERT INTO controls
(id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('a8b0421e-5dbb-4310-b562-d1af6173daeb'::uuid, 'Dinner', 800, 4, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-10 15:39:24.788', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 109, true, false, NULL, 37, false);
INSERT INTO controls
(id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('11f6da2c-598a-48ab-805b-40c691310284'::uuid, 'Day-to-day promotional activities', 150, 4, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-14 06:58:32.696', NULL, NULL, 'ExpenseLimit', 'Meal', 'bfa9d1ed-b6c5-4b59-ad33-c16503e1180a'::uuid, 34, true, false, NULL, 37, false);
