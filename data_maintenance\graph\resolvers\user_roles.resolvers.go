package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertUserRoles is the resolver for the upsertUserRoles field.
func (r *mutationResolver) UpsertUserRoles(ctx context.Context, input model.UserRolesInput) (*model.UpsertResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertUserRoles", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertUserRoles :" + string(inputJson))
	response := controller.UpsertUserRolesData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertUserRoles", "data_maintenance/UpsertUserRoles", time.Since(start), "200")
	return response, nil
}

// GetUserRoles is the resolver for the getUserRoles field.
func (r *queryResolver) GetUserRoles(ctx context.Context, input *model.UserRoleRequest) (*model.UserRoleResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetUserRoles", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetUserRoles :" + string(inputJson))
	response := controller.FetchUserRoles(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetUserRoles", "data_maintenance/GetUserRoless", time.Since(start), "200")
	return response, nil
}
