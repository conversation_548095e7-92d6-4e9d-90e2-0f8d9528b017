package controller

import (
	"context"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func ProcessEmployeeAttendanceExcel(fileURL string, FileName string, FormAnswerID string, ctx context.Context, excelBytes []byte, multiCheck bool) *model.ValidationResult {
	userID := auth.GetUserID(ctx)
	var userUUID uuid.UUID
	var err error

	userUUID, err = uuid.FromString(*userID)
	if err != nil {
		panic(err)
	}

	sheetName := "employee"
	sheet, err := util.ParseExcelEZFlow(excelBytes, sheetName)
	if err != nil {
		panic(err)
	}

	//var validationResult *model.ValidationResult

	uploadId := postgres.InsertUploadLogsAttendance(fileURL, FileName, sheetName, userUUID)
	upload, err := uuid.FromString(uploadId)
	if err != nil {
		panic(err)
	}

	uploadUUID := &upload

	entities, entitiesEmail, validationResult := mapper.MapEmployeeAttendanceExcelToEntities(sheet, userUUID, FormAnswerID)
	if validationResult.Error {
		postgres.InsertUploadValidationForFormAttendanceErrors(validationResult, uploadUUID, userUUID)
		return validationResult
	}

	postgres.UpsertExcelEmployeeAttendance(entities, entitiesEmail, FormAnswerID, multiCheck, false)
	postgres.UpdateUploadStatus(uploadUUID, "Write Success", userUUID)
	return validationResult
}

func ProcessEmployeeAttendanceExcelForApollo(attendanceUrl string, FileName string, formAnswerID string, ctx context.Context, excelBytes []byte, multiCheck bool) *model.ValidationResult {
	userID := auth.GetUserID(ctx)
	userUUID, err := uuid.FromString(*userID)
	if err != nil {
		panic(err)
	}

	sheetName := "employee"
	sheet, err := util.ParseExcel(excelBytes, sheetName, 6)
	if err != nil {
		panic(err)
	}

	var validationResult *model.ValidationResult

	uploadId := postgres.InsertUploadLogsAttendance(attendanceUrl, FileName, sheetName, userUUID)
	upload, err := uuid.FromString(uploadId)
	if err != nil {
		panic(err)
	}

	uploadUUID := &upload
	entities, entitiesEmail, validationResult := mapper.MapEmployeeAttendanceExcelToEntitiesForApollo(sheet, userUUID, formAnswerID)
	if !validationResult.Error {
		postgres.UpsertExcelEmployeeAttendance(entities, entitiesEmail, formAnswerID, multiCheck, true)
		postgres.UpdateUploadStatus(uploadUUID, "Write Success", userUUID)
		return validationResult
	}
	postgres.InsertUploadValidationForFormAttendanceErrors(validationResult, uploadUUID, userUUID)
	return validationResult
}
