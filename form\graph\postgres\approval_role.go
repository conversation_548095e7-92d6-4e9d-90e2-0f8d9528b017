package postgres

import (
	"context"
	"log"

	"github.com/ihcp/form/graph/logengine"
)

func GetRoleIDByValueInUserRoles(roleValue string) string {
	functionName := "GetRoleIDByValueInUserRoles()"
	log.Println(functionName)
	var codeValue string
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select id from user_roles where value = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), queryString, roleValue).Scan(&value)
	logengine.GetTelemetryClient().TrackEvent("GetRoleIDByValueInUserRoles query called")
	// var codeValue string
	if err == nil {
		logengine.GetTelemetryClient().TrackException(err)
		codeValue = value
	}
	return codeValue
}

func GetRoleValueByIDInUserRoles(roleID string) string {
	functionName := "GetRoleIDByValueInUserRoles()"
	log.Println(functionName)
	var codeValue string
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select value from user_roles where id = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), queryString, roleID).Scan(&value)
	logengine.GetTelemetryClient().TrackEvent("GetRoleIDByValueInUserRoles query called")
	// var codeValue string
	if err == nil {
		logengine.GetTelemetryClient().TrackException(err)
		codeValue = value
	}
	return codeValue
}
