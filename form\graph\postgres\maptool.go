package postgres

import (
	"context"
	"database/sql"
	"errors"
	"github.com/ihcp/form/graph/maptool_service"
	"github.com/jackc/pgx/v4"
	"time"
	"unicode"
	"unicode/utf8"
)

type MapTool struct {
}

type MapToolUser struct {
	ID        string
	CountryID int
	RoleCode  int
	RoleID    string
}

func (*MapTool) GetMaptoolUser(ctx context.Context, approver string) *MapToolUser {
	q := `
	SELECT id, country, approval_role, user_role_id
	FROM "user"
	WHERE active_directory = $1 and is_active = 'T'
	LIMIT 1`

	var id, roleId string
	var approvalRole, country int
	row := GetPool().QueryRow(ctx, q, approver)
	if err := row.Scan(&id, &country, &approvalRole, &roleId); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}

		panic(err)
	}

	return &MapToolUser{
		ID:        id,
		CountryID: country,
		RoleCode:  approvalRole,
		RoleID:    roleId,
	}
}

func (*MapTool) CreateApprovalLog(ctx context.Context, tx pgx.Tx, u *MapToolUser, formAnswerID string, status int, comment string) {
	querystring := `
		INSERT INTO approval_log 
			(form_answer, actioned_by, approval_role,user_role_id, status, comments)
		VALUES($1,$2,$3,$4,$5,$6)`
	_, err := tx.Exec(ctx, querystring, formAnswerID, u.ID, u.RoleCode, u.RoleID, status, comment)
	if err != nil {
		panic(err)
	}
}

func (*MapTool) UpdateEventApprovers(ctx context.Context, tx pgx.Tx, u *MapToolUser, formAnswerID string, status int) {
	querystring := `
		UPDATE approvers 
		SET status = $2 ,modified_by = $3, last_modified = now()
		WHERE form_answer_id = $1 and is_active = true`
	_, err := tx.Exec(ctx, querystring, formAnswerID, status, u.ID)
	if err != nil {
		panic(err)
	}
}

func (*MapTool) UpdateEventData(ctx context.Context, tx pgx.Tx, formAnswerID string, status int, statusStr string) {
	var isExceptionalApprove bool
	if statusStr == `exceptional approve` {
		isExceptionalApprove = true
	}
	querystring := `
	UPDATE 
		form_answers 
	SET status = $2, local_approval_status = $3, is_exceptional_approval = $4 where id = $1`

	_, err := tx.Exec(ctx, querystring,
		formAnswerID, status, status, isExceptionalApprove)
	if err != nil {
		panic(err)
	}

	queryString := `
	UPDATE reporting_db
	SET status_title = $2, status_value = $3, updated_date = now(), exceptionalapprover = $4
						WHERE form_answer_id = $1 `
	_, err = tx.Exec(ctx, queryString,
		formAnswerID, firstToUpper(statusStr), statusStr, isExceptionalApprove)
	if err != nil {
		panic(err)
	}
}

func firstToUpper(s string) string {
	r, size := utf8.DecodeRuneInString(s)
	if r == utf8.RuneError && size <= 1 {
		return s
	}
	lc := unicode.ToUpper(r)
	if r == lc {
		return s
	}
	return string(lc) + s[size:]
}

func (*MapTool) UpsertMaptoolEventLog(ctx context.Context, tx pgx.Tx, formAnswerID string, traceId, payload, response string, isSynced bool) {
	q := `
INSERT INTO maptool_event_logs as l (form_answers_id, trace_id, payload, response, synced_at, created_at, updated_at)
VALUES ($1,$2,$3,$4,$5,now(),now())
ON CONFLICT (form_answers_id) DO UPDATE
SET
	trace_id = COALESCE(NULLIF(EXCLUDED.trace_id,''), l.trace_id) ,
	payload = EXCLUDED.payload,
	response = EXCLUDED.response,
	synced_at = EXCLUDED.synced_at,
	updated_at = now()
	`

	var syncedAt sql.NullTime
	if isSynced {
		syncedAt = sql.NullTime{
			Time:  time.Now(),
			Valid: true,
		}
	}

	var err error
	if tx == nil {
		_, err = GetPool().Exec(ctx, q,
			formAnswerID, traceId, payload, response, syncedAt)
	} else {
		_, err = tx.Exec(ctx, q, formAnswerID,
			formAnswerID, traceId, payload, response, syncedAt)
	}

	if err != nil {
		panic(err)
	}

}

type MaptoolSyncLog struct {
	Id            string
	FormAnswersId string
	TraceId       string
	SyncedAt      *time.Time
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

func (*MapTool) GetMaptoolEventLogByFormAnswerID(ctx context.Context, formAnswerID string) *MaptoolSyncLog {
	q := `
	SELECT id, form_answers_id, trace_id, synced_at, created_at, updated_at
	FROM maptool_event_logs
	WHERE form_answers_id = $1`

	var id, formAnswersId, traceId string
	var createdAt, updatedAt time.Time
	var syncedAt sql.NullTime
	row := GetPool().QueryRow(ctx, q, formAnswerID)
	if err := row.Scan(&id, &formAnswersId, &traceId, &syncedAt, &createdAt, &updatedAt); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}

	var syncTime *time.Time
	if syncedAt.Valid {
		syncTime = &syncedAt.Time
	}

	return &MaptoolSyncLog{
		Id:            id,
		FormAnswersId: formAnswersId,
		TraceId:       traceId,
		SyncedAt:      syncTime,
		CreatedAt:     time.Time{},
		UpdatedAt:     time.Time{},
	}
}

func (*MapTool) GetEventApprovers(ctx context.Context, formAnswerID string) []*maptool_service.Approver {
	q := `
SELECT 
	sequence_no::INT,
	approver.email,
	approver.full_name,
	urole.title,
	(SELECT value FROM code WHERE code.id = status) as approval_status,
	ap.last_modified as actioned_at
FROM approvers ap
LEFT JOIN (SELECT id, email, concat(first_name, ' ', last_name) as full_name, user_role_id FROM "user") as approver ON approver.id = ap.approver_id
LEFT JOIN user_roles urole ON urole.id = approver.user_role_id
WHERE form_answer_id = $1
	AND ap.is_active = true AND ap.is_deleted = false
ORDER BY ap.sequence_no`

	rows, err := GetPool().Query(ctx, q, formAnswerID)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	var rs []*maptool_service.Approver
	for rows.Next() {
		var approver maptool_service.Approver
		var actionedAt sql.NullTime
		if err := rows.Scan(
			&approver.SequenceNo,
			&approver.Email,
			&approver.Name,
			&approver.Role,
			&approver.ApprovalStatus,
			&actionedAt); err != nil {
			panic(err)
		}

		if actionedAt.Valid {
			approver.ActionedAt = actionedAt.Time.UTC().String()
		}

		rs = append(rs, &approver)
	}

	return rs
}
