package controller

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertExceptionalApprovalManagementData(ctx *context.Context, input model.ExceptionalApprovalManagementInput) *model.ExceptionalApprovalManagementResponse {
	var upsertResponse model.ExceptionalApprovalManagementResponse
	var entity *entity.UpsertExceptionalApprovalManagementEntity
	userID := auth.GetUserID(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
	entity, err = mapper.MapExceptionalApprovalManagementInputModelToEntity(input)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}

	if input.ID == nil && !entity.IsDelete {
		err := postgres.InsertExceptionalDetails(entity, userUUID)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Exception details successfully inserted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else if entity.IsDelete {
		err := postgres.DeleteInsertExceptionalDetails(entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Exception details successfully deleted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else {
		err := postgres.UpdateInsertExceptionalDetails(entity, userUUID)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Exception details successfully updated"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
}

func FetchExceptionalApprovalManagementData(ctx *context.Context, input model.FetchExceptionalApprovalManagementInput) *model.FetchExceptionalApprovalManagementResponse {
	var response model.FetchExceptionalApprovalManagementResponse
	var exceptionentity *entity.FetchExceptionDetails
	var outputentity []entity.OutputFetchEntity
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	log.Println(*userCountry)
	if userID == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	exceptionentity, err := mapper.FetchExceptionalApprovalManagementDataModelToEntity(input, userCountry)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputentity, err = postgres.FetchExceptionalApprovalManagementDataFromDB(exceptionentity)

	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	datamodel, err := mapper.FetchExceptionalApprovalManagementDataEntityToModel(outputentity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	return &datamodel
}
