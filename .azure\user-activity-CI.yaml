pool:
  vmImage: 'ubuntu-latest'
trigger:
 branches:
   include:
     - development
     - staging
 paths:
   exclude:
     - "*.yaml"

resources:
  repositories: 
  - repository: azure-pipeline-template
    type: git
    ref: refs/heads/webapp
    name: Digital and Data Program Management/azure-pipeline-template
    endpoint: build-service

variables:
  - name: app-name
    value: user-activity
  - group: ezflow-cicd
  - name: dockerfilepath
    value: '$(Build.SourcesDirectory)/user_activity/build-Dockerfile'
  - name: context
    value: '$(Build.Repository.LocalPath)'
  # - name: dockerRegistryServiceConnection
  #   value: 
  - name: imageRepository
    value: stg/ezflow/$(app-name)
  - name: ado-project
    value: 'Commercialization Technologies'
  - name: ado-repo
    value: 'eZFlow-Backend'
  - name: ado-branch
    value: staging
     

extends:
  template: stages/ci.yml@azure-pipeline-template
  parameters:
    sonarqubeProjectKey: ezflow-user-activity
    skipUnitTest: true
    skipSonarqubeAnalysis: true
    exclude: '**/data_maintenance/**,**/upload/**,**/form/**,**/schema/**'