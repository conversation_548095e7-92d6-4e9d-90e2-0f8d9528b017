package util

import (
	"bytes"
	"log"

	"github.com/360EntSecGroup-Skylar/excelize"
)

func ParseExcel(b []byte, sheetname string, totalColumns int) ([][]string, string) {
	log.Println("ParseExcel()")
	r := bytes.NewReader(b)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.Error())
	}
	rows := f.GetRows(sheetname)
	if len(rows) == 0 || len(rows[0]) < totalColumns || len(rows[0]) > totalColumns {
		log.Println("Format of excel columns is wrong!")
		return nil, "Format of excel columns is wrong!"
	} else {

		return rows, ""
	}
	return nil, ""
}

func GetSheetName(data []byte) string {
	r := bytes.NewReader(data)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.<PERSON><PERSON><PERSON>())
	}
	return f.GetSheetName(f.GetActiveSheetIndex())
}
