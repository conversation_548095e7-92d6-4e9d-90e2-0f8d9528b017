package veeva

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"

	"github.com/ihcp/form/graph/entity"
)

func GetAuthRest() (entity.VeevaAuthorizationRestResponse, error) {

	data := url.Values{}
	data.Set("grant_type", os.Getenv("GRANT_TYPE"))
	data.Set("client_id", os.Getenv("CLIENT_ID"))
	data.Set("client_secret", os.Getenv("CLIENT_SECRET"))
	data.Set("username", os.Getenv("USERNAME_VEEVA"))
	data.Set("password", os.Getenv("PASSWORD"))
	request, err := http.NewRequest("POST", os.Getenv("VEEVA_AUTH_URL"), strings.NewReader(data.Encode()))
	if err != nil {
		panic(err)
	}

	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	request.Header.Add("Content-Length", strconv.Itoa(len(data.Encode())))

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	log.Println("GetAuthRest()", data.Encode())
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	var rs entity.VeevaAuthorizationRestResponse
	if response.StatusCode != http.StatusOK {
		return rs, errors.New(fmt.Sprintf("Response Code: %v \n%s", response.StatusCode, string(responseBody)))
	}

	if err := json.Unmarshal(responseBody, &rs); err != nil {
		panic(err)
	}

	os.Setenv("AUTH_VEEVA_TOKEN", rs.Access_token)
	os.Setenv("AUTH_VEEVA_URL", rs.Instance_url)

	log.Println("+++ Authorized Veeva Successfully")
	return rs, nil
}

func GetVeevaBaseURL(instanceUrl string) string {
	var veevaInstanceURL string
	if strings.TrimSpace(instanceUrl) != "" {
		veevaInstanceURL = instanceUrl
	} else {
		veevaInstanceURL = os.Getenv("VEEVA_BASE_URL")
	}
	return veevaInstanceURL
}
func GetVeevaErrorResponse(responseBody []byte) error {
	var veevaError []entity.VeevaErrResponse
	err := json.Unmarshal(responseBody, &veevaError)
	for _, veevaErr := range veevaError {
		return errors.New(veevaErr.Message)
	}
	return err
}
