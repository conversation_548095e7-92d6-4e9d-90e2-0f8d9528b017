package mapper

import (
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
)

func MapLoginModelToEntity(userLoginModel model.UserCredentialsInput) *entity.UserLogin {
	userEntity := &entity.UserLogin{
		ActiveDirectoryName: userLoginModel.Username,
		Password:            userLoginModel.Password,
	}
	return userEntity
}
func ProxyMapLoginModelToEntity(userLoginModel model.ProxyUserCredentialsInput) *entity.ProxyUserLogin {
	userEntity := &entity.ProxyUserLogin{
		ActiveDirectoryName: userLoginModel.Username,
	}
	return userEntity
}
func MapRegenerateJWTModelToEntity(userLoginModel model.RegenerateJwtTokenWithCountryRequest) *entity.Usercountry {
	userEntity := &entity.Usercountry{
		Country: userLoginModel.Country,
	}
	return userEntity
}

func MapUserEntityToLoginModel(userEntity *entity.UserAuth) *model.UserDetails {
	var userModel model.UserDetails
	var countryCurrency []*model.CountryCurrency

	userModel.ActiveDirectoryName = userEntity.ActiveDirectory
	userModel.JwtToken = userEntity.JWTToken
	for i, userSelection := range userEntity.AccessControl {
		conversionRate := *userEntity.AccessControl[i].ConversionRate
		var accessControl model.CountryCurrency
		accessControl.AccessControl = MapUserAccessControlToLoginModel(&userSelection)
		accessControl.ID = userSelection.ID
		accessControl.CountryDescription = userSelection.CountryDescription
		accessControl.ApprovalRole = userSelection.ApprovalRole
		accessControl.CountryValue = userSelection.CountryValue
		accessControl.CurrencyDescription = userSelection.CurrencyDescription
		accessControl.CurrencyValue = strings.Split(userSelection.CurrencyValue, "|")[1]
		if conversionRate != 0 {
			accessControl.ConversionRate = &conversionRate
		} else {
			accessControl.ConversionRate = nil
		}
		if userSelection.IsChangeRequestAllow != nil {
			if *userSelection.IsChangeRequestAllow == 1 {
				accessControl.IsChangeRequestAllow = true
			} else {
				accessControl.IsChangeRequestAllow = false
			}
		} else {
			accessControl.IsChangeRequestAllow = false
		}
		if userSelection.SuperAdmin != nil {
			accessControl.IsMonitoring = userSelection.SuperAdmin
		}
		countryCurrency = append(countryCurrency, &accessControl)
	}
	userModel.CountryCurrency = countryCurrency
	return &userModel
}

func ProxyMapUserEntityToLoginModelForAdmin(userEntity *entity.ProxyUserAuth) *model.ProxyUserDetails {
	var userModel model.ProxyUserDetails
	var countryCurrency []*model.ProxyCountryCurrency

	userModel.ActiveDirectoryName = userEntity.ActiveDirectory
	// userModel.JwtToken = userEntity.JWTToken
	for _, userSelection := range userEntity.AccessControl {
		var accessControl model.ProxyCountryCurrency
		if userSelection.ApprovalRole != nil && (*userSelection.ApprovalRole == "superadmin" || *userSelection.ApprovalRole == "admin") {
			continue
		}
		accessControl.AccessControl = ProxyMapUserAccessControlToLoginModel(&userSelection)
		accessControl.ID = userSelection.ID
		accessControl.JwtToken = userSelection.JWTToken
		accessControl.CountryDescription = userSelection.CountryDescription
		accessControl.ApprovalRole = userSelection.ApprovalRole
		accessControl.CountryValue = userSelection.CountryValue
		accessControl.CurrencyDescription = userSelection.CurrencyDescription
		accessControl.CurrencyValue = strings.Split(userSelection.CurrencyValue, "|")[1]
		if *userSelection.ConversionRate != 0 {
			accessControl.ConversionRate = userSelection.ConversionRate
		} else {
			accessControl.ConversionRate = nil
		}
		if userSelection.IsChangeRequestAllow != nil {
			if *userSelection.IsChangeRequestAllow == 1 {
				accessControl.IsChangeRequestAllow = true
			} else {
				accessControl.IsChangeRequestAllow = false
			}
		} else {
			accessControl.IsChangeRequestAllow = false
		}
		if userSelection.SuperAdmin != nil {
			accessControl.IsMonitoring = userSelection.SuperAdmin
		}
		countryCurrency = append(countryCurrency, &accessControl)
	}
	userModel.CountryCurrency = countryCurrency
	return &userModel
}

func ProxyMapUserEntityToLoginModelForSuperadmin(userEntity *entity.ProxyUserAuth) *model.ProxyUserDetails {
	var userModel model.ProxyUserDetails
	var countryCurrency []*model.ProxyCountryCurrency

	userModel.ActiveDirectoryName = userEntity.ActiveDirectory
	// userModel.JwtToken = userEntity.JWTToken
	for _, userSelection := range userEntity.AccessControl {
		var accessControl model.ProxyCountryCurrency
		if userSelection.ApprovalRole != nil && *userSelection.ApprovalRole == "superadmin" {
			continue
		}
		accessControl.AccessControl = ProxyMapUserAccessControlToLoginModel(&userSelection)
		accessControl.ID = userSelection.ID
		accessControl.JwtToken = userSelection.JWTToken
		accessControl.CountryDescription = userSelection.CountryDescription
		accessControl.ApprovalRole = userSelection.ApprovalRole
		accessControl.CountryValue = userSelection.CountryValue
		accessControl.CurrencyDescription = userSelection.CurrencyDescription
		accessControl.CurrencyValue = strings.Split(userSelection.CurrencyValue, "|")[1]
		if *userSelection.ConversionRate != 0 {
			accessControl.ConversionRate = userSelection.ConversionRate
		} else {
			accessControl.ConversionRate = nil
		}
		if userSelection.IsChangeRequestAllow != nil {
			if *userSelection.IsChangeRequestAllow == 1 {
				accessControl.IsChangeRequestAllow = true
			} else {
				accessControl.IsChangeRequestAllow = false
			}
		} else {
			accessControl.IsChangeRequestAllow = false
		}
		if userSelection.SuperAdmin != nil {
			accessControl.IsMonitoring = userSelection.SuperAdmin
		}
		countryCurrency = append(countryCurrency, &accessControl)
	}
	userModel.CountryCurrency = countryCurrency
	return &userModel
}

func MapUserAccessControlToLoginModel(Permission *entity.Permission) *model.Permission {
	userModel := &model.Permission{
		IsApprover:          Permission.IsApprover,
		IsRequestor:         Permission.IsRequestor,
		ReadOnlySubmissions: Permission.ReadOnlySubmissions,
		MasterData:          Permission.MasterData,
		IsAdmin:             Permission.IsAdmin,
		SuperAdmin:          Permission.SuperAdmin,
	}
	return userModel
}

func ProxyMapUserAccessControlToLoginModel(Permission *entity.Permission) *model.ProxyPermission {
	userModel := &model.ProxyPermission{
		IsApprover:          Permission.IsApprover,
		IsRequestor:         Permission.IsRequestor,
		ReadOnlySubmissions: Permission.ReadOnlySubmissions,
		MasterData:          Permission.MasterData,
		IsAdmin:             Permission.IsAdmin,
		SuperAdmin:          Permission.SuperAdmin,
	}
	return userModel
}
