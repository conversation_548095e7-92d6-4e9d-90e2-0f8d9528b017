scalar Upload


type excelValidationMessage {
    row: Int!
    message: String!
}

type validationResult {
    error: Boolean!
    validationTimeTaken: String!
    excelValidationMessages: [excelValidationMessage]
}

input excelUploadRequest {
    fileName: String!
    url: String!
}

type excelUploadResponse {
    error: Boolean!
    message: String!
}



type azureExcelUploadResponse {
    error: Boolean!
    message: String!
    url: String!
}

input excelTemplateRequest {
    templateType: String!
}

input excelStatusRequest{
  type: String
}



