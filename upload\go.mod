module github.com/ihcp/upload

go 1.18

replace github.com/ihcp/login => ../login

replace github.com/ihcp/code => ../code

require (
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/99designs/gqlgen v0.17.42
	github.com/Azure/azure-storage-blob-go v0.10.0
	github.com/go-chi/chi v3.3.2+incompatible
	github.com/gorilla/websocket v1.5.0
	github.com/ihcp/code v0.0.0-00010101000000-000000000000
	github.com/ihcp/login v0.0.0-00010101000000-000000000000
	github.com/jackc/pgtype v1.4.2
	github.com/jackc/pgx/v4 v4.8.1
	github.com/jmoiron/sqlx v1.3.5
	github.com/joho/godotenv v1.3.0
	github.com/microsoft/ApplicationInsights-Go v0.4.3
	github.com/rs/cors v1.6.0
	github.com/satori/go.uuid v1.2.0
	github.com/vektah/gqlparser/v2 v2.5.10
)

require (
	code.cloudfoundry.org/clock v0.0.0-20180518195852-02e53af36e6c // indirect
	github.com/Azure/azure-pipeline-go v0.2.2 // indirect
	github.com/agnivade/levenshtein v1.1.1 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/google/uuid v1.3.0 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.3 // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.6.4 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.0.2 // indirect
	github.com/jackc/pgservicefile v0.0.0-20200714003250-2b9c44734f2b // indirect
	github.com/jackc/puddle v1.1.1 // indirect
	github.com/mattn/go-ieproxy v0.0.0-20190702010315-6dee0af9227d // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/sosodev/duration v1.1.0 // indirect
	golang.org/x/crypto v0.14.0 // indirect
	golang.org/x/net v0.17.0 // indirect
	golang.org/x/sys v0.13.0 // indirect
	golang.org/x/text v0.13.0 // indirect
	golang.org/x/xerrors v0.0.0-20191204190536-9bdfabe68543 // indirect
)
