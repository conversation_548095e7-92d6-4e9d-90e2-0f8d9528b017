delete from approval_role_management where country = 185




INSERT INTO approval_role_management 
(approval_role, group_type, created_by, is_active, is_deleted, sequence_no, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, department, alternate_department_id) 
values
 (0,55,'00000000-0000-0000-0000-000000000000',TRUE,FALSE,1,NULL,0,50000,101,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000001',TRUE,FALSE,1,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000002',TRUE,FALSE,3,NULL,0,50000,93,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000003',TRUE,FALSE,1,NULL,0,50000,96,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (48,55,'00000000-0000-0000-0000-000000000004',TRUE,FALSE,1,'[0,50000]',0,50000,100,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (0,55,'00000000-0000-0000-0000-000000000005',TRUE,FALSE,4,NULL,50001,'100000',100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000006',FALSE,TRUE,3,NULL,0,10000,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (47,55,'00000000-0000-0000-0000-000000000007',TRUE,FALSE,1,'[0,10000]',0,10000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000008',TRUE,FALSE,6,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000009',TRUE,FALSE,5,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000010',TRUE,FALSE,4,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000011',TRUE,FALSE,3,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000012',FALSE,TRUE,7,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000013',TRUE,FALSE,6,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (54,56,'00000000-0000-0000-0000-000000000014',FALSE,TRUE,7,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000015',TRUE,FALSE,5,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000016',TRUE,FALSE,4,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000017',TRUE,FALSE,3,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000018',FALSE,TRUE,6,'[0,10000]',0,10000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000019',FALSE,TRUE,8,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (51,56,'00000000-0000-0000-0000-000000000020',TRUE,FALSE,4,'[0,10000]',0,10000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000021',TRUE,FALSE,5,'[0,10000]',0,10000,89,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000022',TRUE,FALSE,1,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000023',FALSE,TRUE,3,'[0,10000]',0,10000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000024',TRUE,FALSE,2,NULL,0,50000,101,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000025',TRUE,FALSE,3,NULL,0,50000,101,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,55,'00000000-0000-0000-0000-000000000026',TRUE,FALSE,4,'[0,10000]',0,10000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (53,55,'00000000-0000-0000-0000-000000000027',TRUE,FALSE,5,'[0,10000]',0,10000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (413,56,'00000000-0000-0000-0000-000000000028',FALSE,TRUE,6,'[0,10000]',0,10000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000029',TRUE,FALSE,3,'[0,10000]',0,10000,420,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,56,'00000000-0000-0000-0000-000000000030',TRUE,FALSE,1,NULL,0,'100000',101,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000031',TRUE,FALSE,2,NULL,0,'100000',101,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Compliance' and is_active = true),NULL),
    (0,56,'00000000-0000-0000-0000-000000000032',TRUE,FALSE,3,NULL,0,'100000',101,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000033',TRUE,FALSE,4,NULL,0,'100000',101,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000034',TRUE,FALSE,1,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000035',TRUE,FALSE,6,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000036',TRUE,FALSE,5,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000037',TRUE,FALSE,2,'[0,1499)',0,1499,94,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000038',TRUE,FALSE,5,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000039',FALSE,TRUE,4,'[0,50000]',0,50000,100,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000040',FALSE,TRUE,5,NULL,0,50000,93,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000041',FALSE,TRUE,4,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000042',FALSE,TRUE,5,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000043',FALSE,TRUE,4,NULL,0,10000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (51,55,'00000000-0000-0000-0000-000000000044',FALSE,TRUE,4,'[0,10000]',0,10000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000045',TRUE,FALSE,4,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000046',TRUE,FALSE,3,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000047',TRUE,FALSE,3,'[0,50000]',0,50000,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000048',TRUE,FALSE,4,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000049',TRUE,FALSE,3,NULL,0,50000,96,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000050',TRUE,FALSE,1,NULL,0,50000,92,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000051',TRUE,FALSE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000052',TRUE,FALSE,3,NULL,10001,NULL,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000053',TRUE,FALSE,2,NULL,10001,NULL,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000054',TRUE,FALSE,1,NULL,10001,NULL,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000055',FALSE,TRUE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000056',FALSE,TRUE,3,NULL,10001,50000,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000057',FALSE,TRUE,2,NULL,10001,50000,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000058',FALSE,TRUE,1,NULL,10001,50000,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000059',TRUE,FALSE,3,NULL,0,10000,418,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000060',TRUE,FALSE,2,NULL,0,10000,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000061',TRUE,FALSE,1,NULL,0,10000,418,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000062',TRUE,FALSE,3,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000063',TRUE,FALSE,3,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000064',TRUE,FALSE,2,NULL,0,50000,96,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000065',FALSE,TRUE,5,NULL,0,50000,92,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000066',TRUE,FALSE,2,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (50,55,'00000000-0000-0000-0000-000000000067',TRUE,FALSE,4,NULL,0,50000,93,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000068',TRUE,FALSE,1,'[0,10000]',0,10000,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000069',TRUE,FALSE,1,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000070',TRUE,FALSE,2,'[0,10000]',0,10000,98,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000071',TRUE,FALSE,1,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (413,56,'00000000-0000-0000-0000-000000000072',TRUE,FALSE,4,'[0,1499)',0,1499,94,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (49,55,'00000000-0000-0000-0000-000000000073',TRUE,FALSE,2,'[0,50000]',0,50000,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000074',FALSE,TRUE,8,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (47,55,'00000000-0000-0000-0000-000000000075',TRUE,FALSE,1,NULL,0,50000,93,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000076',TRUE,FALSE,3,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (47,55,'00000000-0000-0000-0000-000000000077',TRUE,FALSE,1,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),NULL),
    (0,56,'00000000-0000-0000-0000-000000000078',TRUE,FALSE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000079',TRUE,FALSE,3,NULL,10001,NULL,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000080',TRUE,FALSE,2,NULL,10001,NULL,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000081',TRUE,FALSE,1,NULL,10001,NULL,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000082',FALSE,TRUE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000083',FALSE,TRUE,3,NULL,10001,50000,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000084',FALSE,TRUE,2,NULL,10001,50000,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000085',FALSE,TRUE,1,NULL,10001,50000,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (0,56,'00000000-0000-0000-0000-000000000086',TRUE,FALSE,3,NULL,0,10000,419,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (0,55,'00000000-0000-0000-0000-000000000087',TRUE,FALSE,2,NULL,0,10000,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000088',TRUE,FALSE,1,NULL,0,10000,419,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,NULL),
    (48,55,'00000000-0000-0000-0000-000000000089',TRUE,FALSE,2,'[50001,)',50001,NULL,89,FALSE,53,56,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Regional Medical' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000090',TRUE,FALSE,2,'[0,10000]',0,10000,420,FALSE,50,55,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000091',TRUE,FALSE,2,'[10001,50000]',10001,50000,98,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000092',TRUE,FALSE,2,'[50001,)',50001,NULL,98,FALSE,53,56,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Regional Medical' and is_active = true) ),
    (53,56,'00000000-0000-0000-0000-000000000093',FALSE,TRUE,5,NULL,0,50000,96,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000094',FALSE,TRUE,2,'[0,1499]',0,1499,86,TRUE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (51,55,'00000000-0000-0000-0000-000000000095',TRUE,FALSE,1,'[0,1499]',0,1499,86,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Manager' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000096',FALSE,TRUE,4,'[0,1499]',0,1499,86,TRUE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (413,56,'00000000-0000-0000-0000-000000000097',FALSE,TRUE,5,'[0,1499]',0,1499,86,TRUE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000098',TRUE,FALSE,3,'[0,1499)',0,1499,94,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (54,56,'00000000-0000-0000-0000-000000000099',FALSE,TRUE,6,NULL,0,50000,96,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (48,55,'00000000-0000-0000-0000-000000000100',TRUE,FALSE,2,'[50001,)',50001,NULL,420,FALSE,53,56,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),(select id from departments where department = 'Regional Medical' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000101',TRUE,FALSE,2,NULL,0,50000,92,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000102',TRUE,FALSE,2,NULL,0,50000,93,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000103',TRUE,FALSE,1,'[50001,)',50001,NULL,100,FALSE,53,56,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Regional Medical' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000104',FALSE,TRUE,2,'[0,10000]',0,10000,98,FALSE,50,55,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (53,56,'00000000-0000-0000-0000-000000000105',FALSE,TRUE,6,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000106',FALSE,TRUE,6,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000107',FALSE,TRUE,5,'[0,50000]',0,50000,100,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000108',FALSE,TRUE,5,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000109',FALSE,TRUE,4,NULL,0,50000,92,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (53,56,'00000000-0000-0000-0000-000000000110',FALSE,TRUE,6,NULL,0,50000,93,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (54,56,'00000000-0000-0000-0000-000000000111',FALSE,TRUE,6,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (413,56,'00000000-0000-0000-0000-000000000112',FALSE,TRUE,6,'[0,10000]',0,10000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (54,56,'00000000-0000-0000-0000-000000000113',FALSE,TRUE,7,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000114',FALSE,TRUE,3,NULL,0,50000,92,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (53,55,'00000000-0000-0000-0000-000000000115',FALSE,TRUE,5,'[0,10000]',0,10000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (47,55,'00000000-0000-0000-0000-000000000116',TRUE,FALSE,1,'[0,10000]',0,10000,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),NULL),
    (0,55,'00000000-0000-0000-0000-000000000117',TRUE,FALSE,3,NULL,0,10000,89,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000118',FALSE,TRUE,7,NULL,0,50000,96,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000119',FALSE,TRUE,6,'[0,50000]',0,50000,100,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000120',FALSE,TRUE,7,NULL,0,50000,93,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000121',FALSE,TRUE,7,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000122',FALSE,TRUE,4,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (50,55,'00000000-0000-0000-0000-000000000123',TRUE,FALSE,4,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (49,55,'00000000-0000-0000-0000-000000000124',TRUE,FALSE,3,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Compliance' and is_active = true),NULL),
    (413,56,'00000000-0000-0000-0000-000000000125',FALSE,TRUE,7,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (53,56,'00000000-0000-0000-0000-000000000126',TRUE,FALSE,6,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Medical' and is_active = true) ,NULL),
    (54,56,'00000000-0000-0000-0000-000000000127',FALSE,TRUE,7,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Regional Franchise Head' and is_active = true) ,NULL),
    (51,56,'00000000-0000-0000-0000-000000000128',TRUE,FALSE,5,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (51,55,'00000000-0000-0000-0000-000000000129',TRUE,FALSE,4,NULL,0,50000,96,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Marketing' and is_active = true) ,NULL),
    (50,55,'00000000-0000-0000-0000-000000000130',TRUE,FALSE,4,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ,NULL),
    (413,56,'00000000-0000-0000-0000-000000000131',FALSE,TRUE,8,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,185,TRUE,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true),NULL),
    (48,55,'00000000-0000-0000-0000-000000000132',TRUE,FALSE,2,'[10001,50000]',10001,50000,89,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000133',TRUE,FALSE,2,'[0,10000]',0,10000,89,FALSE,50,55,185,FALSE,(select id from departments where department = 'Country Medical' and is_active = true) ,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000134',FALSE,TRUE,1,NULL,0,50000,96,FALSE,53,56,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),(select id from departments where department = 'Regional Medical' and is_active = true) ),
    (48,55,'00000000-0000-0000-0000-000000000135',TRUE,FALSE,2,'[10001,50000]',10001,50000,420,FALSE,50,55,185,FALSE,(select id from departments where department = 'BU Head' and is_active = true),(select id from departments where department = 'Country Commercial Solution Head' and is_active = true) );

