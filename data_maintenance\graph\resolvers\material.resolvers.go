package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertMaterial is the resolver for the upsertMaterial field.
func (r *mutationResolver) UpsertMaterial(ctx context.Context, input model.MaterialInput) (*model.UpsertMaterialResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertMaterial", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertMaterial :" + string(inputJson))
	response := controller.UpsertMaterialData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertMaterial", "data_maintenance/UpsertMaterial", time.Since(start), "200")
	return response, nil
}

// Material is the resolver for the material field.
func (r *queryResolver) Material(ctx context.Context, input model.MaterialRequest) (*model.MaterialResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "Material", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Material :" + string(inputJson))
	response := controller.ExportMaterialExcel(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("Material", "data_maintenance/Material", time.Since(start), "200")
	return response, nil
}
