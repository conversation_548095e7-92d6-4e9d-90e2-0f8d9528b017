package mapper

import (
	"strconv"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapCustomerExcelToEntities(data [][]string, userUUID uuid.UUID) ([]*entity.CustomerExcelInput, *model.ValidationResult) {
	var customers []*entity.CustomerExcelInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	countryID, err := postgres.GetUserCountryByActiveDir(userUUID)
	if err != nil {
		errorMessage := &model.ExcelValidationMessage{Message: err.Error()}
		validationMessages = append(validationMessages, errorMessage)

	}
	country := countryID
	codes := codeController.GetValueKeyCodes()
	genders := codes["gender"]
	cmslClasses := codes["cmslclass"]
	// var flag int
	for i := 1; i < len(data); i++ {
		// flag = 0
		var innerValidationMessages []*model.ExcelValidationMessage
		row := i + 1
		var uid uuid.UUID
		if strings.TrimSpace(data[i][0]) != "" {
			id, err := uuid.FromString(strings.TrimSpace(data[i][0]))
			if err != nil {
				errorMessage := &model.ExcelValidationMessage{Row: (row), Message: err.Error()}
				innerValidationMessages = append(innerValidationMessages, errorMessage)
			} else {
				uid = id
			}
		}
		var customerNumber string
		// customerID := strings.TrimSpace(data[i][0])
		customerNumber = strings.TrimSpace(data[i][6])
		// if postgres.CheckCustomerNumberUnique(customerNumber, customerID) {
		// 	// errorMessage := &model.ExcelValidationMessage{Row: (row), Message: "Row " + strconv.Itoa(row) + ": Customer ID Must Be Unique"}
		// 	// innerValidationMessages = append(innerValidationMessages, errorMessage)
		// 	flag = 1
		// }

		teamName := strings.TrimSpace(data[i][2])
		cmslClass := strings.TrimSpace(data[i][9])
		if teamName != "" {
			if cmslClass != "" {
				_, cmslClassExists := cmslClasses[cmslClass]
				if !cmslClassExists {
					errorMessage := &model.ExcelValidationMessage{Row: row, Message: " CMSLClass is invalid"}
					innerValidationMessages = append(innerValidationMessages, errorMessage)
				}
			}
		}
		speakerWeight, _ := strconv.ParseFloat(data[i][12], 8)
		var isActive bool
		var isDeleted bool
		if data[i][13] != "" {
			if strings.TrimSpace(data[i][13]) == "Y" {
				isActive = true
				isDeleted = false
			} else {
				isActive = false
				isDeleted = false
			}
		} else {
			isActive = true
			isDeleted = false
		}
		c := &entity.CustomerExcelInput{
			ID:               &uid,
			CountryName:      "",
			TeamName:         strings.TrimSpace(data[i][2]),
			Position:         strings.TrimSpace(data[i][3]),
			EmployeeName:     strings.TrimSpace(data[i][4]),
			ActiveDirectory:  strings.TrimSpace(data[i][5]),
			CustomerNo:       customerNumber,
			CountryNo:        country,
			Name:             strings.TrimSpace(data[i][7]),
			SpDesc:           strings.TrimSpace(data[i][8]),
			CMSLClass:        cmslClass,
			City:             strings.TrimSpace(data[i][10]),
			Gender:           strings.TrimSpace(data[i][11]),
			SpeakerWeight:    speakerWeight,
			IsActive:         isActive,
			IsDeleted:        isDeleted,
			Author:           &userUUID,
			VeevareferenceId: strings.TrimSpace(data[i][14]),
			Organization:     strings.TrimSpace(data[i][15]),
			AccountType:      strings.TrimSpace(data[i][16]),
		}

		if c.Gender != "" {
			_, genderExists := genders[c.Gender]
			if !genderExists {
				errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Gender is invalid"}
				innerValidationMessages = append(innerValidationMessages, errorMessage)
			} else {
				c.GenderID = genders[c.Gender].ID
			}
		}
		// Validate Team name
		if c.TeamName != "" {
			teamUUID, err := postgres.GetTeamByTeamName(c.TeamName, c.CountryNo)
			if err == nil {
				c.TeamID = &teamUUID
			} else {
				errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Team does not exist"}
				innerValidationMessages = append(innerValidationMessages, errorMessage)
			}
		}

		if c.ActiveDirectory != "" {
			userID, err := postgres.GetEmployeeIdByActiveDirName(c.ActiveDirectory)
			if err == nil {
				c.EmployeeID = userID
			} else {
				errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Active directory does not exist"}
				innerValidationMessages = append(innerValidationMessages, errorMessage)
			}
		}
		if c.TeamName != "" && c.ActiveDirectory != "" {
			if c.TeamID != nil && c.EmployeeID != nil {
				teamMemberID, err := postgres.GetTeamMemberByTeamEmployee(c.TeamID, c.EmployeeID)
				if err == nil {
					c.TeamMemberID = &teamMemberID
				} else {
					errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Employee does not belong to the team"}
					innerValidationMessages = append(innerValidationMessages, errorMessage)
				}
			}
		}

		if c.ID != nil && c.TeamName == "" && c.Position == "" && c.EmployeeName == "" && c.Name == "" && c.SpDesc == "" && c.CMSLClass == "" && c.City == "" && c.Gender == "" && c.VeevareferenceId == "" && c.Organization == "" && c.AccountType == "" {
			c.IsActive = false
			c.IsDeleted = false
		}

		c.ValidateExcelData(row, innerValidationMessages)
		if !c.IsDeleted && len(innerValidationMessages) > 0 {
			validationMessages = append(validationMessages, innerValidationMessages...)
		}
		// if flag != 1 {
		customers = append(customers, c)
		// }
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}
	return customers, result
}
