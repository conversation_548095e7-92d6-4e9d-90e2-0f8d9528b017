package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.

import (
	"context"
	"fmt"
)

// func (r *queryResolver) ExcelTemplate(ctx context.Context, input model.ExcelTemplateRequest) (*model.AzureExcelUploadResponse, error) {
// 	return controller.GetExcelTemplateURL(&input), nil
// }

// func (r *queryResolver) ExcelStatus(ctx context.Context, input model.ExcelStatusRequest) (*model.ExcelStatusResponse, error) {
// 	userID, salesOrgID, authRole := auth.ForContext(ctx)
// 	if userID == nil || salesOrgID == nil || authRole == nil {
// 		return nil, errors.New("You are unauthorized, please login!")
// 	}
// 	response := controller.ListExcelUploads(&ctx, &input)
// 	return response, nil
// }

// Query returns generated.QueryResolver implementation.
// func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

// type queryResolver struct{ *Resolver }

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//  - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//    it when you're done.
//  - You have helper methods in this file. Move them out to keep these resolver files clean.

func (r *queryResolver) GetMaterial(ctx context.Context, divisionName string) (*string, error) {
	panic(fmt.Errorf("not implemented"))
}
