package postgres

import (
	"context"
	"log"

	"github.com/ihcp/upload/graph/entity"
	uuid "github.com/satori/go.uuid"
)

func GetTeamIdByCountryAndName(countryId int64, teamName string) (*uuid.UUID, error) {
	functionName := "GetTeamIdByCountryAndName()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var teamId uuid.UUID
	query := `SELECT id from team where country = $1 AND name = $2 AND is_deleted = false AND is_active = true`

	err := pool.QueryRow(context.Background(), query, countryId, teamName).Scan(&teamId)

	if err != nil {
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &teamId, nil
}

func UpsertTeamForExcel(entity *entity.Team) (*uuid.UUID, error) {
	functionName := "UpsertTeamForExcel()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: Failed to begin transaction", functionName)

	}
	query := `INSERT INTO team (name,country,is_active,created_by) VALUES($1,$2,$3,$4) RETURNING(id)`
	var teamId uuid.UUID
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.Name, entity.Country, true, uuid.Nil)
	err = tx.QueryRow(context.Background(), query, inputArgs...).Scan(&teamId)

	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: Failed to commit material data", functionName)
	}
	return &teamId, err
}

func deleteTeam(id uuid.UUID) {
	functionName := "deleteTeam()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: Failed to begin transaction", functionName)
		tx.Rollback(context.Background())
		return
	}
	defer tx.Rollback(context.Background())
	queryTeamMember := `update team_member set is_active=false and is_deleted=true where team=$1`
	_, err = tx.Exec(context.Background(), queryTeamMember, id)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		tx.Rollback(context.Background())
		return
	}
	queryTeam := `update division set is_active=false and is_deleted=true where id=$1`
	_, err = tx.Exec(context.Background(), queryTeam, id)
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		tx.Rollback(context.Background())
		return
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: Failed to commit material data", functionName)
	}
}
