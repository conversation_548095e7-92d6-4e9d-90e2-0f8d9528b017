package mapper

import (
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

func MapTeamFromEmployee(teamName string, countryId int64) *entity.Team {
	id := uuid.NewV4()
	return &entity.Team{
		ID:        &id,
		Name:      teamName,
		Country:   countryId,
		IsActive:  true,
		IsDeleted: false,
		CreatedBy: nil,
	}
}

func MapTeamModelToEntity(inputModel *model.TeamInput) (*entity.UpsertTeam, *model.UpsertTeamResponse) {
	result := &model.UpsertTeamResponse{Error: false}
	var entity entity.UpsertTeam
	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			entity.ID = &uuid
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "Team ID format is invalid!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}

	if strings.TrimSpace(inputModel.TeamCountry) != "" {
		entity.CountryName = inputModel.TeamCountry
		codes := codeController.GetTitleKeyCodes()["country"]
		_, countryExists := codes[entity.CountryName]
		if countryExists {
			entity.Country = codes[entity.CountryName].ID
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "invalid country name"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}

	entity.Name = inputModel.TeamName
	if inputModel.IsDelete != nil {
		entity.IsDeleted = *inputModel.IsDelete
	}
	if entity.IsDeleted {
		if entity.ID == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Team ID format is invalid"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}
	entity.ValidateTeamUpsertData(result)

	return &entity, result
}

func FetchTeamValuesEntityToModel(input []entity.FetchTeamValues) []*model.TeamData {
	var outEntity []*model.TeamData
	for _, item := range input {
		team := new(model.TeamData)
		team.ID = item.ID
		team.TeamName = item.Title
		outEntity = append(outEntity, team)
	}
	return outEntity
}
