INSERT INTO event_type_access_permission
(user_role, country, is_active, is_deleted, created_by, event_type, user_role_id)
VALUES ('{68,69,46,71,72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',98,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',92,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{47,46,69}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',100,(select array(select id from user_roles where value = 'productmanager' or  value = 'bumanager' or value = 'salesmanager' or value = 'medicalmanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{69,46,71,72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',93,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'countrymedical' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',94,(select array(select id from user_roles where value = 'salesrepresentative' or value = 'salesmanager' or value = 'clustercomplianceofficer' or value = 'admin' and is_active = true and is_deleted = false))),
    ('{47}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',96,(select array(select id from user_roles where value = 'bumanager' or  value = 'countrymedical' or value = 'admin' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
    ('{69}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',91,(select array(select id from user_roles where value = 'productmanager' and is_active = true and is_deleted = false))),
    ('{72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',116,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'countrymedical' or value = 'admin' or value = 'clustercomplianceofficer' or value = 'marketaccessmanager' or value = 'bumanager' and is_active = true and is_deleted = false))),
    ('{72,48,63}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',418,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'countrymedical' or value = 'admin' or value = 'productmanager' and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',86,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'salesmanager' or value = 'productmanager' or value = 'clustercomplianceofficer' or value = 'admin' and is_active = true and is_deleted = false))),
    ('{49}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',89,(select array(select id from user_roles where value = 'clustercomplianceofficer' or  value = 'medicalmanager' or value = 'productmanager' or value = 'salesmanager' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{69}',10,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',101,(select array(select id from user_roles where value = 'productmanager' or  value = 'medicalmanager' or value = 'salesmanager' or value = 'clustercomplianceofficer' or value = 'admin' and is_active = true and is_deleted = false)));


INSERT INTO event_type_access_permission
(user_role, country, is_active, is_deleted, created_by, event_type, user_role_id)
VALUES ('{68}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',98,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'admin' or value = 'clustercomplianceofficer' or value = 'sfeadmin' or value = 'manager' or value = 'bumanager' or value = 'medicalmanager' or value = 'marketaccessmanager' or value = 'productmanager' or value = 'regionalfranchisehead' or value = 'regionalmedical' or value = 'regionalcompliance' or value = 'regionalmarketing' or value = 'bumanager' or value = 'datastewardadmin' and is_active = true and is_deleted = false))),
    ('{72,69,63,49,48,47,46}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',100,(select array(select id from user_roles where value = 'medicalmanager' or value = 'productmanager' or value = 'admin' or value = 'clustercomplianceofficer' or value = 'countrymedical' or value = 'bumanager' or value = 'salesmanager'  and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',98,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' and is_active = true and is_deleted = false))),
    ('{47,46,69}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',70,(select array(select id from user_roles where value = 'productmanager' or value = 'salesmanager' or value = 'bumanager' and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',86,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
    ('{72,71,69,63,49,48,47}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',116,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'countrymedical' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{68,69,46,71,72}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',92,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin' or value = 'manager' or value = 'countrycommercialsolutionhead'  and is_active = true and is_deleted = false))),
    ('{69,46,71,72}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',93,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'countrymedical' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
    ('{47}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',96,(select array(select id from user_roles where value = 'manager' or  value = 'medicalmanager' or value = 'marketaccessmanager' or value = 'productmanager' or value = 'admin' or value = 'regionalmedical' or value = 'regionalmarketing' or value = 'countrycommercialsolutionhead' or value = 'clustercomplianceofficer' or value = 'countrymedical' or value = 'bumanager' and is_active = true and is_deleted = false))),
    ('{72,71,69,63,48,47,46}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',101,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'admin' or value = 'countrymedical' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'salesmanager'  and is_active = true and is_deleted = false))),
    ('{49}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',89,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin' or value = 'countrymedical'  and is_active = true and is_deleted = false))),
    ('{72,68,63,48,49}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',418,(select array(select id from user_roles where value = 'medicalmanager' or value = 'salesrepresentative' or value = 'admin' or value = 'countrymedical' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
    ('{72,68,63,48,49}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',419,(select array(select id from user_roles where value = 'medicalmanager' or value = 'salesrepresentative' or value = 'admin' or value = 'countrymedical' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
    ('{72,69,71,63,48,46,47,49}',7,TRUE,FALSE,'00000000-0000-0000-0000-000000000000',420,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'countrymedical' or value = 'salesmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false)));
    
