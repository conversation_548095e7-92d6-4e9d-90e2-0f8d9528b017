package postgres

import (
	"context"
	"log"
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

var existingDivisions map[int]map[string]uuid.UUID

func GetMaterialExcelInfo(materialInput *model.MaterialRequest, input *entity.ExportMaterialExcel) ([]entity.MaterialExcel, error) {
	functionName := "GetMaterialExcelInfo()"
	log.Printf("%s", functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `SELECT mat.id as id, mat.group_code as groupCode, mat.group_name as groupName, mat.is_active as status, 
	div.name as division, mat.country as country,mat.veeva_reference_id ,po.owner_name 
	FROM material mat 
	LEFT JOIN division div on div.id = mat.division
	left join product_owner po on po.id =mat.product_owner_uid 
	WHERE mat.is_deleted = false`

	var inputArgs []interface{}
	if input.ID != nil && *(input).ID != uuid.Nil {
		queryString += ` AND mat.id = ? `
		inputArgs = append(inputArgs, input.ID)
	}

	if input.SearchItem != "" {
		queryString += ` AND (div.name ilike ? or mat.group_code ilike ? 
			or mat.group_name ilike ? or po.owner_name ilike ? or mat.veeva_reference_id ilike ? or (
				case 
				when 'Active' ilike ? then mat.is_active = true 
				when 'InActive' ilike ? then mat.is_active = false 
				end)) `
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.Status != "" {
			if input.Status == "Active" {
				queryString += ` AND mat.is_active = true `
			} else if input.Status == "InActive" {
				queryString += ` AND mat.is_active = false `
			}
		}
		if input.IsActive != nil {
			queryString += ` AND mat.is_active = ?`
			inputArgs = append(inputArgs, input.IsActive)
		}
		if input.Division != "" {
			queryString += ` AND div.name ilike ? `
			inputArgs = append(inputArgs, "%"+input.Division+"%")
		}
		if input.GroupCode != "" {
			queryString += ` AND mat.group_code ilike ? `
			inputArgs = append(inputArgs, "%"+input.GroupCode+"%")
		}
		if input.GroupName != "" {
			queryString += ` AND mat.group_name ilike ? `
			inputArgs = append(inputArgs, "%"+input.GroupName+"%")
		}
		if input.VeevaReferenceID != "" {
			queryString += ` AND mat.veeva_reference_id ilike ? `
			inputArgs = append(inputArgs, "%"+input.VeevaReferenceID+"%")
		}
		if input.ClientName != "" {
			queryString += ` AND po.owner_name ilike ? `
			inputArgs = append(inputArgs, "%"+input.ClientName+"%")
		}
	}
	if input.Country > 0 {
		queryString += ` AND mat.country = ? `
		inputArgs = append(inputArgs, input.Country)
	}
	if materialInput.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY mat.date_created desc, id asc`
	}
	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ?
		`
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetMaterialExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var materials []entity.MaterialExcel
	for rows.Next() {
		var material entity.MaterialExcel
		err := rows.Scan(&material.ID, &material.GroupCode, &material.GroupName, &material.IsActive, &material.Division, &material.Country, &material.VeevaReferenceID, &material.ClientName)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return materials, err
		}
		materials = append(materials, material)
	}
	return materials, nil
}

func mapDivisionFromMaterial(divisionName string, countryId int) *entity.Division {
	return &entity.Division{
		Name:    divisionName,
		Country: int64(countryId),
	}
}

func InsertMaterialData(entity *entity.MaterialUpsertInput, userUUID uuid.UUID, response *model.UpsertMaterialResponse) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertMaterialData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	if entity.DivisionName != nil {
		var divisionID string
		querystring := `INSERT INTO division (name, country, created_by, is_active)VALUES($1,$2,$3,$4) RETURNING(id)`
		err = tx.QueryRow(context.Background(), querystring, *entity.DivisionName, entity.CountryCode, userUUID, entity.IsActive).Scan(&divisionID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to roll back division data"
			response.Error = true
			return
		}
		MaterialQueryString := `INSERT INTO material (group_name, group_code, division, created_by, is_active, country,product_owner_uid) VALUES($1, $2, $3, $4, $5, $6,$7)`
		commandTag, err := tx.Exec(context.Background(), MaterialQueryString, entity.GroupName, entity.GroupCode, divisionID, userUUID, entity.IsActive, entity.CountryCode, entity.ClientId)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to roll back material data"
			response.Error = true
			return
		}
		if commandTag.RowsAffected() != 1 {
			response.Message = "Invalid material data"
			response.Error = true
			return
		} else {
			response.Message = "material successfully inserted"
			response.Error = false
		}
	} else {
		MaterialQueryString := `INSERT INTO material (group_name, group_code, created_by, is_active, country,product_owner_uid) VALUES($1, $2, $3, $4, $5,$6)`
		commandTag, err := tx.Exec(context.Background(), MaterialQueryString, entity.GroupName, entity.GroupCode, userUUID, entity.IsActive, entity.CountryCode, entity.ClientId)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to roll back material data"
			response.Error = true
			return
		}
		if commandTag.RowsAffected() != 1 {
			response.Message = "Invalid material data"
			response.Error = true
			return
		} else {
			response.Message = "material successfully inserted"
			response.Error = false
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit division and material data"
		response.Error = true
	}
}

func DeleteMaterialData(entity *entity.MaterialUpsertInput, response *model.UpsertMaterialResponse, divisionID *uuid.UUID, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("DeleteMaterialData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	if divisionID != nil {
		classQueryString := "update division set is_active = false, is_deleted = true, modified_by = $2, last_modified = $3 where id=$1"
		_, err = tx.Exec(context.Background(), classQueryString, *divisionID, userUUID, timenow)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = err.Error()
			response.Error = true
			return
		}

		response.Message = "division successfully deleted"
		response.Error = false
	}

	querystring := "UPDATE material SET is_active = false, modified_by = $2, last_modified = $3 WHERE id = $1"
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userUUID, timenow)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = err.Error()
		response.Error = true
		return
	}
	response.Message = "material successfully deactivated"
	response.Error = false
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit division and material data"
		response.Error = true
	}
}

func UpdateMaterialData(entity *entity.MaterialUpsertInput, inputModel *model.MaterialInput, response *model.UpsertMaterialResponse, divisionID *uuid.UUID, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("UpdateMaterialData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	if divisionID != nil {
		querystring := `UPDATE division set last_modified = now(), `
		var inputArgs []interface{}
		if inputModel.Division != nil {
			querystring += `name = ? ,`
			inputArgs = append(inputArgs, entity.DivisionName)
		}

		querystring += `is_active = ? ,`
		inputArgs = append(inputArgs, entity.IsActive)

		querystring += `country= ? ,`
		inputArgs = append(inputArgs, entity.CountryCode)
		querystring += `modified_by= ? `
		inputArgs = append(inputArgs, userUUID)
		querystring += `WHERE id= ? AND is_deleted = false`
		inputArgs = append(inputArgs, *divisionID)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err := tx.Exec(context.Background(), querystring, inputArgs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = err.Error()
			response.Error = true
			return
		}
		response.Message = "division successfully updated"
		response.Error = false
		materialQueryString := `UPDATE material set last_modified = now(), `
		var inputArg []interface{}
		if inputModel.GroupName != nil {
			materialQueryString += `group_name = ? ,`
			inputArg = append(inputArg, entity.GroupName)
		}
		if inputModel.GroupCode != nil {
			materialQueryString += `group_code = ? ,`
			inputArg = append(inputArg, entity.GroupCode)
		}

		if inputModel.ClientName != nil {
			materialQueryString += `product_owner_uid = ? ,`
			inputArg = append(inputArg, entity.ClientId)
		}

		materialQueryString += `is_active = ? ,`
		inputArg = append(inputArg, entity.IsActive)

		materialQueryString += `country= ? ,`
		inputArg = append(inputArg, entity.CountryCode)
		materialQueryString += `modified_by= ? `
		inputArg = append(inputArg, userUUID)
		materialQueryString += `WHERE id= ? AND is_deleted = false`
		inputArg = append(inputArg, entity.ID)
		materialQueryString = sqlx.Rebind(sqlx.DOLLAR, materialQueryString)
		_, errMaterial := tx.Exec(context.Background(), materialQueryString, inputArg...)
		if errMaterial != nil {
			response.Message = errMaterial.Error()
			response.Error = true
			return
		}

	} else if entity.DivisionName != nil {
		querystring := `INSERT INTO division (name, country, created_by, is_active)VALUES($1,$2,$3,$4) RETURNING(id)`
		err = tx.QueryRow(context.Background(), querystring, *entity.DivisionName, entity.CountryCode, userUUID, entity.IsActive).Scan(&divisionID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to roll back division data"
			response.Error = true
			return
		}
		querystring1 := `UPDATE material set last_modified = now(), `
		var inputArgNew []interface{}
		if inputModel.GroupName != nil {
			querystring1 += `group_name = ? ,`
			inputArgNew = append(inputArgNew, entity.GroupName)
		}
		if inputModel.GroupCode != nil {
			querystring1 += `group_code = ? ,`
			inputArgNew = append(inputArgNew, entity.GroupCode)
		}

		if inputModel.ClientName != nil {
			querystring1 += `product_owner_uid = ? ,`
			inputArgNew = append(inputArgNew, entity.ClientId)
		}

		querystring1 += `is_active = ? ,`
		inputArgNew = append(inputArgNew, entity.IsActive)

		querystring1 += `country= ? ,`
		inputArgNew = append(inputArgNew, entity.CountryCode)
		querystring1 += `division= ? ,`
		inputArgNew = append(inputArgNew, divisionID)
		querystring1 += `modified_by= ? `
		inputArgNew = append(inputArgNew, userUUID)
		querystring1 += `WHERE id= ? AND is_deleted = false`
		inputArgNew = append(inputArgNew, entity.ID)
		querystring1 = sqlx.Rebind(sqlx.DOLLAR, querystring1)
		_, errMaterial := tx.Exec(context.Background(), querystring1, inputArgNew...)
		if errMaterial != nil {
			response.Message = errMaterial.Error()
			response.Error = true
			return
		}

		response.Message = "Material successfully updated"
		response.Error = false
	} else {
		materialQueryString := `UPDATE material set last_modified = now(), `
		var inArg []interface{}
		if inputModel.GroupName != nil {
			materialQueryString += `group_name = ? ,`
			inArg = append(inArg, entity.GroupName)
		}
		if inputModel.GroupCode != nil {
			materialQueryString += `group_code = ? ,`
			inArg = append(inArg, entity.GroupCode)
		}

		if inputModel.ClientName != nil {
			materialQueryString += `product_owner_uid = ? ,`
			inArg = append(inArg, entity.ClientId)
		}

		materialQueryString += `is_active = ? ,`
		inArg = append(inArg, entity.IsActive)

		materialQueryString += `country= ? ,`
		inArg = append(inArg, entity.CountryCode)
		materialQueryString += `modified_by= ? `
		inArg = append(inArg, userUUID)
		materialQueryString += `WHERE id= ? AND is_deleted = false`
		inArg = append(inArg, entity.ID)
		materialQueryString = sqlx.Rebind(sqlx.DOLLAR, materialQueryString)
		_, errMaterial := tx.Exec(context.Background(), materialQueryString, inArg...)
		if errMaterial != nil {
			response.Message = err.Error()
			response.Error = true
			return
		}
		response.Message = "Material successfully updated"
		response.Error = false

	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit division and material data"
		response.Error = true
	}
	response.Message = "material successfully updated"
	response.Error = false
}

func MaterialCount(country int) (int, error) {
	functionName := "MaterialCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `SELECT count(id) FROM material WHERE is_deleted = false and country = $1`
	var result int
	err := pool.QueryRow(context.Background(), query, country).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("MaterialCount query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return result, nil
}
func CheckMaterialDB(entity *entity.MaterialUpsertInput) bool {
	functionName := "CheckMaterialDB()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var val int
	query := `select 1 from material m where lower(m.group_code) =$1 and lower(m.group_name) =$2 and m.country =$3 and m.product_owner_uid =$4  and m.is_active =true `
	err := pool.QueryRow(context.Background(), query, strings.ToLower(entity.GroupCode), strings.ToLower(entity.GroupName), entity.CountryCode, entity.ClientId).Scan(&val)
	if err == nil {
		return false
	}
	return true
}
func GetClientNameToClientID(ClientName string, country int) (string, error) {
	functionName := "GetClientNameToClientID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	clientName := strings.ReplaceAll(strings.ToLower(ClientName), " ", "")
	query := `SELECT id FROM product_owner WHERE is_active=true and is_deleted = false and country = $1 and REPLACE(lower(owner_name),' ','')=$2`
	var result string
	err := pool.QueryRow(context.Background(), query, country, clientName).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("ClientNameToClientID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return result, nil

}
