package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/generated"
	"github.com/ihcp/form/graph/model"
)

// ExcelUploadForEmployeeAttendance is the resolver for the excelUploadForEmployeeAttendance field.
func (r *mutationResolver) ExcelUploadForEmployeeAttendance(ctx context.Context, input model.ExcelUploadRequestForEmployeeAttendance) (*model.ExcelUploadResponse, error) {
	return controller.HandleExcelUploadForEmployeeAttendance(ctx, input), nil
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

type mutationResolver struct{ *Resolver }
