
trigger:
- main

resources:
- repo: self

variables: 
  ## Add this under variables section in the pipeline
  azureSubscription: azurerm-dnd-devops-managed
  appName: ezflow-staging-form-latest
  dev-containerRegistry: 'ezflowlatestcr.azurecr.io'
  dockerRegistryServiceConnection: 'ezflowlatestcr'
  imageRepository: 'stg/ezflow/form'
  tag: a65ae260f9a3abe957f096fad8d03db6964bf924

  vmImageName: 'ubuntu-latest'

stages:
- stage: Deployment
  displayName: Staging Deployment
  jobs:
  - job: Deploy
    displayName: App Service Depoyment
    steps:
    ## Add the below snippet at the end of your pipeline
    - task: AzureWebAppContainer@1
      displayName: 'Azure Web App on Container Deploy'
      inputs:
        azureSubscription: $(azureSubscription)
        appName: $(appName)
        containers: $(dev-containerRegistry)/$(imageRepository):$(tag)