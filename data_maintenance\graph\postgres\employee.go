package postgres

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strconv"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
	"github.com/jackc/pgtype"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

var existingTeams map[int64]map[string]uuid.UUID

func createEmployee(tx pgx.Tx, item *entity.EmployeeExcelInput, teamId uuid.UUID, userUUID uuid.UUID) error {
	functionName := "createEmployee()"
	var employeeID uuid.UUID
	query := `INSERT INTO "user" (first_name,last_name,country,employee_code,is_active,created_by,active_directory,email,user_role_id,approval_role) VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,0) RETURNING(id)`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.FirstName, item.LastName, item.CountryID, item.EmployeeCode, item.IsActive, userUUID, item.ActiveDirectory, item.Email, item.UserRoleID)
	err := tx.QueryRow(context.Background(), query, inputArgs...).Scan(&employeeID)
	logengine.GetTelemetryClient().TrackEvent("createEmployee query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return err
	}
	CreateUsermanagementlogsData(tx, &employeeID, item.UserRoleID, item.FirstName, item.LastName, item.Email, item.ActiveDirectory, item.EmployeeCode, userUUID, "Created")
	team := util.UUIDV4ToString(teamId)
	if &teamId != nil && team != "00000000-0000-0000-0000-000000000000" {
		return UpsertTeamMemberForExcel(tx, mapTeamMember(&teamId, &employeeID, item.Position), userUUID)
	}
	return err

}

func updateEmployee(tx pgx.Tx, entity *entity.EmployeeExcelInput, teamId *uuid.UUID, index int, uploadId *uuid.UUID, userUUID uuid.UUID, usermanagementlogsMessage string) error {
	functionName := "updateEmployee()"
	log.Printf("%s", functionName)
	var validationResult model.ValidationResult
	previousTeamMember := GetTeamMemberByEmployeeId(entity.ID)
	queryString := `UPDATE "user" set last_modified = now(), `
	var inputArgs []interface{}
	if entity.FirstName != "" {
		queryString += `first_name = ? ,`
		inputArgs = append(inputArgs, entity.FirstName)
	}
	if entity.LastName != "" {
		queryString += `last_name = ? ,`
		inputArgs = append(inputArgs, entity.LastName)
	}
	if entity.EmployeeCode != "" {
		queryString += `employee_code = ? ,`
		inputArgs = append(inputArgs, entity.EmployeeCode)
	}
	if entity.UserRoleID != nil {
		queryString += `user_role_id = ? ,`
		inputArgs = append(inputArgs, entity.UserRoleID)
	}
	if entity.ActiveDirectory != "" {
		queryString += `active_directory = ? ,`
		inputArgs = append(inputArgs, entity.ActiveDirectory)
	}
	if entity.Email != "" {
		queryString += `email = ? ,`
		inputArgs = append(inputArgs, entity.Email)
	}

	queryString += `is_active= ? , `
	inputArgs = append(inputArgs, entity.IsActive)
	queryString += `modified_by= ? ,`
	inputArgs = append(inputArgs, userUUID)
	queryString += `country= ? `
	inputArgs = append(inputArgs, entity.CountryID)
	queryString += `WHERE id= ? AND is_deleted = false`
	inputArgs = append(inputArgs, entity.ID)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)

	commandTag, err := tx.Exec(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("updateEmployee query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}

	if commandTag.RowsAffected() != 1 {
		err = errors.New("invalid id")
		if uploadId != &uuid.Nil && index < 0 {
			errorMessage := &model.ExcelValidationMessage{Row: (index + 1), Message: "Row " + strconv.Itoa(index+1) + ": " + err.Error()}
			validationResult.ExcelValidationMessages = append(validationResult.ExcelValidationMessages, errorMessage)
			InsertUploadValidationErrors(nil, &validationResult, uploadId, uuid.UUID{})
		}
		return err
	}
	CreateUsermanagementlogsData(tx, entity.ID, entity.UserRoleID, entity.FirstName, entity.LastName, entity.Email, entity.ActiveDirectory, entity.EmployeeCode, userUUID, usermanagementlogsMessage)
	if previousTeamMember != nil && entity.TeamID != "" {
		if previousTeamMember.Team.String() != teamId.String() {
			err := deleteTeamMemberByEmployeeAndTeamId(tx, previousTeamMember.Team, entity.ID, userUUID)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				return err
			}
			return UpsertTeamMemberForExcel(tx, mapTeamMember(teamId, entity.ID, entity.Position), userUUID)
		} else if previousTeamMember.Position != entity.Position {
			return updateTeamMember(tx, mapTeamMember(teamId, entity.ID, entity.Position), previousTeamMember.ID, userUUID)
		}
	} else if entity.TeamID != "" {
		return UpsertTeamMemberForExcel(tx, mapTeamMember(teamId, entity.ID, entity.Position), userUUID)
	}
	return nil
}

func CreateUsermanagementlogsData(tx pgx.Tx, employeeID *uuid.UUID, userRoleID *string, firstName string, lastName string, email string, activeDirectory string, employeeCode string, userUUID uuid.UUID, usermanagementlogsMessage string) {
	functionName := "CreateUsermanagementlogsData"
	querystring := `INSERT INTO user_management_logs
	(user_id, user_role, first_name, last_name, email, active_directory, employee_code, action_by, action_type, action_date)
	VALUES( $1, $2, $3, $4, $5, $6, $7, $8, $9, now());`
	_, err := tx.Exec(context.Background(), querystring, employeeID, userRoleID, firstName, lastName, email, activeDirectory, employeeCode, userUUID, usermanagementlogsMessage)
	logengine.GetTelemetryClient().TrackEvent("CreateUsermanagementlogsData query called")
	if err != nil {
		log.Printf("%s - error: {%s}  %s", functionName, err.Error())
	}
}

func deleteEmployee(tx pgx.Tx, entity *entity.EmployeeExcelInput, teamID *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "deleteEmployee()"
	log.Printf("%s \n", functionName)
	query := `UPDATE "user" set is_active=false,modified_by=$2,last_modified=now() where id=$1`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.ID, userUUID)
	_, err := tx.Exec(context.Background(), query, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("deleteEmployee query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	if teamID != nil {
		return deleteTeamMemberByEmployeeAndTeamId(tx, teamID, entity.ID, userUUID)
	}
	return err
}

func checkExistingTeams(tx pgx.Tx, entity *entity.EmployeeExcelInput) *uuid.UUID {
	functionName := "checkExistingTeams()"
	log.Printf("%s \n", functionName)
	var err error
	teamID := uuid.Nil
	_, countryExists := existingTeams[int64(entity.CountryID)]
	if !countryExists {
		teamID, err = handleTeam(tx, entity, int64(entity.CountryID))
		if err != nil {
			return &uuid.Nil
		}
		existingTeams[int64(entity.CountryID)] = map[string]uuid.UUID{
			entity.TeamName: teamID,
		}
	} else {
		_, teamExists := existingTeams[int64(entity.CountryID)][entity.TeamName]
		if !teamExists {
			teamID, err = handleTeam(tx, entity, int64(entity.CountryID))
			if err != nil {
				return &uuid.Nil
			}
			existingTeams[int64(entity.CountryID)][entity.TeamName] = teamID
		}
		teamID = existingTeams[int64(entity.CountryID)][entity.TeamName]
	}
	log.Printf("%s - TeamId: %v ", functionName, teamID)
	return &teamID
}

func handleTeam(tx pgx.Tx, input *entity.EmployeeExcelInput, countryId int64) (uuid.UUID, error) {
	functionName := "handleTeam()"
	log.Println(functionName)
	id, err := GetTeamIdByCountryAndName(int64(countryId), input.TeamName)
	teamID := uuid.Nil
	if err != nil {
		teamID, err := UpsertTeamForExcel(tx, mapTeamFromEmployee(input.TeamName, int64(countryId)))
		if err != nil {
			return uuid.Nil, err
		}
		return *teamID, nil
	} else {
		teamID = *id
		return teamID, nil
	}
}

func GetEmployeeExcelInfo(userInput *model.EmployeeRequest, input *entity.ExportEmployeeExcel) ([]entity.FetchEmployee, error) {
	functionName := "GetEmployeeExcelInfo()"
	if pool == nil {
		pool = GetPool()
	}
	queryString := `SELECT emp.id as id, emp.first_name as firstName,emp.last_name as lastName, emp.employee_code as employeeCode,
	emp.active_directory as activeDirectoryName,emp.email as email,
	tm.v_position as position,t.id as teamId,
	t.name as team, emp.country as country , ur.value as roleValue ,ur.title as roleTitle,emp.is_active
	,emp.veeva_reference_id 
	FROM "user" emp 
	LEFT JOIN team_member tm on emp.id = tm.employee
	AND ( (tm.is_active = true or tm.is_active  is null )  AND ( tm.is_deleted = false or tm.is_deleted is null ) ) 
	LEFT JOIN team t on t.id = tm.team
	left JOIN user_roles ur on ur.id = emp.user_role_id 
	WHERE emp.is_deleted = false
	`
	log.Printf("%s", functionName)
	var inputArgs []interface{}
	if input.SearchItem != "" {
		queryString += ` AND ( emp.employee_code ILIKE ? or 
			t.name ILIKE ? or tm.v_position ILIKE ? or  emp.active_directory ILIKE ? or 
			first_name ILIKE ? or last_name ILIKE ? or emp.veeva_reference_id ILIKE ? or
			emp.approval_role in (select id from code where title ILIKE ?))`
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.ID != nil && *(input).ID != uuid.Nil {
			queryString += ` AND emp.id = ? `
			inputArgs = append(inputArgs, input.ID)
		}
		if input.EmployeeCode != "" {
			queryString += ` AND emp.employee_code ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.EmployeeCode+"%")
		}
		if input.Team != "" {
			queryString += ` AND t.name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Team+"%")
		}
		if input.Position != "" {
			queryString += ` AND tm.v_position ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Position+"%")
		}
		if input.ActiveDirectoryName != "" {
			queryString += ` AND emp.active_directory ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.ActiveDirectoryName+"%")
		}
		if input.FirstName != "" {
			queryString += ` AND emp.first_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.FirstName+"%")
		}
		if input.LastName != "" {
			queryString += ` AND emp.last_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.LastName+"%")
		}
		if input.Role != "" {
			queryString += ` AND emp.user_role_id = ? `
			inputArgs = append(inputArgs, input.Role)
		}
		if input.IsActive != nil {
			queryString += ` AND emp.is_active = ? `
			inputArgs = append(inputArgs, *input.IsActive)
		}
		if input.VeevaReferenceId != "" {
			queryString += ` AND emp.veeva_reference_id ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VeevaReferenceId+"%")
		}
	}
	if input.Country > 0 {
		queryString += ` AND emp.country = ? `
		inputArgs = append(inputArgs, input.Country)
	}
	if userInput.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY emp.date_created desc, id asc`
	}

	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ?
		`
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetEmployeeExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var employees []entity.FetchEmployee
	for rows.Next() {
		var employee entity.FetchEmployee
		err := rows.Scan(&employee.ID, &employee.FirstName, &employee.LastName, &employee.EmployeeCode, &employee.ActiveDirectory, &employee.EmailFetch, &employee.Position, &employee.TeamID, &employee.Team, &employee.Country, &employee.RoleValue, &employee.RoleTitle, &employee.IsActive, &employee.VeevaReferenceId)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return employees, err
		}
		employees = append(employees, employee)
	}
	return employees, nil
}

func mapTeamMember(teamId, employeeId *uuid.UUID, position string) *entity.TeamMember {
	id := uuid.NewV4()
	return &entity.TeamMember{
		ID:        &id,
		Team:      teamId,
		Employee:  employeeId,
		Position:  position,
		IsActive:  true,
		IsDeleted: false,
	}
}

func GetUserForAuthentication(activeDirectoryName string) ([]entity.Permission, error) {
	log.Println("GetUserForAuthentication()")
	pool = GetPool()

	var result []entity.Permission

	queryString := `
		SELECT 
			"user".id,
			(SELECT value FROM user_roles WHERE id = "user".user_role_id ),
			"roles_permission".approver,
			"roles_permission".master_data,
			"roles_permission".read_submissions,
			"roles_permission".requestor,
			"roles_permission".is_admin,
			"roles_permission".super_admin,
			country.title, country.value, currency.title, currency.value,
			(select 1 from code where category = 'ChangeRequestCountryAllow' and upper(country.value) = ANY(string_to_array(value,'|')::text[]))
		FROM "user"
		INNER JOIN "roles_permission" on "user".user_role_id = "roles_permission".user_role_id 
		INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Country') country ON country.id = "user".country
		INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Currency') currency ON LEFT(currency.value,2) = LEFT(country.value,2)
		WHERE 
			LOWER(active_directory) = $1 
			AND "user".is_active = true AND "user".is_deleted = false`
	rows, err := pool.Query(context.Background(), queryString, strings.ToLower(activeDirectoryName))
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		var conversionRate float64
		var userPermission entity.Permission
		err := rows.Scan(
			&userPermission.ID,
			&userPermission.ApprovalRole,
			&userPermission.IsApprover,
			&userPermission.MasterData,
			&userPermission.ReadOnlySubmissions,
			&userPermission.IsRequestor,
			&userPermission.IsAdmin,
			&userPermission.SuperAdmin,
			&userPermission.CountryDescription, &userPermission.CountryValue, &userPermission.CurrencyDescription, &userPermission.CurrencyValue,
			&userPermission.IsChangeRequestAllow)
		if err != nil {
			panic(err)
		}

		conversionRate = GetCurrencyFromCurrencyExchange(userPermission.CurrencyValue)
		userPermission.ConversionRate = &conversionRate
		result = append(result, userPermission)
	}

	return result, nil
}

func GetUserForRegenerate(activeDirectoryName string, country string) ([]entity.Permission, error) {
	log.Println("GetUserForAuthentication()")

	pool = GetPool()

	var result []entity.Permission
	activeDirectoryName = strings.ToLower(activeDirectoryName)

	queryString := `SELECT "user".id, (SELECT value FROM user_roles WHERE id = "user".user_role_id ),
		"roles_permission".approver ,"roles_permission".master_data ,"roles_permission".read_submissions ,"roles_permission".requestor,"roles_permission".is_admin,"roles_permission".super_admin,
		country.title, country.value, currency.title, currency.value,
		(select 1 from code where  category = 'ChangeRequestCountryAllow' and 
		upper(country.value) = ANY(string_to_array(value,'|')::text[]))
		FROM "user" inner join "roles_permission" on "user".user_role_id = "roles_permission".user_role_id 
		INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Country') country
		ON country.id = "user".country
		INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Currency') currency
		ON LEFT(currency.value,2) = LEFT(country.value,2)
		WHERE LOWER(active_directory) = $1 AND "user".is_active = true AND "user".is_deleted = false
		and "user".country = (select id from code where title = $2 and is_active= true and category = 'Country')`
	rows, err := pool.Query(context.Background(), queryString, activeDirectoryName, country)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		var userPermission entity.Permission
		err := rows.Scan(&userPermission.ID, &userPermission.ApprovalRole, &userPermission.IsApprover, &userPermission.MasterData, &userPermission.ReadOnlySubmissions, &userPermission.IsRequestor, &userPermission.IsAdmin, &userPermission.SuperAdmin, &userPermission.CountryDescription, &userPermission.CountryValue, &userPermission.CurrencyDescription, &userPermission.CurrencyValue, &userPermission.IsChangeRequestAllow)
		if err != nil {
			panic(err)
		}

		conversionRate := GetCurrencyFromCurrencyExchange(userPermission.CurrencyValue)
		userPermission.ConversionRate = &conversionRate
		result = append(result, userPermission)
	}

	return result, nil
}

func UpdateLoggedInUser(input *entity.UserAuth) error {
	functionName := "UpdateLoggedInUser()"
	if pool == nil {
		pool = GetPool()
	}
	for _, userSelection := range input.AccessControl {
		query := `UPDATE "user" set last_login=now(),modified_by=$2,last_modified=now() where id =$1`
		_, err := pool.Exec(context.Background(), query, userSelection.ID, userSelection.ID)
		logengine.GetTelemetryClient().TrackEvent("UpdateLoggedInUser query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return err
		}
	}
	return nil
}

func GetCurrencyFromCurrencyExchange(currency string) float64 {
	log.Println(fmt.Sprintf(`GetCurrencyFromCurrencyExchange('%s')`, currency))
	pool = GetPool()

	codes := codeController.GetValueKeyCodes()["country"]
	usdValue := codes["us"].ID

	queryString := `
		SELECT
			target_rate 
		FROM currency_exchange
		WHERE 
			country = (select id from code where value = left ($1,2) and category='Country' and is_active=true and is_deleted=false ) 
			AND target_country = $2 
		ORDER BY date_created DESC 
		limit 1`
	row := pool.QueryRow(context.Background(), queryString, currency, usdValue)

	var Value float64
	err := row.Scan(&Value)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return 0
		}
	}

	return 1 / Value
}

func mapTeamFromEmployee(teamName string, countryId int64) *entity.Team {
	id := uuid.NewV4()
	return &entity.Team{
		ID:        &id,
		Name:      teamName,
		Country:   countryId,
		IsActive:  true,
		IsDeleted: false,
		CreatedBy: nil,
	}
}

func GetEmployeeIdByActiveDirName(activeDirectoryName string) (*uuid.UUID, error) {
	functionName := "GetEmployeeIdByActiveDir()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var employeeID uuid.UUID
	query := `SELECT id from "user" where active_directory = $1 AND is_deleted = false AND is_active = true`
	err := pool.QueryRow(context.Background(), query, activeDirectoryName).Scan(&employeeID)
	logengine.GetTelemetryClient().TrackEvent("GetEmployeeIdByActiveDirName query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &employeeID, nil
}

func ValidateUpsertEmployee(entity *entity.Employee) error {
	if entity.ID != nil {
		if !hasEmployeeID(entity.ID) {
			return errors.New("Employee ID does not exists!")
		}
	}
	return nil
}

func ValidateTeamID(entity *entity.Employee) error {
	if entity.Team != "" {
		_, err := uuid.FromString(entity.Team)
		logengine.GetTelemetryClient().TrackEvent("ValidateTeamID query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			return errors.New("Invalid UUID Format for team id!")
		}
		if !hasTeamID(entity.Team) {
			return errors.New("Team ID does not exists!")
		}
	}
	return nil
}

func hasEmployeeID(employeeID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from "user" where id = $1 AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, employeeID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}

func UpsertEmployee(inputEntity *entity.Employee, userUUID uuid.UUID) (err error, message string) {
	functionName := "UpsertEmployee()"
	message = ""
	var usermanagementlogsMessage string
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("UpsertEmployee query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to begin transaction"), message
	}
	defer tx.Rollback(context.Background())
	existingTeams = map[int64]map[string]uuid.UUID{}
	excelInputEntity := entity.EmployeeExcelInput{
		CountryID:       inputEntity.CountryID,
		TeamID:          inputEntity.Team,
		Position:        inputEntity.Position,
		FirstName:       inputEntity.FirstName,
		LastName:        inputEntity.LastName,
		EmployeeCode:    inputEntity.EmployeeCode,
		ActiveDirectory: inputEntity.ActiveDirectory,
		// Role:            inputEntity.RoleID,
		Email:      inputEntity.Email,
		UserRoleID: inputEntity.UserRoleID,
	}
	var teamID uuid.UUID
	if excelInputEntity.TeamID != "" {
		teamID = util.StringToUUID4(excelInputEntity.TeamID)
	}
	if inputEntity.ID == nil {
		if !inputEntity.IsDeleted {
			excelInputEntity.IsActive = true
			excelInputEntity.IsDeleted = false
		} else {
			excelInputEntity.IsActive = false
			excelInputEntity.IsDeleted = false
		}
		err := createEmployee(tx, &excelInputEntity, teamID, userUUID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - error: {%s}", functionName, err.Error())
			return err, message
		}
		message = "Record created successfully"
	} else {
		excelInputEntity.ID = inputEntity.ID
		if !inputEntity.IsDeleted {
			excelInputEntity.IsActive = true
			excelInputEntity.IsDeleted = false
			if !CheckActiveUserWithRespectToId(excelInputEntity.IsActive, excelInputEntity.IsDeleted, excelInputEntity.ID) {
				usermanagementlogsMessage = "Activate"
			} else {
				usermanagementlogsMessage = "Modified"
			}
		} else {
			usermanagementlogsMessage = "Deactivate"
			excelInputEntity.IsActive = false
			excelInputEntity.IsDeleted = false
		}

		err := updateEmployee(tx, &excelInputEntity, &teamID, -1, &uuid.Nil, userUUID, usermanagementlogsMessage)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - error: {%s}", functionName, err.Error())
			return err, message
		}
		message = "Record updated successfully"
	}
	// if inputEntity.ID != nil {
	// 	excelInputEntity.ID = inputEntity.ID
	// 	teamMember := GetTeamMemberByEmployeeId(inputEntity.ID)
	// 	var teamID uuid.UUID
	// 	if teamMember != nil {
	// 		teamID = *teamMember.Team
	// 	}
	// 	err := deleteEmployee(tx, &excelInputEntity, &teamID, userUUID)
	// 	if err != nil {
	// 		tx.Rollback(context.Background())
	// 		log.Printf("%s - error: {%s}", functionName, err.Error())
	// 		return err, message
	// 	}
	// 	message = "Record deactivated successfully"
	// } else {
	// 	return errors.New("Please provide employee ID"), message
	// }

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - error: {%s}", functionName, txErr.Error())
		return errors.New("Failed to commit employee data"), message
	}
	return err, message
}

func CheckActiveUserWithRespectToId(IsActive bool, IsDeleted bool, ID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result int
	query := `select 1 from "user" u where u.is_active =$1 and u.is_deleted =$2 and u.id =$3`
	err := pool.QueryRow(context.Background(), query, IsActive, IsDeleted, ID).Scan(&result)
	if err != nil {
		return false
	}
	return true
}

func GetEmployeeInfoByRole(employeeRoleID string) ([]entity.Employee, error) {
	functionName := "GetEmployeeInfoByRole()"
	if pool == nil {
		pool = GetPool()
	}
	var response []entity.Employee
	queryString := `SELECT emp.id as id, emp.first_name as firstName,emp.last_name as lastName, emp.active_directory as activeDirectory FROM "user" emp
	WHERE emp.is_active = true AND emp.is_deleted = false AND emp.user_role_id = $1`
	rows, err := pool.Query(context.Background(), queryString, employeeRoleID)
	logengine.GetTelemetryClient().TrackEvent("GetEmployeeInfoByRole query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var values entity.Employee
		rows.Scan(&values.ID, &values.FirstName, &values.LastName, &values.ActiveDirectory)
		response = append(response, values)
	}
	return response, nil
}

func GetUserCountryByActiveDir(activeDir string) (*int64, error) {
	functionName := "GetUserCountryByActiveDir()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int64
	query := `SELECT country FROM "user" WHERE active_directory = $1 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), query, activeDir).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetUserCountryByActiveDir query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &countryID, nil
}

func GetUserCountryByID(userID uuid.UUID) (int, error) {
	functionName := "GetUserCountryByID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `SELECT country FROM "user" WHERE id = $1 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetUserCountryByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func GetUserRoleIdByUserRole(userRole string) (string, error) {
	functionName := "GetUserRoleIdByUserRole()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var userRoleID string
	query := `select id from user_roles where value = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userRole).Scan(&userRoleID)
	logengine.GetTelemetryClient().TrackEvent("GetUserRoleIdByUserRole query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return userRoleID, nil
}

func GetCountryIDByUserID(userID string) (int, error) {
	functionName := "GetGroupIDByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `select country from "user" 
	where id = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetCountryIDByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}
func CheckActivDirNameInUserForACountry(activeDir string, userID *string, country int) bool {
	functionName := "CheckActivDirNameInUserForACountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	if userID != nil {
		querystring := `select 1 from "user" ur where upper(ur.active_directory)  = upper($1)  and ur.country = $2 and ur.id <> $3
		and ur.is_deleted = false`
		var hasValue int
		err := pool.QueryRow(context.Background(), querystring, activeDir, country, *userID).Scan(&hasValue)
		if err == nil && hasValue > 0 {
			result = true
		}
	} else {
		querystring := `select 1 from "user" ur where upper(ur.active_directory)  = upper($1)  and ur.country = $2
		and ur.is_deleted = false`
		var hasValue int
		err := pool.QueryRow(context.Background(), querystring, activeDir, country).Scan(&hasValue)
		if err == nil && hasValue > 0 {
			result = true
		}
	}
	return result
}

func UserIsAdmin(employeeID string) bool {
	functionName := "UserIsAdmin()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `SELECT rp.master_data FROM "user" u 
	INNER JOIN roles_permission rp ON rp.user_role_id = u.user_role_id 
	WHERE u.id = $1`
	var result bool
	err := pool.QueryRow(context.Background(), query, employeeID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("UserIsAdmin query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
	}
	return result
}

func EmployeeCount(country int) (int, error) {
	functionName := "EmployeeCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `	SELECT count(id) FROM "user" WHERE is_deleted = false and country = $1`
	var result int
	err := pool.QueryRow(context.Background(), query, country).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("EmployeeCount query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return result, nil
}

func GetExportEmployeeAuditLogsData(input *entity.EmployeeAuditLogsEntity) ([]*model.EmployeeAuditLogsData, error) {
	functionName := "GetEmployeeExcelInfo()"
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select uml.id,uml.user_id ,uml.first_name ,uml.last_name ,uml.email ,
	uml.active_directory ,uml.employee_code ,ur.title ,c.title as country,uml.action_type  ,
	concat(u2.first_name ,' ',u2.last_name ) as action_by ,(EXTRACT(EPOCH FROM uml.action_date::timestamp)::integer)::text  from user_management_logs uml 
	inner join "user" u on  u.id =uml.user_id 
	inner join "user" u2 on u2.id =uml.action_by 
	inner join code c on c.id =u.country 
	inner join user_roles ur on ur.id =uml.user_role 
	where u.country =?
	`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, input.Country)
	if input.SearchItem != "" {
		queryString += ` AND ( concat(u2.first_name ,' ',u2.last_name ) ILIKE ? or uml.action_type ILIKE ? or
			concat(uml.first_name,' ',uml.last_name) ILIKE ? or   uml.active_directory ILIKE ? or 
			uml.first_name ILIKE ? or uml.last_name ILIKE ?  or
			ur.title ILIKE ?) `
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.UserID != nil && *(input).UserID != uuid.Nil {
			queryString += ` AND uml.user_id  = ? `
			inputArgs = append(inputArgs, input.UserID)
		}
		if input.ActiveDirectoryName != "" {
			queryString += ` AND uml.active_directory = ? `
			inputArgs = append(inputArgs, input.ActiveDirectoryName)
		}
		if input.FirstName != "" {
			queryString += ` AND uml.first_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.FirstName+"%")
		}
		if input.LastName != "" {
			queryString += ` AND uml.last_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.LastName+"%")
		}
		if input.Email != "" {
			queryString += ` AND uml.email ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Email+"%")
		}
		if input.ActionBy != "" {
			queryString += ` AND concat(u2.first_name ,' ',u2.last_name ) = ? `
			inputArgs = append(inputArgs, input.ActionBy)
		}
		if input.ActionType != "" {
			queryString += ` AND uml.action_type = ? `
			inputArgs = append(inputArgs, input.ActionType)
		}
		if input.ActionStartDate != "" || input.ActionEndDate != "" {
			if input.ActionStartDate != "" && input.ActionEndDate != "" {
				queryString = queryString + ` and (uml.action_date::date<= ? and uml.action_date::date>= ?)`
				inputArgs = append(inputArgs, input.ActionEndDate, input.ActionStartDate)
			} else if input.ActionStartDate != "" && input.ActionEndDate == "" {
				queryString = queryString + ` and (uml.action_date::date>= ?)`
				inputArgs = append(inputArgs, input.ActionStartDate)
			} else if input.ActionStartDate == "" && input.ActionEndDate != "" {
				queryString = queryString + ` and (uml.action_date::date<= ?)`
				inputArgs = append(inputArgs, input.ActionEndDate)
			}
		}
	}
	if input.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY uml.action_date desc, id asc`
	}
	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ?
		`
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetEmployeeExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var employees []*model.EmployeeAuditLogsData
	for rows.Next() {
		employee := &model.EmployeeAuditLogsData{}
		err := rows.Scan(&employee.ID, &employee.UserID, &employee.FirstName, &employee.LastName, &employee.Email, &employee.ActiveDirectoryName, &employee.EmployeeCode, &employee.RoleTitle, &employee.Country, &employee.ActionType, &employee.ActionBy, &employee.ActionDate)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return employees, err
		}
		employees = append(employees, employee)
	}
	return employees, nil

}

func InsertIntoUploadDb(url string, filename string, excelType string, author uuid.UUID) {
	log.Println("InsertUploadLogs()")
	if pool == nil {
		pool = GetPool()
	}
	var u2 pgtype.UUID
	querystring := "INSERT INTO upload (blob_url, blob_name, type, created_by, status) VALUES($1, $2, $3, $4, $5) RETURNING(id)"
	err := pool.QueryRow(context.Background(), querystring, url, filename, excelType, author, "Write Success").Scan(&u2)
	if err != nil {
		log.Println(err)
	}
	mybyte := u2.Get().([16]byte)
	uuidstr := util.UUIDV4ToString(mybyte)
	log.Println("InsertUploadLogs(): uuidstr: " + uuidstr)
}
func ProxyUpdateLoggedInUser(input *entity.ProxyUserAuth) error {
	functionName := "ProxyUpdateLoggedInUser()"
	if pool == nil {
		pool = GetPool()
	}
	for _, userSelection := range input.AccessControl {
		query := `UPDATE "user" set last_login=now(),modified_by=$2,last_modified=now() where id =$1`
		_, err := pool.Exec(context.Background(), query, userSelection.ID, userSelection.ID)
		logengine.GetTelemetryClient().TrackEvent("ProxyUpdateLoggedInUser query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return err
		}
	}
	return nil
}
