CREATE TABLE "approval_role_management" (
	"id" "uuid" default public.uuid_generate_v4(),
	"activity" "uuid",
	"approval_role" BIGINT,
	"group_type" BIGINT,
	"created_by" "uuid",
	"last_modified" "timestamptz",
	"modified_by" "uuid",
	"is_active" BOOLEAN,
	"is_deleted" BOOLEAN,
	"sequence_no" BIGINT,
	"date_created" "timestamptz"	
);

ALTER TABLE "approval_role_management" ALTER COLUMN "id" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "activity" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "approval_role" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "group_type" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "date_created" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "sequence_no" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "is_active" SET NOT NULL;
ALTER TABLE "approval_role_management" ALTER COLUMN "is_deleted" SET NOT NULL;

ALTER TABLE "approval_role_management" ADD CONSTRAINT fk_activity FOREIGN KEY (activity) REFERENCES "activity"(id);
