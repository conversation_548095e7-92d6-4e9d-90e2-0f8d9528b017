DELETE FROM approval_role_management
where country = 7;


INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 418, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 418, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 418, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 418, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));


INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
	 (47,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,93,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,93,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,93,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,93,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,93,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,93,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,93,false,NULL,NULL,7,true),
	 (48,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,96,false,53,56,7,false),
	 (49,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,96,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,96,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,96,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,96,false,NULL,NULL,7,true),
	 (54,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,96,false,NULL,NULL,7,false),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,96,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,98,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,98,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,98,true,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,98,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,98,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,98,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,98,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,98,false,NULL,NULL,7,true),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,92,false,50,55,7,false),
	 (51,56,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,92,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,92,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,92,false,NULL,NULL,7,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[0,50000]',0,50000,100,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,50000]',0,50000,100,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[0,50000]',0,50000,100,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,50000]',0,50000,100,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,50000]',0,50000,100,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,50000]',0,50000,100,false,NULL,NULL,7,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,100,false,53,56,7,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,100,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,100,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,100,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,100,false,NULL,NULL,7,true),
	 (54,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,100,false,NULL,NULL,7,false),
	 (413,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,100,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,98,false,NULL,NULL,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,98,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,98,false,53,56,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,98,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,98,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,98,false,NULL,NULL,7,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,98,false,NULL,NULL,7,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,98,false,NULL,NULL,7,true),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,1499]',0,1499,86,true,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,3,'[0,1499]',0,1499,86,true,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,4,'[0,1499]',0,1499,86,true,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,5,'[0,1499]',0,1499,86,true,NULL,NULL,7,true),
	 (51,56,NULL,NULL,NULL,true,false,2,'[0,1499)',0,1499,94,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,3,'[0,1499)',0,1499,94,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,4,'[0,1499)',0,1499,94,false,NULL,NULL,7,true);
	
	
INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 7, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 7, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));


INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
(47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,89,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,89,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,89,true,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,89,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,89,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,89,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,89,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,89,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,89,false,NULL,NULL,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,89,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,89,false,53,56,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,89,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,89,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,89,false,NULL,NULL,7,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,89,false,NULL,NULL,7,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,89,false,NULL,NULL,7,true);
	 
	
INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
(47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,420,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,420,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,420,true,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,420,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,420,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,420,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,420,false,50,55,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,420,false,NULL,NULL,7,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,420,false,NULL,NULL,7,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,420,false,NULL,NULL,7,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,420,false,53,56,7,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,420,false,NULL,NULL,7,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,420,false,NULL,NULL,7,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,420,false,NULL,NULL,7,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,420,false,NULL,NULL,7,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,420,false,NULL,NULL,7,true);
	 
	
	  UPDATE approval_role_management SET department = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active = true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.approval_role = subquery.id and approval_role_management.country =7

    UPDATE approval_role_management SET alternate_department_id = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active =true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.alternate_role = subquery.id and approval_role_management.country =7