package ad_service

import (
	"context"
	"errors"
	"fmt"
	"log"
	"strings"
)

func (s *Service) bind(ctx context.Context) error {
	bindReqResult := make(chan error, 1)

	go func(rs chan<- error) {
		var resErr error
		var tryCount int
		for {
			tryCount++
			if tryCount > 5 {
				rs <- resErr
			}
			err := s.internalConn.Bind(s.internalUsername, s.internalPassword)
			if err != nil {
				resErr = err
				if strings.Contains(err.Error(), `LDAP Result Code 49`) { // Wrong env username/password
					panic(err)
				}

				s.reDial()
				continue
			}

			err = s.externalConn.Bind(s.externalUsername, s.externalPassword)
			if err != nil && !strings.Contains(err.Error(), `LDAP Result Code 49`) {
				resErr = err
				if strings.Contains(err.Error(), `LDAP Result Code 49`) { // Wrong env username/password
					panic(err)
				}

				s.reDial()
				continue
			}

			break
		}

		bindReqResult <- nil
	}(bindReqResult)

	select {
	case err := <-bindReqResult:
		return err
	case <-ctx.Done():
		log.Println(fmt.Sprintf(`--- %v - Redialing...`, ctx.Err()))
		s.reDial()
		return errors.New("connection binding timed out")
	}

	return nil
}
