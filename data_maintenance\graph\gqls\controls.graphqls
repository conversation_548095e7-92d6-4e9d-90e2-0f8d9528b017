type Control {
    id: String!
    maxLimit: Int!
    category:  String!
    type: String!
    description: String!
	status: String!
    country:String!
}

input ControlInput {
    id: String
    isDelete: Boolean
    maxLimit: Int
}

input ControlRequest{
    id:String
    category: String
    type: String
    description: String
	status: String
    limit:Int
    PageNo:Int
}

type ControlResponse{
    error: Boolean!
    message: String!
    TotalCount:Int
    data: [Control]!
}

type UpsertControlResponse{
    error: Boolean!
    message: String!
}

extend type Query {
    controls(input: ControlRequest!): ControlResponse!
}

extend type Mutation {
    upsertControls(input: ControlInput!): UpsertControlResponse!
}

