package controller

import (
	"bufio"
	"bytes"

	"github.com/360EntSecGroup-Skylar/excelize"
)

func getBytesFromFile(f *excelize.File) []byte {
	var excelBytes bytes.Buffer
	writer := bufio.NewWriter(&excelBytes)
	f.Write(writer)
	writer.Flush()
	return excelBytes.Bytes()
}

func getColumnName(i int) string {
	value := ""
	multiples := i / 26
	for count := 0; count < multiples; count++ {
		value = value + "A"
	}
	i = i % 26
	value = value + string(rune('A'-1+i))
	return value
}
