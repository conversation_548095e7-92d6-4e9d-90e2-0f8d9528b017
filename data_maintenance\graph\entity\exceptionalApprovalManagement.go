package entity

import (
	uuid "github.com/satori/go.uuid"
)

type UpsertExceptionalApprovalManagementEntity struct {
	ID         *uuid.UUID
	UserID     *uuid.UUID
	UserRole   *string
	Country    *int
	Department *string
	IsDelete   bool
	SequenceNo *int
	GroupId    *int
}

type FetchUpsertExceptionalApprovalManagement struct {
	UserRole   *string
	Country    *int
	Department *string
}
type FetchExceptionDetails struct {
	UserID       *uuid.UUID
	UserCountry  *int
	IsCompliance *bool
}
type OutputFetchEntity struct {
	UserId         *string
	UserName       *string
	UserRole       *string
	UserRoleValue  *string
	UserRoleTitle  *string
	DepartmentId   *string
	DepartmentName *string
	SequenceNo     *int
	GroupType      *int
}
type Departmentid struct {
	DepartmentId *string
}
