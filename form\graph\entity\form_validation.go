package entity

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/maptool_service"
	"github.com/ihcp/form/graph/model"
	"github.com/jackc/pgtype"
	"log"
	"reflect"
	"slices"
	"strconv"
)

type DataCollection struct {
	EventRequesterAD    string
	EventOwnerAD        string
	EventOwner          string //Name
	ActivityName        string
	ActivityType        string
	ActivityTypeName    string
	EventType           string
	LedBy               string
	Country             string
	Status              string
	StartDate           string
	EndDate             string
	ActivityDuration    string
	Location            string //Venue
	MeetingMode         string
	VirtualEvent        string
	VirtualEventDetails string
	MultiProducts       string // [primary, secondary, tertiary]
	Products            string // old version of multi product
	EventOrganizer      string

	NoOfHCPs           string
	NoOfNonHCP         string
	NoOfEmployee       string
	TargetAudience     string
	MaterialCodeNumber string
	MeetingObjective   string
	GeneralRemark      string
	AttendeeAttachment []string
	ProposalAttachment []string

	OrganizationName  string
	HCONature         string
	ContactPersonName string

	GrantPurpose             string
	DDQDocuments             string
	RequestDocuments         string
	ScreeningDocuments       string
	RedFlag                  string
	RedFlagDetails           string
	HasRedFlagResolved       string
	RedFlagResolutionDetails string
	OwnedByGovernment        string
	GovernmentDetails        string
	ZuelligPharmaBenefit     string
	ZPBenefitDetails         string
	TherapeuticArea          string
	AgendaAttachmentURLs     []string

	HCP      []*EventHCP
	Expenses []*EventExpense
}

var LedByOptions []string = []string{
	"Sales (FF)",
	"Marketing",
	"Medical",
}

func (f FormAnswerEntity) GetEventExpenses() []*EventExpense {
	dataCollected := &DataCollection{}
	browseThroughTheInputAndCollectData(f.Design, dataCollected, "expense")

	return dataCollected.Expenses
}

func (f FormAnswerEntity) GetEventHCPs() []*EventHCP {
	dataCollected := &DataCollection{}
	browseThroughTheInputAndCollectData(f.Design, dataCollected, "hcp")

	return dataCollected.HCP
}

func (f FormAnswerEntity) GetEventDetails(ctx context.Context) *DataCollection {
	dataCollected := &DataCollection{}
	browseThroughTheInputAndCollectData(f.Design, dataCollected, "")

	return dataCollected
}

func (f FormAnswerEntity) Validate(countryID int, isApollo bool) error {
	dataCollected := &DataCollection{}
	browseThroughTheInputAndCollectData(f.Design, dataCollected, "")

	if dataCollected.EventOwner == "" {
		return errors.New("invalid event owner input. Please try to select event owner again")
	} else if dataCollected.ActivityDuration == "" {
		return errors.New("invalid activity duration input. Please try to select again")
	} else if dataCollected.Country == "invalid" {
		return errors.New("invalid country input. Please try to select country again")
	} else if _, ok := ExtractProducts(dataCollected.MultiProducts); !ok {
		return errors.New("invalid products input. Please try to select clients and products again")
	} else if countryID == 400 && !slices.Contains[[]string](LedByOptions, dataCollected.LedBy) {
		return errors.New("invalid led by input. Please try to select 'Led By' again")
	} else if dataCollected.ActivityType != "99" && dataCollected.TherapeuticArea == "" {
		return errors.New("therapeutic area input can not be blank")
	}

	if dataCollected.ActivityType == "99" { // Educational Grants to HCO
		if dataCollected.OrganizationName == "" {
			return errors.New("invalid organization name input")
		} else if dataCollected.HCONature == "" {
			return errors.New("invalid nature of HCO/HCI input")
		} else if dataCollected.ContactPersonName == "" {
			return errors.New("invalid name of contact person input")
		} else if dataCollected.GrantPurpose == "" {
			return errors.New("invalid purpose of grant input")
		} else if dataCollected.DDQDocuments == "" {
			return errors.New("please upload the DDQ document to proceed")
		} else if dataCollected.RequestDocuments == "" {
			return errors.New("please upload the request document to proceed")
		} else if dataCollected.ScreeningDocuments == "" {
			return errors.New("please upload the screening document to proceed")
		} else if dataCollected.OwnedByGovernment == "" {
			return errors.New("invalid government owned input")
		} else if dataCollected.OwnedByGovernment == "true" && dataCollected.GovernmentDetails == "" {
			return errors.New("invalid government details input")
		} else if dataCollected.RedFlag == "" {
			return errors.New("invalid red flags input")
		} else if dataCollected.RedFlag == "true" && (dataCollected.RedFlagDetails == "" || dataCollected.HasRedFlagResolved == "") {
			return errors.New("invalid red flags details or resolved input")
		} else if dataCollected.HasRedFlagResolved == "true" && dataCollected.RedFlagResolutionDetails == "" {
			return errors.New("invalid red flags resolved details input")
		} else if dataCollected.ZuelligPharmaBenefit == "" {
			return errors.New("invalid zuellig pharma benefit input")
		} else if dataCollected.ZuelligPharmaBenefit == "true" && dataCollected.ZPBenefitDetails == "" {
			return errors.New("invalid zuellig pharma benefit details input")
		}
	}

	return nil
}

func ExtractProducts(s string) ([]map[string]string, bool) {
	if len(s) == 0 {
		return nil, true
	}

	var r []map[string]string
	if err := json.Unmarshal([]byte(s), &r); err != nil {
		log.Println("extractProducts", err)
		return nil, false
	}

	return r, true
}

func browseThroughTheInputAndCollectData(input pgtype.JSONB, d *DataCollection, opt string) {
	formSection := input.Get()
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {

		if opt == "" { // default - will read through the whole event
			for _, item := range formSection.([]interface{}) {
				v = reflect.ValueOf(item)
				if v.Kind() == reflect.Map {
					for _, key := range v.MapKeys() {
						strct := v.MapIndex(key).Interface()
						switch key.Interface() {
						case "sectionAnswer":
							if strct != nil {
								getFormAnswerSectionValueFromMap(strct, d)
							}
						}
					}
				}
			}
		} else if opt == "hcp" {
			a := formSection.([]interface{})
			item := a[2]
			// this contains hcp data
			// logic implemented below is customized for hcp data only. So it skips some validation.
			v = reflect.ValueOf(item)
			for _, key := range v.MapKeys() {
				if key.Interface() == `sectionAnswer` {
					sectionAnswer := v.MapIndex(key).Interface().([]interface{})
					item := sectionAnswer[0]
					v = reflect.ValueOf(item)

					hcps, expenses := extractEventHCPData(v)
					for _, v := range expenses {
						hcps[v.HcpSequence].Expenses = append(hcps[v.HcpSequence].Expenses, v)
					}

					d.HCP = hcps
				}
			}
		} else if opt == "expense" {
			a := formSection.([]interface{})
			item := a[3] // expense
			v = reflect.ValueOf(item)
			for _, key := range v.MapKeys() {
				if key.Interface() == `sectionAnswer` {
					sectionAnswer := v.MapIndex(key).Interface().([]interface{})
					item := sectionAnswer[0]
					v = reflect.ValueOf(item)
					expenses := extractEventExpense(v)
					d.Expenses = expenses
				}
			}
		}
	}
}

type HCPExpense struct {
	HcpSequence int
	ExpenseType string   `json:"expense_type"`
	TotalCost   string   `json:"total_cost"`
	Remark      string   `json:"remark"`
	Description string   `json:"description"`
	Attachment  []string `json:"attachment"`
}

type EventHCP struct {
	EngagementType            string                     `json:"engagement_type"`
	IndividualCategory        string                     `json:"individual_category"`
	HcpType                   string                     `json:"hcp_type"`
	GovtType                  string                     `json:"govt_type"`
	HcpName                   string                     `json:"hcp_name"`
	Specialty                 string                     `json:"specialty"`
	HcoName                   string                     `json:"hco_name"`
	BaseType                  string                     `json:"base_type"`
	InfluenceLevel            string                     `json:"influence_level"`
	ExpertLevel               string                     `json:"expert_level"`
	Role                      string                     `json:"role"`
	Remark                    string                     `json:"remark"`
	Attachment                []string                   `json:"attachment"`
	PreparationTime           string                     `json:"preparation_time"`
	ServiceTime               string                     `json:"service_time"`
	Currency                  string                     `json:"currency"`
	MaxPayableHonorarium      string                     `json:"max_payable_honorarium"`
	ProposedPayableHonorarium string                     `json:"proposed_payable_honorarium"`
	TotalCost                 string                     `json:"total_cost"`
	Expenses                  []*maptool_service.Expense `json:"expenses"`
}

// func extract
type Question struct {
	QuestionID string
	Answers    []struct {
		Value       string
		Description string
	}
}

func extractEventHCPData(HCPSectionData reflect.Value) ([]*EventHCP, []*maptool_service.Expense) {
	var rs []*EventHCP
	var expenses []*maptool_service.Expense
	v := HCPSectionData

	for _, key := range v.MapKeys() {
		strct := v.MapIndex(key).Interface()
		switch key.Interface() {
		case "form": // Form contains HCP data
			v := reflect.ValueOf(strct)
			for _, key := range v.MapKeys() {
				strct := v.MapIndex(key).Interface()
				switch key.Interface() {
				case "groupAnswer":
					v := reflect.ValueOf(strct)
					for _, hcp := range strct.([]interface{}) {
						m := make(map[string][]string)
						v = reflect.ValueOf(hcp)
						for _, key := range v.MapKeys() {
							strct := v.MapIndex(key).Interface()
							switch key.Interface() {
							case "questionAnswers":
								v := reflect.ValueOf(strct)
								for _, item := range strct.([]interface{}) { // loop through each answer
									v = reflect.ValueOf(item)
									for _, key := range v.MapKeys() {
										strct := v.MapIndex(key).Interface()
										switch key.Interface() {
										case "answers":
											v := reflect.ValueOf(strct)
											for _, item := range strct.([]interface{}) {
												v = reflect.ValueOf(item)
												for _, key := range v.MapKeys() {
													obj := v.MapIndex(key).Interface()
													if obj == nil {
														continue
													}

													answers := extractAnswerData(v)
													switch key.Interface() {
													case "id":
														id := obj.(string)
														//assignAnswerToField(id, answers, d)
														var ansStringList []string
														for _, ans := range answers {
															ansStringList = append(ansStringList, nilToBlank(ans.Value))
														}

														if val, ok := m[id]; ok {
															m[id] = append(val, ansStringList...)
														} else {
															m[id] = ansStringList
														}
													}
												}
											}
										}
									}
								}
							}
						}

						nilIsBlank := func(a []string) string {
							if len(a) == 0 {
								return ""
							}
							return a[0]
						}
						rs = append(rs, &EventHCP{
							EngagementType:            nilIsBlank(m["typeOfEngagement"]),
							IndividualCategory:        nilIsBlank(m["individualCategory"]),
							HcpType:                   nilIsBlank(m["type-of-hcp"]),
							GovtType:                  nilIsBlank(m["govt-or-non-govt-hcp"]),
							HcpName:                   nilIsBlank(m["speaker-name"]),
							Specialty:                 nilIsBlank(m["specialty"]),
							HcoName:                   nilIsBlank(m["hco-institute-name"]),
							BaseType:                  nilIsBlank(m["base-type"]),
							InfluenceLevel:            nilIsBlank(m["level-of-influence"]),
							ExpertLevel:               nilIsBlank(m["expert-level-text"]),
							Role:                      nilIsBlank(m["specialty"]),
							Remark:                    nilIsBlank(m["remarks"]),
							Attachment:                m["upload"],
							PreparationTime:           nilIsBlank(m["preparation-time"]),
							ServiceTime:               nilIsBlank(m["service-time"]),
							MaxPayableHonorarium:      nilIsBlank(m["calculated-honorarium"]),
							ProposedPayableHonorarium: nilIsBlank(m["proposed-honorarium"]),
							Currency:                  "Rp",
							TotalCost:                 "0",
							//Expenses:                  nil,
						})
					}
				}
			}
		case "childForm": // childForm contains hcps' expenses data mapped by 'HcpSequence'
			getData := func(item interface{}, t string) Question {
				var rs Question
				rs.QuestionID = t
				v = reflect.ValueOf(item)
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					if key.Interface().(string) == "answers" {
						v := reflect.ValueOf(strct)
						for _, item := range strct.([]interface{}) {
							v = reflect.ValueOf(item)
							answers := extractAnswerData(v)
							for _, v := range answers {
								rs.Answers = append(rs.Answers, struct {
									Value       string
									Description string
								}{
									Value:       *v.Value,
									Description: *v.Description,
								})
							}
						}

					}

				}
				return rs
			}
			v := reflect.ValueOf(strct)
			for _, key := range v.MapKeys() {
				strct := v.MapIndex(key).Interface()
				switch key.Interface() {
				case "groupAnswer":
					v := reflect.ValueOf(strct)
					for _, hcp := range strct.([]interface{}) {
						// loop through each hcp expense
						var sequenceNo int
						m := make(map[string]*Question)
						v = reflect.ValueOf(hcp)
						for _, key := range v.MapKeys() {
							strct := v.MapIndex(key).Interface()

							switch key.Interface() {
							case "sequenceNo":
								sequenceNo = int(strct.(float64))
							case "questionAnswers":
								//v := reflect.ValueOf(strct)

								items := strct.([]interface{})
								q1 := getData(items[0], "Registration Fee")
								m[q1.QuestionID] = &q1
								q2 := getData(items[1], "Transportation")
								m[q2.QuestionID] = &q2
								q3 := getData(items[2], "Meal Package")
								m[q3.QuestionID] = &q3
								q4 := getData(items[4], "Accommodation")
								m[q4.QuestionID] = &q4
								q5 := getData(items[6], "Other Expense")
								m[q5.QuestionID] = &q5
								q6 := getData(items[7], "Total Cost")
								m[q6.QuestionID] = &q6

							}
						}
						for k, v := range m {
							q := k

							valid := false
							totalCost := 0.0
							description := ""
							remark := ""
							attachment := []string{}
							for _, vv := range v.Answers {
								if vv.Value != "" && vv.Value != `0.00` && vv.Value != `0` && vv.Value != "false" {
									valid = true
									if q != "Total Cost" {
										if vv.Description == "Total Cost" || vv.Description == "Registration Fee" || vv.Description == "Ground Transportation" ||
											vv.Description == "Airfare" {
											s, err := strconv.ParseFloat(vv.Value, 64)
											if err != nil {
												panic(err)
											}
											totalCost += s
										}
										if vv.Description == "Remarks" {
											remark = vv.Value
										} else if vv.Description == "Upload" {
											attachment = append(attachment, vv.Value)
										} else {
											if vv.Description != `Do you want to add any attachment?` {
												description += fmt.Sprintf("%v: %v\n", vv.Description, vv.Value)
											}
										}
									}
								}
							}

							if q != "Total Cost" && valid {
								expenses = append(expenses, &maptool_service.Expense{
									HcpSequence: sequenceNo,
									ExpenseType: q,
									TotalCost:   fmt.Sprintf("%f", totalCost),
									Remark:      remark,
									Description: description,
									Attachment:  attachment,
								})
							}
						}

					}
				}
			}
		}
	}

	return rs, expenses
}

func extractEventExpense(expenseSectionData reflect.Value) []*EventExpense {
	var rs []*EventExpense
	getData := func(item interface{}) []string {
		var rs []string
		v := reflect.ValueOf(item)
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			if key.Interface().(string) == "answers" {
				v := reflect.ValueOf(strct)
				for _, item := range strct.([]interface{}) {
					v = reflect.ValueOf(item)
					answers := extractAnswerData(v)
					for _, v := range answers {
						rs = append(rs, *v.Value)
					}
				}

			}

		}

		return rs
	}

	type Attachment struct {
		Files  []string
		Remark string
	}
	getAttachmentData := func(item interface{}) Attachment {
		var rs Attachment
		v := reflect.ValueOf(item)
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			if key.Interface().(string) == "answers" {
				v := reflect.ValueOf(strct)
				for _, item := range strct.([]interface{}) {
					v = reflect.ValueOf(item)
					answers := extractAnswerData(v)
					for _, v := range answers {

						if *v.Description == "Event Expense Attachment" {
							rs.Files = append(rs.Files, *v.Value)
						}

						if *v.Description == "Remarks" {
							rs.Remark = *v.Value
						}
					}
				}

			}

		}

		return rs
	}

	type Expense struct {
		Description string
		Total       float64
	}

	getExpenseData := func(item interface{}) Expense {
		var rs Expense
		v := reflect.ValueOf(item)
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			var ans []model.ValuesAnswer
			if key.Interface().(string) == "answers" {
				v := reflect.ValueOf(strct)
				for _, item := range strct.([]interface{}) {
					v = reflect.ValueOf(item)
					answers := extractAnswerData(v)
					for _, v := range answers {
						if *v.Description == "Total Cost" {
							val, err := strconv.ParseFloat(*v.Value, 64)
							if err != nil {
								panic(err)
							}
							rs.Total += val
						}

						ans = append(ans, model.ValuesAnswer{
							Description: v.Description,
							Value:       v.Value,
						})
						//fmt.Println("---------------", *v.Description, *v.Value)
					}
				}

			}
			for j := 0; j < len(ans); j++ {
				if *ans[j].Value == "" {
					continue
				}

				rs.Description += fmt.Sprintf(`%s - %s\n`, *ans[j].Description, *ans[j].Value)

			}
		}

		return rs
	}

	v := expenseSectionData
	for _, key := range v.MapKeys() {
		strct := v.MapIndex(key).Interface()
		switch key.Interface() {
		case "form":
			v := reflect.ValueOf(strct)
			for _, key := range v.MapKeys() {
				strct := v.MapIndex(key).Interface()
				switch key.Interface() {
				case "groupAnswer":
					v := reflect.ValueOf(strct)
					for _, hcp := range strct.([]interface{}) {
						m := make(map[string][]string)
						v = reflect.ValueOf(hcp)

						for _, key := range v.MapKeys() {
							strct := v.MapIndex(key).Interface()
							switch key.Interface() {
							case "questionAnswers":
								//v := reflect.ValueOf(strct)

								items := strct.([]interface{})
								q1 := getData(items[0])
								m["Type of Event Expenses"] = q1

								q2 := getData(items[1])
								m["Type of Meeting Package"] = q2

								q4 := getAttachmentData(items[3])
								m["Event Expense Attachment"] = q4.Files
								eventRemark := q4.Remark

								q6 := getData(items[5])
								m["Expense will be paid to HCO"] = q6

								q7 := getExpenseData(items[6])

								total := q7.Total
								var category string = m["Type of Event Expenses"][0]
								if nilToBlank(&m["Type of Event Expenses"][1]) != "" {
									category = fmt.Sprintf(`%s - %s`, category, m["Type of Event Expenses"][1])
								}

								if nilToBlank(&m["Type of Event Expenses"][2]) != "" {
									category = fmt.Sprintf(`%s - %s`, category, m["Type of Event Expenses"][2])
								}

								ss := EventExpense{
									Category:    m["Type of Event Expenses"][0],
									TotalCost:   fmt.Sprintf(`%f`, total),
									Attachment:  m["Event Expense Attachment"],
									Remark:      eventRemark,
									Description: q7.Description,
								}
								rs = append(rs, &ss)

							}

						}

					}
				}
			}
		}
	}
	return rs
}

func getFormAnswerSectionValueFromMap(formSection interface{}, d *DataCollection) {
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "form":
						if strct != nil {
							getFormQuestionAnswerFromMap(strct, d)
						}
					case "childForm":
						if strct != nil {
							getFormQuestionAnswerFromMap(strct, d)
						}
					}
				}
			}
		}
	}
}
func getFormQuestionAnswerFromMap(questions interface{}, d *DataCollection) {
	v := reflect.ValueOf(questions)
	if v.Kind() == reflect.Map {
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			switch key.Interface() {
			case "groupAnswer":
				if strct != nil {
					getQuestionGroupFromAnswerInterfaceMap(strct, d)
				}
			}
		}
	}
}
func getQuestionGroupFromAnswerInterfaceMap(groups interface{}, d *DataCollection) {
	v := reflect.ValueOf(groups)
	if v.Kind() == reflect.Slice {
		for _, item := range groups.([]interface{}) {
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "questionAnswers":
						if strct != nil {
							getGroupFromAnswerInterfaceMap(strct, d)
						}
					}
				}
			}
		}
	}
}
func getGroupFromAnswerInterfaceMap(inputGroup interface{}, d *DataCollection) {
	v := reflect.ValueOf(inputGroup)
	if v.Kind() == reflect.Slice {
		for _, item := range inputGroup.([]interface{}) {
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "answers":
						if strct != nil {
							getInputsAnswersFromInterfaceMap(strct, d)
						}
					}
				}
			}
		}
	}
}

type ValuesAnswer struct {
}

func extractAnswerData(v reflect.Value) []*model.ValuesAnswer {
	if v.Kind() != reflect.Map {
		return []*model.ValuesAnswer{}
	}
	for _, key := range v.MapKeys() {
		obj := v.MapIndex(key).Interface()
		switch key.Interface() {
		case "values":
			if obj != nil {
				return getValuesAnswersFromInterfaceMap(obj)
			}
		}
	}
	return nil
}

func getInputsAnswersFromInterfaceMap(inputs interface{}, d *DataCollection) {
	v := reflect.ValueOf(inputs)
	if v.Kind() == reflect.Slice {
		for _, item := range inputs.([]interface{}) {
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					//sectionID := ""
					obj := v.MapIndex(key).Interface()
					if obj == nil {
						continue
					}

					answers := extractAnswerData(v)
					switch key.Interface() {
					case "id":
						id := obj.(string)
						//fmt.Println("===========", id)
						assignAnswerToField(id, answers, d)

					}
				}
			}
		}
	}
}

func nilToBlank(s *string) string {
	if s != nil {
		return *s
	}

	return ""
}

func assignAnswerToField(id string, answers []*model.ValuesAnswer, d *DataCollection) {
	switch id {
	case "event-requestor":
		for _, ans := range answers {
			d.EventRequesterAD = *ans.Value
		}
	case "activity-name":
		for _, ans := range answers {
			d.ActivityName = *ans.Value
		}
	case "venue":
		for _, ans := range answers {
			d.Location = *ans.Value
		}
	case "meeting-mode":
		for _, ans := range answers {
			d.MeetingMode = *ans.Value
		}
	case "virtual-event":
		for _, ans := range answers {
			d.VirtualEvent = *ans.Value
		}
	case "details-of-the-virtual-event":
		for _, ans := range answers {
			d.VirtualEventDetails = *ans.Value
		}
	case "led-by":
		for _, ans := range answers {
			d.LedBy = *ans.Value
		}
	case "event-owner":
		for _, ans := range answers {
			d.EventOwner = nilToBlank(ans.Value)
			d.EventOwnerAD = nilToBlank(ans.ID)
		}
	case "duration-of-the-activity":
		for _, ans := range answers {
			d.ActivityDuration = *ans.Value
		}
	case "activity-start-date":
		for _, ans := range answers {
			d.StartDate = *ans.Value
		}
	case "activity-end-date":
		for _, ans := range answers {
			d.EndDate = *ans.Value
		}
	case "country":
		for _, ans := range answers {
			if *ans.Value != *ans.Description || *ans.Value == "" || *ans.Value == "Country" {
				d.Country = "invalid"
			}
			d.Country = *ans.Value
		}

	case "type-of-ihcp-activity":
		for _, ans := range answers {
			d.ActivityType = *ans.Value
			d.ActivityTypeName = *ans.Description
		}
	case "type-of-event":
		for _, ans := range answers {
			d.EventType = *ans.Description
		}
	case "name-of-organization":
		for _, ans := range answers {
			d.OrganizationName = *ans.Value
		}
	case "nature-of-hcohci":
		for _, ans := range answers {
			d.HCONature = *ans.Value
		}
	case "name-of-contact-person":
		for _, ans := range answers {
			d.ContactPersonName = *ans.Value
		}
	case "purpose-of-grant":
		for _, ans := range answers {
			d.GrantPurpose = *ans.Value
		}
	case "upload-documents-of-ddq":
		for _, ans := range answers {
			d.DDQDocuments = *ans.Value
		}
	case "upload-documents-of-request-letter":
		for _, ans := range answers {
			d.RequestDocuments = *ans.Value
		}
	case "upload-documents-of-screening-report":
		for _, ans := range answers {
			d.ScreeningDocuments = *ans.Value
		}
	case "any-red-flags?":
		for _, ans := range answers {
			d.RedFlag = *ans.Value
		}
	case "details-of-the-red-flags":
		for _, ans := range answers {
			d.RedFlagDetails = *ans.Value
		}
	case "has-the-red-flag-been-resolved?":
		for _, ans := range answers {
			d.HasRedFlagResolved = *ans.Value
		}
	case "has-the-red-flag-been-resolved-provide-more-details":
		for _, ans := range answers {
			d.RedFlagResolutionDetails = *ans.Value
		}
	case "is-the-recipient-owned-or-controlled-by-a-government-or-government-official":
		for _, ans := range answers {
			d.OwnedByGovernment = *ans.Value
		}
	case "details-of-the-recipient-owned-or-controlled-by-a-government":
		for _, ans := range answers {
			d.GovernmentDetails = *ans.Value
		}
	case "in-return-will-zuellig-pharma-be-benefiting-from-this-grant?":
		for _, ans := range answers {
			d.ZuelligPharmaBenefit = *ans.Value
		}
	case "details-of-the-zuellig-pharma-be-benefiting-from-this-grant":
		for _, ans := range answers {
			d.ZPBenefitDetails = *ans.Value
		}
	case "multi-product":
		for _, ans := range answers {
			d.MultiProducts = *ans.Value
		}
	case "product":
		for _, ans := range answers {
			d.Products = *ans.Value
		}
	case "therapeutic-area":
		for _, ans := range answers {
			d.TherapeuticArea = nilToBlank(ans.Value)
		}
	case "do-you-want-to-add-any-attachment-for-agenda-or-proposal":
		for _, ans := range answers {
			d.AgendaAttachmentURLs = append(d.AgendaAttachmentURLs, *ans.Value)
		}
	case "event-organizer":
		for _, ans := range answers {
			d.EventOrganizer = nilToBlank(ans.Value)
		}
	case "no-of-hcps":
		for _, ans := range answers {
			d.NoOfHCPs = nilToBlank(ans.Value)
		}
	case "no-of-non-hcps":
		for _, ans := range answers {
			d.NoOfNonHCP = nilToBlank(ans.Value)
		}
	case "employee-no":
		for _, ans := range answers {
			d.NoOfEmployee = nilToBlank(ans.Value)
		}

	case "target-audience":
		for _, ans := range answers {
			d.TargetAudience = nilToBlank(ans.Value)
		}
	case "material-code-number":
		for _, ans := range answers {
			d.MaterialCodeNumber = nilToBlank(ans.Value)
		}
	case "meeting-objective":
		for _, ans := range answers {
			d.MeetingObjective = nilToBlank(ans.Value)
		}
	case "general-remarks":
		for _, ans := range answers {
			d.GeneralRemark = nilToBlank(ans.Value)
		}
	case "attendees-attachments":
		for _, ans := range answers {
			d.AttendeeAttachment = append(d.AttendeeAttachment, *ans.Value)
		}
	case "proposal-attachments":
		for _, ans := range answers {
			d.ProposalAttachment = append(d.ProposalAttachment, *ans.Value)
		}
	}

}

func getValuesAnswersFromInterfaceMap(question interface{}) []*model.ValuesAnswer {
	var response []*model.ValuesAnswer
	v := reflect.ValueOf(question)
	if v.Kind() == reflect.Slice {
		for _, item := range question.([]interface{}) {
			var answer model.ValuesAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					obj := v.MapIndex(key).Interface()
					if obj == nil {
						continue
					}
					val := obj.(string)
					switch key.Interface() {
					case "id":
						answer.ID = &val
					case "description":
						answer.Description = &val
					case "value":
						answer.Value = &val
					}
				}
			}
			response = append(response, &answer)
		}
	}
	return response
}
