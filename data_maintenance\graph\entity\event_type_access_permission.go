package entity

import (
	"database/sql"

	uuid "github.com/satori/go.uuid"
)

type EventTypeAccessPermissionUpsert struct {
	ID                  *uuid.UUID
	EventType           int
	UserRole            []string
	CreatedBy           *uuid.UUID
	Country             int
	IsDelete            bool
	UserRoleForApprover []int
}

type EventAccessTypeEntity struct {
	UserRoles      []int
	EventTypeName  string
	EventTypeValue string
	ID             string
	CountryTitle   sql.NullString
	CountryValue   sql.NullString
	UserRoleID     []string
}

type UserRole struct {
	UserRoleTitle string
	UserRoleValue string
}
