package postgres

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"log"
	"os"
	"path"
	"strings"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/postgres/util"
	uuid "github.com/satori/go.uuid"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

func SendAttachmentMaileZClaim(attachments []string, emailFrom string, emailTo string, subject string, body string, meetingId string, FormAnswerID string) {
	api_key := os.Getenv("SENDGRID_API_KEY")
	sg := sendgrid.NewSendClient(api_key)
	var allerr string
	// Compose your message
	from := mail.NewEmail(emailFrom, emailFrom)
	// subject = subject
	to := mail.NewEmail(emailTo, emailTo)
	plainTextContent := meetingId
	htmlContent := ""
	message := mail.NewSingleEmail(from, subject, to, plainTextContent, htmlContent)

	// Attach files from URLs
	for _, blobURL := range attachments {
		data, err := util.DownloadExcelFile(blobURL) // Replace with your download function
		if err != nil {
			log.Printf("Error downloading file from %s: %v\n", blobURL, err)
			allerr += blobURL + " : " + err.Error()
			continue // Skip this attachment and proceed with others
		}

		fileName := meetingId + "-" + path.Base(blobURL)       // Generate a unique file name
		fileContent := base64.StdEncoding.EncodeToString(data) // Convert data to string (example)

		// Create an attachment object
		attachment := mail.NewAttachment()
		attachment.SetContent(fileContent)                // Set content of the attachment
		attachment.SetType("application/octet-stream")    // Set the MIME type of the attachment
		attachment.SetFilename(fileName)                  // Set the filename
		attachment.SetDisposition("attachment")           // Set the disposition as attachment
		attachment.SetContentID("Attachment-" + fileName) // Set content ID (optional)

		// Add attachment to the message
		message.AddAttachment(attachment)
	}

	// Send the email

	_, err := sg.Send(message)
	if err != nil {
		log.Println("Error sending email:", err)
		// return err
		allerr += " " + err.Error()
	}

	err = InsertEzClaimAttachmentEmailLogsForApollo(attachments, emailFrom, emailTo, subject, body, allerr, FormAnswerID)
	if err != nil {
		log.Println("SendAttachmentMaileZClaim----error:", err.Error())
	}
}
func InsertEzClaimAttachmentEmailLogsForApollo(attachments []string, emailFrom string, emailTo string, subject string, body string, allerr string, FormAnswerID string) error {
	functionName := "InsertEzClaimAttachmentEmailLogsForApollo()"
	if pool == nil {
		pool = GetPool()
	}
	var status bool
	var ezClaimEmailJsonMarsalData entity.EzCLaimEmailEventDetailesJSonDataForApollo
	ezClaimEmailJsonMarsalData.FromEmailAddress = emailFrom
	ezClaimEmailJsonMarsalData.ToEmailAddress = emailTo
	ezClaimEmailJsonMarsalData.Attachments = attachments
	ezClaimEmailJsonMarsalData.EmailBody = body
	ezClaimEmailJsonMarsalData.EmailSubject = subject
	ezClaimEmailJsonAllData, err := json.Marshal(ezClaimEmailJsonMarsalData)
	if err != nil {
		allerr += " " + err.Error()
	}
	if strings.TrimSpace(allerr) == "" {
		status = true
	}
	var inputargs []interface{}
	var eventLogId uuid.UUID
	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_EZCLAIM_EMAIL")
	querystring := `Insert into event_logs (form_answer_uid,status,	error_message,integration_type,is_apollo)
					Values($1,$2,$3,$4,$5)RETURNING id`
	inputargs = append(inputargs, FormAnswerID, status, allerr, intregationType, true)
	err = pool.QueryRow(context.Background(), querystring, inputargs...).Scan(&eventLogId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)

		log.Println(functionName)
		return err
	}
	query := `insert into event_log_details(event_log_uid,	event_details)
		          values($1,$2)`
	_, err = pool.Exec(context.Background(), query, eventLogId, ezClaimEmailJsonAllData)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)

		log.Println(functionName)
		return err
	}
	return nil
}
