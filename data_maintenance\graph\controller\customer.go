package controller

import (
	"context"
	"errors"
	"log"
	"reflect"
	"strings"
	"time"

	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/ihcp/data_maintenance/graph/azure"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func timeTrack(start time.Time, name string) {
	elapsed := time.Since(start)
	log.Printf("%s took %s", name, elapsed)
}
func ExportCustomerDetails(ctx *context.Context, input *model.CustomerInfoRequest) *model.CustomerExcelResponse {
	var response model.CustomerExcelResponse
	var userUUID uuid.UUID
	var err error
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", "ExportCustomerExcel", err.Error())
		}
	}
	exportCustomer, err := mapper.ExportCustomerInfoModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	customerCount, countErr := postgres.CustomerCount(exportCustomer.Country, exportCustomer.IsForm)
	if countErr != nil {
		response.Error = true
		response.Message = countErr.Error()
		return &response
	}
	customerInfo, err := postgres.GetCustomerInfo(input, exportCustomer)
	if err != nil {
		if err == pgx.ErrNoRows {
			response.Error = false
			response.Message = "No Customer data found!"
			return &response
		} else {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}

	outputModels := mapper.ExportCustomerInfoEntityToModel(customerInfo)
	response.Data = outputModels

	response.TotalCount = customerCount
	return &response
}
func ExportCustomerExcel(ctx *context.Context, input *model.CustomerExcelRequest) *model.CustomerExcelResponse {
	var response model.CustomerExcelResponse
	var userUUID uuid.UUID
	var err error
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var isForm bool
	if input.IsForm != nil {
		isForm = *input.IsForm
	}
	isAdmin := postgres.UserIsAdmin(*userID)
	if !isAdmin && input.IsExcel || (!isAdmin && !isForm) {
		// response.Error = true
		// response.Message = "User is not authorized to access"
		// return &response
	}
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", "ExportCustomerExcel", err.Error())
		}
	}
	exportCustomer, err := mapper.ExportCustomerInputModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	customerCount, countErr := postgres.CustomerCount(exportCustomer.Country, exportCustomer.IsForm)
	if countErr != nil {
		response.Error = true
		response.Message = countErr.Error()
		return &response
	}
	customerInfo, err := postgres.GetCustomerExcelInfo(input, exportCustomer)
	if err != nil {
		if err == pgx.ErrNoRows {
			response.Error = false
			response.Message = "No Customer data found!"
			return &response
		} else {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}

	if input.IsExcel {
		outputModelsExcel := mapper.ExportCustomerInfoEntityToExcelModel(customerInfo)
		url, err := createExportCustomerExcel(outputModelsExcel)

		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.URL = url
	} else {
		outputModels := mapper.ExportCustomerInfoEntityToModel(customerInfo)
		response.Data = outputModels
	}
	response.TotalCount = customerCount
	return &response
}

func createExportCustomerExcel(customers []*entity.CustomerExcelData) (string, error) {

	if len(customers) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "customer"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)

	// Populate excel 1 row header columns
	typeInfo := reflect.TypeOf(entity.CustomerExcelResponse{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
		headerRow = append(headerRow, strings.ToLower(typeInfo.Field(fieldIndex).Name))
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	for index, item := range customers {
		rowNumber := index + 2

		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "customerExcel" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }
	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}

func UpsertCustomerData(ctx *context.Context, inputModel model.CustomerInput) *model.UpsertCustomerResponse {
	upsertResponse := &model.UpsertCustomerResponse{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, validationResult := mapper.MapCustomerModelToEntity(&inputModel, userUUID)

	if !validationResult.Error {
		if entity.ID == nil && (postgres.CheckSpeakernamevalid(entity.Name, entity.Country, entity.SpDesc, entity.City, entity.Organization)) {
			postgres.InsertCustomerData(entity, userUUID, upsertResponse)
		} else if entity.ID != nil && ((postgres.CheckSpeakernamevalid(entity.Name, entity.Country, entity.SpDesc, entity.City, entity.Organization)) || !entity.IsActive) {
			postgres.UpdateCustomerData(entity, userUUID, upsertResponse)
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = "This Customer(" + entity.Name + ") Already Exist!"
			upsertResponse.ValidationErrors = validationResult.ValidationErrors
		}
		// else if entity.IsDeleted {
		// 	postgres.DeleteCustomerData(entity, userUUID, upsertResponse)
		// }
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = "Customer validation failed!"
		upsertResponse.ValidationErrors = validationResult.ValidationErrors
	}
	return upsertResponse
}
