package veeva_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"io"
	"log"
	"net/http"
)

type VeevaTerritory struct {
	Id                 string `json:"Id"`
	Name               string `json:"Name"`
	ParentTerritory2Id string `json:"ParentTerritory2Id"`
}

//SELECT+ObjectId+FROM+ObjectTerritory2Association+WHERE+TerritoryId+IN+

func (s *Controller) RequestTerritoryDataFromVeeva(ctx context.Context) ([]VeevaTerritory, error) {
	if err := s.authenticate(); err != nil {
		return nil, err
	}

	type VeevaResponse struct {
		TotalSize      int              `json:"totalSize"`
		Done           bool             `json:"done"`
		NextRecordsURL string           `json:"nextRecordsUrl"`
		Records        []VeevaTerritory `json:"records"`
	}

	veevaInstanceURL := s.auth.InstanceURL
	bearer := "Bearer " + s.auth.AccessToken
	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Id+,+Name+,+ParentTerritory2Id+FROM+Territory2+`
	//url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+FROM+Territory2+LIMIT+1`
	var totalResponse VeevaResponse

	f := func(url string, bearer string) VeevaResponse {
		var body []byte
		request, err := http.NewRequest("GET", url, bytes.NewBuffer(body))
		if err != nil {
			panic(err)
		}

		request.Header.Add("Authorization", bearer)
		request.Header.Set("Content-Type", "application/json")

		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}

		response, err := client.Do(request)
		if err != nil {
			panic(err)
		}
		defer response.Body.Close()

		responseBody, err := io.ReadAll(response.Body)
		if err != nil {
			panic(err)
		}

		var res VeevaResponse
		//log.Println(string(responseBody))
		err = json.Unmarshal(responseBody, &res)
		if err != nil {
			log.Println(string(responseBody))
			panic(err)
		}

		return res
	}

	res := f(url, bearer)
	totalResponse = res
	for totalResponse.NextRecordsURL != "" {
		res = f(veevaInstanceURL+totalResponse.NextRecordsURL, bearer)
		totalResponse.Records = append(totalResponse.Records, res.Records...)
		totalResponse.NextRecordsURL = res.NextRecordsURL
	}

	return totalResponse.Records, nil
}

func (s *Controller) SaveTerritoryData(ctx context.Context, db *pgxpool.Pool, data []VeevaTerritory) {
	if len(data) == 0 {
		return
	}

	batch := &pgx.Batch{}
	for _, d := range data {
		fmt.Println("inserting territory: ", d.Id, d.Name, " Parent terrytory ID: ", d.ParentTerritory2Id)
		d := d
		query := `
		INSERT INTO territories (
			territory_id, name, parent_territory_id,	
			created_at, updated_at
		) VALUES (
			$1, $2, $3, 
			now(), now()
		)
		ON CONFLICT (lower(territory_id))
		DO UPDATE
		SET 
			name = EXCLUDED.name,
			parent_territory_id = EXCLUDED.parent_territory_id,	
			updated_at = now()
		`
		batch.Queue(
			query,
			d.Id, d.Name, d.ParentTerritory2Id,
		)
	}

	results := db.SendBatch(ctx, batch)
	defer func() {
		err := results.Close()
		if err != nil {
			panic(err)
		}
	}()

	_, err := results.Exec()
	if err != nil {
		panic(err)
	}
}
