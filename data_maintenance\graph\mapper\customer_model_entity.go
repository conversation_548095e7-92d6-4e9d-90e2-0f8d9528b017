package mapper

import (
	"errors"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func ExportCustomerInfoModelToEntity(input *model.CustomerInfoRequest, userUUID uuid.UUID) (*entity.ExportCustomerExcel, error) {
	var outEntity entity.ExportCustomerExcel
	sortElements := []entity.SortingElements{}
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid

	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New(err.Error())

	}
	outEntity.Country = countryID

	if input.IsActive != nil {
		outEntity.IsActive = input.IsActive
	}
	if input.Team != nil {
		outEntity.Team = strings.TrimSpace(*(input).Team)
	}
	if input.VPosition != nil {
		outEntity.VPosition = strings.TrimSpace(*(input).VPosition)
	}
	if input.EmpFirstName != nil {
		outEntity.EmpFirstName = strings.TrimSpace(*(input).EmpFirstName)
	}
	if input.EmpLastName != nil {
		outEntity.EmpLastName = strings.TrimSpace(*(input).EmpLastName)
	}
	if input.CustomerNumber != nil {
		outEntity.CustomerNumber = *(input).CustomerNumber
	}
	if input.CustomerIds != nil {
		outEntity.CustomerIDs = (input).CustomerIds
	}
	if input.CustomerName != nil {
		outEntity.CustomerName = strings.TrimSpace(*(input).CustomerName)
	}
	if input.VSpDesc != nil {
		outEntity.VSpDesc = strings.TrimSpace(*(input).VSpDesc)
	}
	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if input.VeevaReferenceID != nil {
		outEntity.VeevaReferenceId = *input.VeevaReferenceID
	}
	if input.Organization != nil {
		outEntity.Organization = *input.Organization
	}
	if input.AccountType != nil {
		outEntity.AccountType = *input.AccountType
	}
	codes := codeController.GetValueKeyCodes()
	if input.CmslClass != nil {
		outEntity.CmslClass = *input.CmslClass
	}
	if input.City != nil {
		outEntity.City = strings.TrimSpace(*(input).City)
	}
	if input.Gender != nil {
		genders := codes["gender"]
		genderText := strings.TrimSpace(*(input).Gender)
		_, genderExists := genders[genderText]
		if genderExists {
			outEntity.Gender = genders[genderText].ID
		} else {
			return nil, errors.New("invalid gender")
		}
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = *input.SearchItem
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements

	return &outEntity, nil
}
func ExportCustomerInputModelToEntity(input *model.CustomerExcelRequest, userUUID uuid.UUID) (*entity.ExportCustomerExcel, error) {
	var outEntity entity.ExportCustomerExcel
	sortElements := []entity.SortingElements{}
	outEntity.IsExcel = input.IsExcel
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid

	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New(err.Error())

	}
	outEntity.Country = countryID

	if input.IsActive != nil {
		outEntity.IsActive = input.IsActive
	}
	if input.Team != nil {
		outEntity.Team = strings.TrimSpace(*(input).Team)
	}
	if input.VPosition != nil {
		outEntity.VPosition = strings.TrimSpace(*(input).VPosition)
	}
	if input.EmpFirstName != nil {
		outEntity.EmpFirstName = strings.TrimSpace(*(input).EmpFirstName)
	}
	if input.EmpLastName != nil {
		outEntity.EmpLastName = strings.TrimSpace(*(input).EmpLastName)
	}
	if input.CustomerNumber != nil {
		outEntity.CustomerNumber = *(input).CustomerNumber
	}
	if input.CustomerName != nil {
		outEntity.CustomerName = strings.TrimSpace(*(input).CustomerName)
	}
	if input.VSpDesc != nil {
		outEntity.VSpDesc = strings.TrimSpace(*(input).VSpDesc)
	}
	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if input.IsForm != nil {
		outEntity.IsForm = *input.IsForm
	}
	if input.VeevaReferenceID != nil {
		outEntity.VeevaReferenceId = *input.VeevaReferenceID
	}
	if input.Organization != nil {
		outEntity.Organization = *input.Organization
	}
	if input.AccountType != nil {
		outEntity.AccountType = *input.AccountType
	}
	codes := codeController.GetValueKeyCodes()
	if input.CmslClass != nil {
		outEntity.CmslClass = *input.CmslClass
		// cmslClasses := codes["cmslclass"]
		// cmslText := strings.TrimSpace(*(input).CmslClass)
		// _, cmslExists := cmslClasses[cmslText]
		// if cmslExists {
		// 	outEntity.CmslClass = cmslClasses[cmslText].ID
		// } else {
		// 	return nil, errors.New("invalid cmsl class")
		// }
	}
	if input.City != nil {
		outEntity.City = strings.TrimSpace(*(input).City)
	}
	if input.Gender != nil {
		genders := codes["gender"]
		genderText := strings.TrimSpace(*(input).Gender)
		_, genderExists := genders[genderText]
		if genderExists {
			outEntity.Gender = genders[genderText].ID
		} else {
			return nil, errors.New("invalid gender")
		}
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = *input.SearchItem
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements

	return &outEntity, nil
}

func ExportCustomerInfoEntityToModel(input []entity.CustomerExcel) []*model.Customer {
	codes := codeController.GetIdKeyCodes()
	countries := codeController.GetIdKeyCodes()["country"]
	genders := codes["gender"]
	var response []*model.Customer
	// var responseExcel []*entity.CustomerExcelResponse
	for _, item := range input {
		res := new(model.Customer)
		res.ID = item.ID.String()
		res.Country = countries[int(item.Country.Int32)].Title.String
		res.CountryValue = countries[int(item.Country.Int32)].Value
		res.Team = item.Team.String
		res.VPosition = item.VPosition.String
		res.EmpFirstName = item.EmpFirstName.String
		res.EmpLastName = item.EmpLastName.String
		res.CustomerNumber = item.CustomerNumber.String
		res.CustomerName = item.CustomerName.String
		res.VSpDesc = item.VSpDesc.String
		res.CmslClass = item.CmslClass.String
		res.City = item.City.String
		res.Gender = genders[int(item.Gender.Int32)].Value
		res.SpeakerWeight = item.SpeakerWeight.Float64
		res.IsActive = item.IsActive
		res.VeevaReferenceID = item.VeevaReferenceID.String
		res.Organization = item.Organization.String
		res.AccountType = item.AccountType.String
		response = append(response, res)
	}
	return response
}

func ExportCustomerInfoEntityToExcelModel(input []entity.CustomerExcel) []*entity.CustomerExcelData {
	codes := codeController.GetIdKeyCodes()
	countries := codeController.GetIdKeyCodes()["country"]
	genders := codes["gender"]
	var responseExcel []*entity.CustomerExcelData
	for index, item := range input {

		resExcel := entity.CustomerExcelData{}
		var rowData []interface{}
		resExcel.RowNo = index + 1
		rowData = append(rowData, item.ID.String())
		rowData = append(rowData, countries[int(item.Country.Int32)].Title.String)
		rowData = append(rowData, item.Team.String)
		rowData = append(rowData, item.VPosition.String)
		rowData = append(rowData, item.EmpFirstName.String+" "+item.EmpLastName.String)
		rowData = append(rowData, item.EmpActiveDirectoryName.String)
		rowData = append(rowData, item.CustomerNumber.String)
		rowData = append(rowData, item.CustomerName.String)
		rowData = append(rowData, item.VSpDesc.String)
		rowData = append(rowData, item.CmslClass.String)
		rowData = append(rowData, item.City.String)
		rowData = append(rowData, genders[int(item.Gender.Int32)].Value)
		rowData = append(rowData, item.SpeakerWeight.Float64)
		if item.IsActive {
			rowData = append(rowData, "Y")
		} else {
			rowData = append(rowData, "N")
		}
		rowData = append(rowData, item.VeevaReferenceID.String)
		rowData = append(rowData, item.Organization.String)
		rowData = append(rowData, item.AccountType.String)
		resExcel.Data = rowData
		responseExcel = append(responseExcel, &resExcel)
	}
	return responseExcel
}

func MapCustomerModelToEntity(inputModel *model.CustomerInput, userUUID uuid.UUID) (*entity.CustomerUpserInput, *model.UpsertCustomerResponse) {
	result := &model.UpsertCustomerResponse{Error: false}
	var entity entity.CustomerUpserInput
	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			entity.ID = &uuid
			if !postgres.HasCustomerID(entity.ID) {
				if !result.Error {
					result.Error = true
					result.ValidationErrors = []*model.ValidationMessage{}
				}
				errorMessage := &model.ValidationMessage{Message: "Customer ID does not exists!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "Customer ID format is invalid!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	} else {
		if inputModel.CustomerNumber == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Please Provide Customer Number"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		if inputModel.CustomerName == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Please Provide Customer Name"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		if inputModel.Gender == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Please Provide Gender"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}

	if inputModel.Team != nil {
		entity.TeamName = *inputModel.Team
		teamID, err := postgres.GetTeamIdByName(entity.TeamName)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Team Does not exist!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}

		if inputModel.VPosition != nil {
			entity.Position = *inputModel.VPosition
		}
		if inputModel.ActiveDirName != nil {
			entity.ActiveDirName = *inputModel.ActiveDirName
		}
		employeeID, err := postgres.GetEmployeeIdByActiveDirName(entity.ActiveDirName)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Employee Does not exist!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}

		var teamMemberID *uuid.UUID
		if teamID != nil && employeeID != nil {
			teamMemberID, err = postgres.GetTeamMemberIDByEmployeeIDAndTeamID(teamID, employeeID, entity.Position)
			if err != nil {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Team Member Does not exist!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}

		}
		entity.TeamMemberID = teamMemberID
		if inputModel.CmslClass == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Please Provide CmslClass"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	} else {
		entity.TeamName = ""
	}
	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		result.Error = true
		result.ValidationErrors = []*model.ValidationMessage{}
		errorMessage := &model.ValidationMessage{Message: "Country does not exists!"}
		result.ValidationErrors = append(result.ValidationErrors, errorMessage)
	}
	entity.Country = countryID
	if inputModel.CustomerName != nil {
		entity.Name = *inputModel.CustomerName
	}
	if inputModel.ID != nil {
		if inputModel.CustomerNumber != nil {
			// if postgres.CheckCustomerNumberUnique(*inputModel.CustomerNumber, inputModel.ID) {
			// 	result.Error = true
			// 	result.ValidationErrors = []*model.ValidationMessage{}
			// 	errorMessage := &model.ValidationMessage{Message: "Customer Number Must be unique!"}
			// 	result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			// } else {
			entity.CustomerNo = *inputModel.CustomerNumber
			// }
		} else {
			customerNumber, err := postgres.GetCustomerNumberByID(*inputModel.ID)
			if err != nil {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: err.Error()}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
			entity.CustomerNo = customerNumber
		}
	} else {
		if inputModel.CustomerNumber != nil {
			// if postgres.CheckCustomerNumberUnique(*inputModel.CustomerNumber, inputModel.ID) {
			// 	result.Error = true
			// 	result.ValidationErrors = []*model.ValidationMessage{}
			// 	errorMessage := &model.ValidationMessage{Message: "Customer Number Must be unique!"}
			// 	result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			// } else {
			entity.CustomerNo = *inputModel.CustomerNumber
			// }
		} else {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Please Provide Customer Number !"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}

	}
	if inputModel.CmslClass != nil {
		entity.CMSLClass = *inputModel.CmslClass
		// if entity.TeamName != "" {
		// 	if strings.TrimSpace(entity.CMSLClass) != "" {
		// 		codes := codeController.GetTitleKeyCodes()["cmslclass"]
		// 		_, cmslClassExists := codes[entity.CMSLClass]
		// 		if cmslClassExists {
		// 			entity.CMSLClassID = codes[entity.CMSLClass].ID
		// 		} else {
		// 			if !result.Error {
		// 				result.Error = true
		// 				result.ValidationErrors = []*model.ValidationMessage{}
		// 			}
		// 			errorMessage := &model.ValidationMessage{Message: "invalid cmsl class "}
		// 			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		// 		}
		// 	}
		// }
	}
	if inputModel.Gender != nil {
		entity.Gender = *inputModel.Gender
	}
	if strings.TrimSpace(entity.Gender) != "" {
		codes := codeController.GetTitleKeyCodes()["gender"]
		_, GenderExists := codes[entity.Gender]
		if GenderExists {
			entity.GenderID = codes[entity.Gender].ID
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "invalid gender"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}

	if inputModel.VSpDesc != nil {
		entity.SpDesc = *inputModel.VSpDesc
	}
	if inputModel.City != nil {
		entity.City = *inputModel.City
	}
	if inputModel.SpeakerWeight != nil {
		entity.SpeakerWeight = *inputModel.SpeakerWeight
	}

	if inputModel.IsDeleted != nil {
		entity.IsDeleted = *inputModel.IsDeleted
	} else {
		entity.IsDeleted = false
	}
	// if entity.IsDeleted {
	// 	if entity.ID == nil {
	// 		result.Error = true
	// 		result.ValidationErrors = []*model.ValidationMessage{}
	// 		errorMessage := &model.ValidationMessage{Message: "Customer ID format is invalid"}
	// 		result.ValidationErrors = append(result.ValidationErrors, errorMessage)
	// 	}
	// }
	// entity.ValidateCustomerUpsertData(result)
	if inputModel.Organization != nil {
		entity.Organization = *inputModel.Organization
	}

	if inputModel.AccountType != nil {
		entity.AccountType = *inputModel.AccountType
	}

	return &entity, result
}
