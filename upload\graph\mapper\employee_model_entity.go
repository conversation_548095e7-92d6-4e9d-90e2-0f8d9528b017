package mapper

import (
	"log"
	"strings"

	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapEmployeeExcelToEntities(data [][]string, userUUID uuid.UUID, isImport bool) ([]*entity.EmployeeExcelInput, *model.ValidationResult) {
	log.Println("MapEmployeeExcelToEntities")
	var employees []*entity.EmployeeExcelInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	countryID, err := postgres.GetUserCountryByActiveDir(userUUID)
	if err != nil {
		errorMessage := &model.ExcelValidationMessage{Message: err.Error()}
		validationMessages = append(validationMessages, errorMessage)
	}
	country := countryID
	// codes := codeController.GetValueKeyCodes()["roletype"]
	// entity.TypeID = codes[entity.Team].ID
	var flag int
	var nameExistmap map[string]bool
	nameExistmap = make(map[string]bool)
	for i := 1; i < len(data); i++ {
		flag = 0
		var uid uuid.UUID
		var err error
		uuidString := strings.TrimSpace(data[i][0])
		if uuidString != "" {
			uid, err = uuid.FromString(uuidString)
			if err != nil {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: err.Error()}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		// code := codeController.GetValueKeyCodes()["userrole"]
		// userRole := code[strings.TrimSpace(data[i][7])].ID
		userRole := postgres.GetRoleIDByValueInUserRoles(data[i][7])
		if data[i][7] != "" {
			if userRole == "" {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: " Approval Role is invalid"}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		var isActive bool
		var isDeleted bool
		if data[i][10] != "" {
			if strings.TrimSpace(data[i][10]) == "Y" {
				isActive = true
				isDeleted = false
			} else {
				isActive = false
				isDeleted = false
			}
		} else {
			isActive = true
			isDeleted = false
		}
		e := &entity.EmployeeExcelInput{
			ID:               &uid,
			Country:          country,
			TeamName:         strings.TrimSpace(data[i][2]),
			Position:         strings.TrimSpace(data[i][3]),
			FirstName:        data[i][4],
			LastName:         data[i][5],
			EmployeeCode:     strings.TrimSpace(data[i][6]),
			ApprovalRole:     userRole,
			ActiveDirectory:  strings.TrimSpace(data[i][8]),
			Email:            strings.TrimSpace(data[i][9]),
			VeevaReferenceId: strings.TrimSpace(data[i][11]),
			IsActive:         isActive,
			IsDeleted:        isDeleted,
		}
		if e.TeamName != "" {
			teamID := postgres.CheckExistingTeams(e)
			e.TeamID = teamID
		}

		if !isImport {

			if e.ID != nil && e.TeamName == "" && e.Position == "" && e.EmployeeCode == "" && e.ActiveDirectory == "" && e.FirstName == "" && e.LastName == "" && e.VeevaReferenceId == "" {
				e.IsActive = false
				e.IsDeleted = false
			}
			if e.ActiveDirectory != "" {
				if postgres.CheckActivDirNameInUser(e.ActiveDirectory, e.ID, e.Country) {
					flag = 1
				}
			}
		} else {

			if e.ID != nil && e.TeamName == "" && e.Position == "" && e.EmployeeCode == "" && e.FirstName == "" && e.LastName == "" && e.VeevaReferenceId == "" {
				e.IsActive = false
				e.IsDeleted = false
			}
		}

		if e.ActiveDirectory != "" {
			if _, exist := nameExistmap[e.ActiveDirectory]; exist {
				continue
			} else {
				nameExistmap[e.ActiveDirectory] = true
			}
		}

		e.ValidateEmployeeExcelData(i+1, validationMessages)
		if flag != 1 {
			employees = append(employees, e)
		}
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}
	return employees, result
}
