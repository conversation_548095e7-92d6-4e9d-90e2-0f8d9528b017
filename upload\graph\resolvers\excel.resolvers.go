package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.42

import (
	"context"
	"errors"
	"fmt"

	"github.com/ihcp/login/auth"
	"github.com/ihcp/upload/graph/controller"
	"github.com/ihcp/upload/graph/generated"
	"github.com/ihcp/upload/graph/model"
)

// ExcelUpload is the resolver for the excelUpload field.
func (r *mutationResolver) ExcelUpload(ctx context.Context, input model.ExcelUploadRequest) (*model.ExcelUploadResponse, error) {
	return controller.HandleExcelUpload(&ctx, &input), nil
}

// AzureExcelUpload is the resolver for the azureExcelUpload field.
func (r *mutationResolver) AzureExcelUpload(ctx context.Context, input model.AzureExcelUploadInput) (*model.AzureExcelUploadResponse, error) {
	return controller.AzureExcelUpload(&ctx, &input), nil
}

// GetExcel is the resolver for the getExcel field.
func (r *queryResolver) GetExcel(ctx context.Context) (string, error) {
	panic(fmt.Errorf("not implemented"))
}

// ExcelTemplate is the resolver for the excelTemplate field.
func (r *queryResolver) ExcelTemplate(ctx context.Context, input model.ExcelTemplateRequest) (*model.AzureExcelUploadResponse, error) {
	return controller.GetExcelTemplateURL(&input), nil
}

// ExcelStatus is the resolver for the excelStatus field.
func (r *queryResolver) ExcelStatus(ctx context.Context, input model.ExcelStatusRequest) (*model.ExcelStatusResponse, error) {
	userID, salesOrgID, authRole := auth.ForContext(ctx)
	if userID == nil || salesOrgID == nil || authRole == nil {
		return nil, errors.New("You are unauthorized, please login!")
	}
	response := controller.ListExcelUploads(&ctx, &input)
	return response, nil
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }

// !!! WARNING !!!
// The code below was going to be deleted when updating resolvers. It has been copied here so you have
// one last chance to move it out of harms way if you want. There are two reasons this happens:
//   - When renaming or deleting a resolver the old code will be put in here. You can safely delete
//     it when you're done.
//   - You have helper methods in this file. Move them out to keep these resolver files clean.
func (r *queryResolver) GetMaterial(ctx context.Context, divisionName string) (*string, error) {
	panic(fmt.Errorf("not implemented"))
}
