package ad_service

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-ldap/ldap/v3"
	"log"
	"sync"
	"time"
)

type IService interface {
	AuthenticateUser(ctx context.Context, username, password string) error
}

type Service struct {
	m sync.RWMutex
	*config
	internalConn *ldap.Conn
	externalConn *ldap.Conn
}

type config struct {
	internalLDAPURL  string // `env:"INTERNAL_LDAP_URL"`
	internalBaseDN   string // `env:"INTERNAL_BASE_DN"`
	internalUsername string // `env:"INTERNAL_USER_NAME"`
	internalPassword string // `env:"INTERNAL_PASSWORD"`
	externalLDAPURL  string // `env:"EXTERNAL_LDAP_URL"`
	externalBaseDN   string // `env:"EXTERNAL_BASE_DN"`
	externalUsername string // `env:"EXTERNAL_USER_NAME"`
	externalPassword string // `env:"EXTERNAL_PASSWORD"`
}

func New() *Service {
	s := &Service{
		sync.RWMutex{},
		nil, nil, nil,
	}
	s.dial()
	return s
}

var ErrUserNotFound error = errors.New("USER_NOT_FOUND")
var ErrInvalidCredentials = errors.New("INVALID_CREDENTIALS")
var LDAPConnectionTimeout = time.Second * 6

func (s *Service) AuthenticateUser(ctx context.Context, username, password string) error {
	log.Println(fmt.Sprintf(`Call LDAP service for authenticating user with username %s ...`, username))

	ctxWithDeadline, cancel1 := context.WithDeadline(ctx, time.Now().Add(LDAPConnectionTimeout))
	defer cancel1()
	if err := s.bind(ctxWithDeadline); err != nil {
		log.Println(fmt.Sprintf(`xxx LDAP binding request has failed with err: %v...`, err))
		return err
	}
	log.Println(`1-- Binding LDAP service done.`)

	ctxWithDeadline, cancel2 := context.WithDeadline(ctx, time.Now().Add(LDAPConnectionTimeout))
	defer cancel2()
	if err := s.authenticate(ctxWithDeadline, username, password); err != nil {
		log.Println(fmt.Sprintf(`xxx Authentication has failed for username %s with err: %v...`, username, err))
		return err
	}
	log.Println(fmt.Sprintf(`>>> Successfully authenticated user with username %s...`, username))

	return nil
}

func (s *Service) authenticate(ctx context.Context, username, password string) error {
	result := make(chan error, 1)

	go func() {
		err := s.authenticateInternal(ctx, username, password)
		if err != nil && !errors.Is(err, ErrUserNotFound) {
			result <- err
		}
		fmt.Println("authenticate internal", func() string {
			if err != nil {
				return err.Error()

			}
			return "success"
		}())
		if err == nil {
			result <- nil
			return
		}

		if err := ctx.Err(); err != nil {
			result <- err
		}

		err = s.authenticateExternal(ctx, username, password)
		fmt.Println("authenticate external", func() string {
			if err != nil {
				return err.Error()

			}
			return "success"
		}())
		result <- err
	}()

	select {
	case err := <-result:
		log.Println(`2-- Searching LDAP user done.`)
		return err
	case <-ctx.Done():
		log.Println(fmt.Sprintf(`--- %v - Redialing...`, ctx.Err()))
		s.reDial()
		return errors.New("authentication timed out")
	}

	return nil
}
