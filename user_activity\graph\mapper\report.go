package mapper

import (
	"bufio"
	"bytes"
	"errors"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	excelizev2 "github.com/360EntSecGroup-Skylar/excelize"
	"github.com/ihcp/user_activity/graph/azure"
	"github.com/ihcp/user_activity/graph/entity"
)

func CreateExportPreviousUserActivityExcelGenerate(input []entity.UserTrackingEntityAllData) (string, string, error) {
	var UserActivity []entity.UserTrackingEntityAllDataForExcel
	for _, item := range input {
		var userActivityData entity.UserTrackingEntityAllDataForExcel
		userActivityData.UserId = item.UserId.String
		userActivityData.UserIp = item.UserIp.String
		userActivityData.UserName = item.UserName.String
		userActivityData.MeetingId = item.MeetingId.String
		userActivityData.Action = item.Action.String
		userActivityData.PageName = item.PageName.String
		userActivityData.AccessedDatetime = item.AccessedDatetime.String
		UserActivity = append(UserActivity, userActivityData)
	}
	if len(UserActivity) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "employee"
	f := excelize.NewFile()
	index := f.NewSheet(sheetName)
	//remove default sheet
	f.DeleteSheet("Sheet1")
	for index, item := range UserActivity {
		typeInfo := reflect.TypeOf(item)
		if index == 0 {
			for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
				f.SetCellValue(sheetName, getColumnName(fieldIndex+1)+"1", strings.ToLower(typeInfo.Field(fieldIndex).Name))
			}
		}
		//rows of data
		rowNumber := index + 2
		rowNumberString := strconv.FormatInt(int64(rowNumber), 10)
		for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
			typeValue := reflect.ValueOf(item)
			f.SetCellValue(sheetName, getColumnName(fieldIndex+1)+rowNumberString, typeValue.Field(fieldIndex))
		}
	}
	f.SetActiveSheet(index)
	filename := "userActivity" + time.Now().Format("20060102150405") + ".xlsx"

	//For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFile(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}
func getBytesFromFileV2(f *excelizev2.File) []byte {
	buffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
	}
	myresult := buffer.Bytes()
	return myresult
}
func getBytesFromFile(f *excelize.File) []byte {
	var excelBytes bytes.Buffer
	writer := bufio.NewWriter(&excelBytes)
	f.Write(writer)
	writer.Flush()
	return excelBytes.Bytes()
}

func getColumnName(i int) string {
	value := ""
	multiples := i / 26
	for count := 0; count < multiples; count++ {
		value = value + "A"
	}
	i = i % 26
	value = value + string(rune('A'-1+i))
	return value
}
