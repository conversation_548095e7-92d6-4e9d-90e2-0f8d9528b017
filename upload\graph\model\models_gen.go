// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"github.com/99designs/gqlgen/graphql"
)

type Mutation struct {
}

type Query struct {
}

type AzureExcelUploadInput struct {
	File     graphql.Upload `json:"file"`
	FileName string         `json:"fileName"`
}

type AzureExcelUploadResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	URL     string `json:"url"`
}

type Excel struct {
	ID               string            `json:"id"`
	Filename         string            `json:"filename"`
	URL              string            `json:"url"`
	ExcelType        string            `json:"excelType"`
	Status           string            `json:"status"`
	DateCreated      string            `json:"dateCreated"`
	ValidationResult *ValidationResult `json:"validationResult"`
}

type ExcelStatusRequest struct {
	Type *string `json:"type,omitempty"`
}

type ExcelStatusResponse struct {
	Message string   `json:"message"`
	Excels  []*Excel `json:"excels"`
}

type ExcelTemplateRequest struct {
	TemplateType string `json:"templateType"`
}

type ExcelUploadRequest struct {
	FileName string `json:"fileName"`
	URL      string `json:"url"`
	IsImport *bool  `json:"isImport,omitempty"`
}

type ExcelUploadResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type ExcelValidationMessage struct {
	Row     int    `json:"row"`
	Message string `json:"message"`
}

type ValidationResult struct {
	Error                   bool                      `json:"error"`
	ValidationTimeTaken     string                    `json:"validationTimeTaken"`
	ExcelValidationMessages []*ExcelValidationMessage `json:"excelValidationMessages,omitempty"`
}
