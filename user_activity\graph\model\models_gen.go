// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

type Mutation struct {
}

type Query struct {
}

type FetchUserTrackingData struct {
	ID               string `json:"id"`
	UserName         string `json:"userName"`
	Action           string `json:"action"`
	PageName         string `json:"pageName"`
	UserIP           string `json:"userIp"`
	AccessedDatetime string `json:"accessedDatetime"`
	EventID          string `json:"eventId"`
}

type FetchUserTrackingRequest struct {
	FormID *string `json:"formId,omitempty"`
	Action *string `json:"action,omitempty"`
}

type FetchUserTrackingResponse struct {
	Error   bool                     `json:"error"`
	Message string                   `json:"message"`
	Data    []*FetchUserTrackingData `json:"data"`
}

type UserTrackingRequest struct {
	Action   string  `json:"action"`
	PageName string  `json:"pageName"`
	UserIP   string  `json:"userIp"`
	EventID  *string `json:"eventId,omitempty"`
}

type UserTrackingResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}
