package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/teams_service"
	"github.com/ihcp/veeva_service"
	"log"
	"os"
	"strings"
	"sync"
	"time"

	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/azure"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/form/graph/veeva"
	"github.com/ihcp/login/auth"
)

func CheckStatusFromVeevaEventLogsController() error {
	log.Println("----------------- SyncEventsToVeeva - sync events to <PERSON><PERSON><PERSON> -----------------------")
	formAnswerIds := postgres.GetUnsynchronizedEvents(os.Getenv("VEEVA_ALL_EVENT_PUSH_DAY_INTERVAL"))
	if len(formAnswerIds) == 0 {
		log.Println(`--- No event to sync.`)
		return nil
	}
	log.Println(fmt.Sprintf(`--- %d event(s) to sync.`, len(formAnswerIds)))

	_, err := veeva.GetAuthRest()
	if err != nil {
		teams_service.Send(context.Background(), "--- Cronjob: Sync Events To Veeva.\n Veeva API authentication has failed:", err.Error(), `Cronjob`)
		return err
	}
	// var isComplete bool
	var maxWorker int = 20
	workBucket := make(chan struct{}, maxWorker)
	wg := sync.WaitGroup{}

	for _, id := range formAnswerIds {
		workBucket <- struct{}{}
		wg.Add(1)
		go func() {
			status := postgres.GetStatusForEachEvent2(id)
			defer func() {
				if r := recover(); r != nil {
					msg := fmt.Sprintf(`--- Panic happens while syncing event: %v - status: %v - Err: %v`, id, status, err)
					log.Println(msg)
					teams_service.Send(context.Background(), msg, "", "")
				}
			}()
			//
			log.Println(`--- Trying to sync event: `, id, status)
			if err := postgres.InsertIntoVeeva(id, status); err != nil {
				teams_service.Send(context.Background(), fmt.Sprintf(`--- Syncing Event Error: %v %v`, id, status), err.Error(), "")
			}
			wg.Done()
			<-workBucket
		}()
	}

	wg.Wait()
	return nil
}

func VeevaGetAllCreatedDataForCustomer() {
	defer func() {
		if r := recover(); r != nil {
			log.Println()
			teams_service.SendMsg(context.Background(), fmt.Sprintf("--- VeevaGetAllModifiedDataForCustomer panic with error: %v", r), "cronjob", "cronjob")
		}
	}()

	log.Println("VeevaGetAllCreatedDataForCustomer")
	var responseValueforCustomer []entity.CustomerInputForVeevaAccount
	allCreatedValuesForCustomer, allCreatedValueFromAccountVeeva, err := veeva.VeevaGetAllCreatedDataForCustomer()
	var errStr string
	if len(allCreatedValuesForCustomer) == 0 {
		log.Println("-- No newly created customer. ")
	}
	for _, item := range allCreatedValuesForCustomer {
		checkExist, customerID := postgres.CheckCustomerExistOrNot(item.CustomerNo, item.VeevareferenceId, item.Name)
		if checkExist {
			item.ID = customerID

			postgres.CreateCustomer(item)
			responseValueforCustomer = append(responseValueforCustomer, item)
		}
	}
	getjsonvalueForCustomerTable, err := json.Marshal(responseValueforCustomer)
	if err != nil {
		errStr += err.Error() + " "
	}
	getjsonvalueFromAccountVeeva, err := json.Marshal(allCreatedValueFromAccountVeeva)
	if err != nil {
		errStr += err.Error() + " "
	}
	err = postgres.InsertAllAccountVeevaJsonValues(getjsonvalueForCustomerTable, getjsonvalueFromAccountVeeva, "INSERTAUTOMATION", errStr)
	if err != nil {
		panic(err)
	}
}

func VeevaGetAllModifiedDataForCustomer() error {
	defer func() {
		if r := recover(); r != nil {
			log.Println()
			teams_service.SendMsg(context.Background(), fmt.Sprintf("--- VeevaGetAllModifiedDataForCustomer panic with error: %v", r), "cronjob", "cronjob")
		}
	}()

	log.Println("VeevaGetAllModifiedDataForCustomer()")
	var res []entity.CustomerInputForVeevaAccount

	customerList, allModifiedValueFromAccountVeeva, err := veeva.VeevaGetAllModifiedDataForCustomer()
	if len(customerList) == 0 {
		log.Println("-- No modified customer. ")
	}
	var errStr string
	for _, item := range customerList {
		checkExist, customerID := postgres.CheckCustomerExistOrNot(item.CustomerNo, item.VeevareferenceId, item.Name)
		if checkExist {
			item.ID = customerID
			postgres.CreateCustomer(item)
			res = append(res, item)
		} else {
			item.ID = customerID
			postgres.UpdateCustomer(item)
			res = append(res, item)
		}

	}

	getjsonvalueForCustomerTable, err := json.Marshal(res)
	if err != nil {
		panic(err)
	}
	getjsonvalueFromAccountVeeva, err := json.Marshal(allModifiedValueFromAccountVeeva)
	if err != nil {
		panic(err)
	}
	err = postgres.InsertAllAccountVeevaJsonValues(getjsonvalueForCustomerTable, getjsonvalueFromAccountVeeva, "UPDATEAUTOMATION", errStr)
	if err != nil {
		panic(err)
	}
	return nil
}

func FetchAllVeevaAccountDetails(ctx context.Context, input *model.RequestAllVeevaAccountDetails) model.ResponseAllVeevaAccountDetails {
	var Response model.ResponseAllVeevaAccountDetails
	userID := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	country := auth.GetCountry(ctx)
	superadmin, _ := postgres.GetRoleIDByValueInUserRolesCcO("superadmin")
	if userID == nil && approvalRole == nil {
		Response.Error = true
		Response.Message = "You are not authorized to login please contact your country ezflow admin."
		return Response
	}
	if *approvalRole == superadmin {
		Response.Error = false
	} else {
		Response.Error = true
		Response.Message = "Only Super admin can take action !!"
		return Response
	}
	allData, err := postgres.GetAllVeevaAccountDetails(country, input)
	if err != nil {
		Response.Error = true
		Response.Message = "Error!!"
		return Response
	}
	Response.Data = allData
	return Response
}
func VeevaGetAllveevaUsersModifiedData() error {
	log.Println("VeevaGetAllveevaUsersModifiedData()")
	var userResponse []entity.UsersInputForVeevaAccount
	allModifiedValuesForUsers, err := veeva.VeevaGetAllUpdateUsersData()
	var Err string
	if err != nil {
		Err = err.Error() + " "
	}
	codes := codeController.GetValueKeyCodes()
	country := codes["country"]
	for _, item := range allModifiedValuesForUsers {
		var userEntity entity.UsersInputForVeevaAccount
		var countryId int
		var countryValue string
		if strings.TrimSpace(item.ZLGActiveDirectoryNameC) != "" && strings.TrimSpace(item.Country) != "" {
			if strings.TrimSpace(item.Country) == "PH" {
				countryValue = "phzpc"
			} else {
				countryValue = strings.ToLower(item.Country)
			}
			countryId = country[countryValue].ID
			userEntity.Country = countryId
			checkExist, userid := postgres.CheckUsersExistOrNot(item.ZLGActiveDirectoryNameC, countryId)
			if checkExist {
				userEntity.ActiveDirectoryName = item.ZLGActiveDirectoryNameC
				userEntity.Email = item.Username
				userEntity.IsActive = item.IsActive
				userEntity.VeevareferenceId = item.ID[:len(item.ID)-3]
				userEntity.UserId = userid
				if userEntity.IsActive {
					err := postgres.UpdateUsers(userEntity, userid)
					if err != nil {
						Err += err.Error() + " "
					}
				}
				userResponse = append(userResponse, userEntity)
			}

		}
	}
	getjsonvalueForUsersTable, err := json.Marshal(userResponse)
	if err != nil {
		Err += err.Error() + " "
	}
	getjsonvalueFromAccountVeevaUsers, err := json.Marshal(allModifiedValuesForUsers)
	if err != nil {
		Err += err.Error() + " "
	}
	err = postgres.InsertAllAccountVeevaJsonValuesForUsers(getjsonvalueForUsersTable, getjsonvalueFromAccountVeevaUsers, "MODIFIEDUSERUPDATE", Err)
	if err != nil {
		return err
	}
	return nil
}
func VeevaGetAllveevaUsersCreatedData() error {
	log.Println("VeevaGetAllveevaUsersCreatedData()")
	var userResponse []entity.UsersInputForVeevaAccount
	allModifiedValuesForUsers, err := veeva.VeevaGetAllCreatedUsersData()
	var Err string
	if err != nil {
		Err = err.Error() + " "
	}
	codes := codeController.GetValueKeyCodes()
	country := codes["country"]
	for _, item := range allModifiedValuesForUsers {
		var userEntity entity.UsersInputForVeevaAccount
		var countryId int
		var countryValue string
		if strings.TrimSpace(item.ZLGActiveDirectoryNameC) != "" && strings.TrimSpace(item.Country) != "" {
			if strings.TrimSpace(item.Country) == "PH" {
				countryValue = "phzpc"
			} else {
				countryValue = strings.ToLower(item.Country)
			}
			countryId = country[countryValue].ID
			userEntity.Country = countryId
			checkExist, userid := postgres.CheckUsersExistOrNot(item.ZLGActiveDirectoryNameC, countryId)
			if checkExist {
				userEntity.ActiveDirectoryName = item.ZLGActiveDirectoryNameC
				userEntity.Email = item.Username
				userEntity.IsActive = item.IsActive
				userEntity.VeevareferenceId = item.ID[:len(item.ID)-3]
				userEntity.UserId = userid
				if userEntity.IsActive {
					err := postgres.UpdateUsers(userEntity, userid)
					if err != nil {
						Err += err.Error() + " "
					}
				}
				userResponse = append(userResponse, userEntity)
			}

		}
	}
	getjsonvalueForUsersTable, err := json.Marshal(userResponse)
	if err != nil {
		Err += err.Error() + " "
	}
	getjsonvalueFromAccountVeevaUsers, err := json.Marshal(allModifiedValuesForUsers)
	if err != nil {
		Err += err.Error() + " "
	}
	err = postgres.InsertAllAccountVeevaJsonValuesForUsers(getjsonvalueForUsersTable, getjsonvalueFromAccountVeevaUsers, "CREATEDUSERUPDATE", Err)
	if err != nil {
		return err
	}
	return nil
}
func VeevaGetAllveevaProductCreatedData() error {
	log.Println("VeevaGetAllveevaUsersCreatedData()")
	var ProductResponse []entity.ProductInputForVeevaAccount

	allModifiedValuesForProduct, err := veeva.VeevaGetAllCreatedProductData()

	var Err string
	if err != nil {
		Err = err.Error() + " "
	}
	codes := codeController.GetValueKeyCodes()
	country := codes["country"]
	for _, item := range allModifiedValuesForProduct {
		var ProductEntity entity.ProductInputForVeevaAccount
		var countryId int
		var countryValue string
		if strings.TrimSpace(item.Country) != "" {
			if strings.TrimSpace(item.Country) == "PH" {
				countryValue = "phzpc"
			} else {
				countryValue = strings.ToLower(item.Country)
			}
			countryId = country[countryValue].ID
			ProductEntity.Country = countryId
			checkExist, productId := postgres.CheckProductExistOrNot(item.ProductName, countryId)
			if !checkExist {
				ProductEntity.ProductOwner = item.ProductOwner
				ProductEntity.ProductName = item.ProductName
				ProductEntity.GroupCode = item.GroupCode
				ProductEntity.VeevareferenceId = item.ID[:len(item.ID)-3]
				ProductEntity.Productid = productId
				if item.IsDeleted == "Inactive" {
					ProductEntity.IsDeleted = true
				} else {
					ProductEntity.IsDeleted = false
				}
				existsProductOwner, ProductOwnerId := postgres.CheckProductOwner(ProductEntity.ProductOwner, countryId)
				if !existsProductOwner {
					if strings.TrimSpace(ProductEntity.ProductOwner) != "" {
						ProductOwnerId = postgres.InsertProductOwner(ProductEntity)
					}
				}
				err := postgres.CreateProducts(ProductEntity, ProductOwnerId)
				if err != nil {
					Err += err.Error() + " "
				}
				ProductResponse = append(ProductResponse, ProductEntity)
			}

		}
	}
	getjsonvalueForProductTable, err := json.Marshal(ProductResponse)
	if err != nil {
		Err += err.Error() + " "
	}
	getjsonvalueFromAccountVeevaProducts, err := json.Marshal(allModifiedValuesForProduct)
	if err != nil {
		Err += err.Error() + " "
	}
	err = postgres.InsertAllAccountVeevaJsonValuesForProduct(getjsonvalueForProductTable, getjsonvalueFromAccountVeevaProducts, "CREATEDPRODUCT", Err)
	if err != nil {
		return err
	}
	return nil
}
func VeevaGetAllveevaProductModifiedData() error {
	log.Println("VeevaGetAllveevaProductModifiedData()")
	var ProductResponse []entity.ProductInputForVeevaAccount
	allModifiedValuesForProduct, err := veeva.VeevaGetAllModifiedProductData()
	var Err string
	if err != nil {
		Err = err.Error() + " "
	}
	codes := codeController.GetValueKeyCodes()
	country := codes["country"]
	for _, item := range allModifiedValuesForProduct {
		var ProductEntity entity.ProductInputForVeevaAccount
		var countryId int
		var countryValue string
		if strings.TrimSpace(item.Country) != "" {
			if strings.TrimSpace(item.Country) == "PH" {
				countryValue = "phzpc"
			} else {
				countryValue = strings.ToLower(item.Country)
			}
			countryId = country[countryValue].ID
			ProductEntity.Country = countryId
			checkExist, productId := postgres.CheckProductExistOrNot(item.ProductName, countryId)
			if checkExist {
				ProductEntity.ProductOwner = item.ProductOwner
				ProductEntity.ProductName = item.ProductName
				ProductEntity.GroupCode = item.GroupCode
				ProductEntity.VeevareferenceId = item.ID[:len(item.ID)-3]
				ProductEntity.Productid = productId
				if item.IsDeleted == "Inactive" {
					ProductEntity.IsDeleted = true
				} else {
					ProductEntity.IsDeleted = false
				}
				existsProductOwner, ProductOwnerId := postgres.CheckProductOwner(ProductEntity.ProductOwner, countryId)
				if !existsProductOwner {
					if strings.TrimSpace(ProductEntity.ProductOwner) != "" {
						ProductOwnerId = postgres.InsertProductOwner(ProductEntity)
					}
				}
				err := postgres.UpdateProducts(ProductEntity, ProductOwnerId)
				if err != nil {
					Err += err.Error() + " "
				}
				ProductResponse = append(ProductResponse, ProductEntity)
			} else {
				ProductEntity.ProductOwner = item.ProductOwner
				ProductEntity.ProductName = item.ProductName
				ProductEntity.GroupCode = item.GroupCode
				ProductEntity.VeevareferenceId = item.ID[:len(item.ID)-3]
				ProductEntity.Productid = productId
				if item.IsDeleted == "Inactive" {
					ProductEntity.IsDeleted = true
				} else {
					ProductEntity.IsDeleted = false
				}
				existsProductOwner, ProductOwnerId := postgres.CheckProductOwner(ProductEntity.ProductOwner, countryId)
				if !existsProductOwner {
					if strings.TrimSpace(ProductEntity.ProductOwner) != "" {
						ProductOwnerId = postgres.InsertProductOwner(ProductEntity)
					}
				}
				err := postgres.CreateProducts(ProductEntity, ProductOwnerId)
				if err != nil {
					Err += err.Error() + " "
				}
				ProductResponse = append(ProductResponse, ProductEntity)
			}

		}
	}
	getjsonvalueForProductTable, err := json.Marshal(ProductResponse)
	if err != nil {
		Err += err.Error() + " "
	}
	getjsonvalueFromAccountVeevaProducts, err := json.Marshal(allModifiedValuesForProduct)
	if err != nil {
		Err += err.Error() + " "
	}
	err = postgres.InsertAllAccountVeevaJsonValuesForProduct(getjsonvalueForProductTable, getjsonvalueFromAccountVeevaProducts, "UPDATEPRODUCT", Err)
	if err != nil {
		return err
	}
	return nil
}
func FetchAllAttendanceVeevaAccountDetails(ctx context.Context, input *model.RequestAllAttendanceVeevaAccountDetails) model.ResponseAllAttendanceVeevaAccountDetails {
	var Response model.ResponseAllAttendanceVeevaAccountDetails
	userID := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	country := auth.GetCountry(ctx)
	if userID == nil && approvalRole == nil {
		Response.Error = true
		Response.Message = "You are not authorized to login please contact your country ezflow admin."
		return Response
	}
	//c := 10
	//country := &c
	entityData, err := mapper.FetchAllAttendanceVeevaAccountDetailsModelToEntity(input)
	if err != nil {
		Response.Error = true
		Response.Message = "Error:-" + err.Error()
		return Response
	}

	allData, err := postgres.GetAllAttendanceVeevaAccountDetails(country, entityData)
	if err != nil {
		Response.Error = true
		Response.Message = "Error:-" + err.Error()
		return Response
	}
	res := mapper.ValidateAllAttendanceData(allData)
	Response.Data = res
	return Response
}
func CheckStatusFromVeevaAttendanceController() error {
	log.Println("CheckStatusFromVeevaEventLogsController")
	formId, veevaId, err := postgres.CheckStatusFromVeevaAttendance()
	if err != nil {
		return err
	}
	veeva.GetAuthRest()
	for i, val := range formId {
		postgres.VeevaGetAllveevaAttendanceCreatedData(veevaId[i], val, "CREATEATTENDACEVEEVA")
	}
	return nil
}

func CheckStatusFromVeevaAttendanceController2() error {
	log.Println("CheckStatusFromVeevaEventLogsController2")
	veeva.GetAuthRest()
	rawInput := `Put RAW INPUT HERE`
	type Input struct {
		MeetingID string `json:"meetingID"`
	}
	inputList := make([]*Input, 0)
	if err := json.Unmarshal([]byte(rawInput), &inputList); err != nil {
	}

	for i := range inputList {
		formId, veevaId, err := postgres.CheckStatusFromVeevaAttendance2(inputList[i].MeetingID)
		if err != nil {
			panic(err)
		}

		postgres.VeevaGetAllveevaAttendanceCreatedData(veevaId, formId, "CREATEATTENDACEVEEVA")
	}

	return nil
}

func CheckStatusFromVeevaAttendanceFailureController() error {
	log.Println("CheckStatusFromVeevaAttendanceFailureController")
	veevaIds, err := postgres.GetFailureFromVeevaAttendance()
	if err != nil {
		return err
	}
	formId, veevaId, err := postgres.FetchCheckStatusFromVeevaAttendanceFailure(veevaIds)

	if err != nil {
		return err
	}

	veeva.GetAuthRest()
	for i, val := range formId {
		go postgres.VeevaGetAllveevaAttendanceCreatedData(veevaId[i], val, "FAILATTENDACECREATE")
	}
	return nil
}
func EmailAlertForVeevaFailureEvent() error {
	functionName := "EmailAlertForVeevaFailureEvent()"
	log.Println(functionName)
	res, err := postgres.EmailAlertForVeevaFailureEventDb()
	if err != nil {
		log.Println(err, "error in email alert")
		return err
	}
	responseOthers, err := postgres.EmailAlertForVeevaAllProductUserAndCustomerDb()
	if err != nil {
		log.Println(err, "error in email alert")
		return err
	}

	uploadUrl, filename, err := CreateExportVeevaFailureEventExcelGenerate(res)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent():excel ")
		return err
	}
	uploadUrlProduct, filenameProduct, err := CreateExportVeevaProductExcelGenerate(responseOthers)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent():excel ")
		return err
	}
	uploadUrlCustomer, filenameCustomer, err := CreateExportVeevaCustomerExcelGenerate(responseOthers)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent():excel ")
		return err
	}
	uploadUrlUser, filenameUser, err := CreateExportVeevaUserExcelGenerate(responseOthers)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent():excel ")
		return err
	}
	uploadUrlAttendance, filenameAttendance, err := CreateExportVeevaAttendanceExcelGenerate(responseOthers)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent():excel ")
		return err
	}
	err = util.SendMailForVeevaEventFailure(uploadUrl, filename, uploadUrlProduct, filenameProduct, uploadUrlCustomer, filenameCustomer, uploadUrlUser, filenameUser, uploadUrlAttendance, filenameAttendance)
	if err != nil {
		log.Println(err, "EmailAlertForVeevaFailureEvent()")
		return err
	}
	return nil

}
func CreateExportVeevaFailureEventExcelGenerate(input []entity.EventDetailsExcelEmailAlert) (string, string, error) {
	var veevaFailureEvent []entity.RequestorFormAnswerListExcel
	for _, item := range input {
		var veevaFailureEventData entity.RequestorFormAnswerListExcel
		veevaFailureEventData.Data = append(veevaFailureEventData.Data, item.ActivityName.String, item.ActivityType.String, item.ActivityStartDate.String, item.ActivityEndDate.String, item.EventId.String, item.EventType.String, item.Venue.String, item.Status.String, item.RequestorVeevaId.String, item.RequestorId.String, item.OwnerId.String, item.ProductId.String, item.Country.String, item.MeetingMode.String, item.VirtualEventDetails.String, item.VeevaStatus.String, item.VeevaDateCreated.String, item.ErrorMessage.String)
		veevaFailureEvent = append(veevaFailureEvent, veevaFailureEventData)
	}
	if len(input) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "Failure_Event"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)

	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"ActivityName", "ActivityType", "ActivityStartDate", "ActivityEndDate", "EventId", "EventType", "Venue", "Status", "RequestorVeevaId", "RequestorId", "OwnerId", "ProductId", "Country", "MeetingMode", "VirtualEventDetails", "VeevaStatus", "VeevaDateCreated", "ErrorMessage"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
		return "", "", err
	}
	// log.Println(input.Data)
	for index, item := range veevaFailureEvent {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
			return "", "", err
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
		return "", "", err
	}
	filename := "veeva_failure_event-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}
func CreateExportVeevaProductExcelGenerate(input []entity.OtherVeevaDetailsExcelEmailAlert) (string, string, error) {
	var veevaProduct []entity.RequestorFormAnswerListExcel
	for _, item := range input {
		if item.IntegrationType.String == "CREATEDPRODUCT" || item.IntegrationType.String == "UPDATEPRODUCT" {
			var veevaProductData entity.RequestorFormAnswerListExcel
			veevaProductData.Data = append(veevaProductData.Data, item.Id.String, item.Status.String, item.ErrorMessage.String, item.IntegrationType.String, item.EventDetails.String, item.Payload.String, item.DateCreated.String)
			veevaProduct = append(veevaProduct, veevaProductData)
		}
	}
	if len(input) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "Product"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)

	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Id", "Status", "ErrorMessage", "IntegrationType", "EventDetails", "Payload", "DateCreated"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
		return "", "", err
	}
	// log.Println(input.Data)
	for index, item := range veevaProduct {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
			return "", "", err
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
		return "", "", err
	}
	filename := "veeva_product-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}
func CreateExportVeevaUserExcelGenerate(input []entity.OtherVeevaDetailsExcelEmailAlert) (string, string, error) {
	var veevaUser []entity.RequestorFormAnswerListExcel
	for _, item := range input {
		if item.IntegrationType.String == "CREATEDUSERUPDATE" || item.IntegrationType.String == "MODIFIEDUSERUPDATE" {
			var veevaUserData entity.RequestorFormAnswerListExcel
			veevaUserData.Data = append(veevaUserData.Data, item.Id.String, item.Status.String, item.ErrorMessage.String, item.IntegrationType.String, item.EventDetails.String, item.Payload.String, item.DateCreated.String)
			veevaUser = append(veevaUser, veevaUserData)
		}
	}
	if len(input) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "User"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)

	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Id", "Status", "ErrorMessage", "IntegrationType", "EventDetails", "Payload", "DateCreated"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
		return "", "", err
	}
	// log.Println(input.Data)
	for index, item := range veevaUser {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
			return "", "", err
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
		return "", "", err
	}
	filename := "veeva_user-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}
func CreateExportVeevaCustomerExcelGenerate(input []entity.OtherVeevaDetailsExcelEmailAlert) (string, string, error) {
	var veevaCustomer []entity.RequestorFormAnswerListExcel
	for _, item := range input {
		if item.IntegrationType.String == "INSERTAUTOMATION" || item.IntegrationType.String == "UPDATEAUTOMATION" {
			var veevaCustomerData entity.RequestorFormAnswerListExcel
			veevaCustomerData.Data = append(veevaCustomerData.Data, item.Id.String, item.Status.String, item.ErrorMessage.String, item.IntegrationType.String, item.EventDetails.String, item.Payload.String, item.DateCreated.String)
			veevaCustomer = append(veevaCustomer, veevaCustomerData)
		}
	}
	if len(input) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "Customer"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)

	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Id", "Status", "ErrorMessage", "IntegrationType", "EventDetails", "Payload", "DateCreated"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
		return "", "", err
	}
	// log.Println(input.Data)
	for index, item := range veevaCustomer {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
			return "", "", err
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
		return "", "", err
	}
	filename := "veeva_customer-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}
func CreateExportVeevaAttendanceExcelGenerate(input []entity.OtherVeevaDetailsExcelEmailAlert) (string, string, error) {
	var veevaAttendance []entity.RequestorFormAnswerListExcel
	for _, item := range input {
		if item.IntegrationType.String == "CREATEATTENDACEVEEVA" {
			var veevaAttendanceData entity.RequestorFormAnswerListExcel
			veevaAttendanceData.Data = append(veevaAttendanceData.Data, item.Id.String, item.Status.String, item.ErrorMessage.String, item.IntegrationType.String, item.EventDetails.String, item.Payload.String, item.DateCreated.String)
			veevaAttendance = append(veevaAttendance, veevaAttendanceData)
		}
	}
	if len(input) == 0 {
		return "", "", errors.New("No data found")
	}
	sheetName := "Attendance"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)

	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Id", "Status", "ErrorMessage", "IntegrationType", "EventDetails", "Payload", "DateCreated"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
		return "", "", err
	}
	// log.Println(input.Data)
	for index, item := range veevaAttendance {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
			return "", "", err
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
		return "", "", err
	}
	filename := "veeva_attendance-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", "", err
	}
	return blobURL, filename, nil
}

func SyncTerritoryDataFromVeeva(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			errMsg := `Cronjob SyncTerritoryDataFromVeeva has failed`
			log.Println(errMsg)
			teams_service.SendMsg(context.Background(), errMsg, r.(error).Error(), "")
		}
	}()

	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}

	territoryList, err := service.RequestTerritoryDataFromVeeva(ctx)
	if err != nil {
		panic(err)
	}

	var finalList []veeva_service.VeevaTerritory
	for _, t := range territoryList {
		if t.ParentTerritory2Id != "" {
			finalList = append(finalList, t)
		}
	}

	service.SaveTerritoryData(ctx, postgres.GetPool(), finalList)
	log.Println("--- Syncing Territory Data done.")
	return nil
}

func removeDuplicateStr(strSlice []string) []string {
	allKeys := make(map[string]bool)
	list := []string{}
	for _, item := range strSlice {
		if _, value := allKeys[item]; !value {
			allKeys[item] = true
			list = append(list, item)
		}
	}
	return list
}

func SyncUserTerritoryFromVeeva(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			errMsg := `Cronjob SyncUserTerritoryFromVeeva has failed`
			log.Println(errMsg)
			teams_service.SendMsg(context.Background(), errMsg, r.(error).Error(), "")
		}
	}()
	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}

	db := postgres.GetPool()
	emailList := veeva_service.QueryActiveUserEmails(ctx, db)

	syncUserTerritoryFromVeevaAndSaveToDatabase := func(ctx context.Context, emails []string) error {
		list, err := service.RequestUserDataFromVeeva(ctx, emails)
		if err != nil {
			return err
		}

		m := make(map[string]*veeva_service.VeevaUserTerritory, 0)
		for i := range list {
			m[list[i].Email] = &list[i]
		}

		data := make([]*veeva_service.VeevaUserTerritory, len(emails))
		for i, email := range emails {
			k := &veeva_service.VeevaUserTerritory{
				User:             &veeva_service.User{Email: email},
				VeevaReferenceId: "NA",
				VeevaTerritoryId: "NA",
				LastModifiedDate: "NA",
			}
			if v, found := m[email]; found {
				k = v
			}

			fmt.Println(k.Email, k.VeevaTerritoryId)
			data[i] = k
		}

		service.SaveUserTerritoryData(ctx, db, data)
		return nil
	}

	if len(emailList) != 0 {
		emailList = append(emailList, "")
		var emails []string

		for i := 0; i < len(emailList); i++ {
			if len(emails) == 10 {
				emails = removeDuplicateStr(emails)
				if len(emails) < 10 {
					i--
					continue
				}

				syncUserTerritoryFromVeevaAndSaveToDatabase(ctx, emails)

				emails = []string{}
				i--
				continue
			}
			emails = append(emails, emailList[i])
		}
		syncUserTerritoryFromVeevaAndSaveToDatabase(ctx, emails)

	}

	log.Println("--- Syncing User Territory done.")
	return nil
}

func SyncHCOInfoFromVeeva(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			errMsg := `Cronjob SyncHCOInfoFromVeeva has failed`
			log.Println(errMsg)
			teams_service.SendMsg(context.Background(), errMsg, r.(error).Error(), "")
		}
	}()
	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}

	db := postgres.GetPool()

	cityList := veeva_service.QueryHCOCities(ctx, db)

	fmt.Println(cityList, len(cityList))

	var bucket []string
	var bucketCap int = 1
	for i := 0; i < len(cityList); i++ {
		if len(bucket) == bucketCap {
			list := service.SyncHCOInfo(ctx, bucket[0])
			service.UpdateHCPProvinceData(ctx, db, list)

			bucket = []string{}
			i--
			continue
		}
		bucket = append(bucket, cityList[i])
	}

	list := service.SyncHCOInfo(ctx, bucket[0])
	service.UpdateHCPProvinceData(ctx, db, list)

	fmt.Println("Done")
	return nil
}

func SyncHCPTerritoryFromVeeva(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			errMsg := `Cronjob SyncHCPTerritoryFromVeeva has failed`
			log.Println(errMsg)
			teams_service.SendMsg(context.Background(), errMsg, r.(error).Error(), "")
		}
	}()

	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}

	db := postgres.GetPool()
	territoryList := veeva_service.QueryTerritories(ctx, db)

	syncHCPTerritoryIdAndUpdateToDatabase := func(ctx context.Context, territoryIds []string) error {
		fmt.Println(`-----------------`, len(territoryIds))
		territoryList, err := service.RequestHCPByTerritory(ctx, territoryIds)
		if err != nil {
			return err
		}
		fmt.Println("----------------- Territory HCP:", len(territoryList))
		//fmt.Println("----------------- Territory HCP:", len(territoryList), territoryList[0].Territory2Id, territoryList[0].HCPVeevaId)

		service.SaveHCPTerritoryData(ctx, db, territoryList)
		return nil
	}
	var bucket []string
	var bucketCap int = 20
	for i := 0; i < len(territoryList); i++ {
		if len(bucket) == bucketCap {
			bucket = removeDuplicateStr(bucket)
			if len(bucket) < bucketCap {
				i--
				continue
			}

			err := syncHCPTerritoryIdAndUpdateToDatabase(ctx, bucket)
			if err != nil {
				panic(err)
			}
			bucket = []string{}
			i--
			continue
		}
		bucket = append(bucket, territoryList[i])
	}

	err = syncHCPTerritoryIdAndUpdateToDatabase(ctx, bucket)
	if err != nil {
		panic(err)
	}

	fmt.Println("Done", territoryList)
	return nil
}

func SearchHCP(ctx context.Context, input model.SearchHcp) ([]*model.Hcp, error) {
	//var productNameList, classificationList []string

	db := postgres.GetPool()

	var territoryList []string
	if len(input.TerritoryIDList) > 0 {
		territoryList = input.TerritoryIDList
		//territoryList = veeva_service.QueryRelativeTerritory(ctx, db, input.TerritoryIDList)
	}

	hcps := veeva_service.QueryHCPList(ctx, db, veeva_service.SearchHCPParams{
		Country:     10,
		HCPName:     input.HCPNameList,
		TerritoryId: territoryList,
		HCO:         input.HCONameList,
		City:        input.Cities,
		Province:    input.ProvinceList,
		Specialty:   input.Specialties,
	}, 10000)
	//
	//for i := range hcps {
	//	fmt.Println(hcps[i])
	//}
	//fmt.Println(len(hcps))
	//
	//return nil

	var rs []*model.Hcp
	if len(input.ClassificationList)+len(input.ProductNameList) == 0 {
		for _, obj := range hcps {
			rs = append(rs, &model.Hcp{
				HCPVeevaID: &obj.Id,
				Country:    &input.Country,
				HCPName:    &obj.Name,
				HCOName:    &obj.HcoName,
				HCOId:      &obj.HCOId,
				Province:   &obj.Province,
				City:       &obj.City,
				Specialty:  &obj.Specialty,
			})
		}
		return rs, nil
	}

	m := make(map[string]*veeva_service.HCP)
	for i := range hcps {
		m[hcps[i].Id] = &hcps[i]
	}

	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}
	l := service.QueryProductByNameAndClassificationFromVeeva(ctx, input.ProductNameList, input.ClassificationList, input.Country)
	for i := range l {
		if obj, found := m[l[i].HCPId[:15]]; found {
			rs = append(rs, &model.Hcp{
				HCPVeevaID:     &obj.Id,
				Country:        &input.Country,
				HCPName:        &obj.Name,
				HCOName:        &obj.HcoName,
				HCOId:          &obj.HCOId,
				Province:       &obj.Province,
				City:           &obj.City,
				Specialty:      &obj.Specialty,
				ProductName:    &l[i].ProductName,
				Classification: &l[i].Classification,
			})
		}
	}

	return rs, nil
}

func SyncHCPDataFromVeeva(ctx context.Context) error {
	defer func() {
		if r := recover(); r != nil {
			errMsg := `Cronjob SyncHCPTerritoryFromVeeva has failed`
			log.Println(errMsg)
		}
	}()

	service, err := veeva_service.NewVeevaService()
	if err != nil {
		panic(err)
	}

	db := postgres.GetPool()
	territoryList := veeva_service.QueryTerritories(ctx, db)
	//territoryList = territoryList[:]
	m := map[string][]string{}
	count := 0
	for i := range territoryList {
		territoryID := territoryList[i]
		fmt.Println("---- Get HCP for: ", territoryID)

		hcpIdList := service.GetHCPFromVeevaByCountry(ctx, territoryID)
		if len(hcpIdList) == 0 {
			fmt.Println("-skip")
			continue
		}

		fmt.Println("----", len(hcpIdList))
		m[territoryID] = hcpIdList
		count += len(hcpIdList)
		service.UpdateHCPTerritory(ctx, db, hcpIdList, territoryID)
	}
	//
	//service.GetFromVeevaByCountry(ctx, "vn")
	fmt.Println(count)
	fmt.Println("Done")
	return nil
}
