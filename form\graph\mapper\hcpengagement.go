package mapper

import (
	"strconv"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/constants"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapSponsoredHcpExcelToEntities(data [][]string, userUUID uuid.UUID, activityId int, country int) ([]*model.SponsoredHcpEngagementData, *model.ValidationResultForSponsoredHcp) {
	var response []*model.SponsoredHcpEngagementData
	result := &model.ValidationResultForSponsoredHcp{Error: false}
	uniqueSpeakerCheck := make(map[entity.SpeakerDuplicateValidationCheck]bool)
	var speakerArrForDuplicate []entity.SpeakerDuplicateValidationCheck
	var speakerArryForExist []string
	var speakerOrganizationArryForExist []entity.SpeakerOrganizationValidationCheck
	var speakerCityArryForExist []entity.SpeakerCityValidationCheck
	var speakerNamearray []string
	for i := 1; i < len(data); i++ {
		var row int
		res := &model.SponsoredHcpEngagementData{}
		Expense := &model.SponsoredExpenseData{}
		SponsoredOtherexpenses := &model.SponsoredOtherexpenses{}
		TypeOfEngagement := strings.TrimSpace(data[i][0])
		if strings.ToLower(TypeOfEngagement) == "sponsorship" {
			res.Typeofengagement = &TypeOfEngagement
		} else {
			row = (i + 1)
			ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide type of engagement sponsorship!!"
			result.Error = true
			result.ExcelValidationMessages = ErrorMessageHcpEngagement
			return nil, result
		}
		IndividualCategory := strings.TrimSpace(data[i][1])
		if strings.ToLower(IndividualCategory) == "hcp" {
			res.Individualcategory = &IndividualCategory
			govtOrNonGovtHCP := strings.TrimSpace(data[i][2])
			govtOrNonGovtHCPResult := strings.ReplaceAll(govtOrNonGovtHCP, " ", "")
			if strings.ToLower(govtOrNonGovtHCPResult) == "govthcp" || strings.ToLower(govtOrNonGovtHCPResult) == "non-govthcp" {
				res.Govtornongovthcp = &govtOrNonGovtHCP
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Invalid govtOrNonGovtHCP!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}
			Specialty := strings.TrimSpace(data[i][4])
			if Specialty != "" {
				res.Specialty = &Specialty
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide specialty!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			loiquestion1 := strings.TrimSpace(data[i][5])
			loiquestion1Remarks := strings.TrimSpace(data[i][6])
			if loiquestion1 != "" && (strings.ToLower(strings.TrimSpace(loiquestion1)) == "yes" || strings.ToLower(strings.TrimSpace(loiquestion1)) == "no") {
				if strings.ToLower(strings.TrimSpace(loiquestion1)) == "yes" {
					if strings.TrimSpace(loiquestion1Remarks) != "" {
						res.Tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernmentremarks = &loiquestion1Remarks
					} else {
						row = (i + 1)
						ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (To the best of your knowledge, does the HCP or an immediate family member work for the Government?) this question's remarks!!"
						result.Error = true
						result.ExcelValidationMessages = ErrorMessageHcpEngagement
						return nil, result
					}
				}
				res.Tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernment = &loiquestion1
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (To the best of your knowledge, does the HCP or an immediate family member work for the Government?) this question's answer!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			loiquestion2 := strings.TrimSpace(data[i][7])
			loiquestion2Remarks := strings.TrimSpace(data[i][8])
			if loiquestion2 != "" && (strings.ToLower(strings.TrimSpace(loiquestion2)) == "yes" || strings.ToLower(strings.TrimSpace(loiquestion2)) == "no") {
				if strings.ToLower(strings.TrimSpace(loiquestion2)) == "yes" {
					if strings.TrimSpace(loiquestion2Remarks) != "" {
						res.Doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilarremarks = &loiquestion2Remarks
					} else {
						row = (i + 1)
						ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Influence in Purchasing/Reimbursement/Pricing/Drug Approval or Similar Committee?) this question's remarks!!"
						result.Error = true
						result.ExcelValidationMessages = ErrorMessageHcpEngagement
						return nil, result
					}
				}
				res.Doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilar = &loiquestion2
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Influence in Purchasing/Reimbursement/Pricing/Drug Approval or Similar Committee?) this question's answer!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			loiquestion3 := strings.TrimSpace(data[i][9])
			loiquestion3Remarks := strings.TrimSpace(data[i][10])
			if loiquestion3 != "" && (strings.ToLower(strings.TrimSpace(loiquestion3)) == "yes" || strings.ToLower(strings.TrimSpace(loiquestion3)) == "no") {
				if strings.ToLower(strings.TrimSpace(loiquestion3)) == "yes" {
					if strings.TrimSpace(loiquestion3Remarks) != "" {
						res.Doesthehcpinfluenceinpublichealthpolicyorlawsorregulationsremarks = &loiquestion3Remarks
					} else {
						row = (i + 1)
						ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Influence in Public Health Policy/Laws/Regulations?) this question's remarks!!"
						result.Error = true
						result.ExcelValidationMessages = ErrorMessageHcpEngagement
						return nil, result
					}
				}
				res.Doesthehcpinfluenceinpublichealthpolicyorlawsorregulations = &loiquestion3
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Influence in Public Health Policy/Laws/Regulations?) this question's answer!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			loiquestion4 := strings.TrimSpace(data[i][11])
			loiquestion4Remarks := strings.TrimSpace(data[i][12])
			if loiquestion4 != "" && (strings.ToLower(strings.TrimSpace(loiquestion4)) == "yes" || strings.ToLower(strings.TrimSpace(loiquestion4)) == "no") {
				if strings.ToLower(strings.TrimSpace(loiquestion4)) == "yes" {
					if strings.TrimSpace(loiquestion4Remarks) != "" {
						res.Doesthehcpserveoninternationalnongovernmentalhealthorganizationremarks = &loiquestion4Remarks
					} else {
						row = (i + 1)
						ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Serve on International Non-Governmental Health Organization (e.g. WHO, UNICEF, PAHO)?) this question's remarks!!"
						result.Error = true
						result.ExcelValidationMessages = ErrorMessageHcpEngagement
						return nil, result
					}
				}
				res.Doesthehcpserveoninternationalnongovernmentalhealthorganization = &loiquestion4
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (Does the HCP Serve on International Non-Governmental Health Organization (e.g. WHO, UNICEF, PAHO)?) this question's answer!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			loiquestion5 := strings.TrimSpace(data[i][13])
			if loiquestion5 != "" && (strings.ToLower(strings.ReplaceAll(loiquestion5, " ", "")) == "localregions" || strings.ToLower(strings.ReplaceAll(loiquestion5, " ", "")) == "national" || strings.ToLower(strings.ReplaceAll(loiquestion5, " ", "")) == "na" || strings.ToLower(strings.ReplaceAll(loiquestion5, " ", "")) == "hospital") {
				res.Whatisthehcpsphereofinfluence = &loiquestion5
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide (What is the HCP’s Sphere of Influence?) this question's answer!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			levelOfInfluence := LevelOfInfluenceCalculateForBulkUpload(loiquestion1, loiquestion2, loiquestion3, loiquestion4, loiquestion5)
			if levelOfInfluence != "" {
				res.Levelofinfluence = &levelOfInfluence
			}
			if strings.TrimSpace(strings.ToLower(levelOfInfluence)) == "high" {
				question1 := strings.TrimSpace(data[i][14])
				if question1 != "" {
					res.Question1 = &question1
				} else {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide Question's answer(Yes or No) mandatory for Question-1(Will this individual be involved in any decisions regarding these Company-promoted products in the coming 6-12 months?)!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				}

				if strings.TrimSpace(strings.ToLower(question1)) == "yes" {
					remarks1 := strings.TrimSpace(data[i][15])
					if remarks1 != "" {
						res.Remarks1 = &remarks1
					} else {
						row = (i + 1)
						ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide remarks1 mandatory for Question1!!"
						result.Error = true
						result.ExcelValidationMessages = ErrorMessageHcpEngagement
						return nil, result
					}
				} else {
					remarks1 := strings.TrimSpace(data[i][15])
					if remarks1 != "" {
						res.Remarks1 = &remarks1
					}
				}
			}
			Hcoinstitutename := strings.TrimSpace(data[i][16])
			if Hcoinstitutename != "" {
				res.Hcoinstitutename = &Hcoinstitutename
			}

			remarks := strings.TrimSpace(data[i][41])
			if remarks != "" {
				res.Remarks = &remarks
			}
			Speakername := strings.TrimSpace(data[i][3])
			spekaerArray := postgres.CheckSpeakernameArrayQuery(Speakername, country, Specialty, Hcoinstitutename)
			var ExistspeakerName, ExistOrganization, cityChecking bool
			for _, val := range spekaerArray {
				if val.QueryValue == 1 {
					speakerNamearray = append(speakerNamearray, Speakername)
					res.Speakername = &Speakername
					res.Speakernamevalue = &Speakername
					res.SpeakerID = &val.SpeakerId
				} else if val.QueryValue == 4 {
					ExistspeakerName = true
				} else if val.QueryValue == 3 {
					ExistOrganization = true
				} else if val.QueryValue == 2 {
					cityChecking = true
				}
			}
			if !ExistspeakerName {
				speakerArryForExist = append(speakerArryForExist, Speakername)
			}

			if !ExistOrganization {
				speakerOrganizationExist := entity.SpeakerOrganizationValidationCheck{
					SpeakerName:      Speakername,
					SpeakerSpecialty: Specialty,
				}
				speakerOrganizationArryForExist = append(speakerOrganizationArryForExist, speakerOrganizationExist)
			}
			if !cityChecking {
				speakerCityExist := entity.SpeakerCityValidationCheck{
					SpeakerName: Speakername,
					SpeakerCity: Hcoinstitutename,
				}
				speakerCityArryForExist = append(speakerCityArryForExist, speakerCityExist)
			}
			speakerValidationRes := entity.SpeakerDuplicateValidationCheck{
				SpeakerName:      Speakername,
				SpeakerCity:      Hcoinstitutename,
				SpeakerSpecialty: Specialty,
			}
			if _, ok := uniqueSpeakerCheck[speakerValidationRes]; !ok {
				uniqueSpeakerCheck[speakerValidationRes] = true
			} else {
				speakerArrForDuplicate = append(speakerArrForDuplicate, speakerValidationRes)
			}
		} else if strings.ToLower(IndividualCategory) == "non-hcp" {
			res.Individualcategory = &IndividualCategory
			zpStaff := strings.TrimSpace(data[i][42])
			res.Speakername = &zpStaff
			res.Speakernamevalue = &zpStaff

			Position := strings.TrimSpace(data[i][43])
			if Position != "" {
				res.Specialty = &Position
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide Position!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}

			Objective := strings.TrimSpace(data[i][44])
			if Objective != "" {
				res.Hcoinstitutename = &Objective
			} else {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide Objective!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}
			speakerValidationResNonHcp := entity.SpeakerDuplicateValidationCheck{
				SpeakerName:      zpStaff,
				SpeakerCity:      Objective,
				SpeakerSpecialty: Position,
			}
			if _, ok := uniqueSpeakerCheck[speakerValidationResNonHcp]; !ok {
				uniqueSpeakerCheck[speakerValidationResNonHcp] = true
			} else {
				speakerArrForDuplicate = append(speakerArrForDuplicate, speakerValidationResNonHcp)
			}

		} else {
			row = (i + 1)
			ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "Please provide IndividualCategory HCP!!"
			result.Error = true
			result.ExcelValidationMessages = ErrorMessageHcpEngagement
			return nil, result
		}
		RegistrationfeeforSponsored := strings.TrimSpace(data[i][17])
		if RegistrationfeeforSponsored != "" {
			_, err := strconv.ParseFloat(RegistrationfeeforSponsored, 64)
			if err != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "expenseRegistrationFee: Cannot be string!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				Expense.Registrationfee = &RegistrationfeeforSponsored
			}
		} else {
			RegistrationfeeforSponsored = "0"
			Expense.Registrationfee = &RegistrationfeeforSponsored
		}
		registrationremark := strings.TrimSpace(data[i][18])
		if registrationremark != "" {
			Expense.Registrationremark = &registrationremark
		}
		groundtransportation := strings.TrimSpace(data[i][19])
		if groundtransportation != "" {
			_, err := strconv.ParseFloat(groundtransportation, 64)
			if err != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "transportationGround: Cannot be string!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				Expense.Groundtransportation = &groundtransportation
			}
		} else {
			groundtransportation = "0"
			Expense.Groundtransportation = &groundtransportation
		}
		airfare := strings.TrimSpace(data[i][20])
		if airfare != "" {
			_, err := strconv.ParseFloat(airfare, 64)
			if err != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "transportationAirfair:" + err.Error()
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				Expense.Airfare = &airfare
			}
		} else {
			airfare = "0"
			Expense.Airfare = &airfare
		}
		transportremark := strings.TrimSpace(data[i][21])
		if transportremark != "" {
			Expense.Transportremark = &transportremark
		}
		typeofmealBreakFast := strings.TrimSpace(data[i][22])
		typeofmealLunch := strings.TrimSpace(data[i][25])
		typeOfMealCoffeBreak := strings.TrimSpace(data[i][28])
		typeOfMealDinner := strings.TrimSpace(data[i][31])

		code := codeController.GetValueKeyCodes()["expenselimit"]
		var totalmealCost float64
		var totalNoOfMeals float64
		var breakfastCheck bool
		var lunchCheck bool
		var coffebreakCheck bool
		var dinnerCheck bool
		codeIdExpenseBreakFast := code[strings.ToLower(typeofmealBreakFast)].ID
		codeIdExpenseLunch := code[strings.ToLower(typeofmealLunch)].ID
		codeIdExpenseCoffeBreak := code[strings.ToLower(typeOfMealCoffeBreak)].ID
		codeIdExpenseDinner := code[strings.ToLower(typeOfMealDinner)].ID
		if codeIdExpenseBreakFast != 0 && !breakfastCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			breakfastCheck = true
			Expense.Typeofmeal0 = &typeofmealBreakFast
			SponsoredMealExpenses.Typeofmeal0 = &typeofmealBreakFast
			nom0 := strings.TrimSpace(data[i][23])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom0 != "" {
				mealNoOfDay, err := strconv.Atoi(nom0)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "breakfastMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom0 = &mealNoOfDay
					SponsoredMealExpenses.Nom0 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom0, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom0 = &mealNoOfDay
				SponsoredMealExpenses.Nom0 = &mealNoOfDay
			}
			costpermeal0 := strings.TrimSpace(data[i][24])
			if costpermeal0 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal0, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "breakfastmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal0 = &costpermeal0
					SponsoredMealExpenses.Costpermeal0 = &costpermeal0
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal0 = "0"
				Expense.Costpermeal0 = &costpermeal0
				SponsoredMealExpenses.Costpermeal0 = &costpermeal0
			}
			totalNoOfMeals = noOfMeal
			totalmealCost = (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)

		} else if codeIdExpenseLunch != 0 && !lunchCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			lunchCheck = true
			Expense.Typeofmeal0 = &typeofmealLunch
			SponsoredMealExpenses.Typeofmeal0 = &typeofmealLunch
			nom0 := strings.TrimSpace(data[i][26])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom0 != "" {
				mealNoOfDay, err := strconv.Atoi(nom0)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "lunchMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom0 = &mealNoOfDay
					SponsoredMealExpenses.Nom0 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom0, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom0 = &mealNoOfDay
				SponsoredMealExpenses.Nom0 = &mealNoOfDay
			}
			costpermeal0 := strings.TrimSpace(data[i][27])
			if costpermeal0 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal0, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "lunchmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal0 = &costpermeal0
					SponsoredMealExpenses.Costpermeal0 = &costpermeal0
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal0 = "0"
				Expense.Costpermeal0 = &costpermeal0
				SponsoredMealExpenses.Costpermeal0 = &costpermeal0
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)

		} else if codeIdExpenseCoffeBreak != 0 && !coffebreakCheck {
			coffebreakCheck = true
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			Expense.Typeofmeal0 = &typeOfMealCoffeBreak
			SponsoredMealExpenses.Typeofmeal0 = &typeOfMealCoffeBreak
			nom0 := strings.TrimSpace(data[i][29])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom0 != "" {
				mealNoOfDay, err := strconv.Atoi(nom0)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom0 = &mealNoOfDay
					SponsoredMealExpenses.Nom0 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom0, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom0 = &mealNoOfDay
				SponsoredMealExpenses.Nom0 = &mealNoOfDay
			}
			costpermeal0 := strings.TrimSpace(data[i][30])
			if costpermeal0 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal0, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal0 = &costpermeal0
					SponsoredMealExpenses.Costpermeal0 = &costpermeal0
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal0 = "0"
				Expense.Costpermeal0 = &costpermeal0
				SponsoredMealExpenses.Costpermeal0 = &costpermeal0
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)

		} else if codeIdExpenseDinner != 0 && !dinnerCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			dinnerCheck = true
			Expense.Typeofmeal0 = &typeOfMealDinner
			SponsoredMealExpenses.Typeofmeal0 = &typeOfMealDinner
			nom0 := strings.TrimSpace(data[i][32])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom0 != "" {
				mealNoOfDay, err := strconv.Atoi(nom0)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "DinnerMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom0 = &mealNoOfDay
					SponsoredMealExpenses.Nom0 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom0, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom0 = &mealNoOfDay
				SponsoredMealExpenses.Nom0 = &mealNoOfDay
			}
			costpermeal0 := strings.TrimSpace(data[i][33])
			if costpermeal0 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal0, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "DinnermealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal0 = &costpermeal0
					SponsoredMealExpenses.Costpermeal0 = &costpermeal0
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal0 = "0"
				Expense.Costpermeal0 = &costpermeal0
				SponsoredMealExpenses.Costpermeal0 = &costpermeal0
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		}
		if codeIdExpenseLunch != 0 && !lunchCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			lunchCheck = true
			Expense.Typeofmeal1 = &typeofmealLunch
			SponsoredMealExpenses.Typeofmeal1 = &typeofmealLunch
			nom1 := strings.TrimSpace(data[i][26])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom1 != "" {
				mealNoOfDay, err := strconv.Atoi(nom1)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "lunchMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom1 = &mealNoOfDay
					SponsoredMealExpenses.Nom1 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom1, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom1 = &mealNoOfDay
				SponsoredMealExpenses.Nom1 = &mealNoOfDay
			}
			costpermeal1 := strings.TrimSpace(data[i][27])
			if costpermeal1 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal1, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "lunchmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal1 = &costpermeal1
					SponsoredMealExpenses.Costpermeal1 = &costpermeal1
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal1 = "0"
				Expense.Costpermeal1 = &costpermeal1
				SponsoredMealExpenses.Costpermeal1 = &costpermeal1
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		} else if codeIdExpenseCoffeBreak != 0 && !coffebreakCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			coffebreakCheck = true
			Expense.Typeofmeal1 = &typeOfMealCoffeBreak
			SponsoredMealExpenses.Typeofmeal1 = &typeOfMealCoffeBreak
			nom1 := strings.TrimSpace(data[i][29])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom1 != "" {
				mealNoOfDay, err := strconv.Atoi(nom1)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom1 = &mealNoOfDay
					SponsoredMealExpenses.Nom1 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom1, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom1 = &mealNoOfDay
				SponsoredMealExpenses.Nom1 = &mealNoOfDay
			}
			costpermeal1 := strings.TrimSpace(data[i][30])
			if costpermeal1 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal1, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal1 = &costpermeal1
					SponsoredMealExpenses.Costpermeal1 = &costpermeal1
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal1 = "0"
				Expense.Costpermeal1 = &costpermeal1
				SponsoredMealExpenses.Costpermeal1 = &costpermeal1
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		} else if codeIdExpenseDinner != 0 && !dinnerCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			dinnerCheck = true
			Expense.Typeofmeal1 = &typeOfMealDinner
			SponsoredMealExpenses.Typeofmeal1 = &typeOfMealDinner
			nom1 := strings.TrimSpace(data[i][32])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom1 != "" {
				mealNoOfDay, err := strconv.Atoi(nom1)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnerMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom1 = &mealNoOfDay
					SponsoredMealExpenses.Nom1 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom1, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom1 = &mealNoOfDay
				SponsoredMealExpenses.Nom1 = &mealNoOfDay
			}
			costpermeal1 := strings.TrimSpace(data[i][33])
			if costpermeal1 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal1, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnermealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal1 = &costpermeal1
					SponsoredMealExpenses.Costpermeal1 = &costpermeal1
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal1 = "0"
				Expense.Costpermeal1 = &costpermeal1
				SponsoredMealExpenses.Costpermeal1 = &costpermeal1
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		}
		if codeIdExpenseCoffeBreak != 0 && !coffebreakCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			coffebreakCheck = true
			Expense.Typeofmeal2 = &typeOfMealCoffeBreak
			SponsoredMealExpenses.Typeofmeal2 = &typeOfMealCoffeBreak
			nom2 := strings.TrimSpace(data[i][29])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom2 != "" {
				mealNoOfDay, err := strconv.Atoi(nom2)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom2 = &mealNoOfDay
					SponsoredMealExpenses.Nom2 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom2, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom2 = &mealNoOfDay
				SponsoredMealExpenses.Nom2 = &mealNoOfDay
			}
			costpermeal2 := strings.TrimSpace(data[i][30])
			if costpermeal2 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal2, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "coffeBreakmealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal2 = &costpermeal2
					SponsoredMealExpenses.Costpermeal2 = &costpermeal2
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal2 = "0"
				Expense.Costpermeal2 = &costpermeal2
				SponsoredMealExpenses.Costpermeal2 = &costpermeal2
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)

		} else if codeIdExpenseDinner != 0 && !dinnerCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			dinnerCheck = true
			Expense.Typeofmeal2 = &typeOfMealDinner
			SponsoredMealExpenses.Typeofmeal2 = &typeOfMealDinner
			nom2 := strings.TrimSpace(data[i][32])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom2 != "" {
				mealNoOfDay, err := strconv.Atoi(nom2)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnerMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom2 = &mealNoOfDay
					SponsoredMealExpenses.Nom2 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom2, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom2 = &mealNoOfDay
				SponsoredMealExpenses.Nom2 = &mealNoOfDay
			}
			costpermeal2 := strings.TrimSpace(data[i][33])
			if costpermeal2 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal2, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnermealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal2 = &costpermeal2
					SponsoredMealExpenses.Costpermeal2 = &costpermeal2
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal2 = "0"
				Expense.Costpermeal2 = &costpermeal2
				SponsoredMealExpenses.Costpermeal2 = &costpermeal2
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		}
		if codeIdExpenseDinner != 0 && !dinnerCheck {
			SponsoredMealExpenses := &model.SponsoredMealExpenses{}
			dinnerCheck = true
			Expense.Typeofmeal3 = &typeOfMealDinner
			SponsoredMealExpenses.Typeofmeal3 = &typeOfMealDinner
			nom3 := strings.TrimSpace(data[i][32])
			var noOfMeal float64
			var MealCostPerDay float64
			if nom3 != "" {
				mealNoOfDay, err := strconv.Atoi(nom3)
				if err != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnerMealNoOfDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Nom3 = &mealNoOfDay
					SponsoredMealExpenses.Nom3 = &mealNoOfDay
				}
				noOfMeal, _ = strconv.ParseFloat(nom3, 64)
			} else {
				mealNoOfDay := 0
				Expense.Nom3 = &mealNoOfDay
				SponsoredMealExpenses.Nom3 = &mealNoOfDay
			}
			costpermeal3 := strings.TrimSpace(data[i][33])
			if costpermeal3 != "" {
				mealCostPerDay, err1 := strconv.ParseFloat(costpermeal3, 64)
				if err1 != nil {
					row = (i + 1)
					ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "dinnermealCostPerDay: Cannot be string!!"
					result.Error = true
					result.ExcelValidationMessages = ErrorMessageHcpEngagement
					return nil, result
				} else {
					Expense.Costpermeal3 = &costpermeal3
					SponsoredMealExpenses.Costpermeal3 = &costpermeal3
					MealCostPerDay = mealCostPerDay
				}
			} else {
				costpermeal3 = "0"
				Expense.Costpermeal3 = &costpermeal3
				SponsoredMealExpenses.Costpermeal3 = &costpermeal3
			}
			totalNoOfMeals += noOfMeal
			totalmealCost += (MealCostPerDay * noOfMeal)
			Expense.MealExpenses = append(Expense.MealExpenses, SponsoredMealExpenses)
		}
		var inputHcpMealValidation model.HcpMealInput
		if Expense.Typeofmeal0 != nil {
			inputHcpMealValidation.Input = append(inputHcpMealValidation.Input, &model.MealInput{
				MealType:      *Expense.Typeofmeal0,
				MealTotalCost: totalmealCost,
				MealNo:        totalNoOfMeals,
				ActivityID:    activityId,
			})
		}
		validationMesageForMeal := ValidateHcpMealForUploadExcelHcpEngagement(activityId, country, inputHcpMealValidation)
		if validationMesageForMeal.Error {
			row = (i + 1)
			for _, validationHcpMealMsg := range validationMesageForMeal.ValidationMessages {
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + validationHcpMealMsg.Message
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			}
		}
		mealremark := strings.TrimSpace(data[i][34])
		if mealremark != "" {
			Expense.Mealremark = &mealremark
		}
		nodaccomodation := strings.TrimSpace(data[i][35])
		if nodaccomodation != "" {
			accomodationNoOfDay, err1 := strconv.Atoi(nodaccomodation)
			if err1 != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "accomodationNoOfDay: Cannot be string!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				Expense.Nodaccomodation = &accomodationNoOfDay
			}
		} else {
			accomodationNoOfDay := 0
			Expense.Nodaccomodation = &accomodationNoOfDay
		}
		costperdayaccomodation := strings.TrimSpace(data[i][36])
		if costperdayaccomodation != "" {
			_, err1 := strconv.ParseFloat(costperdayaccomodation, 64)
			if err1 != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "accomodationCostPerDay: Cannot be string!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				Expense.Costperdayaccomodation = &costperdayaccomodation
			}
		} else {
			costperdayaccomodation = "0"
			Expense.Costperdayaccomodation = &costperdayaccomodation
		}
		accomodationremark := strings.TrimSpace(data[i][37])
		if accomodationremark != "" {
			Expense.Accomodationremark = &accomodationremark
		}
		otherremark := strings.TrimSpace(data[i][40])
		if otherremark != "" {
			Expense.Otherremark = &otherremark
		}
		otherExpenseName := strings.TrimSpace(data[i][38])
		if otherExpenseName != "" {
			SponsoredOtherexpenses.Nameofexpenses = &otherExpenseName
		}
		otherExpenseTotalCost := strings.TrimSpace(data[i][39])
		if otherExpenseTotalCost != "" {
			_, err := strconv.ParseFloat(otherExpenseTotalCost, 64)
			if err != nil {
				row = (i + 1)
				ErrorMessageHcpEngagement := "Row:" + strconv.Itoa(row) + " " + "otherExpenseTotalCost: Cannot be string!!"
				result.Error = true
				result.ExcelValidationMessages = ErrorMessageHcpEngagement
				return nil, result
			} else {
				SponsoredOtherexpenses.Totalcostsother = &otherExpenseTotalCost
			}
		} else {
			otherExpenseTotalCost = "0"
			SponsoredOtherexpenses.Totalcostsother = &otherExpenseTotalCost
		}
		Morethan2lecturesmeetings := "false"
		res.Morethan2lecturesmeetings = &Morethan2lecturesmeetings
		PreparationtimeAndServiceTime := "0 hr 0 min"
		res.Preparationtime = &PreparationtimeAndServiceTime
		res.Servicetime = &PreparationtimeAndServiceTime
		Expense.Otherexpenses = append(Expense.Otherexpenses, SponsoredOtherexpenses)
		res.Expenses = append(res.Expenses, Expense)
		response = append(response, res)
	}
	if speakerArrForDuplicate != nil {
		result.Error = true
		ErrorMessageHcpEngagement := "Duplicate speakerName("
		for i, SpeakName := range speakerArrForDuplicate {
			ErrorMessageHcpEngagement += SpeakName.SpeakerName
			if i != (len(speakerArrForDuplicate) - 1) {
				ErrorMessageHcpEngagement += " , "
			}
		}
		ErrorMessageHcpEngagement += ") exist in excel!!"
		result.ExcelValidationMessages = ErrorMessageHcpEngagement
		return nil, result
	}
	if speakerArryForExist != nil {
		result.Error = true
		ErrorMessageHcpEngagement := "SpeakerName("
		for i, SpeakName := range speakerArryForExist {
			ErrorMessageHcpEngagement += SpeakName
			if i != (len(speakerArryForExist) - 1) {
				ErrorMessageHcpEngagement += " , "
			}
		}
		ErrorMessageHcpEngagement += ") doesn't exist in this country!!"
		result.ExcelValidationMessages = ErrorMessageHcpEngagement
		return nil, result
	}
	if speakerOrganizationArryForExist != nil {
		result.Error = true
		var ErrorMessageHcpEngagement string
		for i, SpeakOrganization := range speakerOrganizationArryForExist {
			ErrorMessageHcpEngagement += "Specialty("
			ErrorMessageHcpEngagement += SpeakOrganization.SpeakerSpecialty
			ErrorMessageHcpEngagement += ") doesn't match with respect to speaker(" + SpeakOrganization.SpeakerName + ")!!"
			if i != (len(speakerOrganizationArryForExist) - 1) {
				ErrorMessageHcpEngagement += " , "
			}
		}
		result.ExcelValidationMessages = ErrorMessageHcpEngagement
		return nil, result
	}
	if speakerCityArryForExist != nil {
		result.Error = true
		var ErrorMessageHcpEngagement string
		for i, SpeakCity := range speakerCityArryForExist {
			ErrorMessageHcpEngagement += "Organization("
			ErrorMessageHcpEngagement += SpeakCity.SpeakerCity
			ErrorMessageHcpEngagement += ") doesn't match with respect to speaker(" + SpeakCity.SpeakerName + ")!!"
			if i != (len(speakerCityArryForExist) - 1) {
				ErrorMessageHcpEngagement += " , "
			}
		}
		result.ExcelValidationMessages = ErrorMessageHcpEngagement
		return nil, result
	}
	if len(data) <= 1 {
		ErrorMessageHcpEngagement := "Excel File Cannot Be Blank!!"
		result.Error = true
		result.ExcelValidationMessages = ErrorMessageHcpEngagement
		return nil, result
	}
	return response, result
}

func ValidateHcpMealForUploadExcelHcpEngagement(activityId int, country int, input model.HcpMealInput) *model.ValidateHcpMealResponse {
	response := &model.ValidateHcpMealResponse{}
	validationMessages := []*model.ValidationMessage{}
	validationMessage := &model.ValidationMessage{}
	var val = input.Input
	var checkMeal bool
	for _, item := range val {
		if item.MealType == "" {
			response.Error = true
			validationMessage.Message = "Meal type cannot be blank."
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response
		}
		if item.ActivityID == 0 {
			response.Error = true
			validationMessage.Message = "ActivityID can not be zero"
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response

		}
		checkMeal = postgres.GetHcpMealCheck(item.ActivityID)
		if checkMeal {
			response.Error = false
			validationMessage.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") is within the limit!"
			validationMessages = append(validationMessages, validationMessage)
			response.ValidationMessages = validationMessages
			return response

		}
	}
	mealEntites, _ := postgres.GetHcpMealLimit(country)
	HcpMealValidation(mealEntites, &input, response, country)
	return response
}
func BlukUploadValidateHcpSpeaker(SpeakerNamearr []string, country *int) model.HcpSpeakerResponse {
	var response model.HcpSpeakerResponse
	codes := codeController.GetValueKeyCodes()
	countryCode := codes["country"]
	var Message string
	if country != nil {
		if *country == countryCode["phzpc"].ID {
			speakerValidationRes := postgres.ValidationHcpEngagment(country)
			customerUseLimit, CustomerTotalAmount, err := postgres.GetValidationData(country)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return response
			}
			var speakerArray []string
			Message = "Speaker name("
			for _, SpeakerName := range SpeakerNamearr {
				if _, ok := speakerValidationRes[SpeakerName]; ok {
					if speakerValidationRes[SpeakerName].TotalAmountLimit > CustomerTotalAmount || speakerValidationRes[SpeakerName].UseLimit > customerUseLimit {
						speakerArray = append(speakerArray, SpeakerName)
						response.Error = true
					}
				}
			}
			if response.Error {
				for i, speaker := range speakerArray {
					Message += speaker
					if i != (len(speakerArray) - 1) {
						Message += ","
					}
				}
				Message += ") is exceed the use limit of " + strconv.Itoa(customerUseLimit) + " times or " + strconv.Itoa(CustomerTotalAmount) + " amount in a year."
				response.Message = Message
				return response
			}
		}
	}
	return response
}

func LevelOfInfluenceCalculateForBulkUpload(loiquestion1, loiquestion2, loiquestion3, loiquestion4, loiquestion5 string) string {
	var levelOfInfluence string
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.LowlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerNo && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.MediumlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNa {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerLocalRegions {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerHospital {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}
	if strings.TrimSpace(strings.ToLower(loiquestion1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(loiquestion4)) == constants.LoiAnswerYes && (strings.ToLower(loiquestion5)) == constants.LoiAnswerNational {
		levelOfInfluence = constants.HighlevelOfInfluence
		return levelOfInfluence
	}

	return levelOfInfluence
}
