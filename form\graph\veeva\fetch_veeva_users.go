package veeva

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"io/ioutil"
	"log"
	"net/http"
	"os"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
)

func VeevaGetAllCreatedUsersData() ([]entity.UsersRecords, error) {
	functionName := "VeevaGetAllCreatedUsersData()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllUsersData
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		return nil, err
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Id+,+IsActive+,+Username+,+ZLG_Active_Directory_Name__c+,+Country+FROM+User+WHERE+CreatedDate+=+` + os.Getenv("VEEVA_FETCH_CREATED_DATA_FOR_USER_DAYS")
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	bearer := "Bearer " + veevaAuthorizationResponse.Access_token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	defer response.Body.Close()
	responseBody, responseErr := ioutil.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, responseErr
	}
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)
	if err2 != nil {
		return nil, errors.New(string(responseBody) + url)
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllUsersData
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			return nil, errors.New(string(responseForNextRecordBody) + url)
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}
	return ResponseUrlValues.Records, nil
}

func VeevaGetAllUpdateUsersData() ([]entity.UsersRecords, error) {
	functionName := "VeevaGetAllUpdateUsersData()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllUsersData
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		return nil, err
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)

	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Id+,+IsActive+,+Username+,+ZLG_Active_Directory_Name__c+,+Country+FROM+User+WHERE+LastModifiedDate+=+` + os.Getenv("VEEVA_FETCH_MODIFIED_DATA_FOR_USER_DAYS")
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	bearer := "Bearer " + veevaAuthorizationResponse.Access_token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	defer response.Body.Close()
	responseBody, responseErr := ioutil.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, responseErr
	}
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)
	if err2 != nil {
		return nil, errors.New(string(responseBody) + url)
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllUsersData
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			return nil, errors.New(string(responseForNextRecordBody) + url)
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}
	return ResponseUrlValues.Records, nil
}
