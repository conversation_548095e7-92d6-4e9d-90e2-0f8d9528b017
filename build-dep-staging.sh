#!/bin/bash

echo "Building ezFlow backend services ***STAGING*** Environment"
#build-dep-prod.sh


echo "Building Form Template"

docker-compose -f docker-compose-form.yml build

echo "Building Data Maintenance"

docker-compose -f docker-compose-data-maintenance.yml build

echo "Building Upload"

docker-compose -f docker-compose-upload.yml build

echo "Building User Activity"

docker-compose -f docker-compose-user-activity build

echo "Deploying ezFlow backend services ***STAGING*** Environment"

echo "Deploying Form Template"

docker tag ihcp_backend_form:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_form:v1.0.0_STAGING
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_form:v1.0.0_STAGING


echo "Deploying Data Maintenance"

docker tag ihcp_backend_data_maintenance:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_data_maintenance:v1.0.0_STAGING
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_data_maintenance:v1.0.0_STAGING

echo "Deploying Upload"

docker tag ihcp_backend_upload:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_upload:v1.0.0_STAGING
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_upload:v1.0.0_STAGING

echo "Deploying User Activity"

docker tag ihcp_backend_user_activity:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_user_activity:v1.0.0_STAGING
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_user_activity:v1.0.0_STAGING