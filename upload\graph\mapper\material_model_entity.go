package mapper

import (
	"regexp"
	"strings"

	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapMaterialExcelToEntities(data [][]string, userUUID uuid.UUID) ([]*entity.MaterialExcelInput, *model.ValidationResult) {
	var materials []*entity.MaterialExcelInput
	result := &model.ValidationResult{Error: false}
	var validationMessages []*model.ExcelValidationMessage
	countryID, err := postgres.GetUserCountryByActiveDir(userUUID)
	if err != nil {
		errorMessage := &model.ExcelValidationMessage{Message: err.Error()}
		validationMessages = append(validationMessages, errorMessage)

	}
	country := countryID
	patternGroupName := regexp.MustCompile(`^[A-Za-z_/ 0-9]*\([^()]*\)[A-Za-z_/ ]*$`)
	patternGroupCode := regexp.MustCompile(`^[A-Za-z_/ 0-9]*([()]?[A-Za-z_/ ]*)*$`)
	for i := 1; i < len(data); i++ {
		var uid uuid.UUID
		var err error
		uuidString := strings.TrimSpace(data[i][0])
		if uuidString != "" {
			uid, err = uuid.FromString(uuidString)
			if err != nil {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: err.Error()}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		var isActive bool
		var isDeleted bool
		if data[i][4] != "" {
			if strings.TrimSpace(data[i][4]) == "Y" {
				isActive = true
				isDeleted = false
			} else {
				isActive = false
				isDeleted = false
			}
		} else {
			isActive = true
			isDeleted = false
		}
		if strings.TrimSpace(strings.TrimSpace(data[i][3])) != "" {
			matchGroupName := patternGroupName.MatchString(strings.TrimSpace(strings.TrimSpace(data[i][3])))
			if !matchGroupName {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: "Product Name format is invalid!"}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		if strings.TrimSpace(strings.TrimSpace(data[i][2])) != "" {
			matchGroupCode := patternGroupCode.MatchString(strings.TrimSpace(strings.TrimSpace(data[i][2])))
			if !matchGroupCode {
				errorMessage := &model.ExcelValidationMessage{Row: (i + 1), Message: "Product Group Code format is invalid!"}
				validationMessages = append(validationMessages, errorMessage)
			}
		}
		m := &entity.MaterialExcelInput{
			ID:               &uid,
			GroupCode:        strings.TrimSpace(data[i][2]),
			GroupName:        strings.TrimSpace(data[i][3]),
			CountryName:      strings.TrimSpace(data[i][1]),
			CountryNo:        country,
			VeevareferenceId: strings.TrimSpace(data[i][5]),
			IsActive:         isActive,
			IsDeleted:        isDeleted,
			ClientName:       strings.TrimSpace(data[i][6]),
		}

		if m.ID != nil && m.CountryName == "" && m.GroupCode == "" && m.GroupName == "" && m.ClientName == "" {
			m.IsActive = false
			m.IsDeleted = false
		}

		m.ValidateMaterialExcelData(i+1, validationMessages)
		materials = append(materials, m)
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		}
		result.ExcelValidationMessages = append(result.ExcelValidationMessages, validationMessages...)
	}
	return materials, result
}
