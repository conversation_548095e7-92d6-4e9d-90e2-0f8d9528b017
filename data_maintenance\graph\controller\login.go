package controller

import (
	"context"
	"errors"
	"fmt"
	"github.com/ihcp/data_maintenance/graph/ad_service"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	"github.com/jackc/pgx/v4"
	"log"
)

type LoginController struct {
	AdService ad_service.IService
}

func ProxyAuthenticateUser(ctx *context.Context, input model.ProxyUserCredentialsInput) (*model.ProxyUserAuthenticationResponse, error) {
	response := &model.ProxyUserAuthenticationResponse{Error: false}
	userEntity := mapper.ProxyMapLoginModelToEntity(input)
	adname := auth.GetADName(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	superadminID, _ := postgres.GetRoleIDByValueInUserRoles("superadmin")
	adminID, _ := postgres.GetRoleIDByValueInUserRoles("admin")
	country := auth.GetCountry(*ctx)
	if adname == nil || approvalRole == nil {
		if adname != nil && *adname == "workato" {
			approvalRole = &superadminID
		} else {
			response.Error = true
			response.Message = "You are not authorized to login please contact your country ezflow admin."
			return response, nil
		}
	}

	if *approvalRole == superadminID {
		response.Error = false
		userResultEntity, err := postgres.ProxyAuthenticateUserSuperAdmin(userEntity)
		if len(userResultEntity.AccessControl) == 0 {
			response.Error = true
			response.Message = "User does not exist in ezflow."
			return response, nil
		}
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			response.ErrorCode = 999
			return response, nil
		}
		userModel := mapper.ProxyMapUserEntityToLoginModelForSuperadmin(userResultEntity)
		if len(userModel.CountryCurrency) == 0 {
			response.Error = true
			response.Message = "Only access for non admin and admin accounts."
			return response, nil
		}
		err = postgres.ProxyUpdateLoggedInUser(userResultEntity)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			response.ErrorCode = 999
			return response, nil
		}

		response.Data = userModel
		return response, nil
	} else if *approvalRole == adminID {
		response.Error = false
		userResultEntity, err := postgres.ProxyAuthenticateUserAdmin(userEntity, country)
		if len(userResultEntity.AccessControl) == 0 {
			response.Error = true
			response.Message = "User does not exist in ezflow."
			return response, nil
		}
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			response.ErrorCode = 999
			return response, nil
		}
		userModel := mapper.ProxyMapUserEntityToLoginModelForAdmin(userResultEntity)
		if len(userModel.CountryCurrency) == 0 {
			response.Error = true
			response.Message = "Only access for non admin accounts."
			return response, nil
		}
		err = postgres.ProxyUpdateLoggedInUser(userResultEntity)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			response.ErrorCode = 999
			return response, nil
		}
		response.Data = userModel
		return response, nil
	} else {
		response.Error = true
		response.Message = "Only superadmin and admin can take action !!"
		return response, nil
	}

}

func userAuthenticationFailedError(msg string) *model.UserAuthenticationResponse {
	return &model.UserAuthenticationResponse{
		Error:     true,
		Message:   msg,
		ErrorCode: 999,
		Data:      nil,
	}
}

func (s *LoginController) AuthenticateUser(ctx context.Context, input model.UserCredentialsInput) (*model.UserAuthenticationResponse, error) {
	log.Println(`>>>>>>>>>>>> Login Request >>>>>>>>>>>>`)
	log.Println(fmt.Sprintf(`User AD: %s `, input.Username))
	if err := s.AdService.AuthenticateUser(ctx, input.Username, input.Password); err != nil {
		return userAuthenticationFailedError(err.Error()), nil
	}

	// Query User
	userPermissionList, err := postgres.GetUserForAuthentication(input.Username)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return userAuthenticationFailedError(`User does not exist in eZFlow!`), nil
		}

		panic(err)
	}

	// Check Permission
	if len(userPermissionList) == 0 {
		return userAuthenticationFailedError(`Invalid Credentials / Account does not exist`), nil
	}

	// Create jwt Token
	var country string
	if len(userPermissionList) == 1 {
		country = userPermissionList[0].CountryDescription
	}

	jwtToken, err := postgres.GetUserTokenInformation(input.Username, country)
	if err != nil {
		panic(err)
	}

	user := &entity.UserAuth{
		ActiveDirectory: input.Username,
		JWTToken:        jwtToken,
		AccessControl:   userPermissionList,
	}
	if err := postgres.UpdateLoggedInUser(user); err != nil {
		return userAuthenticationFailedError(err.Error()), nil
	}

	log.Println(`<<<<<<<<<<<<<<<<<<<<<<<`)
	return &model.UserAuthenticationResponse{
		Error: false,
		Data:  mapper.MapUserEntityToLoginModel(user),
	}, nil
}

func RegenerateJWTToken(ctx *context.Context, input model.RegenerateJwtTokenWithCountryRequest) (*model.UserAuthenticationResponse, error) {
	response := &model.UserAuthenticationResponse{Error: false}
	adname := auth.GetADName(*ctx)
	if adname == nil || *adname == "" {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response, nil
	}

	userEntity := mapper.MapRegenerateJWTModelToEntity(input)
	userResultEntity, err := postgres.AuthenticateUserWithCountry(userEntity, *adname)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.ErrorCode = 999
		return response, nil
	}

	err = postgres.UpdateLoggedInUser(userResultEntity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.ErrorCode = 999
		return response, nil
	}
	userModel := mapper.MapUserEntityToLoginModel(userResultEntity)
	response.Data = userModel

	return response, nil
}
