type Customer {
    id: String!
    country: String!
    countryValue: String!
    team: String!
    vPosition: String!
    empFirstName: String!
    empLastName: String!
    customerNumber: String!
    customerName: String!
    vSpDesc: String!
    cmslClass: String!
    city: String!
    gender: String!
    SpeakerWeight: Float!
    isActive: Boolean!
    organization:String!
    accountType:String!
    veevaReferenceId:String!
}

input CustomerInput {
    id: String
    isDeleted: Boolean
    team: String
    vPosition: String
    activeDirName: String
    customerNumber: String
    customerName: String
    vSpDesc: String
    cmslClass: String
    city: String
    gender: String
    SpeakerWeight: Float
    organization:String
    accountType:String
}

input CustomerExcelRequest{
    isExcel: Boolean!
    isForm: Boolean
    id:String
    searchItem: String
    team: String
    vPosition: String
    empFirstName: String
    empLastName: String
    customerNumber: String
    customerName: String
    vSpDesc: String
    cmslClass: String
    city: String
    gender: String
    limit: Int
    pageNo: Int
    isActive: Boolean
    veevaReferenceId:String
    organization:String
    accountType:String
    sort: [SortingInputs]
}

input CustomerInfoRequest{
    id:String
    searchItem: String
    team: String
    vPosition: String
    empFirstName: String
    empLastName: String
    customerNumber: String
    customerName: String
    vSpDesc: String
    cmslClass: String
    city: String
    gender: String
    limit: Int
    pageNo: Int
    isActive: Boolean
    veevaReferenceId:String
    organization:String
    accountType:String
    sort: [SortingInputs]
    customerIds:[String]
}


type UpsertCustomerResponse {
  error: Boolean!
  message: String!
  validationErrors: [validationMessage]
}

type upsertResponse {
    error: Boolean!
    message: String!
}

type CustomerExcelResponse{
    error: Boolean!
    message: String!
    url: String!
    totalCount: Int!
    data: [Customer]!
}

extend type Query {
    customerExport(input: CustomerExcelRequest!): CustomerExcelResponse!
    getCustomerDetails(input: CustomerInfoRequest!): CustomerExcelResponse!
}

extend type Mutation {
    upsertCustomer(input: CustomerInput!): UpsertCustomerResponse!
}