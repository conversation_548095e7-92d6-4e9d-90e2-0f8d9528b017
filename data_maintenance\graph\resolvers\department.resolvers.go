package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertDepartment is the resolver for the upsertDepartment field.
func (r *mutationResolver) UpsertDepartment(ctx context.Context, input model.DepartmentInput) (*model.UpsertDepartmentResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertDepartment", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertDepartment :" + string(inputJson))
	response := controller.UpsertDepartmentData(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertDepartment", "data_maintenance/UpsertDepartment", time.Since(start), "200")
	return response, nil
}

// GetDepartment is the resolver for the getDepartment field.
func (r *queryResolver) GetDepartment(ctx context.Context, input *model.GetDepartmentInput) (*model.GetDepartmentResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetDepartment", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetDepartment :" + string(inputJson))
	response := controller.FetchDepartment(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetDepartment", "data_maintenance/GetDepartment", time.Since(start), "200")
	return response, nil
}
