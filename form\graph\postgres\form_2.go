package postgres

import (
	"context"
	"github.com/jackc/pgx/v4"
)

func UpdateEventChangeRequest(ctx context.Context, tx pgx.Tx, formAnswerId, changeRequestType, eventOwnerId string) {
	q := `
	UPDATE form_answers
	SET 
		is_change_request = $2,
		change_request_type = $3
	WHERE
		id = $1 
		AND status = $4
		AND created_by = $5`

	statusApproved := 58
	_, err := tx.Exec(ctx, q, formAnswerId, true, changeRequestType, statusApproved, eventOwnerId)
	if err != nil {
		panic(err)
	}
}
