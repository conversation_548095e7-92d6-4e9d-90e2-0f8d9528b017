INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
	 (47,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,93,false,NULL,NULL,8,false),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,93,false,50,55,8,false),
	 (49,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,93,false,NULL,NULL,8,false),
	 (50,55,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,93,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,93,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,93,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,93,false,NULL,NULL,8,true),
	 (48,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,96,false,53,56,8,false),
	 (49,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,96,false,NULL,NULL,8,false),
	 (50,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,96,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,96,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,96,false,NULL,NULL,8,true),
	 (54,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,96,false,NULL,NULL,8,false),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,96,false,NULL,NULL,8,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,98,false,NULL,NULL,8,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,98,false,50,55,8,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,98,true,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,98,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,98,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,98,false,NULL,NULL,8,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,98,false,50,55,8,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,98,false,NULL,NULL,8,true),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,92,false,50,55,8,false),
	 (51,56,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,92,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,92,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,92,false,NULL,NULL,8,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[0,50000]',0,50000,100,false,50,55,8,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,50000]',0,50000,100,false,NULL,NULL,8,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[0,50000]',0,50000,100,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,50000]',0,50000,100,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,50000]',0,50000,100,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,50000]',0,50000,100,false,NULL,NULL,8,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,100,false,53,56,8,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,100,false,NULL,NULL,8,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,100,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,100,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,100,false,NULL,NULL,8,true),
	 (54,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,100,false,NULL,NULL,8,false),
	 (413,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,100,false,NULL,NULL,8,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,98,false,NULL,NULL,8,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,98,false,NULL,NULL,8,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,98,false,53,56,8,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,98,false,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,98,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,98,false,NULL,NULL,8,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,98,false,NULL,NULL,8,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,98,false,NULL,NULL,8,true),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,1499]',0,1499,86,true,NULL,NULL,8,false),
	 (51,56,NULL,NULL,NULL,true,false,3,'[0,1499]',0,1499,86,true,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,4,'[0,1499]',0,1499,86,true,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,5,'[0,1499]',0,1499,86,true,NULL,NULL,8,true),
	 (51,56,NULL,NULL,NULL,true,false,2,'[0,1499)',0,1499,94,false,NULL,NULL,8,true),
	 (53,56,NULL,NULL,NULL,true,false,3,'[0,1499)',0,1499,94,false,NULL,NULL,8,true),
	 (413,56,NULL,NULL,NULL,true,false,4,'[0,1499)',0,1499,94,false,NULL,NULL,8,true);
	
	INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
	 (47,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,93,false,NULL,NULL,185,false),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,93,false,50,55,185,false),
	 (49,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,93,false,NULL,NULL,185,false),
	 (50,55,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,93,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,93,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,93,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,93,false,NULL,NULL,185,true),
	 (48,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,96,false,53,56,185,false),
	 (49,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,96,false,NULL,NULL,185,false),
	 (50,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,96,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,96,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,96,false,NULL,NULL,185,true),
	 (54,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,96,false,NULL,NULL,185,false),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,96,false,NULL,NULL,185,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,98,false,NULL,NULL,185,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,98,false,50,55,185,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,98,true,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,98,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,98,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,98,false,NULL,NULL,185,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,98,false,50,55,185,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,98,false,NULL,NULL,185,true),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,92,false,50,55,185,false),
	 (51,56,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,92,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,92,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,92,false,NULL,NULL,185,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[0,50000]',0,50000,100,false,50,55,185,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,50000]',0,50000,100,false,NULL,NULL,185,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[0,50000]',0,50000,100,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,50000]',0,50000,100,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,50000]',0,50000,100,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,50000]',0,50000,100,false,NULL,NULL,185,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,100,false,53,56,185,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,100,false,NULL,NULL,185,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,100,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,100,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,100,false,NULL,NULL,185,true),
	 (54,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,100,false,NULL,NULL,185,false),
	 (413,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,100,false,NULL,NULL,185,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,98,false,NULL,NULL,185,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,98,false,NULL,NULL,185,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,98,false,53,56,185,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,98,false,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,98,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,98,false,NULL,NULL,185,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,98,false,NULL,NULL,185,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,98,false,NULL,NULL,185,true),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,1499]',0,1499,86,true,NULL,NULL,185,false),
	 (51,56,NULL,NULL,NULL,true,false,3,'[0,1499]',0,1499,86,true,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,4,'[0,1499]',0,1499,86,true,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,5,'[0,1499]',0,1499,86,true,NULL,NULL,185,true),
	 (51,56,NULL,NULL,NULL,true,false,2,'[0,1499)',0,1499,94,false,NULL,NULL,185,true),
	 (53,56,NULL,NULL,NULL,true,false,3,'[0,1499)',0,1499,94,false,NULL,NULL,185,true),
	 (413,56,NULL,NULL,NULL,true,false,4,'[0,1499)',0,1499,94,false,NULL,NULL,185,true);

INSERT INTO approval_role_management (approval_role,group_type,created_by,last_modified,modified_by,is_active,is_deleted,sequence_no,limit_range,min_limit,max_limit,activity_id,has_condition,alternate_role,alternate_group_type,country,has_international) VALUES
	 (47,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,93,false,NULL,NULL,10,false),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,93,false,50,55,10,false),
	 (49,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,93,false,NULL,NULL,10,false),
	 (50,55,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,93,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,93,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,93,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,93,false,NULL,NULL,10,true),
	 (48,55,NULL,NULL,NULL,true,false,1,NULL,NULL,NULL,96,false,53,56,10,false),
	 (49,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,96,false,NULL,NULL,10,false),
	 (50,55,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,96,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,96,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,96,false,NULL,NULL,10,true),
	 (54,56,NULL,NULL,NULL,true,false,6,NULL,NULL,NULL,96,false,NULL,NULL,10,false),
	 (413,56,NULL,NULL,NULL,true,false,7,NULL,NULL,NULL,96,false,NULL,NULL,10,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[0,10000]',0,10000,98,false,NULL,NULL,10,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[0,10000]',0,10000,98,false,50,55,10,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[0,10000]',0,10000,98,true,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,10000]',0,10000,98,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,10000]',0,10000,98,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,10000]',0,10000,98,false,NULL,NULL,10,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[10001,50000]',10001,50000,98,false,50,55,10,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,7,'[10001,50000]',10001,50000,98,false,NULL,NULL,10,true),
	 (48,55,NULL,NULL,NULL,true,false,2,NULL,NULL,NULL,92,false,50,55,10,false),
	 (51,56,NULL,NULL,NULL,true,false,3,NULL,NULL,NULL,92,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,4,NULL,NULL,NULL,92,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,5,NULL,NULL,NULL,92,false,NULL,NULL,10,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[0,50000]',0,50000,100,false,50,55,10,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,50000]',0,50000,100,false,NULL,NULL,10,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[0,50000]',0,50000,100,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[0,50000]',0,50000,100,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[0,50000]',0,50000,100,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,6,'[0,50000]',0,50000,100,false,NULL,NULL,10,true),
	 (48,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,100,false,53,56,10,false),
	 (49,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,100,false,NULL,NULL,10,false),
	 (50,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,100,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,100,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,100,false,NULL,NULL,10,true),
	 (54,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,100,false,NULL,NULL,10,false),
	 (413,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,100,false,NULL,NULL,10,true),
	 (47,55,NULL,NULL,NULL,true,false,1,'[50001,)',50001,NULL,98,false,NULL,NULL,10,false),
	 (49,55,NULL,NULL,NULL,true,false,3,'[50001,)',50001,NULL,98,false,NULL,NULL,10,false),
	 (48,55,NULL,NULL,NULL,true,false,2,'[50001,)',50001,NULL,98,false,53,56,10,false),
	 (50,55,NULL,NULL,NULL,true,false,4,'[50001,)',50001,NULL,98,false,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,5,'[50001,)',50001,NULL,98,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,6,'[50001,)',50001,NULL,98,false,NULL,NULL,10,true),
	 (54,56,NULL,NULL,NULL,true,false,7,'[50001,)',50001,NULL,98,false,NULL,NULL,10,false),
	 (413,56,NULL,NULL,NULL,true,false,8,'[50001,)',50001,NULL,98,false,NULL,NULL,10,true),
	 (49,55,NULL,NULL,NULL,true,false,2,'[0,1499]',0,1499,86,true,NULL,NULL,10,false),
	 (51,56,NULL,NULL,NULL,true,false,3,'[0,1499]',0,1499,86,true,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,4,'[0,1499]',0,1499,86,true,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,5,'[0,1499]',0,1499,86,true,NULL,NULL,10,true),
	 (51,56,NULL,NULL,NULL,true,false,2,'[0,1499)',0,1499,94,false,NULL,NULL,10,true),
	 (53,56,NULL,NULL,NULL,true,false,3,'[0,1499)',0,1499,94,false,NULL,NULL,10,true),
	 (413,56,NULL,NULL,NULL,true,false,4,'[0,1499)',0,1499,94,false,NULL,NULL,10,true);


     UPDATE approval_role_management SET department = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active = true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.approval_role = subquery.id

    UPDATE approval_role_management SET alternate_department_id = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active =true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.alternate_role = subquery.id
