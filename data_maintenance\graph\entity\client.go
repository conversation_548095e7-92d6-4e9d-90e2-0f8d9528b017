package entity

import (
	"database/sql"

	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

type ClientUpsertInput struct {
	ID          *uuid.UUID
	ClientName  string
	CountryName string
	IsActive    bool
	IsDeleted   bool
	CountryCode int
}

type ClientExcel struct {
	ID         *uuid.UUID
	Country    sql.NullInt32
	ClientName sql.NullString
	IsActive   bool
	IsDeleted  bool
}

type ExportClientExcel struct {
	ID         *uuid.UUID `json:"id"`
	SearchItem string
	Country    int    `json:"country"`
	Status     string `json:"status"`
	Limit      int    `json:"limit"`
	Offset     int    `json:"offset"`
	IsActive   *bool
	ClientName string
	Sort       []SortingElements
}

type ClientExcelResponse struct {
	ID         string
	Country    string
	ClientName string
	Active     string
}

type ClientExcelData struct {
	RowNo int
	Data  []interface{}
}

func (o *ClientUpsertInput) ValidateClientUpsertData(result *model.UpsertClientResponse) {

	validationMessages := []*model.ValidationMessage{}

	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
		}
		result.ValidationErrors = append(result.ValidationErrors, validationMessages...)
	}

}
