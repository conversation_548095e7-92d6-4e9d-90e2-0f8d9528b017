package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v4/pgxpool"
	"github.com/lib/pq"
	"log"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"time"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/form/graph/veeva"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func UpdateAnswersClientAndProduct(ctx context.Context, tx pgx.Tx, meetingID string, input map[string]interface{}) {
	s := strings.Split(meetingID, "-")
	eventCode := fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2])

	q := `
	UPDATE form_answers
	SET 
	answers = jsonb_set( 
				  jsonb_set(
					jsonb_set(answers, 
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,6,values,0,value}', json_value#>'{product, value}'),
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,41,values,0,value}', json_value#>'{client, value}'),
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,41,values,0,description}', json_value#>'{client, value}')	
FROM (VALUES($1::jsonb)) AS vals(json_value)
WHERE event_code=$2 AND event_seq=$3;`
	inputJSON, _ := json.Marshal(input)
	_, err := tx.Exec(ctx, q, inputJSON, eventCode, s[3])
	if err != nil {
		panic(err)
	}
}

type UpdateAnswersRequesterAndOwnerInput struct {
	OwnerUUID     string
	OwnerAD       string
	OwnerName     string
	RequesterUUID string
	RequesterAD   string
	RequesterName string
}

func UpdateAnswersRequesterAndOwner(ctx context.Context, tx pgx.Tx, meetingID string, input *UpdateAnswersRequesterAndOwnerInput) {
	s := strings.Split(meetingID, "-")
	eventCode, eventSeq := fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2]), s[3]

	func() {
		q := `
	UPDATE form_answers
	SET 
	answers = jsonb_set( 
				  jsonb_set(
					jsonb_set(answers, 
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}', json_value#>'{owner,name}'),
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}', json_value#>'{owner,id}'), 
					'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values,0,value}', json_value#>'{requester,id}'), 
					created_by =  (json_value#>>'{requester,uuid}')::uuid
	FROM (VALUES($1::jsonb)) AS vals(json_value)
	WHERE event_code=$2 AND event_seq=$3;`
		inputJSON, _ := json.Marshal(map[string]interface{}{
			"owner": map[string]interface{}{ // MUST
				"uuid": input.OwnerUUID,
				"id":   input.OwnerAD,
				"name": input.OwnerName,
			},
			"requester": map[string]interface{}{
				"uuid": input.RequesterUUID,
				"id":   input.RequesterAD,
				"name": input.RequesterName,
			},
		})
		_, err := tx.Exec(ctx, q, inputJSON, eventCode, eventSeq)
		if err != nil {
			panic(err)
		}
	}()

	func() {
		q := ` UPDATE reporting_db
			SET 
			requestor_name=$3, active_directory=$4, created_by=$5
			WHERE form_answer_id = (SELECT id FROM form_answers WHERE event_code=$1 AND event_seq=$2)`

		_, err := tx.Exec(ctx, q, eventCode, eventSeq, input.RequesterName, input.RequesterAD, input.RequesterUUID)
		if err != nil {
			panic(err)
		}
	}()

}

type User struct {
	UUID            uuid.UUID
	FullName        string
	ActiveDirectory string
	Country         string
}

// User must in same country with the event id (meeting id)
func GetUserByNameOrByADName(ctx context.Context, pool *pgxpool.Pool, name string, meetingID string) *User {
	if name == "" {
		return nil
	}

	q := `
		SELECT concat(u.first_name,' ',u.last_name), u.active_directory, code.value, u.id
		FROM "user" u 
		JOIN code ON code.id = u.country
		JOIN form_answers fa ON fa.event_code=$1 AND fa.event_seq=$2
		JOIN reporting_db r ON r.form_answer_id = fa.id
		WHERE (concat(u.first_name,' ',u.last_name) = $3 OR u.active_directory= $3)
				and code.value = r.country_value	
		LIMIT 1
	`

	s := strings.Split(meetingID, "-")
	user := User{}
	eventCode := fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2])
	err := pool.QueryRow(ctx, q, eventCode, s[3], name).Scan(&user.FullName, &user.ActiveDirectory, &user.Country, &user.UUID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}
	return &user
}

func GetAnswerByMeetingID(ctx context.Context, tx pgx.Tx, meetingID string) (*uuid.UUID, []map[string]interface{}, string) {
	s := strings.Split(meetingID, "-")

	q := `
	SELECT fa.id, fa.answers, r.status_value
	FROM form_answers fa
	JOIN reporting_db r ON r.form_answer_id = fa.id
WHERE fa.event_code=$1 AND fa.event_seq=$2`
	data := []map[string]interface{}{}
	var id uuid.UUID
	var status string
	err := tx.QueryRow(ctx, q, fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2]), s[3]).Scan(&id, &data, &status)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil, status
		}
		panic(err)
	}
	return &id, data, status
}

func GetEventDetails(ctx context.Context, formAnswerId string) *entity.EventDetails {
	formAnswerUUID, _ := uuid.FromString(formAnswerId)

	d := GetFormAnswerByUUID(formAnswerUUID)
	if d == nil {
		return nil
	}

	var f entity.FormAnswerEntity
	f.Design = *d
	e := f.GetEventDetails(context.Background())
	hcpList := f.GetEventHCPs()
	expense := f.GetEventExpenses()

	rs := &entity.EventDetails{
		LedBy:               sql.NullString{String: e.LedBy},
		ActivityName:        sql.NullString{String: e.ActivityName},
		ActivityType:        sql.NullString{String: e.ActivityTypeName},
		EventType:           sql.NullString{String: e.EventType},
		Venue:               sql.NullString{String: e.Location},
		ActivityStartDate:   e.StartDate,
		ActivityEndDate:     e.EndDate,
		EventId:             sql.NullString{},
		Status:              sql.NullString{},
		RequestorVeevaId:    sql.NullString{},
		RequestorName:       sql.NullString{},
		OwnerId:             sql.NullString{},
		MeetingMode:         sql.NullString{String: e.MeetingMode},
		VirtualEventDetails: sql.NullString{String: e.VirtualEventDetails},
		AgendaAttachment:    e.AgendaAttachmentURLs,
		HCP:                 hcpList,
		EventExpense:        expense,

		TherapeuticArea:    sql.NullString{String: e.TherapeuticArea},
		EventOrganizer:     sql.NullString{String: e.EventOrganizer},
		NoOfHCP:            sql.NullString{String: e.NoOfHCPs},
		NoOfNonHCP:         sql.NullString{String: e.NoOfNonHCP},
		NoOfEmployee:       sql.NullString{String: e.NoOfEmployee},
		TargetAudience:     sql.NullString{String: e.TargetAudience},
		MaterialCodeNumber: sql.NullString{String: e.MaterialCodeNumber},
		MeetingObjective:   sql.NullString{String: e.MeetingObjective},
		GeneralRemark:      sql.NullString{String: e.GeneralRemark},
		AttendeeAttachment: e.AttendeeAttachment,
		ProposalAttachment: e.ProposalAttachment,
	}

	if len(e.MultiProducts) != 0 {
		var v []map[string]string
		err := json.Unmarshal([]byte(e.MultiProducts), &v)
		if err != nil {
			panic(err)
		}
		var l []string
		for i := range v {
			obj := v[i]
			for key, val := range obj {
				if val != "" {
					l = append(l, strings.TrimSpace(val))
				}

				rs.ClientProductList = append(rs.ClientProductList, &entity.ClientProduct{
					Client:  key,
					Product: val,
				})
			}
		}

		rs.ProductList = l
	}

	if len(rs.ProductList) == 0 {
		pList := strings.Split(e.Products, ",")

		for i := range pList {
			pList[i] = strings.TrimSpace(pList[i])
		}
		rs.ProductList = pList
	}

	queryString := `
select	
	concat(fa.event_code,'-',fa.event_seq) as event_id,	
	(select description from code where id = fa.country and is_active = true) as country,
	(select title from code where id=fa.status) as status,
    requestor.veeva_reference_id as requestor_veeva_id,		
	requestor.full_name as requestor_name,
	requestor.email as requestor_email,
	owner.full_name as owner_name,
	owner.email as owner_email,
	owner.veeva_reference_id as owner_veeva_id,
	fa.change_request_type,
	fa.date_created,
	logg.date_created as date_updated
FROM form_answers fa
LEFT JOIN (
     SELECT
     	id,
        concat(u.first_name, '-', u.last_name) as full_name,
        u.email,
        country,
		veeva_reference_id
     FROM
          "user" u
     ) AS requestor ON requestor.id = fa.created_by AND requestor.country = fa.country
LEFT JOIN (
	SELECT
		u.veeva_reference_id,
     	u.active_directory,
        concat(u.first_name, '-', u.last_name) as full_name,
        u.email,
        country,
        first_name, last_name
     FROM
          "user" u
     ) AS owner ON (owner.active_directory = fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}' OR
     				concat(owner.first_name ,' ',owner.last_name) = fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')
     				AND owner.country = fa.country
LEFT OUTER JOIN (
	SELECT
		l.form_answers_id,
		l.date_created
     FROM
          form_answer_logs l
     WHERE l.form_answers_id = $1
     ORDER BY date_created DESC
     LIMIT 1 
		) as logg ON logg.form_answers_id = fa.id 
where fa.id= $1   `

	row := GetPool().QueryRow(ctx, queryString, formAnswerId)
	err := row.Scan(
		&rs.EventId,
		&rs.Country,
		&rs.Status,
		&rs.RequestorVeevaId,
		&rs.RequestorName,
		&rs.RequestorEmail,
		&rs.OwnerName,
		&rs.OwnerEmail,
		&rs.OwnerId,
		&rs.ChangeApprovalType,
		&rs.CreatedAt,
		&rs.UpdatedAt,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}

	return rs
}

type EventApprovers struct {
	SequenceNo int
	Id         string
	Email      string
	Name       string
	Role       string
	Status     string
	ActionedAt time.Time
}

func GetEventApprovers(ctx context.Context, formAnswerId string) []*EventApprovers {
	return nil
}

//func UpdateEventInVeevaEventLogs(formAnswerId string, veevaReferenceId string, status bool, veevaUrl string) {
//	log.Println("UpdateEventInVeevaEventLogs()")
//	pool := GetPool()
//
//	queryString := `update veeva_event_logs set veeva_reference_id =$2, status = $3 , date_retry = $4 , veeva_url = $5 where form_answers_id = $1`
//	tag, err := pool.Exec(context.Background(), queryString, formAnswerId, veevaReferenceId, status, time.Now(), veevaUrl)
//	if tag.RowsAffected() == 0 {
//		insertIntoVeevaEventLogs(formAnswerId, veevaReferenceId, status, veevaUrl)
//	}
//	if err != nil {
//		panic(err)
//	}
//}

//	func insertIntoVeevaEventLogs(formAnswerId string, veevaReferenceId string, status bool, veevaUrl string) {
//		log.Println("InsertIntoVeevaEventLogs()")
//		pool := GetPool()
//
//		queryString := `insert into veeva_event_logs(form_answers_id,status,date_created,veeva_url,veeva_reference_id)values($1,$2,$3,$4,$5)
//		`
//		_, err := pool.Exec(context.Background(), queryString, formAnswerId, status, time.Now(), veevaUrl, veevaReferenceId)
//		if err != nil {
//			panic(err)
//		}
//	}
func GetUnsynchronizedEvents(interval string) []string {
	//functionName := "GetUnsynchronizedEvents()"
	//log.Println(functionName)
	pool = GetPool()
	queryString := `-- select form_answers_id from veeva_event_logs where status=false and date_created  >= current_date - ` + interval
	//queryString := `select id from form_answers where date_created > '2025-01-01 00:00:00+00' AND date_created < '2025-04-01 00:00:00+00' AND (status = 398 OR status = 59)  ORDER BY date_created`
	var formAnswerId string
	rows, err := pool.Query(context.Background(), queryString)
	if err != nil {
		return nil
	}
	defer rows.Close()

	var rs []string
	for rows.Next() {
		err := rows.Scan(&formAnswerId)
		if err != nil {
			panic(err)
		}

		rs = append(rs, formAnswerId)
	}
	return rs
}

func GetVeevaReferenceId(formAnswerId string) string {
	pool = GetPool()

	queryString := `select veeva_reference_id from veeva_event_logs where form_answers_id=$1`

	var veevaReferenceId string
	err := pool.QueryRow(context.Background(), queryString, formAnswerId).Scan(&veevaReferenceId)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ""
		}

		panic(err)
	}
	return veevaReferenceId
}

func GetEventByEventCode(eventCode string) string {
	pool = GetPool()

	s := strings.Split(eventCode, `-`)
	if len(s) != 4 {
		return ""
	}
	queryString := `select l.form_answers_id 
	from veeva_event_logs l
	LEFT JOIN form_answers f on f.id = l.form_answers_id 
	where f.event_code = $1 and f.event_seq = $2`

	var formAnswerId string
	err := pool.QueryRow(context.Background(), queryString, fmt.Sprintf(`%v-%v-%v`, s[0], s[1], s[2]), s[3]).Scan(&formAnswerId)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ""
		}

		panic(err)
	}
	return formAnswerId
}

type FormAnswerBasicInfo struct {
	ID                string
	StatusCode        int
	IsChangeRequest   bool
	ChangeRequestType string
	Country           int
	LedBy             string
}

func GetFormAnswerByID(formID string) *FormAnswerBasicInfo {
	pool = GetPool()
	ctx := context.Background()
	queryString := `
		SELECT
			status, is_change_request,country, change_request_type,
			f.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,45,values,0,value}' as led_by 	
		FROM form_answers f
		WHERE f.id = $1`

	rs := FormAnswerBasicInfo{
		ID: formID,
	}
	var changeRequestType, ledBy sql.NullString
	err := pool.QueryRow(ctx, queryString, formID).Scan(
		&rs.StatusCode,
		&rs.IsChangeRequest,
		&rs.Country,
		&changeRequestType,
		&ledBy)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}

	rs.ChangeRequestType = changeRequestType.String
	rs.LedBy = ledBy.String

	return &rs
}

func GetFormAnswerEntityByID_TwoMostRecentCopies(formID string) (*entity.FormAnswerEntity, *entity.FormAnswerEntity) {
	pool = GetPool()
	ctx := context.Background()
	queryString := `
		SELECT	
			answers as current_answer,
			(SELECT answers FROM form_answer_logs l WHERE l.form_answers_id = f.id and l.status = 57 and l.is_deleted = 'f' ORDER BY date_created DESC limit 1) as previous_answer	
		FROM form_answers f
		WHERE f.id = $1`

	var current, prev entity.FormAnswerEntity
	//current.Design = pgtype.JSONB{}
	//prev.Design = pgtype.JSONB{}

	err := pool.QueryRow(ctx, queryString, formID).Scan(
		&current.Design,
		&prev.Design,
	)
	if err != nil {
		panic(err)
	}

	return &current, &prev
}

func GetProduct(product []string, country string) ([]string, error) {
	pool := GetPool()

	if country == "PH" {
		country = "phzpc"
	}
	country = strings.ToLower(country)
	var inputargs []interface{}
	inputargs = append(inputargs, product, country)

	queryString := `
	SELECT m.veeva_reference_id, m.group_name
	FROM material m 
	WHERE
		m.group_name in (?) 
		and m.country = (select id from code where value = ? and is_active = true and is_deleted = false) 
		and m.veeva_reference_id is not null 
	GROUP BY m.veeva_reference_id, m.group_name
	LIMIT 3`

	queryString, args, err := sqlx.In(queryString, inputargs...)
	if err != nil {
		panic(err)
	}

	rows, err := pool.Query(context.Background(), sqlx.Rebind(sqlx.DOLLAR, queryString), args...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	veevaReferenceIds := make([]string, len(product))
	for rows.Next() {
		var veeva, groupName string
		if err := rows.Scan(&veeva, &groupName); err != nil {
			panic(err)
		}

		for i, v := range product {
			if v == groupName {
				veevaReferenceIds[i] = veeva
			}

		}
	}

	rs := []string{}
	for i, v := range veevaReferenceIds {
		if v != "" {
			rs = append(rs, veevaReferenceIds[i])
		}

	}

	return rs, nil
}

func VeevaUpdateError(formAnswerId string, veevaErr string, veevaError bool) {
	if pool == nil {
		pool = GetPool()
	}
	query := `update veeva_event_logs
	set status = $1 ,error_message = $2, date_retry = now() where form_answers_id = $3`
	_, err := pool.Exec(context.Background(), query, veevaError, veevaErr, formAnswerId)
	if err != nil {
		panic(err)
	}
}

//func InsertErrorMessageInVeevaEventLogs(formAnswerId string, success bool, veevaReferenceId string, veevaErr string) {
//	log.Println("InsertErrorMessageInVeevaEventLogs()")
//
//	upsertQuery := `
//		INSERT INTO veeva_event_logs(form_answers_id, status, error_message, veeva_reference_id)
//		VALUES ($1,$2,$3,$4)
//		ON CONFLICT (form_answers_id) DO UPDATE
//		SET
//			status = EXCLUDED.status,
//			error_message = EXCLUDED.error_message,
//			veeva_reference_id = EXCLUDED.veeva_reference_id,
//			date_retry = now()`
//
//	pool := GetPool()
//	_, err := pool.Exec(context.Background(), upsertQuery, formAnswerId, success, veevaErr, veevaReferenceId)
//	if err != nil {
//		panic(err)
//	}
//}

func UpsertVeevaEventLogs(ctx context.Context, formAnswerId string, success bool, isAttendanceUpdate bool,
	veevaReferenceId string, veevaURL string, veevaErr string) {

	upsertQuery := `
		INSERT INTO "veeva_event_logs" as l (
			form_answers_id, status, error_message, veeva_reference_id, veeva_url, is_attendance_update,
			date_created, date_retry
		) 
		VALUES (
			$1,$2,$3,$4,$5,$6,
			now(),now()
		)
		ON CONFLICT (form_answers_id) DO UPDATE
		SET 
			status = EXCLUDED.status,
			error_message =  COALESCE(EXCLUDED.error_message, l.error_message), 
			veeva_reference_id = EXCLUDED.veeva_reference_id,
			date_retry = now()`

	_, err := GetPool().Exec(ctx, upsertQuery,
		formAnswerId, success, veevaErr, veevaReferenceId, veevaURL, isAttendanceUpdate)
	if err != nil {
		panic(err)
	}
}

func VeevaEventLogChecker(formAnswerId string) (bool, string) {
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	var veevaId string
	querystring := `select 1,vel.veeva_reference_id  from veeva_event_logs vel where vel.form_answers_id =$1`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&hasValue, &veevaId)
	log.Println("querystring", querystring, "inputarges::::", formAnswerId)
	if err != nil {
		log.Println("false", veevaId, "return")
		return false, veevaId
	}
	log.Println("True", veevaId, "return")
	return true, veevaId
}

func GetVeevaEventCountries(ctx context.Context) string {
	functionName := "GetVeevaEventCountries()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select value from code where category ='Veeva Countries' and is_active = true and is_deleted = false`
	rows, err := pool.Query(ctx, queryString)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var countries string
	for rows.Next() {
		err := rows.Scan(&countries)
		if err != nil {
			panic(err)
		}
	}

	return countries
}
func CheckVeevaEventLogsFormAnswerId(ctx context.Context, formAnswerID string) bool {
	pool = GetPool()
	var placeholder int
	querystring := `SELECT 1 FROM  veeva_event_logs WHERE  form_answers_id::text=$1`
	err := pool.QueryRow(ctx, querystring, formAnswerID).Scan(&placeholder)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
		panic(err)
	}

	return true
}

func CheckCustomerExistOrNot(customerNo string, veevareferenceId string, name string) (bool, string) {
	pool = GetPool()

	var input string
	if customerNo != "" {
		input = customerNo
	} else if veevareferenceId != "" {
		input = veevareferenceId
	} else if name != "" {
		input = name
	}

	var check string
	query := `	select id::text from customer c where (c.customer_number = $1 OR c.veeva_reference_id=$1 OR c.name=$1)  AND c.is_active =true`
	err := pool.QueryRow(context.Background(), query, input).Scan(&check)
	if err != nil {
		return true, check
	}
	return false, check
}
func CreateCustomer(item entity.CustomerInputForVeevaAccount) {
	log.Println("createCustomer()", item.Name, item.Country, item.CustomerNo)

	queryString := `INSERT INTO customer (name,country,city,customer_number,gender,v_sp_desc,is_active,is_deleted,created_by,speaker_weight,cmsl_class,veeva_reference_id,organization,account_type) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) RETURNING(id)`
	inputArgs := []interface{}{item.Name, item.Country, item.City, item.CustomerNo, item.GenderID, item.SpDesc, item.IsActive, item.IsDeleted, "********-0000-0000-0000-********0000", item.SpeakerWeight, item.CMSLClass,
		item.VeevareferenceId, item.OrganizationName, item.RecordTypeName}
	_, err := GetPool().Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		panic(err)
	}
}
func UpdateCustomer(item entity.CustomerInputForVeevaAccount) {
	functionName := "updateCustomer()"
	log.Println(functionName, item.Name, item.Country)

	pool = GetPool()

	queryString := ` UPDATE customer set gender=?,is_active=?,is_deleted=?,modified_by=?,last_modified=now(),country=? `
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.GenderID, item.IsActive, !item.IsActive, "********-0000-0000-0000-********0000", item.Country)

	if strings.TrimSpace(item.Name) != "" {
		queryString += ` ,name=? `
		inputArgs = append(inputArgs, item.Name)
	}
	if item.SpeakerWeight != 0 {
		queryString += ` ,speaker_weight=? `
		inputArgs = append(inputArgs, item.SpeakerWeight)
	}
	if strings.TrimSpace(item.City) != "" {
		queryString += ` ,city=? `
		inputArgs = append(inputArgs, item.City)
	}
	if strings.TrimSpace(item.CustomerNo) != "" {
		queryString += ` ,customer_number=? `
		inputArgs = append(inputArgs, item.CustomerNo)
	}
	if strings.TrimSpace(item.SpDesc) != "" {
		queryString += ` ,v_sp_desc=? `
		inputArgs = append(inputArgs, item.SpDesc)
	}
	if strings.TrimSpace(item.CMSLClass) != "" {
		queryString += ` ,cmsl_class=? `
		inputArgs = append(inputArgs, item.CMSLClass)
	}
	if strings.TrimSpace(item.VeevareferenceId) != "" {
		queryString += ` ,veeva_reference_id=? `
		inputArgs = append(inputArgs, item.VeevareferenceId)
	}
	if strings.TrimSpace(item.OrganizationName) != "" {
		queryString += ` ,organization=? `
		inputArgs = append(inputArgs, item.OrganizationName)
	}
	if strings.TrimSpace(item.RecordTypeName) != "" {
		queryString += ` ,account_type=? `
		inputArgs = append(inputArgs, item.RecordTypeName)
	}
	queryString += ` where id=? `
	inputArgs = append(inputArgs, item.ID)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	_, err := pool.Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		panic(err)
	}
}

func InsertAllAccountVeevaJsonValues(jsonvalueForCustomerTable []byte, jsonvalueFromAccountVeeva []byte, integration string, errorMessage string) error {
	log.Println("InsertAllAccountVeevaJsonValues()")
	if pool == nil {
		pool = GetPool()
	}
	query := `insert into veeva_account_logs (status,error_message,integration_type,event_details,payload)
	values($1,$2,$3,$4,$5)`
	_, err := pool.Exec(context.Background(), query, true, errorMessage, integration, jsonvalueForCustomerTable, jsonvalueFromAccountVeeva)
	if err != nil {
		return err
	}
	return nil
}
func GetAllVeevaAccountDetails(country *int, input *model.RequestAllVeevaAccountDetails) ([]*model.AllVeevaAccountDetailsData, error) {
	log.Println("GetAllVeevaAccountDetails()")
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetIdKeyCodes()["country"]
	codesForGender := codeController.GetIdKeyCodes()["gender"]
	response := []*model.AllVeevaAccountDetailsData{}
	eventDetails := []byte{}
	querystring := `SELECT id, event_details, 
	EXTRACT(EPOCH FROM date_created::timestamp)::text FROM veeva_account_logs 
	where integration_type in ('INSERTAUTOMATION','UPDATEAUTOMATION')
	order by date_created desc`
	rows, err := pool.Query(context.Background(), querystring)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		ele := model.AllVeevaAccountDetailsData{}
		err := rows.Scan(
			&ele.ID,
			&eventDetails,
			&ele.DateCreated,
		)
		if err != nil {
			return nil, err
		}
		var res []entity.CustomerInputForVeevaAccount
		err = json.Unmarshal(eventDetails, &res)
		if err != nil {
			return nil, err
		}
		for _, val := range res {
			element := model.AllVeevaAccountDetailsData{}
			name := val.Name
			if country != nil {
				if *country != val.Country {
					continue
				}
			}
			if val.ID == "" {
				methodeValue := "HcpInsertAutomation"
				element.Method = &methodeValue
			} else {
				methodeValue := "HcpUpdateAutomation"
				element.Method = &methodeValue
			}
			CustomerID := val.ID
			CustomerNo := val.CustomerNo
			ErrorMessage := val.ErrorMessage
			CMSLClass := val.CMSLClass
			City := val.City
			Gender := codesForGender[val.GenderID].Title.String
			IsDeleted := val.IsDeleted
			IsActive := val.IsActive
			SpeakerWeight := val.SpeakerWeight
			SpDesc := val.SpDesc
			element.CustomerID = &CustomerID
			element.CustomerNo = &CustomerNo
			element.ErrorMessage = &ErrorMessage
			element.CMSLClass = &CMSLClass
			element.IsActive = &IsActive
			element.IsDeleted = &IsDeleted
			element.Gender = &Gender
			element.City = &City
			VeevareferenceId := val.VeevareferenceId
			element.Name = &name
			element.SpDesc = &SpDesc
			element.SpeakerWeight = &SpeakerWeight
			countryName := codes[val.Country].Title.String
			element.Country = &countryName
			element.VeevareferenceID = &VeevareferenceId
			element.ID = ele.ID
			element.DateCreated = ele.DateCreated
			response = append(response, &element)
		}
	}
	var Limit int
	var PageNo int
	if input.Limit != nil && *input.Limit > 0 {
		Limit = *input.Limit
		if input.Page != nil && *input.Page > 0 {
			PageNo = *input.Limit * (*input.Page - 1)
		}
	}
	responseData := Paginate(response, PageNo, Limit)
	return responseData, nil
}

func GetStatusForEachEvent2(formAnswerId string) string {
	pool = GetPool()
	querystring := `select rd.status_title  from reporting_db rd where rd.form_answer_id =$1`

	var statusEvent string
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&statusEvent)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ""
		}
		panic(err)
	}

	return statusEvent
}

func GetStatusForEachEvent(formAnswerId string) string {
	if pool == nil {
		pool = GetPool()
	}
	var statusEvent string
	querystring := `select rd.status_title  from reporting_db rd where rd.form_answer_id =$1`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&statusEvent)
	if err != nil {
		log.Println(err)
	}
	return statusEvent
}
func Paginate(x []*model.AllVeevaAccountDetailsData, skip int, size int) []*model.AllVeevaAccountDetailsData {
	if skip > len(x) {
		skip = len(x)
	}

	end := skip + size
	if end > len(x) {
		end = len(x)
	}

	return x[skip:end]
}

func GetRoleIDByValueInUserRolesCcO(roleValue string) (string, error) {
	functionName := "GetRoleIDByValueInUserRolesCcO()"
	log.Println(functionName)
	var codeValue string
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select id from user_roles where value = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), queryString, roleValue).Scan(&value)
	logengine.GetTelemetryClient().TrackEvent("GetRoleIDByValueInUserRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return "", err
	} else {
		codeValue = value
	}
	return codeValue, nil
}

func CheckUsersExistOrNot(ActiveDirectoryName string, countryId int) (bool, string) {
	if pool == nil {
		pool = GetPool()
	}
	var check string
	query := `select u.id::text  from "user" u   where lower(u.active_directory) =$1 and u.country=$2 `
	err := pool.QueryRow(context.Background(), query, strings.ToLower(ActiveDirectoryName), countryId).Scan(&check)
	if err != nil {
		return false, check
	}
	return true, check
}
func UpdateUsers(item entity.UsersInputForVeevaAccount, userid string) error {
	functionName := "UpdateUsers()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := ` update "user" set veeva_reference_id =?,is_active =? , is_deleted =false,last_modified=now() where id=?`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.VeevareferenceId, item.IsActive, userid)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	_, err := pool.Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}
func InsertAllAccountVeevaJsonValuesForUsers(usersTableResponse []byte, veevaUsersResponse []byte, integration string, errorMessage string) error {
	log.Println("InsertAllAccountVeevaJsonValues()")
	if pool == nil {
		pool = GetPool()
	}
	query := `insert into veeva_account_logs (status,error_message,integration_type,event_details,payload)
	values($1,$2,$3,$4,$5)`
	_, err := pool.Exec(context.Background(), query, true, errorMessage, integration, usersTableResponse, veevaUsersResponse)
	if err != nil {
		return err
	}
	return nil
}
func CheckProductExistOrNot(ProductName string, countryId int) (bool, string) {
	if pool == nil {
		pool = GetPool()
	}
	var check string
	query := `	select m.id::text from material m where m.group_name =$1 and m.country =$2 order by m.date_created desc`
	err := pool.QueryRow(context.Background(), query, ProductName, countryId).Scan(&check)
	if err != nil {
		return false, check
	}
	return true, check
}
func CreateProducts(item entity.ProductInputForVeevaAccount, ProductOwnerId string) error {
	functionName := "CreateProducts()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `INSERT INTO material  (created_by,is_active,is_deleted,division,group_code,group_name,country,veeva_reference_id`
	query := ` VALUES($1,$2,$3,$4,$5,$6,$7,$8`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, "********-0000-0000-0000-********0000", true, item.IsDeleted, "28a2b691-3dc7-46ad-bab9-384950b529ef", item.GroupCode, item.ProductName, item.Country, item.VeevareferenceId)
	if ProductOwnerId != "" {
		queryString += `,product_owner_uid)`
		query += `,$9) RETURNING(id)`
		inputArgs = append(inputArgs, ProductOwnerId)
	} else {
		queryString += `)`
		query += `) RETURNING(id)`
	}
	queryString += query
	err := pool.QueryRow(context.Background(), queryString, inputArgs...).Scan(&item.Productid)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}

func UpdateProducts(item entity.ProductInputForVeevaAccount, ProductOwnerId string) error {
	functionName := "UpdateProducts()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := ` update material set last_modified =now(),modified_by =?,is_active =?,is_deleted =?,division =?,country =?,group_name =?,veeva_reference_id =? `
	var inputArgs []interface{}
	inputArgs = append(inputArgs, "********-0000-0000-0000-********0000", !item.IsDeleted, item.IsDeleted, "28a2b691-3dc7-46ad-bab9-384950b529ef", item.Country, item.ProductName, item.VeevareferenceId)
	if strings.TrimSpace(item.GroupCode) != "" {
		queryString += ` ,group_code =? `
		inputArgs = append(inputArgs, item.GroupCode)
	}
	if strings.TrimSpace(ProductOwnerId) != "" {
		queryString += ` ,product_owner_uid =? `
		inputArgs = append(inputArgs, ProductOwnerId)
	}
	queryString += ` where id=?  `
	inputArgs = append(inputArgs, item.Productid)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	_, err := pool.Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}
func InsertAllAccountVeevaJsonValuesForProduct(usersTableResponse []byte, veevaUsersResponse []byte, integration string, errorMessage string) error {
	log.Println("InsertAllAccountVeevaJsonValuesForProduct()")
	if pool == nil {
		pool = GetPool()
	}
	query := `insert into veeva_account_logs (status,error_message,integration_type,event_details,payload)
	values($1,$2,$3,$4,$5)`
	_, err := pool.Exec(context.Background(), query, true, errorMessage, integration, usersTableResponse, veevaUsersResponse)
	if err != nil {
		return err
	}
	return nil
}
func CheckProductOwner(productOwner string, country int) (bool, string) {
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	var productOwnerID string
	query := `select 1,id from product_owner po where po.owner_name =$1 and po.country =$2 `
	err := pool.QueryRow(context.Background(), query, productOwner, country).Scan(&hasValue, &productOwnerID)
	if err == nil {
		return true, productOwnerID
	}
	return false, ""
}
func InsertProductOwner(item entity.ProductInputForVeevaAccount) string {
	functionName := "InsertProductOwner"
	if pool == nil {
		pool = GetPool()
	}
	var ProductOwnerid string
	query := `insert into product_owner (is_active,	is_deleted,	country,owner_name) values($1,$2,$3,$4) RETURNING(id)`
	var inputArgsProductOwner []interface{}
	inputArgsProductOwner = append(inputArgsProductOwner, true, false, item.Country, item.ProductOwner)
	err1 := pool.QueryRow(context.Background(), query, inputArgsProductOwner...).Scan(&ProductOwnerid)
	if err1 != nil {
		log.Printf("%s - error: {%s}", functionName, err1.Error())
	}
	return ProductOwnerid
}
func UpdateProductOwner(item entity.ProductInputForVeevaAccount, productOwnerId string) {
	functionName := "UpdateProductOwner"
	if pool == nil {
		pool = GetPool()
	}
	query := `update product_owner set is_active =$2,is_deleted =$3 ,last_modified =now() where id=$1`
	var inputArgsProductOwner []interface{}
	inputArgsProductOwner = append(inputArgsProductOwner, productOwnerId, !item.IsDeleted, item.IsDeleted)
	_, err := pool.Exec(context.Background(), query, inputArgsProductOwner...)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
}

func CheckMeetingModeTitleExist(meetingMode string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var value int
	queryString := `select 1 from code c where c.category ='MeetingMode' and c.title =$1`
	err := pool.QueryRow(context.Background(), queryString, meetingMode).Scan(&value)
	if err != nil {
		return false
	} else {
		return true
	}
}

func GetFailureFromVeevaAttendance() ([]string, error) {
	functionName := "GetFailureFromVeevaAttendance()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select error_message from veeva_account_logs val where  integration_type ='CREATEATTENDACEVEEVA' and error_message <> '' and date_created >= current_date - 1 `
	//  + os.Getenv("VEEVA_ALL_EVENT_ATTENDANCE_FAILURE_DAY_INTERVAL")
	var veevaIdArray []string
	var urls []string
	rows, err := pool.Query(context.Background(), queryString)
	if err != nil {
		return veevaIdArray, err
	}
	for rows.Next() {
		var url string
		rows.Scan(
			&url,
		)
		if err != nil {
			return veevaIdArray, err
		}
		urls = append(urls, url)
	}
	for _, val := range urls {
		parts := strings.Split(val, "Medical_Event_vod__c+=+'")
		if len(parts) > 1 {
			veevaId := strings.Split(parts[1], "'")[0]
			veevaIdArray = append(veevaIdArray, veevaId)
		}
	}
	return veevaIdArray, err
}

func FetchCheckStatusFromVeevaAttendanceFailure(veevaIds []string) ([]string, []string, error) {
	functionName := "FetchCheckStatusFromVeevaAttendanceFailure()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	var veevaIdValues []string

	// Iterate over the slice and format each value
	for _, value := range veevaIds {
		veevaIdValues = append(veevaIdValues, fmt.Sprintf("'%s'", value))
	}

	// Join the formatted values with commas
	result := strings.Join(veevaIdValues, ",")
	queryString := `select veeva_reference_id,form_answers_id from veeva_event_logs 
	where  veeva_reference_id in (` + result + `) `
	var veevaIdArray []string
	var veevaId string
	var formId []string
	var formAnswerId string

	rows, err := pool.Query(context.Background(), queryString)
	if err != nil {
		return formId, veevaIdArray, err
	}
	for rows.Next() {
		err := rows.Scan(
			&veevaId,
			&formAnswerId,
		)
		if err != nil {
			return formId, veevaIdArray, err
		}
		veevaIdArray = append(veevaIdArray, veevaId)
		formId = append(formId, formAnswerId)
	}

	return formId, veevaIdArray, err
}

func CheckStatusFromVeevaAttendance() ([]string, []string, error) {
	functionName := "CheckStatusFromVeevaAttendance()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := fmt.Sprintf(`
		select veeva_reference_id,form_answers_id 
		from veeva_event_logs 
		where  veeva_reference_id is not null  and veeva_reference_id <> '' and  date_created >= current_date -  %s
		ORDER BY date_created`, os.Getenv("VEEVA_ALL_EVENT_ATTENDANCE_DAY_INTERVAL"))
	var veevaIdArray []string
	var veevaId string
	var formId []string
	var formAnswerId string
	rows, err := pool.Query(context.Background(), queryString)
	if err != nil {
		return formId, veevaIdArray, err
	}
	for rows.Next() {
		rows.Scan(
			&veevaId,
			&formAnswerId,
		)
		if err != nil {
			return formId, veevaIdArray, err
		}
		veevaIdArray = append(veevaIdArray, veevaId)
		formId = append(formId, formAnswerId)
	}
	return formId, veevaIdArray, err
}

func CheckStatusFromVeevaAttendance2(meetingID string) (string, string, error) {
	if meetingID == "" {
		return "", "", errors.New("meetingID is empty")
	}

	functionName := "CheckStatusFromVeevaAttendance()"
	log.Println(functionName)

	pool = GetPool()

	queryString := fmt.Sprintf(`
	select l.veeva_reference_id, l.form_answers_id 
	from veeva_event_logs l
	LEFT JOIN reporting_db r ON r.form_answer_id = l.form_answers_id
	WHERE r.event_seq = $1 and r.event_code = $2
`)

	var veevaId string
	var formAnswerId string
	s := strings.Split(meetingID, "-")
	row := pool.QueryRow(context.Background(), queryString, s[3], fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2]))

	err := row.Scan(
		&veevaId,
		&formAnswerId,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", "", nil
		}
		panic(err)
	}

	return formAnswerId, veevaId, err
}

func InsertAttendanceData(attendaceJsonRes []byte, allAttendanceResponse []byte, formanswerID string, veevaID string, isApollo bool) error {
	functionName := "InsertAttendanceData()"
	if pool == nil {
		pool = GetPool()
	}
	query := `INSERT INTO form_attendances
	( form_answers_id, event_attendances, is_active, date_created, event_attendances_internal_jsonb,is_veeva,is_apollo)
	VALUES($1,$2,$3,now(),$4,true,$5 )`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, formanswerID, attendaceJsonRes, true, allAttendanceResponse, isApollo)
	_, err1 := pool.Exec(context.Background(), query, inputArgs...)
	if err1 != nil {
		log.Printf("%s - error: {%s}", functionName, err1.Error())
		return err1
	}
	if allAttendanceResponse != nil && attendaceJsonRes != nil {
		err := VeevaAccountLogsUpdateAttendeesField(formanswerID, veevaID)
		if err != nil {
			log.Printf("%s - error: {%s}", functionName, err.Error())
			return err
		}
	}
	return nil
}

func VeevaAccountLogsUpdateAttendeesField(formanswerID string, veevaID string) error {
	functionName := "VeevaAccountLogsUpdateAttendeesField()"
	if pool == nil {
		pool = GetPool()
	}
	var inputArgForVeevaEventLog []interface{}
	queryString := `update veeva_event_logs set is_attendance_update =true  where form_answers_id =$1 and veeva_reference_id =$2`
	inputArgForVeevaEventLog = append(inputArgForVeevaEventLog, formanswerID, veevaID)
	_, err2 := pool.Exec(context.Background(), queryString, inputArgForVeevaEventLog...)
	if err2 != nil {
		log.Printf("%s - error: {%s}", functionName, err2.Error())
		return err2
	}
	return nil
}

func UpdateAttendanceData(formanswerID string, attendaceId string, attendaceJsonRes []byte, allAttendanceResponse []byte, veevaID string) error {
	functionName := "UpdateAttendanceData()"
	if pool == nil {
		pool = GetPool()
	}
	query := `UPDATE form_attendances
	SET  event_attendances=$3, is_active=true, date_created=now(), event_attendances_internal_jsonb=$4, is_veeva=true
	WHERE id=$1 and form_answers_id=$2;
	`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, attendaceId, formanswerID, attendaceJsonRes, allAttendanceResponse)
	_, err1 := pool.Exec(context.Background(), query, inputArgs...)
	if err1 != nil {
		log.Printf("%s - error: {%s}", functionName, err1.Error())
		return err1
	}
	if allAttendanceResponse != nil && attendaceJsonRes != nil {
		err := VeevaAccountLogsUpdateAttendeesField(formanswerID, veevaID)
		if err != nil {
			log.Printf("%s - error: {%s}", functionName, err.Error())
			return err
		}
	}
	return nil
}

func VeevaGetAllveevaAttendanceCreatedData(veevaId string, formanswerID string, intrigationtype string) error {
	log.Println("VeevaGetAllveevaAttendanceCreatedData()")
	var attendanceOriginalResponse []entity.EmployeeAttendanceExcelForOriginalInput
	var attendanceResponse []entity.EmployeeAttendanceExcelInput
	var allModifiedValuesForAttendance []entity.AttendanceRecords
	if os.Getenv("AUTH_VEEVA_TOKEN") == "X" {
		veeva.GetAuthRest()
	}
	allModifiedValuesForAttendance, err := veeva.VeevaGetAllAttendanceListCreate(veevaId)
	var Err string
	if err != nil {
		if err.Error() == "Session expired or invalid" {
			veeva.GetAuthRest()
			allModifiedValuesForAttendance, _ = veeva.VeevaGetAllAttendanceListCreate(veevaId)
		} else {
			Err = err.Error() + " "
		}
	}
	eventCode, eventSeq, attendanceId, _ := GetEventIdandcheckAttendanceDB(formanswerID)
	getMeetingId := util.ConvertToEventID(eventCode, eventSeq)
	for _, item := range allModifiedValuesForAttendance {
		var AttendanceOriginalEntity entity.EmployeeAttendanceExcelForOriginalInput
		var AttendanceEntity entity.EmployeeAttendanceExcelInput
		var concatName string
		if strings.TrimSpace(getMeetingId) != "" {
			if strings.TrimSpace(item.FirstName) != "" || strings.TrimSpace(item.LastName) != "" {
				concatName += item.FirstName
				concatName += item.LastName
				AttendanceOriginalEntity.FirstNameEzClaim = item.FirstName
				AttendanceOriginalEntity.LastNameEzClaim = item.LastName
				AttendanceEntity.FirstNameEzClaim = item.FirstName
				AttendanceEntity.LastNameEzClaim = item.LastName
				threeDigitRandomNumber := rand.Intn(899) + 100
				concatName += strconv.Itoa(threeDigitRandomNumber)
				AttendanceOriginalEntity.AccountName = strings.ReplaceAll(concatName, " ", "")
				AttendanceEntity.AccountName = strings.ReplaceAll(concatName, " ", "")
				AttendanceEntity.LastName = item.FirstName + " " + item.LastName
				AttendanceOriginalEntity.LastName = item.FirstName + " " + item.LastName
			}
			if strings.TrimSpace(item.AccountId) != "" {
				AttendanceOriginalEntity.AccountId = item.AccountId
				AttendanceEntity.AccountId = item.AccountId
			}
			if strings.TrimSpace(item.ID) != "" {
				AttendanceOriginalEntity.ExternalUniqueId = item.ID
				AttendanceEntity.ExternalUniqueId = item.ID
			}
			if strings.TrimSpace(item.AccountTable.PrimaryParentvodr.Name) != "" {
				AttendanceOriginalEntity.Affiliation = item.AccountTable.PrimaryParentvodr.Name
			} else {
				AttendanceOriginalEntity.Affiliation = item.Organization
			}
			if strings.TrimSpace(item.AccountTable.Specialty1QSr.Name) != "" {
				AttendanceOriginalEntity.Specialty = item.AccountTable.Specialty1QSr.Name
				AttendanceOriginalEntity.Title = item.AccountTable.Specialty1QSr.Name
				AttendanceEntity.Title = item.AccountTable.Specialty1QSr.Name
			} else if strings.TrimSpace(item.AccountTable.Specialty2QSr.Name) != "" {
				AttendanceOriginalEntity.Specialty = item.AccountTable.Specialty2QSr.Name
				AttendanceOriginalEntity.Title = item.AccountTable.Specialty2QSr.Name
				AttendanceEntity.Title = item.AccountTable.Specialty2QSr.Name
			} else if strings.TrimSpace(item.AccountTable.ZLGLocalSpecialtyr.Name) != "" {
				AttendanceOriginalEntity.Specialty = item.AccountTable.ZLGLocalSpecialtyr.Name
				AttendanceOriginalEntity.Title = item.AccountTable.ZLGLocalSpecialtyr.Name
				AttendanceEntity.Title = item.AccountTable.ZLGLocalSpecialtyr.Name
			} else {
				AttendanceOriginalEntity.Specialty = item.Specialty
				AttendanceOriginalEntity.Title = item.Specialty
				AttendanceEntity.Title = item.Specialty
			}
			AttendanceEntity.FirstName = getMeetingId
			AttendanceEntity.SystemCode = "eZFlow"
			if strings.ToLower(strings.TrimSpace(item.Status)) == "attended" {
				AttendanceEntity.Status = "ACTIVE"
			} else {
				AttendanceEntity.Status = "INACTIVE"
			}
			AttendanceEntity.ContactType = "CRM"
			AttendanceOriginalEntity.FirstName = getMeetingId
			AttendanceOriginalEntity.SystemCode = "eZFlow"
			if strings.ToLower(strings.TrimSpace(item.Status)) == "attended" {
				AttendanceOriginalEntity.Status = "ACTIVE"
			} else {
				AttendanceOriginalEntity.Status = "INACTIVE"
			}
			AttendanceOriginalEntity.ContactType = "CRM"
			attendanceResponse = append(attendanceResponse, AttendanceEntity)
			attendanceOriginalResponse = append(attendanceOriginalResponse, AttendanceOriginalEntity)
		}

	}
	attendaceJsonRes, err := json.Marshal(attendanceResponse)
	if err != nil {
		Err += err.Error() + " "
	}
	allAttendanceResponse, err := json.Marshal(attendanceOriginalResponse)
	if err != nil {
		Err += err.Error() + " "
	}

	if attendanceId == "" && attendanceResponse != nil && attendanceOriginalResponse != nil {
		err = InsertAttendanceData(attendaceJsonRes, allAttendanceResponse, formanswerID, veevaId, false)
		if err != nil {
			Err += err.Error() + " "
		}
	} else if attendanceId != "" {
		err := UpdateAttendanceData(formanswerID, attendanceId, attendaceJsonRes, allAttendanceResponse, veevaId)
		if err != nil {
			Err += err.Error() + " "
		}
	}
	getjsonvalueFromAccountVeevaAttendances, err := json.Marshal(allModifiedValuesForAttendance)
	if err != nil {
		Err += err.Error() + " "
	}
	err = InsertAllAccountVeevaJsonValuesForAttendancelist(attendaceJsonRes, getjsonvalueFromAccountVeevaAttendances, intrigationtype, Err)
	if err != nil {
		return err
	}
	return nil
}
func InsertAllAccountVeevaJsonValuesForAttendancelist(attendanceTableResponse []byte, veevaUsersResponse []byte, integration string, errorMessage string) error {
	log.Println("InsertAllAccountVeevaJsonValuesForAttendancelist()")
	fmt.Println()
	if pool == nil {
		pool = GetPool()
	}
	query := `insert into veeva_account_logs (status,error_message,integration_type,event_details,payload)
		values($1,$2,$3,$4,$5)`
	_, err := pool.Exec(context.Background(), query, true, errorMessage, integration, attendanceTableResponse, veevaUsersResponse)
	if err != nil {
		return err
	}
	return nil
}
func GetAllAttendanceVeevaAccountDetails(country *int, input entity.AllAttendanceVeevaInputEntityData) ([]entity.AllAttendanceVeevaAccountDetailsEntityData, error) {
	log.Println("GetAllAttendanceVeevaAccountDetails()")
	if pool == nil {
		pool = GetPool()
	}
	response := []entity.AllAttendanceVeevaAccountDetailsEntityData{}
	var inputArgs []interface{}
	var rows pgx.Rows
	querystring := `SELECT id,date_created,
    employee_data ->> 'title' AS title,
    employee_data ->> 'lastName' AS last_name,
	employee_data ->> 'last_name_ezclaim' AS last_name_ezclaim,
    employee_data ->> 'accountId' AS account_id,
    employee_data ->> 'firstName' AS first_name,
	employee_data ->> 'first_name_ezclaim' AS first_name_ezclaim,
    employee_data ->> 'systemCode' AS system_code,
    employee_data ->> 'accountName' AS account_name,
    employee_data ->> 'contactType' AS contact_type,
    employee_data ->> 'affiliation' AS organization,
    employee_data ->> 'externalUniqueId' AS external_unique_id,
    (CASE WHEN employees.is_veeva = true and   (employee_data ->> 'accountId' <> '' and employee_data ->> 'accountId' is not null)  THEN true   ELSE COALESCE((employee_data ->> 'isHcp')::boolean, false) END) AS ishcp,
	(CASE WHEN employees.is_veeva = true and   (employee_data ->> 'accountId' <> '' and employee_data ->> 'accountId' is not null)  THEN true   ELSE COALESCE((employee_data ->> 'isSpeaker')::boolean, false) END) AS isSpeaker
FROM 
    (SELECT 
        fa.id,fa.form_answers_id,fa.is_veeva,fa.is_active,jsonb_array_elements(event_attendances_internal_jsonb) AS employee_data,(EXTRACT(EPOCH FROM fa.date_created::timestamp)::integer)::text as date_created
     FROM 
        form_attendances fa 
        left join veeva_event_logs vel on vel.form_answers_id =fa.form_answers_id 
        where fa.event_attendances_internal_jsonb not in  ('null') 
        ) AS employees
        where is_active=true and employee_data ->> 'status'='ACTIVE' `
	if input.FormAnswerID != uuid.Nil {
		querystring += `and form_answers_id =$1`
		inputArgs = append(inputArgs, input.FormAnswerID)
	}
	if strings.TrimSpace(input.Name) != "" {
		querystring += `and replace(employee_data ->> 'lastName',' ','') ilike ?`
		inputArgs = append(inputArgs, "%"+strings.ReplaceAll(strings.TrimSpace(input.Name), " ", "")+"%")
	}
	if strings.TrimSpace(input.EventID) != "" {
		querystring += `and employee_data ->> 'firstName' ilike ?`
		inputArgs = append(inputArgs, "%"+strings.TrimSpace(input.EventID)+"%")
	}
	if strings.TrimSpace(input.DateCreatedFrom) != "" || strings.TrimSpace(input.DateCreatedTo) != "" {
		if strings.TrimSpace(input.DateCreatedFrom) != "" && strings.TrimSpace(input.DateCreatedTo) != "" {
			querystring = querystring + ` and ((( case when
                (date_created) ~ '^[0-9\.]+$'
                then (to_timestamp((date_created):: integer))::timestamp end) >= ? ) and 
                (( case when
                (date_created) ~ '^[0-9\.]+$'
                then (to_timestamp((date_created):: integer))::timestamp end) <= ? ))`
			inputArgs = append(inputArgs, strings.TrimSpace(input.DateCreatedFrom), strings.TrimSpace(input.DateCreatedTo))
		} else if strings.TrimSpace(input.DateCreatedFrom) != "" && (input.DateCreatedTo == "") {
			querystring = querystring + ` and (( case when
                (date_created) ~ '^[0-9\.]+$'
                then (to_timestamp((date_created):: integer))::timestamp end) >= ?)`
			inputArgs = append(inputArgs, strings.TrimSpace(input.DateCreatedFrom))
		} else if (input.DateCreatedFrom == "") && strings.TrimSpace(input.DateCreatedTo) != "" {
			querystring = querystring + ` and (( case when
                (date_created) ~ '^[0-9\.]+$'
                then (to_timestamp((date_created):: integer))::timestamp::date end) <= ?)`
			inputArgs = append(inputArgs, strings.TrimSpace(input.DateCreatedTo))
		}
	}
	querystring += ` order by date_created desc `
	if input.Limit > 0 {
		if input.Page > 0 {
			querystring = querystring + ` limit ? offset ?`

			inputArgs = append(inputArgs, input.Limit)
			inputArgs = append(inputArgs, input.Page)
		} else {
			querystring = querystring + ` limit ? offset ?`
			inputArgs = append(inputArgs, input.Limit)
			inputArgs = append(inputArgs, 0)
		}
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputArgs...)
	//fmt.Println("==============", querystring)
	//fmt.Println(inputArgs)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		ele := entity.AllAttendanceVeevaAccountDetailsEntityData{}
		err := rows.Scan(
			&ele.ID,
			&ele.DateCreated,
			&ele.Specialty,
			&ele.Name,
			&ele.LastName,
			&ele.AccountveevaID,
			&ele.EventID,
			&ele.FirstName,
			&ele.Systemcode,
			&ele.AccountName,
			&ele.ContactType,
			&ele.Organization,
			&ele.AttendanceveevaID,
			&ele.IsHcp,
			&ele.IsSpeaker,
		)
		if err != nil {
			return nil, err
		}
		response = append(response, ele)
	}

	return response, nil
}
func PaginateforAttendance(x []*model.AllAttendanceVeevaAccountDetailsData, skip int, size int) []*model.AllAttendanceVeevaAccountDetailsData {
	if skip > len(x) {
		skip = len(x)
	}

	end := skip + size
	if end > len(x) {
		end = len(x)
	}

	return x[skip:end]
}

func EmailAlertForVeevaFailureEventDb() ([]entity.EventDetailsExcelEmailAlert, error) {
	log.Println("EmailAlertForVeevaFailureEventDb()")
	if pool == nil {
		pool = GetPool()
	}
	var response []entity.EventDetailsExcelEmailAlert
	query := `select fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,2,values,0,value}' as activity_name,
	fa.answers#>>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values,0,description}' as activity_type,
	(case when fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}' ~ '^[0-9\.]+$'
	then (to_timestamp((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}'):: integer))::timestamp end) as activity_start_date,
	(case when fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}' ~ '^[0-9\.]+$'
	then (to_timestamp((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}'):: integer))::timestamp end) as activity_end_date,
	concat(fa.event_code,'-',fa.event_seq) as event_id,
	fa.answers#>>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,description}' as event_type,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,11,values,0,value}' as venue,
	(select title from code where id=fa.status) as event_status,
	(select u.veeva_reference_id from "user" u where u.id =fa.created_by) as requestor_veeva_id,
	(select concat(u.first_name,' ',u.last_name )from "user" u  where u.id =fa.created_by) as requestor_id,
	(select u.veeva_reference_id from "user" u where concat(u.first_name ,' ',u.last_name) = fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}' and u.country = fa.country and u.is_active=true and u.is_deleted=false limit 1) as owner_id,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,6,values,0,value}' as product,
	(select title  from code where id = fa.country and is_active = true) as country,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,23,values,0,value}' as meeting_mode,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,24,values,0,value}' as virtual_event_details,
	vel.status as veeva_status,vel.date_created as veeva_date_created,vel.error_message 
	from veeva_event_logs vel 
	inner join form_answers fa on fa.id = vel.form_answers_id 
	where  vel.status =false  order by vel.date_created desc `
	rows, err := pool.Query(context.Background(), query)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		ele := entity.EventDetailsExcelEmailAlert{}
		err := rows.Scan(
			&ele.ActivityName,
			&ele.ActivityType,
			&ele.ActivityStartDate,
			&ele.ActivityEndDate,
			&ele.EventId,
			&ele.EventType,
			&ele.Venue,
			&ele.Status,
			&ele.RequestorVeevaId,
			&ele.RequestorId,
			&ele.OwnerId,
			&ele.ProductId,
			&ele.Country,
			&ele.MeetingMode,
			&ele.VirtualEventDetails,
			&ele.VeevaStatus,
			&ele.VeevaDateCreated,
			&ele.ErrorMessage,
		)
		if err != nil {
			return nil, err
		}
		response = append(response, ele)
	}
	return response, nil

}
func EmailAlertForVeevaAllProductUserAndCustomerDb() ([]entity.OtherVeevaDetailsExcelEmailAlert, error) {
	log.Println("EmailAlertForVeevaAllProductUserAndCustomerDb()")
	if pool == nil {
		pool = GetPool()
	}
	var response []entity.OtherVeevaDetailsExcelEmailAlert
	query := `select id,status,error_message,integration_type,event_details,payload,date_created
	from veeva_account_logs val where  (val.integration_type in ('CREATEATTENDACEVEEVA','UPDATEAUTOMATION','CREATEDPRODUCT','UPDATEPRODUCT','MODIFIEDUSERUPDATE','CREATEDUSERUPDATE','INSERTAUTOMATION') and date_created ::date =current_date -1) `
	rows, err := pool.Query(context.Background(), query)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		ele := entity.OtherVeevaDetailsExcelEmailAlert{}
		err := rows.Scan(
			&ele.Id,
			&ele.Status,
			&ele.ErrorMessage,
			&ele.IntegrationType,
			&ele.EventDetails,
			&ele.Payload,
			&ele.DateCreated,
		)
		if err != nil {
			return nil, err
		}
		response = append(response, ele)
	}
	return response, nil

}

func FetchMultiFormAnswersStatus(ctx context.Context, pool *pgxpool.Pool, input []string) []entity.MeetingIDWithEventID {
	log.Println("FetchMultiFormAnswersStatus()")

	rows, err := pool.Query(ctx, `SELECT id,status,event_seq,event_code FROM form_answers WHERE id = Any($1)`, pq.Array(input))
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	response := []entity.MeetingIDWithEventID{}
	for rows.Next() {
		data := entity.MeetingIDWithEventID{}
		var eventSeq int
		var eventCode string
		if err := rows.Scan(&data.Id, &data.Status, &eventSeq, &eventCode); err != nil {
			panic(err)
		}

		data.MeetingID = fmt.Sprintf("%s-%d", eventCode, eventSeq)
		response = append(response, data)
	}

	return response
}

func UpdateMultiEventStatusToApproved(formAnswerId []string, tx pgx.Tx) {
	querystring := `UPDATE form_answers 
	SET status = 58, 
	local_approval_status = 58,
	regional_approval_status = case when regional_approval_status is null then null
	else 58 end
	WHERE form_answers.id = ANY($1)`
	_, err := tx.Exec(context.Background(), querystring, pq.Array(formAnswerId))
	if err != nil {
		panic(err)
	}

	queryString := `UPDATE reporting_db
						SET status_title = 'Approved', status_value = 'approved', updated_date = now() 
						WHERE form_answer_id = ANY($1) `
	_, err = tx.Exec(context.Background(), queryString, pq.Array(formAnswerId))
	if err != nil {
		panic(err)
	}
}

func UpdateEventStartTimeAndEndTimeToNow(tx pgx.Tx, formAnswerID string) {
	queryUpdateStartTime := `
		UPDATE form_answers 
		SET 
			"answers" = jsonb_set("answers"::jsonb, '{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}', to_jsonb(EXTRACT(epoch from now())::INTEGER::TEXT))
		WHERE id = $1`
	queryUpdateEndTime := `
		UPDATE form_answers 
		SET 
			"answers" = jsonb_set("answers"::jsonb, '{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}', to_jsonb(EXTRACT(epoch from now())::INTEGER::TEXT))
		WHERE id = $1`

	_, err := tx.Exec(context.Background(), queryUpdateStartTime, formAnswerID)
	if err != nil {
		panic(err)
	}

	_, err = tx.Exec(context.Background(), queryUpdateEndTime, formAnswerID)
	if err != nil {
		panic(err)
	}

	_, err = tx.Exec(context.Background(), `UPDATE reporting_db SET 
	activity_start_date[3] = EXTRACT(epoch from now())::INTEGER,
	activity_start_date[4] = EXTRACT(epoch from now())::INTEGER
	WHERE form_answer_id = $1`, formAnswerID)
	if err != nil {
		panic(err)
	}
}
func UpdateNumberofEMP(tx pgx.Tx, meetingID string, empNumbers int) {
	s := strings.Split(meetingID, "-")
	eventCodes := fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2])
	eventSeqs := s[3]
	empNumbersStr := fmt.Sprintf("%d", empNumbers)
	queryUpdateEMPNo := `
		UPDATE form_answers 
		SET 
			"answers" = jsonb_set("answers"::jsonb, '{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,17,values,0,value}', to_jsonb($1::text))
		WHERE event_code = $2 AND event_seq = $3`

	_, err := tx.Exec(context.Background(), queryUpdateEMPNo, empNumbersStr, eventCodes, eventSeqs)
	if err != nil {
		panic(err)
	}
}

func DeleteMultiDraftEvent(ctx context.Context, tx pgx.Tx, eventIDs []string) {
	for _, eventID := range eventIDs {
		DeleteDraftEvent(ctx, tx, eventID)
	}
}

func DeleteDraftEvent(ctx context.Context, tx pgx.Tx, eventID string) {
	// List of table and column name which are related to form_answer id.
	tables := map[string]string{
		"approvers":                  "form_answer_id = $1",
		"email_log":                  "event_id = $1",
		"reporting_db":               "form_answer_id = $1",
		"status_change_notification": "form_answer = $1",
		"form_attendances":           "form_answers_id = $1",
		"veeva_event_logs":           "form_answers_id = $1",
		"approval_log":               "form_answer = $1",
	}

	for table, condition := range tables {
		query := fmt.Sprintf("DELETE FROM %s WHERE %s", table, condition)
		//fmt.Println(query)
		_, err := tx.Exec(ctx, query, eventID)
		if err != nil {
			panic(err)
		}

	}
	// Also delete from formAnswers
	_, err := tx.Exec(ctx, "DELETE FROM form_answers WHERE id = $1", eventID)
	if err != nil {
		panic(err)
	}
}

func GetEventIDsFromMeetingIds(ctx context.Context, pool *pgxpool.Pool, meetingIDs []string) []string {
	var eventCodes []string
	var eventSeqs []string
	for _, meetingID := range meetingIDs {
		s := strings.Split(meetingID, "-")
		eventCodes = append(eventCodes, fmt.Sprintf(`%s-%s-%s`, s[0], s[1], s[2]))
		eventSeqs = append(eventSeqs, s[3])
	}

	query := `
        SELECT id FROM form_answers 
        WHERE (event_code, event_seq) IN (
            ` + createValueList(len(eventCodes)) + `
        )
    `

	args := make([]interface{}, 0)
	for i, code := range eventCodes {
		args = append(args, code, eventSeqs[i])
	}

	rows, err := pool.Query(ctx, query, args...)
	if err != nil {
		// Always panic on the errors returned from query, exec, begin, commit ...
		panic(err)
	}
	defer rows.Close()

	// Collect all formAnswerIDs
	var formAnswerIDs []string
	for rows.Next() {
		var formAnswerID string
		if err := rows.Scan(&formAnswerID); err != nil {
			panic(err)
		}

		formAnswerIDs = append(formAnswerIDs, formAnswerID)
	}

	return formAnswerIDs
}

func createValueList(count int) string {
	placeholders := make([]string, 0, count)
	for i := 0; i < count; i++ {
		placeholders = append(placeholders, fmt.Sprintf("($%d, $%d)", i*2+1, i*2+2))
	}
	return strings.Join(placeholders, ", ") // It will look like this ($1, $2), ($3, $4)
}
