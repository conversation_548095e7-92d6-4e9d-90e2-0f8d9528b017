package auth

import (
	"context"
	"log"

	"fmt"
	"net/http"
	"strings"

	jwt "github.com/dgrijalva/jwt-go"
	"github.com/ihcp/login/postgres"
	"github.com/jackc/pgx/v4/pgxpool"
)

var jwtSecret []byte = []byte("A6CF26F631A6DEF753D4697834146")

type contextKey struct {
	name string
}

var userCtxKey = &contextKey{"UserID"}
var userCountryCtxKey = &contextKey{"Country"}
var userADCtxKey = &contextKey{"ActiveDirectoryName"}
var userApprovalRoleCtxKey = &contextKey{"UserRole"}
var userRoleID = &contextKey{"ApprovalRoleID"}

type User struct {
	ActiveDirName string
	IAT           int64
}

var user User

func ExtractUserADFromRequestHeader(header string) (string, error) {
	tokenString := strings.Split(header, " ")
	var adName string
	// Parse Token
	token, err := jwt.Parse(tokenString[len(tokenString)-1], func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("There was an error")
		}
		return jwtSecret, nil
	})
	if err != nil {
		return "", fmt.Errorf("Token secret key is invalid!")
	}
	//Verify token secret
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		adName = claims["loggedInAs"].(string)

	} else {
		return "", fmt.Errorf("Token secret key is invalid!")
	}
	return adName, nil
}

func ExtractUserCountryFromRequestHeader(header string) (string, error) {
	tokenString := strings.Split(header, " ")
	var adName string
	// Parse Token
	token, err := jwt.Parse(tokenString[len(tokenString)-1], func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("There was an error")
		}
		return jwtSecret, nil
	})
	if err != nil {
		return "", fmt.Errorf("Token secret key is invalid!")
	}

	//Verify token secret
	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		adName = claims["country"].(string)

	} else {
		return "", fmt.Errorf("Token secret key is invalid!")
	}
	return adName, nil
}

func AuthMiddleware(pool *pgxpool.Pool) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(response http.ResponseWriter, request *http.Request) {
			defer func() {
				if err := recover(); err != nil {
					ctxADName := context.WithValue(request.Context(), userADCtxKey, nil)
					request = request.WithContext(ctxADName)
					next.ServeHTTP(response, request)
					return
				}
			}()

			// For MAPTOOL
			func() {
				header := request.Header.Get("Maptool-Auth-Token")
				if header != "" {
					fmt.Println(header)
					log.Println(`>>>>>>>>>>>>>>>>>>>>>>>>>>>> Incoming MAPTOOL Request >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>`)
					request = request.WithContext(context.WithValue(request.Context(), "MAPTOOL_AUTH_TOKEN", header))
				}
			}()

			// For EZF Frontend
			header := request.Header.Get("Authorization")
			tokenString := strings.Split(header, " ")
			if header == "" || len(tokenString) < 1 {
				log.Println(`>>>>>>>>>>>>>>>>>>>>>>>>>>>> Incoming Request >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>`)
				request = request.WithContext(context.WithValue(request.Context(), userADCtxKey, nil))
				next.ServeHTTP(response, request)
				return
			} else {
				log.Println(`>>>>>>>>>>>>>>>>>>>>>>>>>>>> Incoming Authentication Request >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>`)
				adName, err := ExtractUserADFromRequestHeader(header)
				ctxADName := context.WithValue(request.Context(), userADCtxKey, adName)
				if err != nil {
					next.ServeHTTP(response, request.WithContext(ctxADName))
					return
				}

				country, err := ExtractUserCountryFromRequestHeader(header)
				if err != nil || country == "" {
					request = request.WithContext(ctxADName)
					next.ServeHTTP(response, request)
					return
				}

				userModel, err := postgres.GetUserDetails(pool, adName, country)
				if err != nil {
					panic(err)
				}

				ctx := context.WithValue(request.Context(), userCtxKey, userModel.ID)
				ctxCountry := context.WithValue(ctx, userCountryCtxKey, userModel.Country)
				ctxApprovalRoleID := context.WithValue(ctxCountry, userRoleID, userModel.UserRoleID)

				request = request.WithContext(context.WithValue(ctxApprovalRoleID, userADCtxKey, adName))

				log.Println(fmt.Sprintf(`User AD: %s - Country: %s`, adName, country))
				next.ServeHTTP(response, request)
			}
		})

	}
}

func ForContext(ctx context.Context) (*string, *int, *string) {
	var userID *string
	var salesOrg *int
	var userRole *string
	userIdValue, userIDOK := ctx.Value(userCtxKey).(string)
	salesOrgValue, salesOrgOK := ctx.Value(userCountryCtxKey).(int)
	userRoleValue, authRoleOK := ctx.Value(userRoleID).(string)
	if userIDOK && salesOrgOK && authRoleOK {
		userID = &userIdValue
		salesOrg = &salesOrgValue
		userRole = &userRoleValue
	}
	return userID, salesOrg, userRole
}

func GetUserID(ctx context.Context) *string {
	var userID *string
	userIdValue, ok := ctx.Value(userCtxKey).(string)
	if ok {
		userID = &userIdValue
	}
	return userID
}

func GetCountry(ctx context.Context) *int {
	var country *int
	countryIdValue, ok := ctx.Value(userCountryCtxKey).(int)
	if ok {
		country = &countryIdValue
	}
	return country
}

func GetADName(ctx context.Context) *string {
	var userAD *string
	userADValue, ok := ctx.Value(userADCtxKey).(string)
	if ok {
		userAD = &userADValue
	}
	return userAD
}

func GetApprovalRole(ctx context.Context) *string {
	var approvalRole *string
	approvalRoleValue, ok := ctx.Value(userRoleID).(string)
	if ok {
		approvalRole = &approvalRoleValue
	}
	return approvalRole
}
