package entity

import "database/sql"

type FetchCodeValues struct {
	ID    int
	Title string
	Value string
}

type FormAnswerForAdmins struct {
	FormAnswerID             sql.NullString
	CompletionTime           sql.NullTime
	RequestorName            sql.NullString
	RequestorActiveDirectory sql.NullString
	CreatedDate              sql.NullTime
	EventCode                sql.NullString
	EventSeq                 int
	Status                   sql.NullString
	StatusValue              sql.NullString
	CountryTitle             sql.NullString
	CountryValue             sql.NullString
	CurrencyTitle            sql.NullString
	CurrencyValue            sql.NullString
	SingleDataArray          *[]string
	TotalCost                sql.NullString
	GovtNonGovtHcp           *[]string
	EventExpenses            *[]string
	ExpenseCost              *[]string
	StatusCheck              int
	EventTypes               *[]string
	Product                  sql.NullString
	Team                     sql.NullString
	ActivityName             sql.NullString
	UserID                   sql.NullString
}
