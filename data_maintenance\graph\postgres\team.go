package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func GetTeamIdByCountryAndName(countryId int64, teamName string) (*uuid.UUID, error) {
	functionName := "GetTeamIdByCountryAndName()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var teamId uuid.UUID
	query := `SELECT id from team where country = $1 AND name = $2 AND is_deleted = false AND is_active = true`

	err := pool.QueryRow(context.Background(), query, countryId, teamName).Scan(&teamId)
	logengine.GetTelemetryClient().TrackEvent("GetTeamIdByCountryAndName query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &teamId, nil
}

func UpsertTeamForExcel(tx pgx.Tx, entity *entity.Team) (*uuid.UUID, error) {
	functionName := "UpsertTeamForExcel()"
	query := `INSERT INTO team (name,country,is_active,created_by) VALUES($1,$2,$3,$4) RETURNING(id)`
	var teamId uuid.UUID
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.Name, entity.Country, true, uuid.Nil)
	err := tx.QueryRow(context.Background(), query, inputArgs...).Scan(&teamId)
	logengine.GetTelemetryClient().TrackEvent("UpsertTeamForExcel query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return &teamId, err
}

func deleteTeam(id uuid.UUID) {
	functionName := "deleteTeam()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("deleteTeam query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: Failed to begin transaction", functionName)
		return
	}
	defer tx.Rollback(context.Background())
	queryTeamMember := `update team_member set is_active=false and is_deleted=true where team=$1`
	_, err = tx.Exec(context.Background(), queryTeamMember, id)

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return
	}
	queryTeam := `update division set is_active=false and is_deleted=true where id=$1`
	_, err = tx.Exec(context.Background(), queryTeam, id)

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return
	}
	txErr := tx.Commit(context.Background())

	if txErr != nil {
		log.Printf("%s - Error: Failed to commit material data", functionName)
	}
}

func InsertTeamData(entity *entity.UpsertTeam, response *model.UpsertTeamResponse, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertTeamData query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	TeamQueryString := `INSERT INTO team (name, country, created_by) VALUES($1, $2, $3)`
	commandTag, err := tx.Exec(context.Background(), TeamQueryString, entity.Name, entity.Country, userUUID)

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to roll back Team data"
		response.Error = true
		return
	}
	if commandTag.RowsAffected() != 1 {
		response.Message = "Invalid Team data"
		response.Error = true
		return
	} else {
		response.Message = "Team inserted"
		response.Error = false
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Team data"
		response.Error = true
	}
}

func DeleteTeamData(entity *entity.UpsertTeam, response *model.UpsertTeamResponse, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("DeleteTeamData query called")
	if err != nil {
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	querystring := "UPDATE team SET is_deleted = true, is_active = false, modified_by = $2, last_modified = now() WHERE id = $1"
	commandTag, err := pool.Exec(context.Background(), querystring, entity.ID, userUUID)

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to delete in Team"
		response.Error = true
		return
	}
	if commandTag.RowsAffected() != 1 {
		response.Message = "Invalid Team data"
		response.Error = true
		return
	} else {
		response.Message = "Team successfully deleted"
		response.Error = false
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Team data"
		response.Error = true
	}
}

func UpdateTeamData(entity *entity.UpsertTeam, response *model.UpsertTeamResponse, userUUID uuid.UUID) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	querystring := "UPDATE team SET name = $2, country = $3, modified_by = $4 , is_active = $5, is_deleted = $6, last_modified = now() WHERE id = $1"
	commandTag, err := pool.Exec(context.Background(), querystring, entity.ID, entity.Name, entity.Country, userUUID, true, false)
	logengine.GetTelemetryClient().TrackEvent("UpdateTeamData query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to update Team data"
		response.Error = true
		return
	}
	if commandTag.RowsAffected() != 1 {
		response.Message = "Invalid Team data"
		response.Error = true
		return
	} else {
		response.Message = "Team successfully updated"
		response.Error = false
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Team data"
		response.Error = true
	}
}

func GetTeamIdByName(teamName string) (*uuid.UUID, error) {
	functionName := "GetTeamIdByName()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var teamId uuid.UUID
	query := `SELECT id from team where name = $1 AND is_deleted = false AND is_active = true`
	err := pool.QueryRow(context.Background(), query, teamName).Scan(&teamId)
	logengine.GetTelemetryClient().TrackEvent("GetTeamIdByName query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &teamId, nil
}

func GetTeamValues(countryID int) ([]entity.FetchTeamValues, error) {
	functionName := "GetTeamValues()"
	log.Println(functionName)
	var response []entity.FetchTeamValues
	if pool == nil {
		pool = GetPool()
	}
	queryString := `SELECT id, name FROM team WHERE country = $1 AND is_active = true AND is_deleted = false`
	rows, err := pool.Query(context.Background(), queryString, countryID)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var values entity.FetchTeamValues
		rows.Scan(&values.ID, &values.Title)
		response = append(response, values)
	}
	return response, nil
}

func hasTeamID(teamID string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from team where id = $1 AND is_active = true AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, teamID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}
