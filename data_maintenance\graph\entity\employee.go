package entity

import (
	"database/sql"
	"strconv"

	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

type Employee struct {
	ID              *uuid.UUID
	EmployeeCode    string
	FirstName       string
	LastName        string
	Team            string
	Country         int
	CountryID       int
	Position        string
	ActiveDirectory string
	CreatedBy       uuid.UUID
	ModifiedBy      *uuid.UUID
	IsActive        bool
	IsDeleted       bool
	// RoleID          int
	Type       string
	TypeID     int
	RoleTitle  string
	RoleValue  string
	Email      string
	EmailFetch sql.NullString
	TeamID     *uuid.UUID
	UserRoleID *string
}
type ProxyUserLogin struct {
	ActiveDirectoryName string
}
type FetchEmployee struct {
	ID               *uuid.UUID
	EmployeeCode     sql.NullString
	FirstName        sql.NullString
	LastName         sql.NullString
	Team             sql.NullString
	Country          sql.NullInt32
	Position         sql.NullString
	ActiveDirectory  sql.NullString
	RoleTitle        sql.NullString
	RoleValue        sql.NullString
	EmailFetch       sql.NullString
	TeamID           sql.NullString
	VeevaReferenceId sql.NullString
	IsActive         bool
}

type EmployeeExcel struct {
	ID                  string
	CountryName         string
	Team                string
	VPositionName       string
	FirstName           string
	LastName            string
	EmpCode             string
	ApprovalRole        string
	ActiveDirectoryName string
	Email               string
	Active              string
	VeevaReferenceId    string
}

type EmployeeExcelData struct {
	RowNo int
	Data  []interface{}
}

type EmployeeExcelDataResponse struct {
	ID                  string
	UserID              string
	Country             string
	FirstName           string
	LastName            string
	ActiveDirectoryName string
	Email               string
	EmployeeCode        string
	RoleTitle           string
	ActionType          string
	ActionDate          string
	ActionBy            string
}

type EmployeeExcelInput struct {
	ID              *uuid.UUID
	EmployeeCode    string
	ActiveDirectory string
	FirstName       string
	LastName        string
	Country         int
	CountryID       int
	TeamName        string
	TeamID          string
	Position        string
	IsActive        bool
	IsDeleted       bool
	// Role            int
	TypeID     int
	Email      string
	UserRoleID *string
}
type SortingElements struct {
	Column string
	Sort   string
}
type ExportEmployeeExcel struct {
	IsExcel             bool
	ID                  *uuid.UUID
	SearchItem          string
	Country             int
	Team                string
	Position            string
	EmployeeCode        string
	Limit               int
	Offset              int
	ActiveDirectoryName string
	FirstName           string
	LastName            string
	Role                string
	IsActive            *bool
	VeevaReferenceId    string
	Sort                []SortingElements
}

type UserLogin struct {
	ActiveDirectoryName string
	Password            string
}

type Usercountry struct {
	Country string
}

type UserAuth struct {
	ActiveDirectory string
	JWTToken        string
	AccessControl   []Permission
}

type Permission struct {
	ID                   string
	JWTToken             string
	ApprovalRole         *string
	CountryDescription   string
	CountryValue         string
	CurrencyDescription  string
	CurrencyValue        string
	ConversionRate       *float64
	IsApprover           *bool
	IsRequestor          *bool
	ReadOnlySubmissions  *bool
	MasterData           *bool
	IsAdmin              *bool
	SuperAdmin           *bool
	IsChangeRequestAllow *int
}
type ProxyUserAuth struct {
	ActiveDirectory string
	AccessControl   []Permission
}
type FetchTeamValues struct {
	ID    string
	Title string
}
type Rates struct {
	Myr float64 `json:"MYR"`
	Php float64 `json:"PHP"`
	Thb float64 `json:"THB"`
	Usd float64 `json:"USD"`
}

type EmployeeAuditLogsEntity struct {
	Limit               int
	Offset              int
	Country             int
	ActionBy            string
	ActionType          string
	ActionStartDate     string
	ActionEndDate       string
	ActiveDirectoryName string
	FirstName           string
	LastName            string
	Email               string
	UserID              *uuid.UUID
	SearchItem          string
	Sort                []SortingElements
}

type Currency struct {
	Rate Rates `json:"rates"`
}

type Config struct {
	Value float64 `xml:"entry>content>properties>ExchangeRate"`
}

func (o *EmployeeExcelInput) ValidateEmployeeExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(o.ID) != uuid.Nil && o.Country == 0 && o.TeamName == "" && o.Position == "" && o.FirstName == "" && o.LastName == "" && o.EmployeeCode == "" {
		o.IsDeleted = true
		o.IsActive = false
	} else {
		if o.FirstName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": Employee Name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.Position == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": Position is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.TeamName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": TeamName is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}

	}
}
