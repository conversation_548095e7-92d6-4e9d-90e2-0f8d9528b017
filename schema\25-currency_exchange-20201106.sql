CREATE TABLE currency_exchange (
	id uuid NOT NULL DEFAULT public.uuid_generate_v4(),
	date_created timestamptz NOT NULL DEFAULT now(),
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	country int not null,
	currency_code int NOT NULL,
	value_usd float not null,
	last_modified timestamptz NULL,
	modified_by uuid NULL,
	CONSTRAINT currency_exchange_pkey PRIMARY KEY (id)
);

ALTER TABLE currency_exchange ADD CONSTRAINT fk_country FOREIGN KEY (country) REFERENCES code(id) ;
ALTER TABLE currency_exchange ADD CONSTRAINT fk_currency_code FOREIGN KEY (currency_code) REFERENCES code(id);