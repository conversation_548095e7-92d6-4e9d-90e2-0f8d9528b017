input UserRolesInput {
    id: String
    isDelete: Boolean
    userRole: String
    description: String
}

input UserRoleRequest{
    id:String
    userRole: String
}

type UserRoles {
    id: String!
    userRole: String!
    Title: String!
    description: String! 
}

type UserRoleResponse{
    error: Boolean!
    message: String!
    data: [UserRoles]!
}

extend type Query {
    getUserRoles(input: UserRoleRequest): UserRoleResponse!
}


extend type Mutation {
    upsertUserRoles(input: UserRolesInput!): upsertResponse!
}