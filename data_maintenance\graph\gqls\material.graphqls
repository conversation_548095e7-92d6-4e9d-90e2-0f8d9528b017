type Material {
    id: String!
    country: String
    division: String
    groupCode: String!
    groupName: String!
    status: String!
    isActive: Boolean!
    clientName:String!
    veevaReferenceId:String!
}

input MaterialInput {
    id: String
    isDelete: Boolean
    division: String
    groupCode: String
    groupName: String
    clientName:String
    isActive: Boolean
}

input MaterialRequest{
    isExcel: Boolean!
    id:String
    searchItem: String
    division: String
    groupCode: String
    groupName: String
    status: String
    limit: Int
    pageNo: Int
    isActive: Boolean
    veevaReferenceId:String
    clientName:String
    sort: [SortingInputs]
}

type MaterialResponse{
    error: Boolean!
    message: String!
    url: String!
    totalCount: Int!
    data: [Material]!
}


type UpsertMaterialResponse {
  error: Boolean!
  message: String!
  validationErrors: [validationMessage]
}

extend type Query {
    material(input: MaterialRequest!): MaterialResponse!
}

extend type Mutation {
    upsertMaterial(input: MaterialInput!): UpsertMaterialResponse!
}