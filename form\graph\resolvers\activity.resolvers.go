package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/generated"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
)

// UpsertActivity is the resolver for the upsertActivity field.
func (r *mutationResolver) UpsertActivity(ctx context.Context, input model.ActivityInput) (*model.UpsertResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertActivity", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertActivity :" + string(inputJson))
	response := controller.UpsertActivity(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertActivity", "form/UpsertActivity", time.Since(start), "200")
	return response, nil
}

// GetActivitySelection is the resolver for the getActivitySelection field.
func (r *queryResolver) GetActivitySelection(ctx context.Context) (*model.ActivitySelectionsResponse, error) {
	start := time.Now().UTC()
	logengine.GetTelemetryClient().TrackEvent("form/GetActivitySelection : No Input")
	response := controller.GetActivitySelections(&ctx)
	logengine.GetTelemetryClient().TrackRequest("GetActivitySelection", "form/GetActivitySelection", time.Since(start), "200")
	return response, nil
}

// GetActivities is the resolver for the getActivities field.
func (r *queryResolver) GetActivities(ctx context.Context, input *model.ActivityFilter) (*model.ActivityModelsResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetActivities", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/GetActivities :" + string(inputJson))
	response := controller.GetActivities(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetActivities", "form/GetActivities", time.Since(start), "200")
	return response, nil
}

// ValidateHcpMeal is the resolver for the validateHcpMeal field.
func (r *queryResolver) ValidateHcpMeal(ctx context.Context, input model.HcpMealInput) (*model.ValidateHcpMealResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "ValidateHcpMeal", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/ValidateHcpMeal :" + string(inputJson))
	response := controller.ValidateHcpMeal(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("ValidateHcpMeal", "form/ValidateHcpMeal", time.Since(start), "200")
	return response, nil
}

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

type queryResolver struct{ *Resolver }
