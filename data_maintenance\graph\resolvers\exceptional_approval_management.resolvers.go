package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertExceptionalApprovalManagement is the resolver for the upsertExceptionalApprovalManagement field.
func (r *mutationResolver) UpsertExceptionalApprovalManagement(ctx context.Context, input model.ExceptionalApprovalManagementInput) (*model.ExceptionalApprovalManagementResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertExceptionalApprovalManagement", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertExceptionalApprovalManagement :" + string(inputJson))
	response := controller.UpsertExceptionalApprovalManagementData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertExceptionalApprovalManagement", "data_maintenance/UpsertExceptionalApprovalManagement", time.Since(start), "200")
	return response, nil
}

// FetchExceptionalApprovalManagement is the resolver for the FetchExceptionalApprovalManagement field.
func (r *queryResolver) FetchExceptionalApprovalManagement(ctx context.Context, input model.FetchExceptionalApprovalManagementInput) (*model.FetchExceptionalApprovalManagementResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "FetchExceptionalApprovalManagement", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/FetchExceptionalApprovalManagement :" + string(inputJson))
	response := controller.FetchExceptionalApprovalManagementData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("FetchExceptionalApprovalManagement", "data_maintenance/FetchExceptionalApprovalManagement", time.Since(start), "200")
	return response, nil
}
