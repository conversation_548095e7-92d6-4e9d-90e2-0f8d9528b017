package util

import (
	"context"
	"github.com/ihcp/form/graph/controller"
	"github.com/jasonlvhit/gocron"
	"log"
	"time"
)

func ExecuteCronServiceToSendEmailToApprover() {
	// gocron.Every(1).Hour().From(gocron.NextTick()).Do(controller.SendEmailToApproverIfNotTakenActionInThreeDays)
	gocron.Every(1).Day().At("00:01").Do(controller.SendEmailToApproverIfNotTakenActionInThreeDays)
	<-gocron.Start()
}

func ManualSyncEventsToVeeva() {
	controller.CheckStatusFromVeevaEventLogsController()
}

func SyncEventsToVeeva() {
	time.Sleep(5 * time.Second)
	s := gocron.NewScheduler()
	s.Every(1).Hour().Do(controller.CheckStatusFromVeevaEventLogsController)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllCreatedDataForCustomer() {
	log.Println("ExecuteCronServiceForVeevaGetAllCreatedDataForCustomer")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllCreatedDataForCustomer)
	s.Every(1).Day().At("10:00").Do(controller.VeevaGetAllCreatedDataForCustomer)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllModifiedDataForCustomer() {
	log.Println("ExecuteCronServiceForVeevaGetAllModifiedDataForCustomer")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllModifiedDataForCustomer)
	s.Every(1).Day().At("10:30").Do(controller.VeevaGetAllModifiedDataForCustomer)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllveevaUsersDataForModifiedData() {
	log.Println("ExecuteCronServiceForVeevaGetAllveevaUsersDataForModifiedData")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllveevaUsersModifiedData)
	s.Every(1).Day().At("14:00").Do(controller.VeevaGetAllveevaUsersModifiedData)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllveevaUsersDataForCreatedData() {
	log.Println("ExecuteCronServiceForVeevaGetAllveevaUsersDataForCreatedData")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllveevaUsersCreatedData)
	s.Every(1).Day().At("15:00").Do(controller.VeevaGetAllveevaUsersCreatedData)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllveevaProductDataForCreatedData() {
	log.Println("ExecuteCronServiceForVeevaGetAllveevaProductDataForCreatedData")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllveevaProductCreatedData)
	s.Every(1).Day().At("12:00").Do(controller.VeevaGetAllveevaProductCreatedData)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllveevaProductDataForModifiedData() {
	log.Println("ExecuteCronServiceForVeevaGetAllveevaProductDataForModifiedData")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.VeevaGetAllveevaProductModifiedData)
	s.Every(1).Day().At("13:00").Do(controller.VeevaGetAllveevaProductModifiedData)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllAttendanceList() {
	log.Println("ExecuteCronServiceForVeevaGetAllAttendanceList")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.CheckStatusFromVeevaAttendanceController)
	s.Every(1).Day().At("19:00").Do(controller.CheckStatusFromVeevaAttendanceController)
	<-s.Start()
}
func ExecuteCronServiceForEmailAlertForVeevaFailureEvent() {
	log.Println("ExecuteCronServiceForEmailAlertForVeevaFailureEvent")
	s := gocron.NewScheduler()
	//s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.EmailAlertForVeevaFailureEvent)
	s.Every(1).Day().At("04:30").Do(controller.EmailAlertForVeevaFailureEvent)
	<-s.Start()
}
func ExecuteCronServiceForVeevaGetAllAttendanceFailureList() {
	log.Println("ExecuteCronServiceForVeevaGetAllAttendanceList")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.CheckStatusFromVeevaAttendanceFailureController)
	s.Every(1).Day().At("00:30").Do(controller.CheckStatusFromVeevaAttendanceFailureController)
	<-s.Start()
}

func ExecuteCronServiceSyncTerritoryDataFromVeeva(ctx context.Context) {
	log.Println("ExecuteCronServiceSyncTerritoryDataFromVeeva")

	controller.SyncTerritoryDataFromVeeva(ctx)
	return
}

func ExecuteCronServiceSyncUserTerritoryFromVeeva(ctx context.Context) {
	log.Println("ExecuteCronServiceSyncUserTerritoryFromVeeva")

	controller.SyncUserTerritoryFromVeeva(ctx)
	return
}

func ExecuteCronServiceSyncHCPProvinceFromVeeva(ctx context.Context) {
	log.Println("ExecuteCronServiceSyncHCPTerritoryFromVeeva")

	controller.SyncHCOInfoFromVeeva(ctx)
	return
}

func ExecuteCronServiceSyncHCPTerritoryFromVeeva(ctx context.Context) {
	log.Println("ExecuteCronServiceSyncHCPTerritoryFromVeeva")

	controller.SyncHCPTerritoryFromVeeva(ctx)
	return
}

func ExecuteCronServiceSyncHCPDataFromVeeva(ctx context.Context) {
	log.Println("ExecuteCronServiceSyncHCPTerritoryFromVeeva")

	controller.SyncHCPDataFromVeeva(ctx)
	return
}
