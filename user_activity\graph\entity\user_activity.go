package entity

import (
	"database/sql"

	"github.com/gofrs/uuid"
)

type UserTrackingEntity struct {
	MeetingId string
	UserId    uuid.UUID
	PageName  string
	UserIp    string
	Action    string
}
type UserActivityListExcel struct {
	RowNo int
	Data  []interface{}
}
type UserTrackingEntityAllData struct {
	MeetingId        sql.NullString
	UserId           sql.NullString
	PageName         sql.NullString
	UserIp           sql.NullString
	Action           sql.NullString
	AccessedDatetime sql.NullString
	UserName         sql.NullString
}

type UserTrackingEntityAllDataForExcel struct {
	MeetingId        string
	UserId           string
	PageName         string
	UserIp           string
	Action           string
	AccessedDatetime string
	UserName         string
}
