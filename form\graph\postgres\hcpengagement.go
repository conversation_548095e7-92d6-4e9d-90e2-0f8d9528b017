package postgres

import (
	"context"
	"strings"

	"github.com/ihcp/form/graph/entity"
)

func CheckSpeakernamevalid(Speakername string, country int, Specialty string, city string) (bool, string) {
	if pool == nil {
		pool = GetPool()
	}
	var SpeakerId string
	var hasValue int
	speakerCity := strings.ToLower(strings.ReplaceAll(city, " ", ""))
	speakerSpecialty := strings.ToLower(strings.ReplaceAll(Specialty, " ", ""))
	query := `select 1,c.id from customer c where c."name" =$1 and country =$2 and is_active =true and is_deleted =false and REPLACE(lower(c.organization),' ','') = $3 and REPLACE(lower(c.v_sp_desc),' ','') = $4`
	err := pool.QueryRow(context.Background(), query, Speakername, country, speakerCity, speakerSpecialty).Scan(&hasValue, &SpeakerId)
	if err != nil {
		return false, ""
	}
	return true, SpeakerId
}
func CheckSpeakerCity(Speakername string, country int, city string) bool {
	if pool == nil {
		pool = GetPool()
	}
	speakerCity := strings.ToLower(strings.ReplaceAll(city, " ", ""))
	var hasValue int
	query := `  select 1 from customer c where c.is_active =true and is_deleted =false and c."name" =$1 and country =$2  and REPLACE(lower(c.organization),' ','') = $3;`
	err := pool.QueryRow(context.Background(), query, Speakername, country, speakerCity).Scan(&hasValue)
	if err != nil {
		return false
	}
	return true
}
func CheckSpecialty(Speakername string, country int, Specialty string) bool {
	if pool == nil {
		pool = GetPool()
	}
	speakerSpecialty := strings.ToLower(strings.ReplaceAll(Specialty, " ", ""))
	var hasValue int
	query := `  select 1 from customer c where c.is_active =true and is_deleted =false and c."name" =$1 and country =$2  and REPLACE(lower(c.v_sp_desc),' ','') = $3;`
	err := pool.QueryRow(context.Background(), query, Speakername, country, speakerSpecialty).Scan(&hasValue)
	if err != nil {
		return false
	}
	return true
}
func CheckSpeakername(Speakername string, country int) bool {
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	query := `select 1 from customer c where c."name" =$1 and country =$2 and is_active =true and is_deleted =false `
	err := pool.QueryRow(context.Background(), query, Speakername, country).Scan(&hasValue)
	if err != nil {

		return false
	}
	return true
}
func CheckSpeakernameArrayQuery(Speakername string, country int, Specialty string, organization string) []entity.SpeakerDetails {
	if pool == nil {
		pool = GetPool()
	}
	var Speakerarr []entity.SpeakerDetails
	organization = strings.ToLower(strings.ReplaceAll(organization, " ", ""))
	Specialty = strings.ToLower(strings.ReplaceAll(Specialty, " ", ""))
	query := `
	SELECT 1 AS query_num, c.id::text
	FROM customer c
	WHERE c."name" =$1
		AND country =$2
		AND is_active = true
		AND is_deleted = false
		AND REPLACE(lower(c.organization),' ','') = $3
		AND REPLACE(lower(c.v_sp_desc),' ','') = $4
	UNION ALL
	SELECT 2 AS query_num, 'SpeakerOrganization'
	FROM customer c
	WHERE c.is_active = true
		AND is_deleted = false
		AND c."name" =$1
		AND country =$2
		AND REPLACE(lower(c.organization),' ','') = $3
	UNION ALL
	SELECT 3 AS query_num, 'Specialty'
	FROM customer c
	WHERE c.is_active = true
		AND is_deleted = false
		AND c."name" =$1
		AND country =$2
		AND REPLACE(lower(c.v_sp_desc),' ','') = $4
	UNION ALL
	SELECT 4 AS query_num, 'Speakername'
	FROM customer c
	WHERE c."name" =$1
		AND country =$2
		AND is_active = true
		AND is_deleted = false; `
	rows, err := pool.Query(context.Background(), query, Speakername, country, organization, Specialty)
	if err != nil {
		return Speakerarr
	}
	for rows.Next() {
		var speakerdetails entity.SpeakerDetails
		rows.Scan(&speakerdetails.QueryValue, &speakerdetails.SpeakerId)
		Speakerarr = append(Speakerarr, speakerdetails)
	}
	return Speakerarr
}
