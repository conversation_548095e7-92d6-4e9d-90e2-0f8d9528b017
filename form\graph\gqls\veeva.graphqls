scalar Map
type responseAllVeevaAccountDetails{
    error: Boolean!
    message: String!
    data:[allVeevaAccountDetailsData]
}

input requestAllVeevaAccountDetails{
  limit:Int
  page:Int
}

input updateEventStatusCompletedToApprovedInput {
  meetingIds:[String!]!
}

input updateEventStatusPendingToApprovedInput {
  meetingIds:[String!]!
}

input updateNumberofEMPInput {
  data: [NumberofEMPData!]!
}

input NumberofEMPData {
  meetingId: String!
  empNumbers: Int!
}

type updateApproveEventsResponse{
  error: Boolean!
  message: String!
}

input deleteDraftEventsInput {
  meetingIds:[String!]!
}

type deleteDraftEventsResponse{
  error: Boolean!
  message: String!
}

type allVeevaAccountDetailsData {
  id: String
  customerId:String
  dateCreated:String
  method:String
  city: String
  name: String
  spDesc: String
  country: String
  gender: String
  isActive: Boolean
  cMSLClass: String
  isDeleted: Boolean
  customerNo: String
  errorMessage: String
  speakerWeight: Float
  veevareferenceId: String
}
input requestAllAttendanceVeevaAccountDetails{
  limit:Int
  page:Int
  formAnswerId:String
  name:String
  dateCreatedFrom:String
  dateCreatedTo:String
  eventId:String
}

type allAttendanceVeevaAccountDetailsData {
  id:String
  dateCreated:String
  specialty:String
  name:String
  eventId:String
  contactType:String
  accountName:String
  accountveevaId:String
  attendanceveevaId:String
  systemcode:String
  organization:String
  isHcp:Boolean
  isSpeaker:Boolean
  firstName:String
  lastName:String
}

type responseAllAttendanceVeevaAccountDetails{
    error: Boolean!
    message: String!
    data:[allAttendanceVeevaAccountDetailsData]
}

type CommonResponse {
  success: Boolean!
  message: String
}

type EventAuditApprover {
  userID: String!
  username: String!
  fullName: String!
  roleName: String!
  status: String!
  statusUpdatedAt: String!
  active: Boolean!
  isApollo: Boolean!
}

type EventAuditLog {
  updatedAt: String!
  status: String!
  isApollo: Boolean!
}

type EventAudit {
  answerJSON: String!
  eventType: String!
  eventName: String!
  meetingID: String!
  eventStatus: String!
  reportingDBStatus: String!
  isExceptionalApproval: Boolean!
  requesterName: String!
  requestUsername: String!
  completionDate: String!

  veevaSyncStatus: String!
  veevaSyncTime: String!
  veevaSyncError: String!
  veevaReferenceID: String!
  eventLogs: [EventAuditLog!]
  eventApprovers: [EventAuditApprover!]
}

extend type Query{
    fetchAllVeevaAccountDetails(input:requestAllVeevaAccountDetails):responseAllVeevaAccountDetails!
    fetchAllAttendanceVeevaAccountDetails(input:requestAllAttendanceVeevaAccountDetails):responseAllAttendanceVeevaAccountDetails!

#  helper api
    syncEventToVeeva(eventID:String!, options:Map):CommonResponse!
    updateEventFormAnswers(userToken:String!, options:[Map!]): CommonResponse!
    getAuditEvent(userToken:String!, options:Map): EventAudit!
}

extend type Mutation{
  updateEventStatusPendingToApproved(userToken:String!,input: updateEventStatusPendingToApprovedInput!): CommonResponse!
  updateEventStatusCompletedToApproved(userToken:String!,input: updateEventStatusCompletedToApprovedInput!): CommonResponse!
  updateNumberofEMP(userToken:String!,input: updateNumberofEMPInput!): CommonResponse!
  deleteDraftEvents(userToken:String!,input: deleteDraftEventsInput!): CommonResponse!
}