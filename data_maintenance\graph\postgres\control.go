package postgres

import (
	"context"
	"errors"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func GetControlInfo(input *entity.Control) ([]entity.ExportControls, int, error) {
	functionName := "GetControlInfo()"
	if pool == nil {
		pool = GetPool()
	}
	var TotalCount int
	queryString := `select c.id,c.description,c.max_limit,c1.title,c.category ,c."type"  ,case when c.is_active =true and c.is_deleted =false then 'Active' else 'Inactive' end as status,count(c.id) over() from controls c 
	inner join code c1 on c1.id =c.country `
	log.Printf("%s", functionName)
	var inputArgs []interface{}
	queryString += ` AND c.country = ? `
	inputArgs = append(inputArgs, input.Country)
	if input.Description != "" {
		queryString += ` AND c.description = ? `
		inputArgs = append(inputArgs, input.Description)
	}
	if input.Category != "" {
		queryString += ` AND c.Category = ? `
		inputArgs = append(inputArgs, input.Category)
	}
	if input.Type != "" {
		queryString += ` AND c.Type = ? `
		inputArgs = append(inputArgs, input.Type)
	}

	if input.Limit > 0 {
		if input.PageNo > 0 {
			queryString = queryString + ` limit ? offset ?`

			inputArgs = append(inputArgs, input.Limit)
			inputArgs = append(inputArgs, input.PageNo)
		} else {
			queryString = queryString + ` limit ? offset ?`
			inputArgs = append(inputArgs, input.Limit)
			inputArgs = append(inputArgs, 0)
		}
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetControlInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, 0, err
	}
	var controls []entity.ExportControls
	for rows.Next() {
		var control entity.ExportControls
		err := rows.Scan(&control.ID, &control.Description, &control.MaxLimit, &control.Country, &control.Category, &control.Type, &control.Status, &TotalCount)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return controls, 0, err
		}
		controls = append(controls, control)
	}
	return controls, TotalCount, nil
}

func FetchControlData(ID *uuid.UUID) (entity.FmvAuditLogsUpsertInput, error) {
	if pool == nil {
		pool = GetPool()
	}
	var response entity.FmvAuditLogsUpsertInput
	queryString := `SELECT id, description, max_limit, country, category, "type", activity_id, code_id, is_active, is_deleted, range_limit::text, currency_code_limit, more_than_two_lecture
	FROM controls where id=$1 `

	err := pool.QueryRow(context.Background(), queryString, ID).Scan(&response.ID, &response.Description, &response.MaxLimit, &response.Country, &response.Category, &response.Type, &response.ActivityId, &response.CodeId, &response.IsActive, &response.IsDelete, &response.RangeLimit, &response.CurrencyCodeLimit, &response.MoreThanTwoLecture)
	if err != nil {
		return response, err
	}
	return response, nil

}

func FmvAuditLogsUpsert(fmvAuditlogsData entity.FmvAuditLogsUpsertInput, userUUID uuid.UUID) {
	functionName := "FmvAuditLogsUpsert"
	if pool == nil {
		pool = GetPool()
	}
	query := `INSERT INTO fmv_controls_audit_logs
	( description, max_limit, country, created_by, date_created,  category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture, control_id)
	VALUES($1, $2, $3, $4, now(), $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
	`
	_, err := pool.Exec(context.Background(), query, fmvAuditlogsData.Description, fmvAuditlogsData.MaxLimit, fmvAuditlogsData.Country, userUUID, fmvAuditlogsData.Category, fmvAuditlogsData.Type, fmvAuditlogsData.ActivityId, fmvAuditlogsData.CodeId, fmvAuditlogsData.IsActive, fmvAuditlogsData.IsDelete, fmvAuditlogsData.RangeLimit, fmvAuditlogsData.CurrencyCodeLimit, fmvAuditlogsData.MoreThanTwoLecture, fmvAuditlogsData.ID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println(err.Error() + ":" + functionName)
	}
}

func DeleteControlData(entity *entity.ControlUpsertInput, userUUID uuid.UUID) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	querystring := "UPDATE controls SET is_deleted = true, is_active = false, modified_by = $2, last_modified = $3 WHERE id = $1"
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userUUID, timenow)
	logengine.GetTelemetryClient().TrackEvent("DeleteControlData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New("Failed to delete data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New("Failed to commit controls data")
	}
	return nil
}

func ActiveControlData(entity *entity.ControlUpsertInput, userUUID uuid.UUID) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	querystring := `UPDATE controls SET is_deleted = false, is_active = true, modified_by = $2, last_modified = $3 WHERE id = $1 `
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userUUID, timenow)
	logengine.GetTelemetryClient().TrackEvent("UpdateControlData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to update data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit controls data")
	}
	return nil
}

func UpdateControlData(entity *entity.ControlUpsertInput, userUUID uuid.UUID) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	querystring := `UPDATE controls SET max_limit = $3, modified_by = $2, last_modified = $4 WHERE id = $1 `
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userUUID, entity.MaxLimit, timenow)
	logengine.GetTelemetryClient().TrackEvent("UpdateControlData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to update data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit controls data")
	}
	return nil
}

func HasControlID(controlID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from controls where id = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, controlID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func HasActivityID(ActivityID *uuid.UUID) bool {
	log.Println("HasActivityID()")
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from activity where id = $1 AND is_active = true AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, ActivityID).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

func GetTitleFromCodeId(id int) (string, error) {
	log.Println("GetTitleFromCodeId()")
	if pool == nil {
		pool = GetPool()
	}
	var isValue int
	var hasValue string
	queryString := `select 1 from code where id = $1 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), queryString, id).Scan(&isValue)
	logengine.GetTelemetryClient().TrackEvent("GetTitleFromCodeId query called")

	if err != nil {
		hasValue = ""
	} else {
		queryString := `select title from code where id = $1 AND is_active = true AND is_deleted = false`
		err := pool.QueryRow(context.Background(), queryString, id).Scan(&hasValue)

		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", err.Error())
			return "", errors.New("value not present in code!")
		}
	}
	return hasValue, nil
}
func CountryExist(username string, country string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var value int
	query := `select  1 from "user" u 
	inner join code c on c.id =u.country 
	where u.active_directory =$1 and c.title  =$2 and u.is_active =true and u.is_deleted =false`
	err := pool.QueryRow(context.Background(), query, username, country).Scan(&value)
	if err != nil {
		return false
	}

	return true
}
