CREATE OR REPLACE FUNCTION update_set_number()
  RETURNS void as 
$func$
DECLARE
  _row RECORD;
BEGIN
   FOR _row IN
      select * from  
		(select distinct on (id2) id2,id, set_number from (select form_answer_id as fa_id, id , approver_id , set_number from approvers
			where set_number is not null and approver_id in (select created_by from approvers))
			as a1
			inner join  (
			select form_answer_id as fa_id2, id as id2, created_by as created_by2, set_number as set_number2 from approvers
			where set_number is null and created_by in (select approver_id from approvers) 
		) as a2 
		on a2.created_by2 = a1.approver_id and a1.fa_id = a2.fa_id2)  AS Table_A
   loop
 	  RAISE NOTICE 'id is %',_row.id2;
      UPDATE uatetl.approvers SET set_number = _row.set_number WHERE id = _row.id2;
   END LOOP;
end;
$func$  LANGUAGE plpgsql;

SELECT * FROM update_set_number()
