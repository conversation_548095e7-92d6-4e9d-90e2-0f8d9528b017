package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/jmoiron/sqlx"
)

func GetDepartmentData(inputModel *model.GetDepartmentInput) ([]entity.FetchDepartment, error) {

	functionName := "GetDepartmentData()"
	log.Println(functionName)
	var result []entity.FetchDepartment
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var err error
	queryString = `select id,department from departments where 
		is_active = true and is_deleted = false`
	var args []interface{}
	if inputModel != nil {
		if inputModel.ID != nil {
			queryString += ` AND id = ? `
			args = append(args, inputModel.ID)
		}
		if inputModel.Department != nil {
			queryString += ` AND department  ILIKE ? `
			args = append(args, "%"+*inputModel.Department+"%")
		}

	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, args...)
	logengine.GetTelemetryClient().TrackEvent("GetDepartmentData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, err
	}
	for rows.Next() {
		var department entity.FetchDepartment
		err = rows.Scan(
			&department.ID,
			&department.Department,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", "GetDepartmentData", err.Error())
			return result, err
		}
		result = append(result, department)

	}
	return result, nil

}

func InsertDepartmentData(entity *entity.Department, response *model.UpsertDepartmentResponse) {
	functionName := "InsertDepartmentData()"
	log.Println(functionName)
	logengine.GetTelemetryClient().TrackEvent("InsertDepartmentData query called")
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	DepartmentQuerystring := `INSERT INTO departments (department ,created_by) VALUES ($1,$2)`
	_, err = tx.Exec(context.Background(), DepartmentQuerystring, entity.Department, entity.UserId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to roll back Customer data"
		response.Error = true
		return
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Department data"
		response.Error = true
		return
	}
	response.Message = "Department successfully inserted"
	response.Error = false
}

func DeleteDepartmentData(entity *entity.Department, response *model.UpsertDepartmentResponse) {
	functionName := "DeleteDepartmentData()"
	log.Println(functionName)
	logengine.GetTelemetryClient().TrackEvent("DeleteDepartmentData query called")
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	DepartmentQuerystring := `UPDATE departments SET is_deleted = true, is_active = false, modified_by = $2, last_modified = now() WHERE id = $1`
	_, err = tx.Exec(context.Background(), DepartmentQuerystring, entity.ID, entity.UserId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to roll back Department data"
		response.Error = true
		return
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Department data"
		response.Error = true
		return
	}
	response.Message = "Department successfully Deleted"
	response.Error = false
}

func UpdateDepartmentData(entity *entity.Department, response *model.UpsertDepartmentResponse) {
	functionName := "UpdateDepartmentData()"
	log.Println(functionName)
	logengine.GetTelemetryClient().TrackEvent("UpdateDepartmentData query called")
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	var inputargs []interface{}
	if err != nil {
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	DepartmentQuerystring := `UPDATE departments SET last_modified = now(), `

	if entity.Department != "" {
		DepartmentQuerystring += `department = ? ,`
		inputargs = append(inputargs, entity.Department)
	}

	DepartmentQuerystring += `modified_by= ? `
	inputargs = append(inputargs, entity.UserId)

	DepartmentQuerystring += `WHERE id = ? and is_active = true and is_deleted = false`
	inputargs = append(inputargs, entity.ID)
	DepartmentQuerystring = sqlx.Rebind(sqlx.DOLLAR, DepartmentQuerystring)
	_, err = tx.Exec(context.Background(), DepartmentQuerystring, inputargs...)
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		logengine.GetTelemetryClient().TrackException(txErr)
		response.Message = "Failed to commit Department data"
		response.Error = true
		return
	}
	response.Message = "Department successfully Updated"
	response.Error = false
}

func HasDepartment(userRole string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from departments where department =  $1
	and is_active = true and is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, userRole).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}

	return result
}
