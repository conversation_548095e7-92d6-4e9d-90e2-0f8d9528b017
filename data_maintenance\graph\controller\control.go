package controller

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func ExportControlData(ctx *context.Context, input *model.ControlRequest) *model.ControlResponse {
	var response model.ControlResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var userUUID uuid.UUID
	var err error
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		}
	}
	exportControl, err := mapper.ExportControlInputModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	controlInfo, TotalCount, err := postgres.GetControlInfo(exportControl)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputModels := mapper.ExportControlInfoEntityToModel(controlInfo)
	response.Data = outputModels
	response.TotalCount = &TotalCount
	return &response

}

func UpsertControlData(ctx *context.Context, inputModel model.ControlInput) *model.UpsertControlResponse {
	upsertResponse := &model.UpsertControlResponse{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, err := mapper.MapControlModelToEntity(&inputModel, userUUID)

	if err == nil {
		if entity.ID != nil && entity.IsDelete && entity.MaxLimit == nil {
			err = postgres.DeleteControlData(entity, userUUID)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				fmvAuditlogsData, _ := postgres.FetchControlData(entity.ID)
				postgres.FmvAuditLogsUpsert(fmvAuditlogsData, userUUID)
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully Deactivate FMV data"
			}
		} else if entity.ID != nil && !entity.IsDelete && entity.MaxLimit == nil {
			err = postgres.ActiveControlData(entity, userUUID)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				fmvAuditlogsData, _ := postgres.FetchControlData(entity.ID)
				postgres.FmvAuditLogsUpsert(fmvAuditlogsData, userUUID)
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully Activate FMV data"
			}
		} else if entity.ID != nil {
			err = postgres.UpdateControlData(entity, userUUID)
			if err != nil {
				upsertResponse.Error = true
				upsertResponse.Message = err.Error()
			} else {
				fmvAuditlogsData, _ := postgres.FetchControlData(entity.ID)
				postgres.FmvAuditLogsUpsert(fmvAuditlogsData, userUUID)
				upsertResponse.Error = false
				upsertResponse.Message = "Successfully Updated FMV data"
			}
		}
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
	}
	return upsertResponse
}
