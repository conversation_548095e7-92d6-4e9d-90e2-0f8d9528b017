UPDATE approvers SET approval_role = subquery.approval
	FROM (select a.approver_id as aid,u2.approval_role as approval, c2.title,ur.id from approvers a  
inner join "user" u2 on u2.id = a.approver_id 
inner join code c2 on c2.id = u2.approval_role 
inner join user_roles ur  on ur.value = c2.value and ur.is_active = true and ur.is_deleted = false 
where a.approval_role = 393) as subquery
WHERE approvers.approver_id = subquery.aid and approvers.approval_role = 393

	UPDATE approvers SET user_role_id = subquery.uid
	FROM (select a.approver_id as aid,u2.approval_role, c2.title,ur.id as uid from approvers a  
inner join "user" u2 on u2.id = a.approver_id 
inner join code c2 on c2.id = u2.approval_role 
inner join user_roles ur  on ur.value = c2.value and ur.is_active = true and ur.is_deleted = false 
where a.user_role_id = (select id from user_roles where value = 'manager' and is_active= true and is_deleted= false)) as subquery
WHERE approvers.approver_id = subquery.aid and approvers.user_role_id = (select id from user_roles where value = 'manager' and is_active= true and is_deleted= false)


UPDATE approvers SET department_id = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active =true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approvers.user_role_id = subquery.r_id