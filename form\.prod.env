# Server port
PORT: 32000
APPLICATION_INSIGHTS_KEY: 0176352e-ee1a-4ee8-9b97-d640ae50f260
SITE_URL : "https://ezflow-webapp-staging.azurewebsites.net/"
GRANT_TYPE : password
CLIENT_ID : "3MVG95mg0lk4batg2PjOzMX51HAHOOjAEJVWsQRJjQISswsEgYDfyxOJVSNCu9HVfHgsKtmvGpzyKwIOp86wU"
CLIENT_SECRET : "B5F9F114D00D50DBE42AA5D194A57018880312ACAB0ED8A100D0C48F18B67B40"

TEAMS_WEBHOOK_URL: "https://zpssgpatientsolutions.webhook.office.com/webhookb2/b0a225ba-9691-4105-95be-08458b4305b5@19cff0af-7bfb-4dfc-8fdc-ecd1a242439b/IncomingWebhook/c94aeede65d84bbe9df14481eee63259/c5d73a02-21a3-4528-b1cf-0fb10059884e"
TEAMS_WEBHOOK_URL_FOR_DATA_PATCHING: "https://zpssgpatientsolutions.webhook.office.com/webhookb2/b0a225ba-9691-4105-95be-08458b4305b5@19cff0af-7bfb-4dfc-8fdc-ecd1a242439b/IncomingWebhook/0e9245f99de341d697d6204dcf03764c/c5d73a02-21a3-4528-b1cf-0fb10059884e"

# Database connection
IHCP_HOST: "***********"
IHCP_PORT: "5432"
IHCP_USER: "appdev_ops"
IHCP_PASSWORD: "qqSrQWq9uLkBpsyK69n7FPbqxHzBMsxVy7uYnJkTZSZc8nq8NjjC8grnaHcWq4CsQs2Uq7zuaFNJ9pKczCeVbLE6fkf5Fy4zBG9xYuceLvjH28wdHN3HKz6CvbnJsLeN"
IHCP_DB_NAME: "ezflow_prod"
IHCP_SEARCH_PATH: "ezflow_prod"
IHCP_SSLMODE: "disable"
IHCP_MAX_CONN: "5"
IHCP_MAX_LIFETIME: "5"
IHCP_MAX_IDLE_TIME: "5"
IHCP_HEALTHCHECK_PREIOD: "2"
POOL_CONNECTION_INTERVAL: 15
MAX_RETRY : 10
# Azure Blob File Storage
AZURE_BLOB_KEY: "KkgEVaqXGwG3hZl0TXyfH/hioIuMYoky+iVA0KZptejswxbZKNjl+CzcyFzfg+HBoPr57U03ti9m+AStmrfQqQ=="
AZURE_BLOB_ACCOUNT_NAME: "ezflowprodstorage"
AZURE_BLOB_UPLOAD_PATH: "ezflow/prod"
AZURE_ATLAS_URL: "https://atlas.microsoft.com/search/fuzzy/batch/sync/json?api-version=1.0&subscription-key="
AZURE_ATLAS_BULK_URL: "https://atlas.microsoft.com/search/fuzzy/batch/json?api-version=1.0&subscription-key="
AZURE_ATLAS_SUBSCRIPTION_KEY: "rTk9EzxRjAeoo8kmclvUZV7FaXbtdPHtTRk8QJeMkXE"

# Veeva CRM
USERNAME_VEEVA: "<EMAIL>"
PASSWORD: "VZuellig2024"
VEEVA_AUTH_URL: "https://login.salesforce.com/services/oauth2/token"
VEEVA_UPSERT_URL: "https://zpt--full.my.salesforce.com/services/data/v54.0/sobjects/Medical_Event_vod__c"
# Customers/HCPs
VEEVA_FETCH_CREATED_DATA_DAYS: "last_n_days:5"
VEEVA_FETCH_MODIFIED_DATA_DAYS: "last_n_days:2"
# Users
VEEVA_FETCH_CREATED_DATA_FOR_USER_DAYS: "last_n_days:1"
VEEVA_FETCH_MODIFIED_DATA_FOR_USER_DAYS: "last_n_days:1"
# Products
VEEVA_FETCH_CREATED_DATA_FOR_PRODUCT_DAYS: "last_n_days:50"
VEEVA_FETCH_MODIFIED_DATA_FOR_PRODUCT_DAYS: "last_n_days:50"
# Events Sync
VEEVA_ALL_EVENT_PUSH_DAY_INTERVAL: "1"
# Event Attendances
VEEVA_ALL_EVENT_ATTENDANCE_DAY_INTERVAL: "1"

VIRTUAL_EVENT_DETAILS_MAX_LIMIT: "500"
DURATION_MAX_LENGTH_LIMIT: "4"
MAXIMUM_WORKING_HOURS: "6"


# Configurable CONSTs
ADMIN:"admin"
CCO:"clustercomplianceofficer"
FINANCE:"finance"
SUPERADMIN:"superadmin"
# For veeva authentication
AUTH_VEEVA_TOKEN:"X"
AUTH_VEEVA_URL:"V"
# Mail Alert
VEEVA_EMAIL_ALEART_TO_MAIL:"<EMAIL>"
VEEVA_EMAIL_ALEART_CC_MAIL:"<EMAIL>"

