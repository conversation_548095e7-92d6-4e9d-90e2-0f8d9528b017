CREATE TABLE status_change_notification (
	id uuid NOT NULL DEFAULT public.uuid_generate_v4(),
	form_answer uuid NOT null,
	actioned_By uuid not NULL,
	status int8 not null,
	has_read bool default false,
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	date_created timestamptz NOT NULL DEFAULT now()
);
ALTER TABLE status_change_notification ADD CONSTRAINT fk_status FOREIGN KEY (status) REFERENCES code(id);
ALTER TABLE status_change_notification ADD CONSTRAINT fk_form_answer FOREIGN KEY (form_answer) REFERENCES form_answers (id);
