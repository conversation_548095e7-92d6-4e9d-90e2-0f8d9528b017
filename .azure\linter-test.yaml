pool:
  vmImage: 'ubuntu-latest'
# pr:
#     branches:
#         include:
#         - development
trigger:
    - none
resources:
  repositories: 
  - repository: azure-pipeline-template
    type: git
    ref: refs/heads/webapp
    name: Digital and Data Program Management/azure-pipeline-template
    endpoint: build-service

variables:
  - name: app-name
    value: data-maintenance
  - group: ezflow-cicd
  - name: dockerfilepath
    value: '$(Build.SourcesDirectory)/data_maintenance/build-Dockerfile'
  - name: context
    value: '$(Build.Repository.LocalPath)' 
  - name: ado-project
    value: 'Commercialization Technologies'
  - name: ado-repo
    value: 'eZFlow-Backend'
  - name: ado-branch
    value: development

stages:
- stage: linter
  displayName: Linter
  jobs:
  - job: megalinter
    displayName: MegaLinter
    pool:
        vmImage: 'ubuntu-latest'
    steps:
    - template: steps/megalinter-scan.yml@azure-pipeline-template

    # Upload MegaLinter reports
    - task: PublishPipelineArtifact@1
      condition: succeededOrFailed()
      displayName: Upload MegaLinter reports
      inputs:
        targetPath: "$(System.DefaultWorkingDirectory)/megalinter-reports/"
        artifactName: MegaLinterReport