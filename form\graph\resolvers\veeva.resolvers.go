package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"fmt"
	"time"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/teams_service"
)

// UpdateEventStatusPendingToApproved is the resolver for the updateEventStatusPendingToApproved field.
func (r *mutationResolver) UpdateEventStatusPendingToApproved(ctx context.Context, userToken string, input model.UpdateEventStatusPendingToApprovedInput) (*model.CommonResponse, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}
	start := time.Now().UTC()
	teams_service.SendMsg(ctx, `Request UPDATE COMPLETED EVENTS BACK TO APPROVED`, fmt.Sprintf(`User: %v`, userToken), "UpdateApproveEvents")
	Response := controller.UpdateEventStatusPendingToApproved(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpdateEventStatusCompletedToApproved", "support_api/UpdateApproveEvents", time.Since(start), "200")
	return Response, nil
}

// UpdateEventStatusCompletedToApproved is the resolver for the updateEventStatusCompletedToApproved field.
func (r *mutationResolver) UpdateEventStatusCompletedToApproved(ctx context.Context, userToken string, input model.UpdateEventStatusCompletedToApprovedInput) (*model.CommonResponse, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}
	start := time.Now().UTC()
	teams_service.SendMsg(ctx, `Request UPDATE COMPLETED EVENTS BACK TO APPROVED`, fmt.Sprintf(`User: %v`, userToken), "UpdateApproveEvents")
	Response := controller.UpdateApproveEvents(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpdateEventStatusCompletedToApproved", "support_api/UpdateApproveEvents", time.Since(start), "200")
	return Response, nil
}

// UpdateNumberofEmp is the resolver for the updateNumberofEMP field.
func (r *mutationResolver) UpdateNumberofEmp(ctx context.Context, userToken string, input model.UpdateNumberofEMPInput) (*model.CommonResponse, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}
	start := time.Now().UTC()
	teams_service.SendMsg(ctx, `Request UPDATE COMPLETED EVENTS BACK TO APPROVED`, fmt.Sprintf(`User: %v`, userToken), "UpdateNumberofEmp")
	Response := controller.UpdateNumberofEMP(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpdateNumberofEmp", "support_api/UpdateNumberofEmp", time.Since(start), "200")
	return Response, nil
}

// DeleteDraftEvents is the resolver for the deleteDraftEvents field.
func (r *mutationResolver) DeleteDraftEvents(ctx context.Context, userToken string, input model.DeleteDraftEventsInput) (*model.CommonResponse, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}
	start := time.Now().UTC()
	teams_service.SendMsg(ctx, `Request DELETE DRAFT EVENTS`, fmt.Sprintf(`User: %v`, userToken), "DeleteDraftEvents")
	Response := controller.DeleteDraftEvents(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("DeleteDraftEvents", "support_api/DeleteDraftEvents", time.Since(start), "200")
	return Response, nil
}

// FetchAllVeevaAccountDetails is the resolver for the fetchAllVeevaAccountDetails field.
func (r *queryResolver) FetchAllVeevaAccountDetails(ctx context.Context, input *model.RequestAllVeevaAccountDetails) (*model.ResponseAllVeevaAccountDetails, error) {
	start := time.Now().UTC()
	Response := controller.FetchAllVeevaAccountDetails(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("FetchAllVeevaAccountDetails", "form/FetchAllVeevaAccountDetails", time.Since(start), "200")
	return &Response, nil
}

// FetchAllAttendanceVeevaAccountDetails is the resolver for the fetchAllAttendanceVeevaAccountDetails field.
func (r *queryResolver) FetchAllAttendanceVeevaAccountDetails(ctx context.Context, input *model.RequestAllAttendanceVeevaAccountDetails) (*model.ResponseAllAttendanceVeevaAccountDetails, error) {
	start := time.Now().UTC()
	Response := controller.FetchAllAttendanceVeevaAccountDetails(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("FetchAllAttendanceVeevaAccountDetails", "form/FetchAllAttendanceVeevaAccountDetails", time.Since(start), "200")
	return &Response, nil
}

// SyncEventToVeeva is the resolver for the syncEventToVeeva field.
func (r *queryResolver) SyncEventToVeeva(ctx context.Context, eventID string, options map[string]interface{}) (*model.CommonResponse, error) {
	teams_service.SendMsg(ctx, `Sending Event to Veeva`, fmt.Sprintf(`Event ID: %v`, eventID), "SyncEventsToVeeva")
	return controller.SyncEventToVeeva(ctx, eventID, options)
}

// UpdateEventFormAnswers is the resolver for the updateEventFormAnswers field.
func (r *queryResolver) UpdateEventFormAnswers(ctx context.Context, userToken string, options []map[string]interface{}) (*model.CommonResponse, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}

	teams_service.SendMsg(ctx, `Request UPDATE FORM ANSWERS`, fmt.Sprintf(`User: %v`, userToken), "UpdateEventFormAnswers")
	return controller.UpdateFormAnswers(ctx, options)
}

// GetAuditEvent is the resolver for the getAuditEvent field.
func (r *queryResolver) GetAuditEvent(ctx context.Context, userToken string, options map[string]interface{}) (*model.EventAudit, error) {
	if !controller.AuthenticateSupportAPI(userToken) {
		return nil, fmt.Errorf(`access denied`)
	}

	return controller.GetAuditEvent(ctx, options)
}
