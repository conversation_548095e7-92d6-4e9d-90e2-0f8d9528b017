package veeva_service

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v4/pgxpool"
	"strings"
)

func QueryActiveUserEmails(ctx context.Context, pool *pgxpool.Pool) []string {
	q := `
	SELECT email 
	FROM "user"
	WHERE 
		--is_active = true AND is_deleted = false
		--AND 
		email <> '' AND veeva_territory_id = ''`

	rows, err := pool.Query(ctx, q)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	rs := make([]string, 0)
	for rows.Next() {
		var email string
		if err := rows.Scan(&email); err != nil {
			panic(err)
		}

		rs = append(rs, email)
	}

	return rs
}

// QueryTerritories returns ~25000 rows and not likely to increase. Not limiting is acceptable
func QueryTerritories(ctx context.Context, pool *pgxpool.Pool) []string {
	q := `
	SELECT territory_id 
	FROM territories
	WHERE 
		name IS NOT NULL AND name != '' AND name like 'VN%'`

	rows, err := pool.Query(ctx, q)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	rs := make([]string, 0)
	for rows.Next() {
		var territoryId string
		if err := rows.Scan(&territoryId); err != nil {
			panic(err)
		}

		rs = append(rs, territoryId)
	}

	return rs
}

func QueryHCOCities(ctx context.Context, pool *pgxpool.Pool) []string {
	q := fmt.Sprintf(`
	SELECT DISTINCT(city) 
	FROM customer 
	WHERE 
		city != '' 
		and is_active=true and is_deleted=false
	`)

	rows, err := pool.Query(ctx, q)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	rs := make([]string, 0)
	for rows.Next() {
		var city string
		if err := rows.Scan(&city); err != nil {
			panic(err)
		}

		rs = append(rs, city)
	}

	return rs
}

type HCP struct {
	Id             string `json:"id"`
	Name           string `json:"name"`
	HcoName        string `json:"hco_name"`
	Classification string `json:"classification"`
	Specialty      string `json:"specialty"`
	City           string `json:"city"`
	HCOId          string `json:"hco_id"`
	Province       string `json:"province"`
	Product        string `json:"product"`
	TerritoryId    string `json:"territory_id"`
}

type SearchHCPParams struct {
	Country     int
	HCPName     []string
	TerritoryId []string
	HCO         []string
	City        []string
	Province    []string
	Specialty   []string
}

func toLowerSlice(s []string) []string {
	for i, v := range s {
		s[i] = strings.ToLower(v)
	}
	return s
}

func QueryHCPList(ctx context.Context, pool *pgxpool.Pool, searchParams SearchHCPParams, limit int) []HCP {
	var whereCondition string
	if searchParams.Country != 0 {
		whereCondition += fmt.Sprintf(" \nAND country=%d", searchParams.Country)
	}

	if len(searchParams.HCPName) != 0 {
		whereCondition += fmt.Sprintf(" \nAND lower(name) IN ('%s')", strings.Join(toLowerSlice(searchParams.HCPName), `','`))
	}

	if len(searchParams.TerritoryId) != 0 {
		whereCondition += fmt.Sprintf(" \nAND t.territory_id IN ('%s')", strings.Join(searchParams.TerritoryId, `','`))
	}

	if len(searchParams.HCO) != 0 {
		whereCondition += fmt.Sprintf(" \nAND lower(organization) IN ('%s')", strings.Join(toLowerSlice(searchParams.HCO), `','`))
	}

	if len(searchParams.City) != 0 {
		if k := strings.TrimSpace(strings.Join(searchParams.City, `','`)); k != "" {
			whereCondition += fmt.Sprintf(" \nAND city IN ('%s')", strings.Join(searchParams.City, `','`))
		}
	}
	if len(searchParams.Province) != 0 {
		if k := strings.TrimSpace(strings.Join(searchParams.Province, `','`)); k != "" {
			whereCondition += fmt.Sprintf(" \nAND lower(province) IN ('%s')", strings.Join(toLowerSlice(searchParams.Province), `','`))
		}
	}

	if len(searchParams.Specialty) != 0 {
		if k := strings.TrimSpace(strings.Join(searchParams.Specialty, `','`)); k != "" {
			whereCondition += fmt.Sprintf(" \nAND v_sp_desc IN ('%s')", strings.Join(searchParams.Specialty, `','`))
		}
	}
	q := fmt.Sprintf(`
	SELECT
		DISTINCT(t.veeva_id), name, hco_name, hco_id, city, province, specialty
	FROM ( SELECT
					COALESCE(c.veeva_reference_id,'') as veeva_id,
					COALESCE(name,'') as name,
					COALESCE(t.territory_id,'') as territory_id,
					COALESCE(organization,'') as hco_name,
					COALESCE((SELECT c2.veeva_reference_id FROM customer c2 WHERE c2.is_active = true AND lower(c2.account_type) = 'organization' AND lower(c2.name) = lower(c.organization) LIMIT 1),'')  as hco_id,
					COALESCE(city,'') as city,
					COALESCE(province,'') as province,
					COALESCE(v_sp_desc,'') as specialty
			FROM customer c
			LEFT JOIN customer_territories t ON c.veeva_reference_id = t.veeva_reference_id
			WHERE
				is_active = TRUE AND lower(account_type) != 'organization'
				%s	
			LIMIT %d
	) as t LIMIT %d`, whereCondition, limit+5000, limit)

	fmt.Println(q)
	rows, err := pool.Query(ctx, q)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	rs := make([]HCP, 0)
	for rows.Next() {
		var hcp HCP
		if err := rows.Scan(
			&hcp.Id,
			&hcp.Name,
			&hcp.HcoName,
			&hcp.HCOId,
			&hcp.City,
			&hcp.Province,
			&hcp.Specialty); err != nil {
			panic(err)
		}

		rs = append(rs, hcp)
	}

	return rs
}

func QueryRelativeTerritory(ctx context.Context, pool *pgxpool.Pool, territoryList []string) []string {

	q := fmt.Sprintf(`
	with a as (
		SELECT
			territory_id, parent_territory_id
		FROM territories
		WHERE
			territory_id IN ('%s')
			OR parent_territory_id IN ('%s')
			OR territory_id IN (SELECT t2.parent_territory_id FROM territories t2 WHERE t2.territory_id IN ('%s'))
			OR parent_territory_id IN (SELECT t2.parent_territory_id FROM territories t2 WHERE t2.territory_id IN ('%s'))
		)
	SELECT
	    territory_id
	FROM territories
	WHERE territory_id in (SELECT territory_id FROM a) OR territory_id in (SELECT parent_territory_id FROM a)
`,
		strings.Join(territoryList, `','`),
		strings.Join(territoryList, `','`),
		strings.Join(territoryList, `','`),
		strings.Join(territoryList, `','`))

	fmt.Println(q)
	rows, err := pool.Query(ctx, q)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	rs := make([]string, 0)
	for rows.Next() {
		var territoryId string
		if err := rows.Scan(&territoryId); err != nil {
			panic(err)
		}

		rs = append(rs, territoryId)
	}

	return rs
}
