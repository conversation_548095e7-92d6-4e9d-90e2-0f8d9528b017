type values {
    description: String!
    value:       String!
}

type actions {
    id: String
    action: String
}

input getAllEventIdListRequest{
    searchItem:String
    formId:String
}

type getAllEventIdListResponse{
    error: Boolean!
    message: String!
    getAllEventIdListDetails:[getAllEventIdListDetails!]!
}

type getAllEventIdListDetails{
    value:String!
    description:String!
}

type groupActions {
    groupId: String
    action: String
}
type groupRules {
    value: String
    actions: [groupActions]
}

type rules{
    value:String
    actions:[actions]
}

type inputs {
    id: String
    hidden: Boolean
    rules:[rules]
    title: String
    type: String
    readOnly: Boolean
    sequenceNo: Int
    source: String
    operation: String
    values: [values]
    validatesource: String
    validations: [validationInput]
}

type validationInput {
    controlID: String!
    activityID: [String]
    codeID: Int
    country: Int
    comparisonOperator: String!
    category: String!
    description: String  
    limit: Float
    enabled: Boolean!
    type: String
}

type inputGroup {
    groupId: String
    sequenceNo: Int
    title: String
    groupRules: [groupRules]
    hidden: Boolean
    inputs: [inputs]
}

type questionGroup{
    id: String
    sequenceNo: Int
    title: String
    inputs: [inputGroup]
}

type formSectionContent {
    id: String
    title: String
    sequenceNo: Int
    group: [questionGroup]
}

type formSections {
    id: String
    title: String
    sequenceNo: Int
    form:formSectionContent
    childForm: formSectionContent
}

type formTabs {
    id: String!
    title: String!
    sequenceNo: Int!
    sections: [formSections!]
}

type formTemplateResponse {
    error: Boolean!
    message: String!
    design: [formTabs!]!
}

input valueInput {
    description: String
    value:       String
}

input groupActionsInput {
    groupId: Int
    action: String
}

input groupRuleInput {
    value: String
    actions: [groupActionsInput]
}

input actionsInput{
    id: Int
    action: String
}

input ruleInput {
    value: String
    actions:[actionsInput]
}

input questionInput {
    id: Int
    title: String
    type: String
    sequenceNo: Int
    rules: [ruleInput]
    source: String
    hidden: Boolean
    readOnly: Boolean
    operation: String
    values: [valueInput]
}

input inputGroupInput {
    groupId: Int
    sequenceNo: Int
    title: String
    groupRules: [groupRuleInput]
    hidden: Boolean
    inputs: [questionInput]
}

input questionGroupInput {
    sequenceNo: Int
    title: String
    questions: [inputGroupInput]
}

input formSectionContentInput {
    title: String
    sequenceNo: Int
    group: [questionGroupInput]
}

input formSectionInput {
    title: String
    sequenceNo: Int
    form: formSectionContentInput
    childForm:formSectionContentInput
}

input formTabsInput {
    title: String
    sequenceNo: Int
    sections: [formSectionInput]
}

input formTemplateSubmitRequest {
    design: [formTabsInput!]!
}

type formTemplateSubmitResponse {
    error: Boolean!
    message: String!
}


input valuesAnswers {
    id: String
    description: String!
    value:       String!
}

input actionsAnswers {
    id: String
    action: String
}

input rulesAnswer{
    value:String
    actions:[actionsAnswers]
}

input inputAnswer {
    id: String
    title: String
    type: String
    sequenceNo: Int
    source: String
    values: [valuesAnswers]
}

input groupAnswer{
    id: String
    title: String
    sequenceNo: Int
    source: String
    answers: [inputAnswer]
}

input questionGroupAnswer {
    id: String
    sequenceNo: Int
    title: String
    questionAnswers: [groupAnswer]
}

input formSectionContentAnswerInput{
    id: String
    title: String
    sequenceNo: Int
    groupAnswer: [questionGroupAnswer]
}

input formSectionAnswerInput{
    id: String
    title: String
    sequenceNo: Int
    form: formSectionContentAnswerInput
    childForm: formSectionContentAnswerInput
}

input formTabsAnswerInput {
    id: String
    title: String
    sequenceNo: Int
    sectionAnswer: [formSectionAnswerInput!]!
}

# input formAnswerSubmissionRequest {
#     answers: [formTabsAnswerInput]!
#     approvalRoles:[approvalRole!]!
# }

input formAnswerSubmissionRequest {
    id: String
    isDraft: Boolean!
    totalCost: Float!
    parentEventId: String
    answers: [formTabsAnswerInput]!
    isChangeRequest: Boolean
    changeRequestSummary:String
    isApollo:Boolean
}

type valuesAnswer {
    id: String
    description: String
    value:       String
    valueInUsd : String
}

type inputAnswers { 
    id: String
    title: String
    type: String
    sequenceNo: Int
    source: String
    values: [valuesAnswer]
}

type groupAnswers{
    id: String
    title: String
    sequenceNo: Int
    source: String
    answers: [inputAnswers]
}

type questionGroupAnswers {
    id: String
    sequenceNo: Int
    title: String
    questionAnswers: [groupAnswers]
}

type formSectionContentAnswer{
    id: String
    title: String
    sequenceNo: Int
    groupAnswer: [questionGroupAnswers]
}

type formSectionAnswer{
    id: String
    title: String
    sequenceNo: Int
    form: formSectionContentAnswer
    childForm: formSectionContentAnswer
}

type formTabsAnswer {
    id: String
    title: String
    sequenceNo: Int
    sectionAnswer: [formSectionAnswer!]!
}

type BankInformation {
    bankName: String!
    bankBranch: String!
    bankAccountNumber: String!
    bankAccountHolder: String!
}

type Signatory {
    ZPRepresentative: String!
    ZPRepresentativeTitle: String!
    HCORepresentative: String!
    HCORepresentativeTitle: String!
    HCOAbbr: String!
}

type formAnswerResponse{
    error: Boolean!
    message: String!
    requestorCountry: String
    requestorCurrency: String
    dateCreated: Int
    meetingId:String
    answers:[formTabsAnswer!]!
    attachments:[attachmentResponse]!
    isExceptionalApproval: Boolean!
    detailsOfRequest: String
    ihcpOtherLocalPolicies: String
    moreComments: String
    exceptionalApprovalFileUpload: [String]
    scopeOfExceptionalRequest: String
    isChangeRequest:Boolean
    changeRequestSummary:String
    changeRequestType:String
    isRecall:Boolean
    recallSummary:String

    ConversionRate:Float
    lineSecretaryReturnSummary: String

    boardOfDirectory:[boardOfDirectoryData]
    bankInformation:BankInformation
    signatory:Signatory
}

type boardOfDirectoryData{
    name:String 
	position:String 
	question:String 
	remarks:String 
}

type attachmentResponse {
    type: String                                 
    categories: [attachmentCategoryResponse]!
}

type attachmentCategoryResponse{
    description:  String              
    url:[String]!                           
}

input formAnswerInput {
    formId: String!
    eventId:String
} 

input approvalRole {
    userId: String!
    roleId: String!
    sequenceNo: Int!
    departmentId: String!
}    

type formAnswerSubmissionResponse {
    error: Boolean!
    message: String!
    formAnsId: String!
    formAnsSeq: String!
}
input formApprovalInput {
    id: String!
    action: String!
    comment: String
    isExceptionalApprover: Boolean
    isCompliance: Boolean
    isApollo:Boolean
}
type formApprovalResponse {
    error: Boolean!
    message: String!
}
input requestorActionInput{
    id: String!
    comment: String
    isApollo:Boolean
}

type pendingApproval {
    id: String!
    typeOfActivity: [String!]!
    typeOfEvent: [String!]!
    submitedDate: Int!
    requestorName:String!
    approvalStatus: String!
    approvalStatusValue: String!
    totalCost: Float!
    eventStartDate: Int!
    eventEndDate: Int!
    departmentName: String!
    activityName: String!
    productName: String!
    countryTitle: String!
    countryValue: String!
    currencyTitle: String!
    currencyValue: String!
    eventId: String!
    isExceptionalApprover: Boolean
    changeRequestSummary: String
    isApproverAccess:Boolean
    isRecall:Boolean
}

type RequestorResponse {
    approverName: String!
    approverComment: String!
    approverCommentDate: Int!
}

type pendingApprovalResponse {
    error: Boolean!
    message: String!
    maxTotalCost: Float!
    totalCount: Int!
    lastPage:Int
    data: [pendingApproval]!
}

type requesterSubmittedFormAnswerList {
    requestorName: String!
    id: String!
    requestorActiveDirectory: String!
    typeOfActivity: [String!]!
    typeOfEvent: [String!]!
    submitedDate: Int!
    approvalStatus: String!
    approvalStatusValue: String!
    totalCost: Float!
    eventStartDate: Int!
    eventEndDate: Int!
    departmentName: String!
    activityName: String!
    productName: String!
    countryTitle: String!
    countryValue: String!
    currencyTitle: String!
    currencyValue: String!
    eventId: String!
    limit: Int
    pageNo: Int
    isOwner: Boolean
    isExceptionalApprover: Boolean
    productOwner:String
    isRecallAccess:Boolean
    response: [RequestorResponse!]!
}

type requesterSubmittedFormAnswerListResponse {
    error: Boolean!
    message: String!
    url: String!
    maxTotalCost: Float!
    totalCount: Int!
    lastPage:Int
    data: [requesterSubmittedFormAnswerList]!
}

input activityInputs {
    activities: [String!]!
    totalExpense: Float!
    hasHCPEngagement: Boolean
    hasInternational: Boolean
    hasLevelOfInfluence: Boolean
    formAnswerID: String
}

type UserSelection {
    description: String!
    hint: String! 
    value: String!
    roleId: String
}

type UserByApprovalRole {
    departmentName: String!
    sequenceNo: Int!  
    departmentType: String! 
    departmentID: String! 
    userSelection: [UserSelection]!
    highLoi: Boolean
}

type approvalRolesUserSelectionResponse {
    error: Boolean!
    message: String!
    usersByapprovalRole: [UserByApprovalRole!]!        
}

type formAnswerAttachmentResponse {
    error: Boolean!
    message: String!      
}

input AttachmentCategory{
    description:  String              
    url:[String!]!                           
}

input attachment {
    type: String                                 
    categories: [AttachmentCategory!]!
}

input formAnswerAttachment {
    formAnswerId: String!
    attachments: [attachment!]!
    isApollo:Boolean
}
input roleSelection{
    formId: String!
    approvalRoles:[approvalRole!]!
    isChangeRequest: Boolean
    changeRequestSummary: String
    isApollo:Boolean
}

type totalEvents {
    requestorTotalPending: Int
    requestorTotalApproved: Int
    requestorTotalRejected: Int
    approverTotalPending: Int
    approverTotalApproved: Int
    approverTotalRejected: Int
}

type totalEventsResponse {
    error: Boolean!
    message: String!
    data: totalEvents
}

input formAnswerIDInput {
    formAnswerId: String!
    eventId:String
}

input basicInfoDateValidationRequest {
    id: String
    isChangeRequest: Boolean
    activityEvent:String
    eventType:String
    startDate:String!
    virtualEventDetails:String!
    endUnixTime:String!
    startUnixTime:String!
    boardOfDirectoryValidation:[boardOfDirectoryDataValidation]
}
input boardOfDirectoryDataValidation {
    name:String 
	position:String 
	question:String 
	remarks:String 
}
input levelOfInfluenceValidationRequest{
    levelOfInfluence: String!
    loiquestionAnswer:String!
}
type levelOfInfluenceValidationResponse{
    error: Boolean!
    message: String!
}

type basicInfoDateValidationResponse {
    error: Boolean!
    message: String!
}

type approvalData{
    approvalRole: String
    actionedBy: String!
    actionedByRole: String!
    status: String!
    dateCreated: Int!
    comment: String!
}

type approvalTrailFormSubmissionResponse {
    error: Boolean!
    message: String!
    approval: [approvalData!]!
}

input formAnswerByEventIdInput {
    eventId: String!
}

type formAnswerByEventIdResponse {
    error : Boolean!
    message : String!
    data : eventIdData

}

type eventIdData {
    description : String!
    value : String!
}
input notificationKeyLimit{
    limit: Int
    lastID: String 
}
input limit{
    limit: Int
    pageNo: Int
    searchItem: String
    sortItem: [sortItems!]
    typeOfActivityFilter: [Int!]
    typeOfEventFilter: [Int!]
    statusFilter:[String!]
    totalCostRange:[Int!]
    dateFilter: String
    dateFilterStart: String
    dateFilterEnd: String
    dateCreatedFilterStart:String
    dateCreatedFilterEnd:String
    exceptionalStatusFilter:[String!]
}

input requesterInput{
    limit: Int
    pageNo: Int
    status: String
    isAdmin: Boolean
    isExcel: Boolean
    exceptionalStatusFilter:[String!]
    searchItem: String
    sortItem: [sortItems!]
    typeOfActivityFilter: [Int!]
    typeOfEventFilter: [Int!]
    statusFilter:[String!]
    totalCostRange:[Int!]
    dateFilterStart: String
    dateFilterEnd: String
    product: String
    team: String
    requestor: String
    productOwner:String
    dateCreatedFilterStart:String
    dateCreatedFilterEnd:String
    recallActionType:String
}

input sortItems{
    columns : String
    order : String
}

input userformAnsId{
    formAnsId : String!
    isApollo:Boolean
}

type formAnswerforcancel {
    error : Boolean!
    message : String!
}

input formSubmissionActivity{
    formAnsId: String!
    startDate: String!
    endDate: String!
    duration: String!
    isExceptionalApprover:Boolean!
}

type formSubmissionResponse {
    error: Boolean!
    message: String!
}

input approverformAnsId{
    formAnsId: String!
}

type approverformAnsResponse{
    name: String!
    approvalID: String!
    approvalValue: String
    approvalTitle: String
    groupValue: String!
    groupTitle: String!
    sequenceNo: Int!
    departmentName: String
    departmentId: String
}

type fetchapproverDataByFormAnsID{
    error: Boolean!
    message: String!
    approval: [approverformAnsResponse!]!
}

type getNotificationStatus{
    id : String!
    formAnswerID : String!
    message : String!
    isRead : Boolean!
    dateCreated: Int!
}

type getNotificationStatusForUserResponse{
    error: Boolean!
    message: String!
    getNotificationStatus: [getNotificationStatus]!
}

input statusChangeNotificationInput{
    NotificationID: [String!]!
}

type responseStatusChangeNotification{
    error: Boolean!
    message: String!
}

type getActivitiesAndEventsSelectionResponse{
    error: Boolean!
    message: String!
    getActivitiesAndEventsSelection: getActivitiesAndEventSelection!
}

type getRequestorForCountryResponse{
    error: Boolean!
    message: String!
    data: [dropdown]
}

type getActivitiesAndEventSelection {
    status: [dropdown]
    activityType: [dropdown]
    eventType: [dropdown]
    team: [dropdown]
    product: [dropdown]
    productOwner:[dropdown]
    requestorList:[dropdown]
    lineSecretaryList:[dropdown]
}

type dropdown{
    description: String!
    value: String!
}

type approvalLogResponse{
    error: Boolean!
    message: String!
    getApprovalLog: [approvalLog]!
}

type approvalLog {
    id: String!
    formAnsId: String!
    eventId: String!
    actionedBy: String!
    actionedByName: String!
    statusValue: String!
    statusTitle: String!
    dateCreated: Int!
    comment: String!
    userRoleValue: String!
    userRoleTitle: String!
}

input getRequestorForCountryInput {
    searchItem: String
}

input getAttachmentZipInput {
    attachment : [String!]
    eventId:String!
}

type getAttachmentZipResponse{
    error: Boolean!
    message: String!
    url: String! 
}

input updateCompletedEventsInput {
    formAnsId: String!
    status: String!
}

type updateCompletedEventsResponse{
    error: Boolean!
    message: String!
}

input exceptionalDetailsInput{
    id: String!
    detailsOfRequest: String
    ihcpOtherLocalPolicies: String
    moreComments: String
    exceptionalApprovalFileUpload: [String]
    scopeOfExceptionalRequest: String
}

type exceptionalDetailsResponse{
    error: Boolean!
    message: String!
}

input changeRequestInput{
    formAnswerID: String!
    activityStartDate: String
    activityEndDate: String
    duration: String
    noOfHCP: String
    noOfNonHCP: String
    typeOfHCP: String
    roles: [String]
    venue: String
    virtualEvent: Boolean
    govtNonGovtHcp: String
    remarks: String
    sponsoredHcp: String
    speciality: String
    hco: String
}
type changeRequestOutput{
    error: Boolean!
    message: String!
}
input getTypeApprovalListInput{
    formAnswerID: String!
    type: String!
}
type getTypeApprovalListResponse{
    error: Boolean!
    message: String!
    usersByType: [approversByType!]! 
}
type approversByType {
    userId: String!
    roleId: String!
    sequenceNo: Int!
    departmentId: String!
    departmentName: String!
    userName: String!
}

type getEmployeeManagementFilterResponse{
    error: Boolean!
    message: String!
    getEmployeeManagementFilterDropdown: getEmployeeManagementFilterDropdown!
}

type getEmployeeManagementFilterDropdown {
    actionBy: [dropdown]
    actionType: [dropdown]
    requestorActiveDirectory:[dropdown]
}

input approvalRoleSelectionForExceptionalInput{
    formId: String!
    approvalRoles:[approvalRole!]!
    isApollo:Boolean
}

type formSubmissionApprovalForAllEventResponse{
    error: Boolean!
    message: String!
}

input fetchAllProductRequest{
    productOwnerName:String!
}

type allProductDetails{
    description:String
    value:String
}

type fetchAllProductResponse{
    error: Boolean!
    message: String!
    allProductDetails:[allProductDetails!]!
}

input recallFormApprovalInput {
    formAnsId: String!
    action: String!
    comment:String
    isApollo:Boolean
}

type recallFormApprovalResponse {
    error: Boolean!
    message: String!
}

input formAnswerInputForLinkedEvent {
    formId: String!
    eventId:String
}  

type formAnswerResponseForLinkedEvent{
    error: Boolean!
    message: String!
    requestorCountry: String
    requestorCurrency: String
    dateCreated: Int
    meetingId:String
    answers:[formTabsAnswer!]!
    attachments:[attachmentResponse]!
    isExceptionalApproval: Boolean!
    detailsOfRequest: String
    ihcpOtherLocalPolicies: String
    moreComments: String
    exceptionalApprovalFileUpload: [String]
    scopeOfExceptionalRequest: String
    isChangeRequest:Boolean
    changeRequestSummary:String
    isRecall:Boolean
    recallSummary:String
    eventRequestorName:String
    boardOfDirectory:[boardOfDirectoryData]
    ConversionRate:Float
}
input hcpLoiRequest {
    answer1:String!
    answer2:String!
    answer3:String!
    answer4:String!
    answer5:String!
}

type hcpLoiResponse{
    error: Boolean!
    message: String!
    levelOfInfluence:String!
}


input ownerDataRequest{
    userId: String!
}

type ownerDataResponse{
    error: Boolean!
    message: String!
    ownerActiveDirectory:String!
    ownerFullName: String!
}

input getFmvAuditlogsRequest{
    limit: Int
    pageNo: Int
}

type getFmvAuditlogsResponse{
    error: Boolean!
    message: String!
    dataCount:Int!
    getFmvAuditlogsData:[getFmvAuditlogsData!]!
}

type getFmvAuditlogsData{
    description:String!
    maxLimit:String!
    date:String!
    currency:String!
    status:String!
    createdBy:String!
}

input getPdfBlobUrlRequest{
   FormID:String!
   CountryCurrency:String!
   UsdCurrency:String! 
   convertRate:Float!
   startDate:String!
   endDate:String!
   hcpTotalExpense:String!
   eventTotalExpense:String!
   totalCost:String!
   meetingId:String!
   hcpTotalExpenseUSD:String!
   eventTotalExpenseUSD:String!
   submission:String!
}

type getPdfBlobUrlResponse{
    error: Boolean!
    message: String!
    url:String!
}

input formAnswerInputForApollo {
    formId: String!
    eventId:String
}  
type formAnswerResponseForApollo{
    error: Boolean!
    message: String!
    requestorCountry: String
    requestorCurrency: String
    dateCreated: Int
    meetingId:String
    answers:[formTabsAnswer!]!
    attachments:[attachmentResponse]!
    isExceptionalApproval: Boolean!
    detailsOfRequest: String
    ihcpOtherLocalPolicies: String
    moreComments: String
    exceptionalApprovalFileUpload: [String]
    scopeOfExceptionalRequest: String
    isChangeRequest:Boolean
    changeRequestSummary:String
    changeRequestType:String
    isRecall:Boolean
    recallSummary:String
    lineSecretaryReturnSummary:String
    boardOfDirectory:[boardOfDirectoryData]
    typeOfEngagements:[String]
    ConversionRate:Float
    ownerActiveDirectory:String
    eventStatusTitle:String
    eventStatusValue:String
    requestorName:String
}
input apolloFormAnswerIDInput {
    formAnswerId: String!
    eventId:String
}

type apolloApprovalTrailFormSubmissionResponse {
    error: Boolean!
    message: String!
    approval: [apolloApprovalData!]!
}
type apolloApprovalData{
    approvalRole: String
    actionedBy: String!
    approverActiveDirectory:String!
    approverSeq:Int!
    actionedByRole: String!
    status: String!
    dateCreated: Int!
    comment: String!
}
type getActivitiesAndEventSelectionAllDropDownValues {
    meetingMode:[dropdown]
    country:[dropdown]
    expertLevelInternational:[dropdown]
    expertLevel:[dropdown]
    role:[dropdown]
    typeOfEventExpenses:[dropdown]
}

type getActivitiesAndEventSelectionAllDropDownValuesResponse{
    error: Boolean!
    message: String!
    getActivitiesAndEventsSelection: getActivitiesAndEventSelectionAllDropDownValues
}

input excelExtractTrailDataRequest{
    formAnsId:String
    startDate:String
    endDate:String
    isExcel:Boolean
}

type excelExtractTrailDataResponse{
    error:Boolean!
    message:String!
    url:String
}

type calculateExpenseResponse {
    error:Boolean!
    message:String!
    calculateExpenseData:calculateExpenseData!
}

type calculateExpenseData {
   totalCostInLocalCurrency:String!
   totalCostInUsd:String!
   costPerUnitInUsd:String!

}
input totalHcpExpenseRequest {
    totalHcpExpense:[totalHcpExpense!]!
}

input totalHcpExpense {
    poposedHonorarium:Float!
    registrationFeeTotalcost:Float!
    groundTransportationTotalcost:Float!
    airfareTotalcost:Float!
    mealTotalcost:[Float]!
    accomodationTotalCost:Float!
    othersTotalCost:[Float]!
}

type totalHcpExpenseResponse {
    error:Boolean!
    message:String!
    calculateHcpExpenseData:calculateHcpExpenseData!
}

type calculateHcpExpenseData {
   totalHcpExpenseInLocalCurrency:String!
   totalHcpExpenseInUsd:String!
}
type totalEventExpenseResponse {
    error:Boolean!
    message:String!
    calculateEventExpenseData:calculateEventExpenseData!
}

type calculateEventExpenseData {
   totalEventExpenseInLocalCurrency:String!
   totalEventExpenseInUsd:String!
}
input totalEventExpenseRequest {
    totalEventExpense:[totalEventExpenseData!]!
}
input totalEventExpenseData{
    typeOfEventExpense:String!
    totalCost:[Float]!
}
input calculatetotalEventExpenseRequest {
    formId: String!
}
input calculateTotalHcpExpenseRequest {
    formId: String!
}
input getDurationFormatRequest{
    startDate:Int!
    endDate:Int!
}

type getDurationFormatResponse{
    error:Boolean!
    message:String!
    durationFormat:String!
}
input conversionAmountRequest{
    proposedPayableHonorariumAmount:Float!
} 

type conversionAmountResponse{
    error:Boolean!
    message:String!
    proposedPayableHonorariumAmountInLocalCurrency:String!
    proposedPayableHonorariumAmountInUsd:String!
}
input USDValueRequest{
    currency:String!
    expenseAmount:Float!
}
type USDValueResponse{
    error:Boolean!
    message:String!
    expenseAmountUSD:String!
}

input hcpEngagementValidationRequest{
    hcps:[hcpsData!]!
}
input hcpsData{
    veevaID:String!
    name: String!
    specialty: String!
    organization: String!
}

type hcpEngagementValidationResponse{
    error:Boolean!
    message:String!
    hcpEngagementValidationData:[hcpEngagementValidationData!]!
}

type hcpEngagementValidationData{
    id: String!
    name: String!
    veevaID:String!
    specialty: String!
    organization: String!
    valid:Boolean!
    errorMessage:String!
}
input formAnswerAttachmentForEzClaim {
    formAnswerId: String!
    attachments: [attachmentForEzClaim!]!
}

input AttachmentCategoryForEzClaim{
    description:  String              
    url:[String!]!                           
}

input attachmentForEzClaim {
    type: String                                 
    categories: [AttachmentCategoryForEzClaim!]!
}
type formAnswerAttachmentResponseForEzClaim {
    formAnswerId: String!
    error: Boolean!
    message: String!
    attachments: [attachmentForEzClaimData!]!
}

type AttachmentCategoryForEzClaimData{
    description:  String              
    url:[String!]!  
    isValid:Boolean!
    errorMessage:String                         
}

type attachmentForEzClaimData {
    type: String                                 
    categories: [AttachmentCategoryForEzClaimData!]!
}
input calculateExpenseRequest {
    noOfUnits:Int!
    costPerUnit:Float!
}

input getSponsorshipAgreementPdfBlobUrlRequest{
    formID:String!

    countryCurrency:String
    usdCurrency:String
    convertRate:Float
}

input changeApprovalRequest {
    formAnswerId: String!
    changeApprovalType: String!
}

extend type Query {
    getPdfBlobUrl(input:getPdfBlobUrlRequest!):getPdfBlobUrlResponse
    getSponsorshipAgreementPdfBlobUrl(input:getSponsorshipAgreementPdfBlobUrlRequest!):getPdfBlobUrlResponse
    getOwnerInfo(input:ownerDataRequest!):ownerDataResponse!
    getHcpLoi(input:hcpLoiRequest!):hcpLoiResponse!
    getAllEventIdList(input:getAllEventIdListRequest!):getAllEventIdListResponse!
    fetchAllProductDetails(input:fetchAllProductRequest!):fetchAllProductResponse!
    getApprovalLog(input: formAnswerInput!) : approvalLogResponse!
    formTemplate: formTemplateResponse!
    getPendingApproval(input: limit): pendingApprovalResponse!
    basicInfoValidationDate(input:basicInfoDateValidationRequest!):basicInfoDateValidationResponse!
    getRequesterSubmittedFormAnswerList(input: requesterInput): requesterSubmittedFormAnswerListResponse!
    getFormAnswer(input: formAnswerInput!) : formAnswerResponse!
    getFormAnswerForApollo(input: formAnswerInputForApollo!) : formAnswerResponseForApollo!
    getApprovalRolesByActivities (input: activityInputs!): approvalRolesUserSelectionResponse!
    getTotalEvents: totalEventsResponse!
    getApprovalTrailData(input: formAnswerIDInput!): approvalTrailFormSubmissionResponse!
    getApolloApprovalTrailData(input: apolloFormAnswerIDInput!): apolloApprovalTrailFormSubmissionResponse!
    getFormAnswerIdByEventId(input:formAnswerByEventIdInput!): formAnswerByEventIdResponse!
    getApproverDataByFormAnsId(input: approverformAnsId!): fetchapproverDataByFormAnsID!
    getNotificationStatusForUser(input: notificationKeyLimit):getNotificationStatusForUserResponse!
    getActivitiesAndEventsSelection: getActivitiesAndEventsSelectionResponse!
    getActivitiesAndEventSelectionAllDropDownValues: getActivitiesAndEventSelectionAllDropDownValuesResponse!
    getEmployeeManagementFilter:getEmployeeManagementFilterResponse!
    getRequestorForCountry(input: getRequestorForCountryInput): getRequestorForCountryResponse!
    getAttachmentZip(input: getAttachmentZipInput): getAttachmentZipResponse!
    getTypeApprovalList(input: getTypeApprovalListInput!): getTypeApprovalListResponse!
    excelExtractTrailData(input:excelExtractTrailDataRequest!):excelExtractTrailDataResponse!
    getFormAnswerDetailsForLinkedEvent(input: formAnswerInputForLinkedEvent!) : formAnswerResponseForLinkedEvent!
    getFmvAuditlogs(input:getFmvAuditlogsRequest!):getFmvAuditlogsResponse!
    getlevelOfInfluenceValidation(input: levelOfInfluenceValidationRequest!):levelOfInfluenceValidationResponse!
    getCalculatedExpense(input:calculateExpenseRequest!):calculateExpenseResponse!
    getTotalHcpExpense(input:totalHcpExpenseRequest!):totalHcpExpenseResponse!
    getTotalEventExpense(input:totalEventExpenseRequest!):totalEventExpenseResponse!
    getCalculateTotalEventExpense(input:calculatetotalEventExpenseRequest!):totalEventExpenseResponse!
    getCalculateTotalHcpExpense(input:calculateTotalHcpExpenseRequest!):totalHcpExpenseResponse!
    getDurationFormat(input:getDurationFormatRequest!):getDurationFormatResponse!
    getconversionAmount(input: conversionAmountRequest!):conversionAmountResponse!
    getUSDValue(input: USDValueRequest!):USDValueResponse!
    hcpEngagementValidation(input: hcpEngagementValidationRequest!):hcpEngagementValidationResponse!
}

extend type Mutation {
    formTemplateSubmit(input: formTemplateSubmitRequest!): formTemplateSubmitResponse!
    formAnswerSubmission(input: formAnswerSubmissionRequest!): formAnswerSubmissionResponse!
    recallFormSubmission(input: recallFormApprovalInput!): recallFormApprovalResponse!
    formSubmissionApproval(input: formApprovalInput!): formApprovalResponse!
    approvalRoleSelection(input: roleSelection!) : formApprovalResponse!
    approvalRoleSelectionForExceptional(input: approvalRoleSelectionForExceptionalInput!) : formApprovalResponse!
    requestorAction(input: requestorActionInput!):formApprovalResponse
    formAnswerAttachments(input: formAnswerAttachment!):formAnswerAttachmentResponse
    formAnswerAttachmentseZClaimSyncApi(input: formAnswerAttachmentForEzClaim!):formAnswerAttachmentResponseForEzClaim
    saveFormAnswerAttachments(input: formAnswerAttachment!):formAnswerAttachmentResponse
    cancelFormAnswer(input: userformAnsId!): formAnswerforcancel!
    updateFormAnswerActivityDate(input: formSubmissionActivity!): formSubmissionResponse!
    updateStatusChangeNotification(input: statusChangeNotificationInput): responseStatusChangeNotification
    updateCompletedEvents(input: updateCompletedEventsInput!): updateCompletedEventsResponse!
    insertExceptionDetails(input: exceptionalDetailsInput!): exceptionalDetailsResponse!
    changeRequest(input: changeRequestInput!): changeRequestOutput!
    formSubmissionExceptionalApprovalForAllEvent(input:requestorActionInput!):formSubmissionApprovalForAllEventResponse!
    setChangeApproval(input: changeApprovalRequest):CommonResponse!
}
