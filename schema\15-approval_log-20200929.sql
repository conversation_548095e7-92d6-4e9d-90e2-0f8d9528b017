CREATE TABLE "approval_log" (
	"id" "uuid" primary key not null ,
	"form_answer" "uuid",
	"approved_by" "uuid",
	"approval_role" BIGINT,
	"date_created" "timestamptz"
);

ALTER TABLE "approval_log" ALTER COLUMN "id" SET NOT NULL;
ALTER TABLE "approval_log" ALTER COLUMN "form_answer" SET NOT NULL;
ALTER TABLE "approval_log" ALTER COLUMN "approved_by" SET NOT NULL;
ALTER TABLE "approval_log" ALTER COLUMN "approval_role" SET NOT NULL;
ALTER TABLE "approval_log" ALTER COLUMN "date_created" SET NOT NULL;

ALTER TABLE "approval_log" ADD CONSTRAINT fk_form_answer FOREIGN KEY (form_answer) REFERENCES "form_answers"(id);
