-- TABLE TERRITORIES
CREATE TABLE territories (
     id serial ,
     territory_id TEXT NOT NULL,
     name TEXT NOT NULL,
     parent_territory_id TEXT NOT NULL,

     created_at timestamptz default now(),
     updated_at timestamptz default now()
);

CREATE UNIQUE INDEX ON territories ((lower(territory_id)));
CREATE INDEX ON territories ((lower(name)));
CREATE INDEX ON territories ((lower(parent_territory_id)));

-- TABLE CUSTOMER
ALTER TABLE customer
ADD COLUMN territory_id TEXT NOT NULL DEFAULT '',
ADD COLUMN province TEXT NOT NULL DEFAULT '';

CREATE INDEX ON customer (veeva_reference_id);
CREATE INDEX ON customer (is_active);
CREATE INDEX ON customer (city);
CREATE INDEX ON customer (organization) WHERE is_active=true;
CREATE INDEX ON customer ((lower(province)));
CREATE INDEX ON customer (territory_id);

-- TABLE USER

ALTER TABLE "user"
ADD COLUMN veeva_territory_id TEXT NOT NULL DEFAULT '';

CREATE INDEX ON "user"(veeva_territory_id);