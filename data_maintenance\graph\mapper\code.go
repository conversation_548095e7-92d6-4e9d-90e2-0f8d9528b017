package mapper

import (
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
)

func FetchCodeValuesEntityToModel(input []entity.FetchCodeValues) []*model.CodeData {
	var outEntity []*model.CodeData
	for _, item := range input {
		activity := new(model.CodeData)
		activity.ID = item.ID
		activity.Title = item.Title
		activity.Value = item.Value
		outEntity = append(outEntity, activity)
	}
	return outEntity
}
