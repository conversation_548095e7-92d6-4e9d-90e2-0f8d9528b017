package entity

import (
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/upload/graph/model"
	uuid "github.com/satori/go.uuid"
)

type MaterialExcelInput struct {
	ID               *uuid.UUID
	GroupCode        string
	GroupName        string
	CountryName      string
	CountryNo        int
	DivisionName     string
	VeevareferenceId string
	IsActive         bool
	IsDeleted        bool
	ClientName       string
}

type MaterialExcel struct {
	ID        *uuid.UUID
	Country   int
	Division  string
	GroupCode string
	GroupName string
	IsActive  bool
}

type ExportMaterialExcel struct {
	IsExcel   bool       `json:"isExcel"`
	ID        *uuid.UUID `json:"id"`
	Country   int        `json:"country"`
	Division  string     `json:"division"`
	GroupCode string     `json:"groupCode"`
	GroupName string     `json:"groupName"`
	Status    string     `json:"status"`
}

type MaterialInstructionExcel struct {
	Column    string
	Mandatory string
	Limit     string
}

func (o *MaterialExcelInput) ValidateMaterialExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(o.ID) != uuid.Nil && o.CountryName == "" && o.GroupCode == "" && o.GroupName == "" {
		o.IsDeleted = false
		o.IsActive = false
	} else {
		codes := codeController.GetTitleKeyCodes()["country"]
		_, countryExists := codes[o.CountryName]
		if !countryExists {
			errorMessage := &model.ExcelValidationMessage{Row: (row), Message: " Country does not exist"}
			validationMessages = append(validationMessages, errorMessage)
		}
		// if o.DivisionName == "" {
		// 	errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": Division Name is blank"}
		// 	validationMessages = append(validationMessages, errorMessage)
		// }
		if o.GroupCode == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Group Code is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.GroupName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " GroupName is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
	}
}
