

input TeamInput {
    id: String
    isDelete: Boolean
    teamName: String!
    TeamCountry: String!
}


type validationMessage {
  message: String!
}

type UpsertTeamResponse {
  error: Boolean!
  message: String!
  validationErrors: [validationMessage]
}

type teamData{
     id: String!
    teamName: String!
}

type teamResponse {
    error: Boolean!
    message: String!
    data: [teamData]!
}


extend type Mutation {
    upsertTeam(input: TeamInput!): UpsertTeamResponse!
}

extend type Query {
    getTeamList: teamResponse!
}