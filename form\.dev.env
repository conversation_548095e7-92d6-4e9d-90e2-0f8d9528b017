ENVIRONMENT:staging
PORT: 32000
IHCP_HOST: ***********
IHCP_PORT: "5432"
IHCP_USER: "ezflowdevadmin"
IHCP_PASSWORD: "vDk9v82y!dF9"
IHCP_DB_NAME: "ezflow_dev"
IHCP_SSLMODE: "disable"
IHCP_SEARCH_PATH: "ezflow_uat"
IHCP_MAX_CONN: "5"
IHCP_MAX_LIFETIME: "5"
IHCP_MAX_IDLE_TIME: "5"
IHCP_HEALTHCHECK_PREIOD: "2"

AZURE_BLOB_KEY: "bKR7iUW9ZtW0l4LPEqUweIOfEQEqzgU0eY3qMqfTH5xUSn1T8bbIgZ48bE0p5XOEzLDrFyzbKa8b+AStcptAAA=="
AZURE_BLOB_ACCOUNT_NAME: "ezflowlatest"
AZURE_BLOB_UPLOAD_PATH:"ezflow/development"
AZURE_ATLAS_URL:"https://atlas.microsoft.com/search/fuzzy/batch/sync/json?api-version=1.0&subscription-key="
AZURE_ATLAS_BULK_URL:"https://atlas.microsoft.com/search/fuzzy/batch/json?api-version=1.0&subscription-key="
AZURE_ATLAS_SUBSCRIPTION_KEY:"rTk9EzxRjAeoo8kmclvUZV7FaXbtdPHtTRk8QJeMkXE"
APPLICATION_INSIGHTS_KEY: 0176352e-ee1a-4ee8-9b97-d640ae50f260
POOL_CONNECTION_INTERVAL: 15
MAX_RETRY : 10
SITE_URL : "https://ezflow-webapp-staging.azurewebsites.net/"
GRANT_TYPE : password
CLIENT_ID : 3MVG95mg0lk4batg2PjOzMX51HAHOOjAEJVWsQRJjQISswsEgYDfyxOJVSNCu9HVfHgsKtmvGpzyKwIOp86wU
CLIENT_SECRET : ****************************************************************
USERNAME_VEEVA : <EMAIL>
PASSWORD : ZuelligSBX202506!
VEEVA_AUTH_URL : "https://test.salesforce.com/services/oauth2/token"
VEEVA_UPSERT_URL: "https://zpt--full.my.salesforce.com/services/data/v54.0/sobjects/Medical_Event_vod__c"
VEEVA_BASE_URL:"https://zpt--full.sandbox.my.salesforce.com"
PR_CREATION_ARIBA_URL="https://zpgt.apimanagement.ap1.hana.ondemand.com/Ariba/RequisitionImportPull"
CONTACT_API_URL:"https://qa-service.chromeriver.com/v1/contacts"
EZCLAIM_EVENT_LOGS_UPSERT_URL:"https://qa-service.chromeriver.com/v1/entities"
EZCLAIM_EMAIL_ADDRESS:"<EMAIL>"
VEEVA_EMAIL_ALEART_TO_MAIL:"<EMAIL>"
VEEVA_EMAIL_ALEART_CC_MAIL:"<EMAIL>,<EMAIL>,<EMAIL>"
SUPPLIER_CODE_UPDATE_URL:"https://zpgt.apimanagement.ap1.hana.ondemand.com/ehq/ZFI_eZFlow_SRV"
INTEGRATION_TYPE_FOR_ARIBA:"ARIBA"
INTEGRATION_TYPE_FOR_EZCLAIM_EVENT:"EZCLAIMEVENT"
INTEGRATION_TYPE_FOR_EZCLAIM_EMAIL:"EZCLAIMEMAIL"
INTEGRATION_TYPE_FOR_CONTACT_CHROME_RIVER:"EZCLAIMCONTACT"
FORM_ANSWER_ATTACHMENTS_TYPE_DESCRIPTION_FOR_PAYMENT_RECEIPTS:"Receipts"
FORM_ANSWER_ATTACHMENTS_CATEGORY_DESCRIPTION_FOR_PAYMENT_RECEIPTS:"Payment Receipt"
FORM_ANSWER_ATTACHMENTS_CATEGORY_DESCRIPTION_FOR_PROOF_OF_ATTENDANCE:"Certification/proof of Attendance"
FORM_ANSWER_ATTACHMENTS_TYPE_DESCRIPTION_FOR_PROOF_OF_ATTENDANCE:"Proof of Attendance"
FORM_ANSWER_ATTACHMENTS_TYPE_DESCRIPTION_FOR_PROOF_OF_DELIVERY:"Proof of Delivery"
FORM_ANSWER_ATTACHMENTS_CATEGORY_DESCRIPTION_FOR_SERVICE:"Service Agreement"
ARIBA_PR_CREATION_COOKIE:"JTENANTSESSIONID_c9310796d=FFBHgwWczjzjVIHXfkRsvOSRJinjustzdllGEFa%2BA0A%3D; BIGipServerl6088iflmapavshcip.factoryap1.customdomain=!Ek0a14CQOUvFVCuwdlYePQduQOh7gCrvN0bRpd0B2TnNrSM/M+1DmqfacHdF3iPmSZQjO9pnsZX/YOc="
CONTENT_TYPE_ARIBA_PR_CREATION:"application/json"
API_KEY_ARIBA_PR_CREATION:"s6QmU1j7x6KHSVG6jMjQRxmz8w8G4eii"
X_API_KEY_EZCLAIM_EVENT_AND_CONTACT: "63419e27-a6b4-4690-baab-8a53f38a29ab"
CONTENT_TYPE_EZCLAIM_EVENT_AND_CONTACT: "application/json"
CUSTOMER_CODE_EZCLAIM_EVENT_AND_CONTACT: "I7FJ"
  VEEVA_FETCH_CREATED_DATA_DAYS:"TODAY"
  VEEVA_FETCH_MODIFIED_DATA_DAYS:"TODAY"
CHAIN_ID_EZCLAIM_EVENT: "ezflow2d-f2e6-46ca-a04a-35d3d873712d"
CHAIN_ID_CONTACT:"ezflowf3-bb81-4514-a6d4-afba340825cd"
VEEVA_FETCH_CREATED_DATA_FOR_USER_DAYS:"last_n_days:1"
VEEVA_FETCH_MODIFIED_DATA_FOR_USER_DAYS:"last_n_days:1"
VEEVA_FETCH_CREATED_DATA_FOR_PRODUCT_DAYS:"last_n_days:1"
VEEVA_FETCH_MODIFIED_DATA_FOR_PRODUCT_DAYS:"last_n_days:1"
VIRTUAL_EVENT_DETAILS_MAX_LIMIT:"500"
DURATION_MAX_LENGTH_LIMIT:"4"
MAXIMUM_WORKING_HOURS:"6"
ADMIN:"admin"
CCO:"clustercomplianceofficer"
FINANCE:"finance"
SUPERADMIN:"superadmin"
VEEVA_ALL_EVENT_PUSH_DAY_INTERVAL : 10
VEEVA_ALL_EVENT_ATTENDANCE_DAY_INTERVAL : 10
AUTH_VEEVA_TOKEN: X
AUTH_VEEVA_URL: V
EMAIL_ADDR: 10.5.64.9
SENDGRID_API_KEY: *********************************************************************

TEAMS_WEBHOOK_URL: https://zpssgpatientsolutions.webhook.office.com/webhookb2/b0a225ba-9691-4105-95be-08458b4305b5@19cff0af-7bfb-4dfc-8fdc-ecd1a242439b/IncomingWebhook/6c1755d1c9844b829462eef07f859ead/c5d73a02-21a3-4528-b1cf-0fb10059884e
TEAMS_WEBHOOK_URL_FOR_DATA_PATCHING: https://zpssgpatientsolutions.webhook.office.com/webhookb2/b0a225ba-9691-4105-95be-08458b4305b5@19cff0af-7bfb-4dfc-8fdc-ecd1a242439b/IncomingWebhook/0e9245f99de341d697d6204dcf03764c/c5d73a02-21a3-4528-b1cf-0fb10059884e

MAPTOOL_AUTH_TOKEN: ce6d620d4c5bf75e60465b0f1e477c9c1382ce063675b3f2bb10472c2887b072
MAPTOOL_API_URL: http://10.10.20.177:8888
MAPTOOL_USERNAME: ezflow_usr_331143f37816e
MAPTOOL_PASSWORD: 0QBeuVge4jvjq5QOygUUXPgCVdUkg2pF

