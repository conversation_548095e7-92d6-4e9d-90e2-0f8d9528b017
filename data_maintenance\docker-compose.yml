version: "3"
services:
  data_maintenance:
    container_name: ihcp_data-maintenance_service
    build:
      context: ./
      dockerfile: build-Dockerfile
    ports:
      - "31000:31000"
    restart: unless-stopped
    environment:
      TZ: Asia/Singapore
      appname: ihcp_data_maintenance
      PORT: 31000
      IHCP_HOST: ***********
      IHCP_PORT: "5432"
      IHCP_USER: "ezflowdevadmin"
      IHCP_PASSWORD: "vDk9v82y!dF9"
      IHCP_DB_NAME: "ezflow_dev"
      IHCP_SSLMODE: "disable"
      IHCP_MAX_CONN: "5"
      IHCP_MAX_LIFETIME: "5"
      IHCP_MAX_IDLE_TIME: "5"
      IHCP_HEALTHCHECK_PREIOD: "2"
      AZURE_BLOB_KEY: "bKR7iUW9ZtW0l4LPEqUweIOfEQEqzgU0eY3qMqfTH5xUSn1T8bbIgZ48bE0p5XOEzLDrFyzbKa8b+AStcptAAA=="
      AZURE_BLOB_ACCOUNT_NAME: "ezflowlatest"
      AZURE_BLOB_UPLOAD_PATH: "ezflow/development"
      IHCP_AD_HOST: **********
      IHCP_AD_PORT: 8080
      CURRENCY_MAX_ATTEMPT: "5"
      APPLICATION_INSIGHTS_KEY: 0176352e-ee1a-4ee8-9b97-d640ae50f260