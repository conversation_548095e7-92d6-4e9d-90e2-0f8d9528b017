package veeva_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"io"
	"log"
	"net/http"
	"strings"
)

type User struct {
	Email string `json:"Email"`
}

type VeevaUserTerritory struct {
	*User `json:"User"`

	VeevaReferenceId string `json:"UserId"`
	VeevaTerritoryId string `json:"Territory2Id"`
	LastModifiedDate string `json:"LastModifiedDate"`
}

//func (s *Controller) RequestUserTerritoryDataFromVeeva(ctx context.Context,
//	userEmailList []string) ([]VeevaUserTerritory, error) {
//	if err := s.authenticate(); err != nil {
//		return nil, err
//	}
//
//	var condition string
//	for i, userEmail := range userEmailList {
//		if i != 0 {
//			condition += "+OR+"
//		}
//		condition += fmt.Sprintf("User.Email+=+'%s'", userEmail)
//	}
//
//	type VeevaResponse struct {
//		TotalSize      int              `json:"totalSize"`
//		Done           bool             `json:"done"`
//		NextRecordsURL string           `json:"nextRecordsUrl"`
//		Records        []VeevaTerritory `json:"records"`
//	}
//
//	veevaInstanceURL := s.auth.InstanceURL
//	bearer := "Bearer " + s.auth.AccessToken
//	url := veevaInstanceURL +
//		fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+UserId+,+Territory2Id+FROM+Territory2Association+WHERE+Territory2Id+IN+('0MI5h000000xKmYGAU','0MI5h000000xKmHGAU','0MI5h000000xKmyGAE','0MI5h000000xKmzGAE','0MI5h000000xKn0GAE','0MI5h000000xKmcGAE','0MI5h000000xKmdGAE','0MI5h000000xKn3GAE','0MI5h000000DAkaGAG','0MI5h000000DAkfGAG','0MI5h000000DAkkGAG')`)
//
//	//fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+ObjectId+FROM+ObjectTerritory2Association+WHERE+Territory2Id+IN+('0MI5h000000xKmYGAU','0MI5h000000xKmHGAU','0MI5h000000xKmyGAE','0MI5h000000xKmzGAE','0MI5h000000xKn0GAE','0MI5h000000xKmcGAE','0MI5h000000xKmdGAE','0MI5h000000xKn3GAE','0MI5h000000DAkaGAG','0MI5h000000DAkfGAG','0MI5h000000DAkkGAG')`)
//	//0MI5h000000gSQSGA2
//	fmt.Println(url)
//	var totalResponse VeevaResponse
//
//	f := func(url string, bearer string) VeevaResponse {
//		var body []byte
//		request, err := http.NewRequest("GET", url, bytes.NewBuffer(body))
//		if err != nil {
//			panic(err)
//		}
//
//		request.Header.Add("Authorization", bearer)
//		request.Header.Set("Content-Type", "application/json")
//
//		client := &http.Client{
//			Transport: &http.Transport{
//				TLSClientConfig: &tls.Config{
//					InsecureSkipVerify: true,
//				},
//			},
//		}
//
//		response, err := client.Do(request)
//		if err != nil {
//			panic(err)
//		}
//		defer response.Body.Close()
//
//		responseBody, err := io.ReadAll(response.Body)
//		if err != nil {
//			panic(err)
//		}
//
//		fmt.Println(string(responseBody))
//		var res VeevaResponse
//		err = json.Unmarshal(responseBody, &res)
//		if err != nil {
//			log.Println(string(responseBody))
//			panic(err)
//		}
//
//		return res
//	}
//	res := f(url, bearer)
//	totalResponse = res
//	for totalResponse.NextRecordsURL != "" {
//		res = f(veevaInstanceURL+totalResponse.NextRecordsURL, bearer)
//		totalResponse.Records = append(totalResponse.Records, res.Records...)
//		totalResponse.NextRecordsURL = res.NextRecordsURL
//	}
//
//	return nil, nil
//}

func (s *Controller) RequestUserDataFromVeeva(ctx context.Context,
	userEmailList []string) ([]VeevaUserTerritory, error) {
	if err := s.authenticate(); err != nil {
		return nil, err
	}

	var condition string
	for i, userEmail := range userEmailList {
		userEmail := strings.ReplaceAll(userEmail, ` `, ``)
		userEmail = strings.ReplaceAll(userEmail, `\r\n`, ``)
		if len(userEmail) == 0 {
			continue
		}
		if i != 0 {
			condition += "+OR+"
		}

		condition += fmt.Sprintf("User.Email+=+'%s'", strings.ReplaceAll(userEmail, ` `, ``))
		fmt.Println(userEmail)
	}

	type VeevaResponse struct {
		TotalSize      int                  `json:"totalSize"`
		Done           bool                 `json:"done"`
		NextRecordsURL string               `json:"nextRecordsUrl"`
		Records        []VeevaUserTerritory `json:"records"`
	}

	veevaInstanceURL := s.auth.InstanceURL
	bearer := "Bearer " + s.auth.AccessToken
	url := veevaInstanceURL + fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+Territory2Id+,+UserId+,+User.Email+,+LastModifiedDate+FROM+UserTerritory2Association+WHERE+%s`, condition)
	fmt.Println(url)

	var totalResponse VeevaResponse

	f := func(url string, bearer string) VeevaResponse {
		var body []byte
		request, err := http.NewRequest("GET", url, bytes.NewBuffer(body))
		if err != nil {
			panic(err)
		}

		request.Header.Add("Authorization", bearer)
		request.Header.Set("Content-Type", "application/json")

		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}

		response, err := client.Do(request)
		if err != nil {
			panic(err)
		}
		defer response.Body.Close()

		responseBody, err := io.ReadAll(response.Body)
		if err != nil {
			panic(err)
		}

		var res VeevaResponse
		err = json.Unmarshal(responseBody, &res)
		if err != nil {
			log.Println(string(responseBody))
			panic(err)
		}

		return res
	}
	res := f(url, bearer)
	totalResponse = res
	for totalResponse.NextRecordsURL != "" {
		res = f(veevaInstanceURL+totalResponse.NextRecordsURL, bearer)
		totalResponse.Records = append(totalResponse.Records, res.Records...)
		totalResponse.NextRecordsURL = res.NextRecordsURL
	}

	return totalResponse.Records, nil
}

func (s *Controller) SaveUserTerritoryData(ctx context.Context, db *pgxpool.Pool, data []*VeevaUserTerritory) {
	if len(data) == 0 {
		return
	}

	batch := &pgx.Batch{}
	for _, d := range data {
		fmt.Println(d.VeevaTerritoryId, d.Email, "-----------")
		d := d
		query := `
		UPDATE "user"
		SET
			veeva_territory_id = $2
		WHERE
			lower(email) = $1
		`
		batch.Queue(
			query,
			strings.ToLower(d.Email), d.VeevaTerritoryId,
		)
	}

	results := db.SendBatch(ctx, batch)
	defer func() {
		err := results.Close()
		if err != nil {
			panic(err)
		}
	}()

	_, err := results.Exec()
	if err != nil {
		panic(err)
	}
}
