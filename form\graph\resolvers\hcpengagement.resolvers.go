package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
)

// HcpEngagementSponsoredExcel is the resolver for the hcpEngagementSponsoredExcel field.
func (r *queryResolver) HcpEngagementSponsoredExcel(ctx context.Context, input model.ExcelUploadForSponsoredHcpEngagementRequest) (*model.ExcelUploadForSponsoredHcpEngagementResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "HcpEngagementSponsoredExcel", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/HcpEngagementSponsoredExcel :" + string(inputJson))
	Response := controller.SponsoredHcpEngagementTemplate(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("HcpEngagementSponsoredExcel", "form/HcpEngagementSponsoredExcel", time.Since(start), "200")
	return Response, nil
}

// SearchHcp is the resolver for the searchHCP field.
func (r *queryResolver) SearchHcp(ctx context.Context, input model.SearchHcp) (*model.SearchHCPResponse, error) {
	rs, err := controller.SearchHCP(ctx, input)
	if err != nil {
		return &model.SearchHCPResponse{
			Error:   true,
			Message: err.Error(),
		}, nil
	}

	return &model.SearchHCPResponse{
		Error:   false,
		Message: `success`,
		Data:    rs,
		Total:   len(rs),
	}, nil
}
