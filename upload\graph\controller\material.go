package controller

import (
	"context"
	"log"

	"github.com/ihcp/login/auth"
	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/mapper"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/postgres"
	"github.com/ihcp/upload/graph/util"
	uuid "github.com/satori/go.uuid"
)

func processMaterialExcel(input *model.ExcelUploadRequest, ctx *context.Context, excelBytes []byte) {
	functionName := "processMaterialExcel()"
	userID := auth.GetUserID(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", functionName, err.Error())
		}
	}

	sheetName := util.GetSheetName(excelBytes)
	sheet, _ := util.ParseExcel(excelBytes, sheetName, 7)
	var validationResult *model.ValidationResult
	var entities []*entity.MaterialExcelInput
	if sheet != nil {

		uploadId := postgres.InsertUploadLogs(input, sheetName, userUUID)
		entities, validationResult = mapper.MapMaterialExcelToEntities(sheet, userUUID)
		upload, err := uuid.FromString(uploadId)
		var uploadUUID *uuid.UUID
		if err != nil {
			log.Println("Failed to parse upload UUID!")
		} else {
			uploadUUID = &upload
		}
		if !validationResult.Error {
			postgres.UpsertExcelMaterial(entities, uploadUUID, userUUID)
			postgres.UpdateUploadStatus(uploadUUID, "Write Success", userUUID)
		} else {
			postgres.InsertUploadValidationErrors(input, validationResult, uploadUUID, userUUID)
		}

	} else {
		validationResult = &model.ValidationResult{Error: true}
		validationResult.ExcelValidationMessages = []*model.ExcelValidationMessage{}
		errorMessage := &model.ExcelValidationMessage{Row: 0, Message: "Excel format is wrong!"}
		validationResult.ExcelValidationMessages = append(validationResult.ExcelValidationMessages, errorMessage)
	}
}
