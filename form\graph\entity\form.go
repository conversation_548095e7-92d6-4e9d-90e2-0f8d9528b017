package entity

import (
	"database/sql"
	"github.com/jackc/pgtype"
	uuid "github.com/satori/go.uuid"
)

type FormEntity struct {
	ID     *uuid.UUID
	Design pgtype.JSONB
}

type FormAnswerEntity struct {
	Design      pgtype.JSONB
	Attachments []FormAnswerAttachmentEntity
}

type FormSubmissionRequest struct {
	FormAnsId             string
	StartDate             string
	EndDate               string
	Duration              string
	IsExceptionalApprover bool
}

type StatusChangeNotificationEntity struct {
	NotificationID string
}

type FormSubmissionChanges struct {
	FormAnsId string
	StartDate string
	EndDate   string
	Duration  string
}

type FormAnswerForCancel struct {
	status int
}

type FormAnswerAttachmentEntity struct {
	Value      string
	Categories []FormAnswerAttachmentCategoryEntity
}
type FormAnswerAttachmentCategoryEntity struct {
	URL      []string
	Category string
}
type FetchApolloApprovalTrailData struct {
	ApprovalRole            *string
	ActionedBy              sql.NullString
	ActionedByRole          sql.NullString
	IValue                  sql.NullString
	Status                  sql.NullString
	ISequenceNum            sql.NullInt32
	DateCreated             sql.NullTime
	Comment                 sql.NullString
	ApproverSeq             *int
	ApproverActiveDirectory sql.NullString
	GroupID                 *int
}
type Approvers struct {
	UserID       string
	RoleID       string
	GroupID      int
	SequenceNo   int
	StatusID     int
	DepartmentID string
	SetNumber    int
}
type AnswerEntity struct {
	CreatedBy    *uuid.UUID
	FormId       *uuid.UUID
	Answer       pgtype.JSONB
	FormAnswerID *uuid.UUID
	IsDraft      bool
	Country      int
	Currency     int
	TotalCost    float64
}
type AnswerEntityForlog struct {
	CreatedBy *uuid.UUID
	Answer    pgtype.JSONB
}

type FmvAuditlogsEntity struct {
	Description sql.NullString
	MaxLimit    sql.NullString
	Date        sql.NullString
	Currency    sql.NullString
	Status      sql.NullString
	CreatedBy   sql.NullString
}

type CountryCurrency struct {
	Country  int
	Currency int
}

type ApproverGroupSequenceRole struct {
	GroupID      int
	UserRoleID   string
	SequenceNo   int
	DepartmentID string
	SetNumber    int
}
type SpeakerDetailsForApollo struct {
	SpeakerId  string
	QueryValue int
}
type ApproversEntity struct {
	CreatedBy               *uuid.UUID
	RegionalApproverPresent bool
	LocalApproverPresent    bool
	ApproverRoles           []Approvers
	IsChangeRequest         bool
}
type ExcelAuditTrailData struct {
	MeetingId    sql.NullString
	ApproverName sql.NullString
	UserRole     sql.NullString
	DateCreated  sql.NullString
	Comment      sql.NullString
	Action       sql.NullString
}
type Dropdown struct {
	Value       string
	Description string
}

type ApproverListByType struct {
	ApproverId     string
	DepartmentID   string
	UserRoleId     string
	SequenceNo     int
	DepartmentName string
	UserName       string
}

type DropdownIDAndDescription struct {
	Description string
	ID          int
}

type SortItems struct {
	Order string
}

type FormAnswer struct {
	ID                       sql.NullString
	CompletionDate           sql.NullTime
	RequestorName            sql.NullString
	RequestorActiveDirectory sql.NullString
	CreatedDate              sql.NullTime
	Status                   sql.NullString
	StatusValue              sql.NullString
	TotalCost                sql.NullString
	StartDate                sql.NullString
	EndDate                  sql.NullString
	Team                     sql.NullString
	ActivityName             sql.NullString
	ProductName              sql.NullString
	ActivityTypes            sql.NullString
	EventTypes               sql.NullString
	EventDate                sql.NullString
	EventSequence            sql.NullString
	RequestorID              sql.NullString
	CountryTitle             sql.NullString
	CountryValue             sql.NullString
	CurrencyTitle            sql.NullString
	CurrencyValue            sql.NullString
	EventCode                sql.NullString
	EventSeq                 int
	StatusCheck              int
	Venue                    sql.NullString
	SingleDataArray          *[]string
	GovtNonGovtHcp           *[]string
	EventOrganizer           sql.NullString
	SpeakerName              *[]string
	Specialty                *[]string
	InstituteName            *[]string
	EventExpenses            *[]string
	ExpenseCost              *[]string
	HcpCostList              *[]string
	HcpTypeOfExpense         *[]string
	EventOwner               sql.NullString
	IsOwner                  *bool
	IsExceptionalApprover    *bool
	ProductOwner             sql.NullString
	RequestingHco            sql.NullString
	Recipient                sql.NullString
	ApproveAccessTime        sql.NullInt64
	ApproveCancelTime        sql.NullInt64
	MeetingMode              sql.NullString
	VirtualEventDetails      sql.NullString
	BoardOfDirectory         pgtype.JSONB
	LevelOfInfluence         pgtype.JSONB
	TypeOfEngagements        *[]string
	IsApollo                 bool
}

type RecallSubmissionFormApprovalEntity struct {
	ApprovedBy            uuid.UUID
	FormAnswerID          uuid.UUID
	Action                int
	ActionValue           string
	ActionTitle           string
	ApprovalRole          string
	ApprovedStatus        int
	RequestorID           uuid.UUID
	GroupStatus           int
	IsExceptionalApprover bool
	IsRecall              bool
	IsCompliance          bool
	RecallSummary         string
}
type FormAnswerForAdmins struct {
	ID                       sql.NullString
	CompletionTime           sql.NullTime
	RequestorName            sql.NullString
	RequestorActiveDirectory sql.NullString
	CreatedDate              sql.NullTime
	EventCode                sql.NullString
	EventSeq                 int
	Status                   sql.NullString
	StatusValue              sql.NullString
	CountryTitle             sql.NullString
	CountryValue             sql.NullString
	CurrencyTitle            sql.NullString
	CurrencyValue            sql.NullString
	SingleDataArray          *[]string
	TotalCost                sql.NullString
	GovtNonGovtHcp           *[]string
	EventExpenses            *[]string
	ExpenseCost              *[]string
	StatusCheck              int
	EventTypes               *[]string
	HcpCostList              *[]string
	HcpTypeOfExpense         *[]string
	Product                  sql.NullString
	ProductOwner             sql.NullString
	Team                     sql.NullString
	IsException              *bool
	ActivityName             sql.NullString
	UserID                   sql.NullString
	RequestingHco            sql.NullString
	Recipient                sql.NullString
	MeetingMode              sql.NullString
	VirtualEventDetails      sql.NullString
	BoardOfDirectory         pgtype.JSONB
	LevelOfInfluence         pgtype.JSONB
	TypeOfEngagements        *[]string
}

type RequestorFormAnswerListExcel struct {
	RowNo int
	Data  []interface{}
}

type RequestorResponse struct {
	ID          sql.NullString
	Name        sql.NullString
	Comment     sql.NullString
	Status      sql.NullString
	StatusValue sql.NullString
	Date        sql.NullTime
}

type ListNotificationStatusEntity struct {
	ID           *uuid.UUID
	FormAnswerID *uuid.UUID
	HasRead      bool
	Status       int
	EventID      sql.NullString
	ActionedBy   *uuid.UUID
	DateCreated  sql.NullTime
}

type ListPendingApprovalEntity struct {
	ID                    sql.NullString
	IsExceptionalApprover *bool
	ActivityType          sql.NullString
	EventType             sql.NullString
	Answer                pgtype.JSONB
	SubmissionDate        sql.NullTime
	Name                  sql.NullString
	Status                sql.NullString
	StatusValue           sql.NullString
	TotalCost             sql.NullString
	EventID               string
	IsActionable          bool
	FormAnswerID          sql.NullString
	SequenceNo            int
	GroupType             sql.NullString
	StartDate             sql.NullString
	EndDate               sql.NullString
	Team                  sql.NullString
	ActivityName          sql.NullString
	ProductName           sql.NullString
	CountryTitle          string
	CountryValue          string
	CurrencyTitle         string
	CurrencyValue         string
	EventCode             sql.NullString
	EventSeq              int
	FormStatus            int
	ChangeRequestSummary  sql.NullString
	RecallAccessTime      sql.NullInt64
	RecallCancelTime      sql.NullInt64
	IsRecall              sql.NullBool
}
type SubmissionFormApprovalEntity struct {
	ApprovedBy            uuid.UUID
	FormAnswerID          uuid.UUID
	Action                int
	ActionValue           string
	ActionTitle           string
	Comment               string
	ApprovalRole          string
	ApprovedStatus        int
	RequestorID           uuid.UUID
	GroupStatus           int
	IsExceptionalApprover bool
	IsCompliance          bool
}

type FormAttachment struct {
	FormAnswerID uuid.UUID
	Attachments  []string
}

type FormAttachmentInput struct {
	FormAnswerId uuid.UUID
	Type         string
	Description  string
	Url          string
}
type FormAnswerFetchEntity struct {
	FormId uuid.UUID
}

type ListPendingFormAnswerIDEntity struct {
	ID           sql.NullString
	FormAnswerID sql.NullString
	ApproverID   sql.NullString
	RowNumber    sql.NullInt32
}

type FormAnswerIDActivityData struct {
	ActivityStartDate string
	ActivityEndDate   string
	ActivityName      string
	ActivityTeam      string
}

type ControlValidation struct {
	ControlID   *uuid.UUID
	ActivityID  *uuid.UUID
	Country     *int
	Category    string
	Description *string
	Limit       *float64
	//Enabled     bool
	Type   *string
	CodeID *int
}

type EmailStruct struct {
	ID                 string
	RequesterName      string
	TotalCost          float64
	Team               string
	ActivityName       string
	EventName          string
	EventDate          string
	EventCode          string
	EventSubmittedDate string
	SiteURL            string
	ApolloUrl          string
}

type FormAnswerEventEntity struct {
	ID        string
	EventCode *int
}

type GetTotalEventEntity struct {
	RequestorTotalPending  sql.NullInt32
	RequestorTotalApproved sql.NullInt32
	RequestorTotalRejected sql.NullInt32
	ApproverTotalPending   sql.NullInt32
	ApproverTotalApproved  sql.NullInt32
	ApproverTotalRejected  sql.NullInt32
}

type FetchApprovalTrailEntity struct {
	FormAnswerId uuid.UUID
}
type FormAnswerID struct {
	FormAnswerId string
}

type FetchTypeApprovalListEntity struct {
	FormAnswerId uuid.UUID
	Type         string
}

type FetchApprovalTrailData struct {
	ApprovalRole   *string
	ActionedBy     sql.NullString
	ActionedByRole sql.NullString
	IValue         sql.NullString
	ISequenceNum   sql.NullInt32
	Status         sql.NullString
	DateCreated    sql.NullTime
	Comment        sql.NullString
	GroupID        *int
}

type FetchApprovalLogData struct {
	Id             sql.NullString
	FormAnswerId   sql.NullString
	EventCode      sql.NullString
	EventSeq       sql.NullInt32
	ActionedBy     sql.NullString
	ActionedByName sql.NullString
	StatusValue    sql.NullString
	StatusTitle    sql.NullString
	DateCreated    sql.NullTime
	Comment        sql.NullString
	UserRoleValue  sql.NullString
	UserRoleTitle  sql.NullString
}

type FetchApprovalLogDataForRequester struct {
	Id             sql.NullString
	FormAnswerId   sql.NullString
	EventCode      sql.NullString
	EventSeq       sql.NullInt32
	ActionedBy     sql.NullString
	ActionedByName sql.NullString
	StatusValue    sql.NullString
	StatusTitle    sql.NullString
	DateCreated    sql.NullTime
	UserRoleValue  sql.NullString
	UserRoleTitle  sql.NullString
}

type FormAnswerIdByEventIdEntity struct {
	EventCode string
	EventSeq  int
}

type FormAnswerIdByEventID struct {
	EventCode    string
	EventSeq     int
	FormAnswerID string
}

type ApprovalRoleInputEntity struct {
	TotalExpense            float64
	ConvertedTotalExpense   float64
	HasHCPEngagement        bool
	HasInternational        bool
	HasLevelOfInfluence     bool
	Activities              []int
	AuthorApprovalRoleValue string
	AuthorApprovalRoleID    string
	AuthorCountryID         int
	LedBy                   string
}

type FetchApprovalRoleByFormAnsIDModelToEntity struct {
	Name           string
	ApprovalID     string
	ApprovalValue  *string
	ApprovalTitle  *string
	GroupValue     string
	GroupTitle     string
	SequenceNo     int
	DepartmentID   *string
	DepartmentName *string
}
type ExceptionalDetailsInput struct {
	FormID                        uuid.UUID
	DetailsOfRequest              *string
	IhcpOtherLocalPolicies        *string
	MoreComments                  *string
	ExceptionalApprovalFileUpload []*string
	ScopeOfExceptionalRequest     *string
}
type ExceptionDetailsEntity struct {
	IsExceptionalApproval         *bool
	DetailsOfRequest              *string
	IhcpOtherLocalPolicies        *string
	MoreComments                  *string
	ExceptionalApprovalFileUpload []*string
	ScopeOfExceptionalRequest     *string
	IsChangeRequest               *bool
	ChangeRequestType             *string
	ChangeRequestSummary          *string
	EventCode                     string
	EventSeq                      int
	IsRecall                      *bool
	RecallSummary                 *string
	CurrencyCode                  int
}

type BasciInfoDateValidation struct {
	ActivityType sql.NullString
	EventType    sql.NullString
	TodayDate    sql.NullString
	Status       sql.NullString
	CreatedDate  sql.NullString
	StartDate    sql.NullInt64
}

type ChangeRequestInputEntity struct {
	FormAnswerID   uuid.UUID
	StartDate      string
	EndDate        string
	Duration       string
	NoOfHcp        string
	NoOfNonHCP     string
	TypeOfHCP      string
	Roles          []*string
	Venue          string
	VirtualEvent   *bool
	GovtNonGovtHcp string
	Remarks        string
	SponsoredHcp   string
	Speciality     string
	Hco            string
}
type ChangeRequestForAllEvent struct {
	FormAnswerID uuid.UUID
	Comment      string
}
type BoardOfDirectorsData struct {
	Name     string `json:"Name"`
	Position string `json:"Position"`
	Question string `json:"Question"`
	Remarks  string `json:"Remarks"`
}
type LevelOfInfluenceEntity struct {
	Upload           string `json:"Upload"`
	Remarks1         string `json:"Remarks1"`
	Remarks2         string `json:"Remarks2"`
	Question1        string `json:"Question1"`
	Question2        string `json:"Question2"`
	LevelOfInfluence string `json:"levelOfInfluence"`
}
type BasicInfoDataForPDF struct {
	Title sql.NullString
	Value sql.NullString
}
type BodAndAttendeesDataForPDF struct {
	Seq1     sql.NullInt16
	Name     sql.NullString
	Position sql.NullString
	Question sql.NullString
	Remarks  sql.NullString
}

type HcpDataForPDF struct {
	Seq1  sql.NullString
	Seq2  sql.NullString
	Title sql.NullString
	Value sql.NullString
}

type HcpExpenseDataForPDF struct {
	Sum            sql.NullString
	DataexpensePdf []DataexpensePdf
}
type DataexpensePdf struct {
	Seq1  sql.NullString
	Seq2  sql.NullString
	Title sql.NullString
	Value sql.NullString
}

type EventExpenseData struct {
	TypeOfEventExpenses    []string `json:"Type of Event Expenses,omitempty"`
	TypeOfMeetingPackage   []string `json:"Type of Meeting Package,omitempty"`
	Others                 []string `json:"Others,omitempty"`
	EventExpenseAttachment []string `json:"Event Expense Attachment,omitempty"`
	Remarks                []string `json:"Remarks,omitempty"`
	NoOfPeople             []string `json:"No of people,omitempty"`
	Cost                   []string `json:"Cost,omitempty"`
	TypeofTransport        []string `json:"Type of Transport,omitempty"`
	TotalCost              []string `json:"Total Cost,omitempty"`
	TypeofOthers           []string `json:"Type of Others,omitempty"`
}

type EventExpenseDataForPDF struct {
	Val string
}
type PostActivityAttachmentsData struct {
	TypeDesc     string
	CategoryDesc []string
	Value        [][]string
}

type ApproverPdfDataList struct {
	Status         sql.NullString
	ActionedByName sql.NullString
	ApprovalRole   sql.NullString
	LatestCreate   sql.NullString
	LastComment    sql.NullString
}

type PostActivityDataForPDF struct {
	Seq1  sql.NullString
	Seq2  sql.NullString
	Title sql.NullString
	Value sql.NullString
}
