package controller

import (
	"log"

	"github.com/ihcp/data_maintenance/graph/postgres"
)

func InsertReportingData() error {
	functionName := "InsertReportingData()"
	log.Printf("%s", functionName)
	formSubmissionList, err := postgres.GetAdminAnswerList()
	if err != nil {
		log.Println(err)
		return err
	}
	for _, adminData := range formSubmissionList {
		err = postgres.InsertReportingDB(adminData)
		if err != nil {
			log.Println(err)
			return err
		}
	}
	return nil
}
