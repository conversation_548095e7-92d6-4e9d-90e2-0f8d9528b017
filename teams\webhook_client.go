package teams

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

type WebhookClient struct {
	url         string
	serviceName string
	environment string
}

// NewWebhookClient creates new teams webhook client using config
func NewWebhookClient(cfg *WebhookConfig) *WebhookClient {
	return &WebhookClient{
		url:         cfg.URL,
		serviceName: cfg.ServiceName,
		environment: cfg.EnvName,
	}
}

// SendPanicMessage sends panic err and stack trace to the teams notification
func (c *WebhookClient) SendPanicMessage(ctx context.Context, err interface{}, stackTrace, apiName string) error {
	msgTitle := fmt.Sprintf("[%s] %s/%s", strings.ToUpper(c.environment), c.serviceName, apiName)
	activityTitle := fmt.Sprintf("error message: %s", err)
	msg := &Message{
		title:         msgTitle,
		summary:       activityTitle,
		activityTitle: activityTitle,
		body:          stackTrace,
	}
	return c.SendMessage(ctx, msg.CreateMessageCard())
}

// SendMessage posts a message to the teams webhook url
func (c *WebhookClient) SendMessage(ctx context.Context, msg map[string]interface{}) error {
	jsonData, err := json.Marshal(msg)
	if err != nil {
		return fmt.Errorf("error marshalling teams message JSON: %v", err)
	}

	resp, err := http.Post(c.url, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("error sending teams message: %v", err)
	}
	defer resp.Body.Close()

	return nil
}
