package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// GetCodeValues is the resolver for the getCodeValues field.
func (r *queryResolver) GetCodeValues(ctx context.Context, input model.CodeInput) (*model.CodeResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetCodeValues", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetCodeValues :" + string(inputJson))
	response := controller.FetchCodeValues(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("GetCodeValues", "data_maintenance/GetCodeValues", time.Since(start), "200")
	return response, nil
}
