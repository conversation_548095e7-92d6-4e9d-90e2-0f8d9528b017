package entity

type EmployeeAttendanceExcelInput struct {
	ExternalUniqueId string `json:"externalUniqueId"`
	SystemCode       string `json:"systemCode"`

	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Name      string `json:"name"`

	FirstNameEzClaim string `json:"first_name_ezclaim"`
	LastNameEzClaim  string `json:"last_name_ezclaim"`
	Title            string `json:"title"`
	AccountName      string `json:"accountName"`
	AccountId        string `json:"accountId"`
	ContactType      string `json:"contactType"`
	Status           string `json:"status"`
	IsHcp            bool   `json:"isHcp"`
	IsSpeaker        bool   `json:"isSpeaker"`
	HCOName          string `json:"hco_name"`
}
type EmployeeAttendanceExcelForOriginalInput struct {
	ExternalUniqueId string `json:"externalUniqueId"`
	SystemCode       string `json:"systemCode"`
	FirstName        string `json:"firstName"`
	LastName         string `json:"lastName"`
	Title            string `json:"title"`
	AccountName      string `json:"accountName"`
	AccountId        string `json:"accountId"`
	ContactType      string `json:"contactType"`
	Status           string `json:"status"`
	Affiliation      string `json:"affiliation"`
	Specialty        string `json:"specialty"`
	IsHcp            bool   `json:"isHcp"`
	IsSpeaker        bool   `json:"isSpeaker"`
	FirstNameEzClaim string `json:"first_name_ezclaim"`
	LastNameEzClaim  string `json:"last_name_ezclaim"`
	HCOName          string `json:"hco_name"`
}
