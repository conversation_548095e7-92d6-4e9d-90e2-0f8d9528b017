

type Department {
  id: String!
  department: String!
}
input DepartmentInput{
    id: String
    Department: String
    isDeleted: Boolean
}

input GetDepartmentInput{
    id: String
    department: String
}

type UpsertDepartmentResponse {
  error: Boolean!
  message: String!
  validationErrors: [validationMessage]
}
type GetDepartmentResponse {
  error: Boolean!
  message: String!
  departments: [Department]
}


extend type Query {
    getDepartment(input: GetDepartmentInput): GetDepartmentResponse!
}

extend type Mutation {
    upsertDepartment(input: DepartmentInput!): UpsertDepartmentResponse!
}