package entity

import (
	"time"

	uuid "github.com/satori/go.uuid"
)

type FetchActivities struct {
	ID          *uuid.UUID
	Description string
}

type Activity struct {
	ID           *uuid.UUID
	Description  string
	Country      int
	CountryTitle *string
	IsActive     *bool
	IsDeleted    *bool
	DateCreated  *time.Time
	LastModified *time.Time
	CreatedBy    *string
	ModifiedBy   *string
	CodeId       int
}

type HcpMealEntity struct {
	MaxLimit float64
	Value    string
}
