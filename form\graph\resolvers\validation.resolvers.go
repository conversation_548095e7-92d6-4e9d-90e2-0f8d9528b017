package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
)

// ValidateHonorarium is the resolver for the validateHonorarium field.
func (r *queryResolver) ValidateHonorarium(ctx context.Context, input model.HonorariumDetails) (*model.MaxHonorariumResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "ValidateHonorarium", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/ValidateHonorarium :" + string(inputJson))
	response := controller.ValidateHonorarium(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("ValidateHonorarium", "form/ValidateHonorarium", time.Since(start), "200")
	return response, nil
}

// ValidateHcpSpeaker is the resolver for the validateHcpSpeaker field.
func (r *queryResolver) ValidateHcpSpeaker(ctx context.Context, input model.HcpSpeakerRequest) (*model.HcpSpeakerResponse, error) {
	panic(fmt.Errorf("not implemented"))
}
