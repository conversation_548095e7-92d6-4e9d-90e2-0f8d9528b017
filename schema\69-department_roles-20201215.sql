CREATE TABLE department_roles (
	id uuid primary key  NOT NULL DEFAULT public.uuid_generate_v4(),
	department uuid NOT NULL,
	userRole uuid NOT NULL,
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	created_by uuid not NULL,
	date_created timestamptz NOT NULL DEFAULT now(),
	last_modified timestamptz NULL,
	modified_by uuid NULL
);

ALTER TABLE department_roles ADD CONSTRAINT fk_department FOREIGN KEY (department) REFERENCES departments(id);
ALTER TABLE department_roles ADD CONSTRAINT fk_userRole FOREIGN KEY (userRole) REFERENCES user_roles(id);
