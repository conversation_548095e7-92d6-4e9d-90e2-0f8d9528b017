package veeva_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"strings"
)

type Product struct {
	ProductId         string
	Classification    string
	ProductName       string
	HCPId             string
	HCPName           string
	Country           string
	ZLGSuggestedBrand string
}

func (s *Controller) QueryProductByNameAndClassificationFromVeeva(ctx context.Context, productNameList, classificationList []string, country string) []Product {
	err := s.authenticate()
	if err != nil {
		panic(err)
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := s.auth.InstanceURL
	for i := range productNameList {
		productNameList[i] = strings.ReplaceAll(strings.TrimSpace(productNameList[i]), ` `, `+`)
	}

	whereConditionStr := fmt.Sprintf(`+WHERE+Account_vod__r.Primary_Country_QS__c+=+'%s'`, country)
	if len(classificationList) > 0 {
		whereConditionStr += fmt.Sprintf(`+AND+Segment__c+IN+('%s')`, strings.Join(classificationList, `','`))
	}

	if len(productNameList) > 0 {
		whereConditionStr += fmt.Sprintf(`+AND+Products_vod__r.Name+IN+('%s')`, strings.Join(productNameList, `','`))
	}
	for i := range productNameList {
		productNameList[i] = strings.ReplaceAll(strings.TrimSpace(productNameList[i]), ` `, `+`)
	}
	url := veevaInstanceURL + fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+Segment__c,+Products_vod__r.Name,+Account_vod__r.Id,+Account_vod__r.Name,+Account_vod__r.Primary_Country_QS__c,+ZLG_Suggested_Brand_Classification__c+FROM+Product_Metrics_vod__c`) + whereConditionStr
	//Primary_Parent_vod__r.Name+IN+('` + strings.Join(HCOName, `','`) + `')`
	//fmt.Println(url)
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}
	bearer := "Bearer " + s.auth.AccessToken
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	type T struct {
		TotalSize      int    `json:"totalSize"`
		Done           bool   `json:"done"`
		NextRecordsURL string `json:"nextRecordsUrl"`
		Records        []struct {
			Attributes struct {
				Type string `json:"type"`
				Url  string `json:"url"`
			} `json:"attributes"`
			Classification string `json:"Segment__c"`
			Product        struct {
				Attributes struct {
					Type string `json:"type"`
					Url  string `json:"url"`
				} `json:"attributes"`
				Name string `json:"Name"`
				Id   string `json:"Id"`
			} `json:"Products_vod__r"`
			Account struct {
				Attributes struct {
					Type string `json:"type"`
					Url  string `json:"url"`
				} `json:"attributes"`
				Name    string `json:"Name"`
				Id      string `json:"Id"`
				Country string `json:"Primary_Country_QS__c"`
			} `json:"Account_vod__r"`
			ZLGSuggestedBrandClassification string `json:"ZLG_Suggested_Brand_Classification__c"`
		} `json:"records"`
	}

	var responseUrlValues T

	err = json.Unmarshal(responseBody, &responseUrlValues)
	if err != nil {
		fmt.Println(string(responseBody))
		panic(err)
	}
	for responseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + responseUrlValues.NextRecordsURL
		var ResponseUrlNextValues T
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			panic(err)
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			panic(err)
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, err := ioutil.ReadAll(responseForNextRecord.Body)
		if err != nil {
			panic(err)
		}
		err = json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err != nil {
			panic(err)
		}
		responseUrlValues.Records = append(responseUrlValues.Records, ResponseUrlNextValues.Records...)
		responseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}

	rs := make([]Product, len(responseUrlValues.Records))
	for i, v := range responseUrlValues.Records {
		rs[i] = Product{
			Classification:    v.Classification,
			ProductName:       v.Product.Name,
			ProductId:         v.Product.Id,
			HCPId:             v.Account.Id,
			HCPName:           v.Account.Name,
			Country:           v.Account.Country,
			ZLGSuggestedBrand: v.ZLGSuggestedBrandClassification,
		}

	}
	return rs
}
