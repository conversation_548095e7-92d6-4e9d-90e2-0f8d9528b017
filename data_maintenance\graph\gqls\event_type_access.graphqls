
type UserRole{
    userRoleTitle: String!
    userRoleValue: String!
}


type EventTypeAccessPermission {
    userRoles: [UserRole]!
    eventTypeName: String!
    eventTypeValue: String!
    id: String!
    country: String
    countryValue: String
}

type EventTypeAccessPermissionResponse{
    error: Boolean!
    message: String!
    data: [EventTypeAccessPermission]!
}

input EventTypeAccessPermissionInput{
    eventType: String
}

type UpsertEventTypeAccessPermissionResponse{
    error: Boolean!
    message: String!
}

input EventTypeAccessPermissionUpsertInput{
    id: String
    isDelete: Boolean
    eventType:  String
    userRole: [String]
}

extend type Query {
    getEventTypeAccessPermission(input: EventTypeAccessPermissionInput): EventTypeAccessPermissionResponse!
}

extend type Mutation {
    upsertEventTypeAccessPermission(input: EventTypeAccessPermissionUpsertInput!): UpsertEventTypeAccessPermissionResponse!
}