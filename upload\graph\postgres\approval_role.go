package postgres

import (
	"context"
	"log"
)

func GetRoleIDByValueInUserRoles(roleValue string) string {
	functionName := "GetRoleIDByValueInUserRoles()"
	log.Println(functionName)
	var codeValue string
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select id from user_roles where value = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), queryString, roleValue).Scan(&value)
	// var codeValue string
	if err == nil {

		codeValue = value
	}
	return codeValue
}
