input userTrackingRequest{
    action:String!	
    pageName:String!	
    userIp:String!
    eventId:String
}

type userTrackingResponse{
    error: Boolean!
    message: String!
}

input fetchUserTrackingRequest{
    formId:String
    action:String
}

type fetchUserTrackingResponse{
    error: Boolean!
    message: String!
    data:[fetchUserTrackingData]!
}

type fetchUserTrackingData{
    id:String!	
    userName:String!	
    action:String!
    pageName:String!
    userIp:String!	
    accessedDatetime:String!
    eventId:String!
}

type Query{
fetchUserTrackApi(input:fetchUserTrackingRequest!):fetchUserTrackingResponse!
}

type Mutation {
userTrackingAction(input:userTrackingRequest!):userTrackingResponse!
}