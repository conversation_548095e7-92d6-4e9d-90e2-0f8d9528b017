package postgres

import (
	"context"
	"database/sql"
	"errors"
	"github.com/jackc/pgx/v4"
	"log"

	"github.com/ihcp/form/graph/logengine"
	"github.com/jackc/pgtype"
	uuid "github.com/satori/go.uuid"
)

func GetGroupIDByUserID(userID string, formAnswerID string) (int, error) {
	functionName := "GetGroupIDByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var groupID int
	query := `select group_id from approvers where approver_id = $1
	and form_answer_id = $2
	and is_active =true and is_deleted = false `
	err := pool.QueryRow(context.Background(), query, userID, formAnswerID).Scan(&groupID)
	logengine.GetTelemetryClient().TrackEvent("GetGroupIDByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return groupID, nil
}

func GetSequenceIDByUserID(userID string) (int, error) {
	functionName := "GetSequenceIDByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var sequenceID int
	query := `select c.sequence_no from "user" u
	inner join code c on u.approval_role = c.id 
	where u.id = $1 and u.is_active= true and u.is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&sequenceID)
	logengine.GetTelemetryClient().TrackEvent("GetSequenceIDByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return sequenceID, nil
}

func GetTypeIDByUserID(userID string) (string, error) {
	functionName := "GetTypeIDByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var typeID string
	query := `select c.value from "user" u
	inner join code c on u.type = c.id  
	where u.id = $1 and u.is_active= true and u.is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&typeID)
	logengine.GetTelemetryClient().TrackEvent("GetTypeIDByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return typeID, nil
}

func GetCountryByUserID(userID *string) (int, error) {
	functionName := "GetCountryByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `select "user".country from "user" where "user".id = $1 and "user".is_active= true and "user".is_deleted = false `
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetCountryByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func GetNameByUserID(userID string) (string, error) {
	functionName := "GetNameByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var name string
	query := `select CONCAT(first_name ,' ', last_name) as "name"  from "user"
	where id = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&name)
	logengine.GetTelemetryClient().TrackEvent("GetNameByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return name, nil
}

func GetCountryIDByUserID(userID string) (int, error) {
	functionName := "GetCountryIDByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `select country from "user" 
	where id = $1 and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetCountryIDByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func GetEmailIDFromFormAnswerID(formID uuid.UUID) (string, error) {
	query := `SELECT u.email FROM "user" u 
	INNER JOIN form_answers fa ON u.id = fa.created_by 
	WHERE fa.id = $1`

	row := GetPool().QueryRow(context.Background(), query, formID)
	var result string
	if err := row.Scan(&result); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", nil
		}
		panic(err)
	}

	return result, nil
}

func GetOwnersEmailIDFromFormAnswerID2(formID uuid.UUID) (string, string) {
	pool = GetPool()

	query := `select
	email,
	id
from
	"user"
where
	(case
		when (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1) = 'null'
		or (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= '' then replace(LOWER(trim(concat(RTRIM(LTRIM(first_name)), ' ', RTRIM(LTRIM(last_name))))),
		' ',
		'') = (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)
		else (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= replace(LOWER(RTRIM(LTRIM(active_directory))),
		' ',
		'')
	end )
	and country = (
	select
		country
	from
		form_answers
	where
		id = $1)
	`

	var result, resultID string
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result, &resultID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", ""
		}
		panic(err)
	}

	return result, resultID
}

func GetOwnersEmailIDFromFormAnswerID(formID uuid.UUID) (string, string, error) {
	pool = GetPool()
	ctx := context.Background()

	query := `select
	email,
	id
    from
	"user"
    where
	(case
		when (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1) = 'null'
		or (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= '' then replace(LOWER(trim(concat(RTRIM(LTRIM(first_name)), ' ', RTRIM(LTRIM(last_name))))),
		' ',
		'') = (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)
		else (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= replace(LOWER(RTRIM(LTRIM(active_directory))),
		' ',
		'')
	end )
	and country = (
	select
		country
	from
		form_answers
	where
		id = $1)
	`

	var result, resultID string
	err := pool.QueryRow(ctx, query, formID).Scan(&result, &resultID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return "", "", errors.New(`event owner's email not found`)
		}
		panic(err)
	}

	return result, resultID, nil
}

func GetEmailIDOfApproversFromFormAnswerID(formID uuid.UUID) ([]string, error) {
	functionName := "GetEmailIDOfApproversFromFormAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select u.email from "user" u 
	inner join approvers a on u.id = a.approver_id
	where a.form_answer_id = $1 and a.is_active = true`

	var result []string
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetEmailIDOfApproversFromFormAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}

	return result, nil
}

func GetEmailIDFromUserID2(userID string) string {

	pool = GetPool()

	query := `select email from "user" where id = $1`

	var result string
	err := pool.QueryRow(context.Background(), query, userID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetEmailIDFromUserID query called")
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ""
		}
		panic(err)
	}

	return result
}

func GetEmailIDFromUserID(userID string) (string, error) {
	functionName := "GetEmailIDFromUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `select email from "user" where id = $1`

	result := ""
	err := pool.QueryRow(context.Background(), query, userID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetEmailIDFromUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func GetCountryDescriptionByUserID(userID string) (string, error) {
	functionName := "GetCountryDescriptionByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryDes string
	query := `select c.description from "user" u
	inner join code c on u.country = c.id
	where u.id = $1 and 
	u.is_active = true and u.is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryDes)
	logengine.GetTelemetryClient().TrackEvent("GetCountryDescriptionByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return countryDes, nil
}

func GetUserRoleDescriptionByUserID(userID string) (string, error) {
	functionName := "GetUserRoleDescriptionByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var userRoleDes string
	query := `select c.description from "user" u
	inner join code c on u.approval_role = c.id
	where u.id = $1 and 
	u.is_active = true and u.is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&userRoleDes)
	logengine.GetTelemetryClient().TrackEvent("GetUserRoleDescriptionByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return userRoleDes, nil
}

func GetUserWeightByUserID(userID string) (float64, error) {
	functionName := "GetUserWeightByUserID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	// var speakerWeight float64
	var speakerWeight sql.NullFloat64
	query := `select speaker_weight from customer where id = $1 
	and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&speakerWeight)
	logengine.GetTelemetryClient().TrackEvent("GetUserWeightByUserID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return speakerWeight.Float64, nil
}

func GetUserCountryByID(userID string) (int, error) {
	functionName := "GetUserCountryByID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `SELECT country FROM "user" WHERE id = $1 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetUserCountryByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func GetCreatedByIDFromFormAnswerID(formID uuid.UUID) (string, pgtype.JSONB, error) {
	pool = GetPool()

	var answer pgtype.JSONB
	query := `select created_by,answers from form_answers where id = $1`

	result := ""
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result, &answer)
	if err != nil {
		return "", answer, err
	}

	return result, answer, nil
}

func GetDateCreatedByFormAnswerId(formID uuid.UUID) (sql.NullTime, sql.NullString, sql.NullString, error) {
	functionName := "GetDateCreatedByFormAnswerId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select form_answers.date_created,rd.status_title,rd.status_value  from 
	form_answers 
	inner join reporting_db rd on rd.form_answer_id =form_answers.id
	where form_answers.id = $1`

	var result sql.NullTime
	var statusTitle sql.NullString
	var statusValue sql.NullString
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result, &statusTitle, &statusValue)
	logengine.GetTelemetryClient().TrackEvent("GetDateCreatedByFormAnswerId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return result, statusTitle, statusValue, err
	}

	return result, statusTitle, statusValue, err
}

func GetIDOfApproversFromFormAnswerID(formID uuid.UUID) ([]string, error) {
	functionName := "GetIDOfApproversFromFormAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select approver_id from approvers where form_answer_id = $1 
	and is_active = true and is_deleted = false`

	var result []string
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetIDOfApproversFromFormAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}

	return result, nil
}

func GetEmailIDOfAllFinanceOfLoggedinUserCountry(countryID int) ([]string, error) {
	functionName := "GetEmailIDOfAllFinanceOfLoggedinUserCountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	// code := codeController.GetValueKeyCodes()["userrole"]
	// financeID := code["finance"].ID
	financeID := GetRoleIDByValueInUserRoles("finance")
	query := `select email from "user" where user_role_id = $1 and 
	country = $2 and is_active = true and is_deleted = false`

	var result []string
	rows, err := pool.Query(context.Background(), query, financeID, countryID)
	logengine.GetTelemetryClient().TrackEvent("GetEmailIDOfAllFinanceOfLoggedinUserCountry query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}

	return result, nil
}

func GetIDOfFinanceFromLoggedinUserCountry(countryID int) ([]string, error) {
	functionName := "GetIDOfFinanceFromLoggedinUserCountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	// code := codeController.GetValueKeyCodes()["userrole"]
	// financeID := code["finance"].ID
	financeID := GetRoleIDByValueInUserRoles("finance")
	query := `select id from "user" where user_role_id = $1 and 
	country = $2 and is_active = true and is_deleted = false`

	var result []string
	rows, err := pool.Query(context.Background(), query, financeID, countryID)
	logengine.GetTelemetryClient().TrackEvent("GetIDOfFinanceFromLoggedinUserCountry query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}

	return result, nil
}
func GetOwnersEmailID(formID string) (string, string, error) {
	functionName := "GetOwnersEmailIDFromFormAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select
	email,
	id
from
	"user"
where
	(case
		when (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1) is null
		or (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= '' then replace(LOWER(trim(concat(RTRIM(LTRIM(first_name)), ' ', RTRIM(LTRIM(last_name))))),
		' ',
		'') = (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)
		else (
		select
			replace(LOWER((btrim((answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}')::text , '"'))),
			' ',
			'')
		from
			form_answers
		where
			id = $1)= replace(LOWER(RTRIM(LTRIM(active_directory))),
		' ',
		'')
	end )
	and country = (
	select
		country
	from
		form_answers
	where
		id = $1)
	`

	result := ""
	resultID := ""
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result, &resultID)
	logengine.GetTelemetryClient().TrackEvent("GetOwnersEmailIDFromFormAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", "", err
	}

	return result, resultID, nil
}
