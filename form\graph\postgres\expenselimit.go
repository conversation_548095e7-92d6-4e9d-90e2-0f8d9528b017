package postgres

import (
	"context"
	"log"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
)

func GetControlValidationByType(expenseType string, userID *string, countryID int) []*model.ValidationInput {
	functionName := "GetControlValidationByType()"
	log.Println(functionName)
	var validationArray []*model.ValidationInput
	log.Println(functionName + ": expenseType:" + expenseType)
	validations, err := getControlValidations(expenseType, userID, countryID)
	logengine.GetTelemetryClient().TrackEvent("GetControlValidationByType query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	validationArray = fetchValidationEntityToModel(validations)
	return validationArray
}

func getControlValidations(expensetype string, userID *string, countryID int) ([]entity.ControlValidation, error) {
	functionName := "GetControlValidations()"
	log.Println(functionName)
	response := make([]entity.ControlValidation, 0)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select id,activity_id,country,category,description,max_limit,type,code_id from controls WHERE type =$1 and (country = $2 or country is Null);`
	rows, err := pool.Query(context.Background(), queryString, expensetype, countryID)
	log.Printf("Expense Type:%s , Query:%s, Arguements:%s", expensetype, queryString, countryID)
	logengine.GetTelemetryClient().TrackEvent("getControlValidations query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		expense := new(entity.ControlValidation)
		err := rows.Scan(
			&expense.ControlID,
			&expense.ActivityID,
			&expense.Country,
			&expense.Category,
			&expense.Description,
			&expense.Limit,
			&expense.Type,
			&expense.CodeID,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return nil, err
		} else {
			inputResult := entity.ControlValidation{
				expense.ControlID,
				expense.ActivityID,
				expense.Country,
				expense.Category,
				expense.Description,
				expense.Limit,
				expense.Type,
				expense.CodeID,
			}
			response = append(response, inputResult)
		}
	}
	return response, nil
}

func fetchValidationEntityToModel(input []entity.ControlValidation) []*model.ValidationInput {
	outEntity := make([]*model.ValidationInput, 0)

	for _, item := range input {
		activityIds := make([]*string, 0)
		validation := new(model.ValidationInput)
		validation.ControlID = item.ControlID.String()
		if item.ActivityID != nil {
			var activityId = item.ActivityID.String()
			activityIds = append(activityIds, &activityId)
		}
		validation.ActivityID = activityIds
		validation.CodeID = item.CodeID
		validation.Country = item.Country
		validation.Category = item.Category
		validation.ComparisonOperator = ">="
		validation.Description = item.Description
		validation.Limit = item.Limit
		//validation.Enabled = item.Enabled
		validation.Type = item.Type
		outEntity = append(outEntity, validation)
	}
	return outEntity
}
