
type ActivitySelection {
     id: String!
    description: String!
}

type ActivityModel{
    id: String!
    description: String!
    isActive: Boolean!
    country: String!
}

type ActivitySelectionsResponse {
    error: Boolean!
    message: String!
    data: [ActivitySelection]!
}

type ActivityModelsResponse {
    error: Boolean!
    message: String!
    data: [ActivityModel]!
}

type UpsertResponse {
    error: Boolean!
    message: String!
}

input ActivityFilter {
    description: String
    isActive: Boolean
}

input ActivityInput {
    id: String
    description: String
    isActive: Boolean
    isDeleted: Boolean
}

input MealInput {
    mealType: String!
    mealTotalCost: Float!
    mealNo: Float!
    activityId: Int!
}

input HcpMealInput {
    input: [MealInput!]!
}

type ValidationMessage {
    message: String!
}

type ValidateHcpMealResponse {
    error: Boolean!
    ValidationMessages: [ValidationMessage]
}


extend type Query {
    getActivitySelection: ActivitySelectionsResponse!
    getActivities(input: ActivityFilter): ActivityModelsResponse!
    validateHcpMeal(input: HcpMealInput!): ValidateHcpMealResponse!
}

extend type Mutation {
    upsertActivity(input: ActivityInput!): UpsertResponse!
}
