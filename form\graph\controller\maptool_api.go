package controller

import (
	"context"
	"fmt"
	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"slices"
	"strings"
)

func ApproveEventFromMaptool(ctx context.Context, input *model.UpdateEventApprovalFromMaptoolInput) *model.CommonResponse {
	tx, err := postgres.GetPool().Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	var (
		pendingStatusCode        string = `pending`
		resubmittedStatusCode    string = `resubmitted`
		approveStatus            string = `approved`
		rejectStatus             string = `rejected`
		returnStatus             string = `return`
		exceptionalApproveStatus string = `exceptional approve`

		statusCodeAndIDMapper map[string]int = map[string]int{
			approveStatus:            58,
			rejectStatus:             59,
			returnStatus:             397,
			exceptionalApproveStatus: 443,
		}
	)

	formAnswerID, _, eventStatus := postgres.GetAnswerByMeetingID(ctx, tx, input.EventID)
	if formAnswerID == nil {
		errStr := fmt.Sprintf("event not found")
		return &model.CommonResponse{
			Success: false,
			Message: &errStr,
		}
	}

	if eventStatus != pendingStatusCode && eventStatus != resubmittedStatusCode {
		errStr := fmt.Sprintf(`can not perform action on %s event`, strings.ToUpper(eventStatus))
		return &model.CommonResponse{
			Success: false,
			Message: &errStr,
		}
	}

	if !slices.Contains([]string{approveStatus, rejectStatus, returnStatus, exceptionalApproveStatus}, input.ApprovalStatus) {
		errStr := fmt.Sprintf(`invalid approval status`)
		return &model.CommonResponse{
			Success: false,
			Message: &errStr,
		}
	}

	s := postgres.MapTool{}
	user := s.GetMaptoolUser(ctx, input.Approver)
	if user == nil {
		errStr := fmt.Sprintf(`invalid approver`)
		return &model.CommonResponse{
			Success: false,
			Message: &errStr,
		}
	}

	var reason string
	if input.Reason != nil {
		reason = *input.Reason
	}

	s.CreateApprovalLog(ctx, tx, user, formAnswerID.String(), statusCodeAndIDMapper[input.ApprovalStatus], reason)
	s.UpdateEventApprovers(ctx, tx, user, formAnswerID.String(), statusCodeAndIDMapper[input.ApprovalStatus])
	s.UpdateEventData(ctx, tx, formAnswerID.String(), statusCodeAndIDMapper[input.ApprovalStatus], input.ApprovalStatus)

	getStatusCode := func(s string) int {
		code := codeController.GetValueKeyCodes()["approvedstatus"]
		return code[s].ID
	}

	eventRequestorID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(*formAnswerID)

	emailData, err := postgres.GetFormAnswerDetails(formAnswerID.String())
	if err != nil {
		errStr := err.Error()
		return &model.CommonResponse{
			Success: false,
			Message: &errStr,
		}
	}

	if eventStatus == "pending" {
		postgres.InsertStatusNotification2(ctx, tx, formAnswerID.String(), user.ID, 57, eventRequestorID)
		data := mapper.SetEmailData(emailData)
		approverID, _ := postgres.GetNextApproverFromFromAnswerID(formAnswerID.String())
		if approverID != "" {
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			approverStatus := emailCode["requireapproval"].ID
			email, _ := postgres.GetEmailIDFromUserID(approverID)
			if email != "" {
				approverEmailContent := util.SendEmailScenarioOne(email, data)
				newApproverEmailContent := strip.StripTags(approverEmailContent)
				_ = postgres.InsertEmailLog(formAnswerID.String(), approverStatus, newApproverEmailContent, user.ID, approverID)
			}
		}
	}

	if input.ApprovalStatus == approveStatus {
		statusCode := getStatusCode("finalapproved")
		postgres.InsertStatusNotification2(ctx, tx, formAnswerID.String(), user.ID, statusCode, eventRequestorID)

	} else if input.ApprovalStatus == returnStatus {
		statusCode := getStatusCode("return")
		postgres.InsertStatusNotification2(ctx, tx, formAnswerID.String(), user.ID, statusCode, eventRequestorID)

		email, _ := postgres.GetEmailIDFromFormAnswerID(*formAnswerID)
		if email != "" {
			data := mapper.SetEmailData(emailData)
			emailContent := util.SendEmailScenarioSix(email, data)
			requestorStatus := codeController.GetValueKeyCodes()["typeofemail"]["approverreturned"].ID
			newEmailContent := strip.StripTags(emailContent)
			_ = postgres.InsertEmailLog(formAnswerID.String(), requestorStatus, newEmailContent, user.ID, eventRequestorID)

			emailOwner, ownerId, _ := postgres.GetOwnersEmailIDFromFormAnswerID(*formAnswerID)
			if emailOwner != "" && ownerId != "" {
				emailOwnerContent := util.SendEmailScenarioSix(emailOwner, data)
				newOwnerEmailContent := strip.StripTags(emailOwnerContent)
				_ = postgres.InsertEmailLog(formAnswerID.String(), requestorStatus, newOwnerEmailContent, user.ID, ownerId)
			}
		}
	}

	if err := tx.Commit(ctx); err != nil {
		panic(err)
	}

	return &model.CommonResponse{
		Success: true,
	}
}
