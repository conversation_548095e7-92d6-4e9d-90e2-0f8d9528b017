package postgres

import (
	"context"
	"github.com/jackc/pgx/v4"
	"log"
	"strconv"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func GetActivityUUIDByName(activityType string) (*uuid.UUID, error) {
	functionName := "GetActivityUUIDByName()"
	if pool == nil {
		pool = GetPool()
	}
	var activityId uuid.UUID
	queryString := `SELECT id from form where name = $1`
	err := pool.QueryRow(context.Background(), queryString, activityType).Scan(&activityId)
	logengine.GetTelemetryClient().TrackEvent("GetActivityUUIDByName query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return &uuid.Nil, err
	}

	return &activityId, nil
}

func UpsertActivity(inputEntity *entity.Activity, response *model.UpsertResponse) {
	functionName := "CreateFormTemplate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var inputArgs []interface{}
	if inputEntity.ID == nil {
		queryString = `INSERT INTO activity (description, created_by, date_created, country) VALUES ($1,$2,$3,$4)`
		inputArgs = append(inputArgs, inputEntity.Description, inputEntity.CreatedBy, inputEntity.DateCreated, inputEntity.Country)
	} else {
		inputArgs = append(inputArgs, inputEntity.ID, inputEntity.ModifiedBy, inputEntity.LastModified)
		if inputEntity.IsDeleted != nil && *inputEntity.IsDeleted {
			queryString = `UPDATE activity SET modified_by = $2, last_modified = $3, is_deleted = $4, is_active = $5 WHERE id = $1`
			inputArgs = append(inputArgs, inputEntity.IsDeleted, inputEntity.IsActive)
		} else {
			queryString = `UPDATE activity SET modified_by = $2, last_modified = $3, description = $4, is_active = $5  WHERE id = $1`
			inputArgs = append(inputArgs, inputEntity.Description, inputEntity.IsActive)
		}
	}
	commandTag, err := pool.Exec(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("UpsertActivity query called")
	if commandTag.RowsAffected() < 1 {
		response.Error = true
		response.Message = "Activity ID does not exist!"
	}
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Error = true
		response.Message = "Failed to create/update activity!"
	}
}

func GetActivities(loggedInUserCountry int, response *model.ActivityModelsResponse) []*entity.Activity {
	queryString := `SELECT activity.id, activity.description, activity.is_active, code.title FROM activity
					INNER JOIN code 
					ON code.id = activity.country
					WHERE activity.is_deleted = false AND activity.country = $1`

	activities := []*entity.Activity{}
	rows, err := pool.Query(context.Background(), queryString, loggedInUserCountry)
	logengine.GetTelemetryClient().TrackEvent("GetActivities query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Error = true
		response.Message = err.Error()
		return activities
	}

	defer rows.Close()
	for rows.Next() {
		activity := &entity.Activity{}
		err := rows.Scan(&activity.ID, &activity.Description, &activity.IsActive, &activity.CountryTitle)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Error = true
			response.Message = err.Error()
			return activities
		}
		activities = append(activities, activity)
	}

	return activities
}

func GetActivitiesByID(acivitieIDs []int) ([]*entity.Activity, error) {
	queryString := `SELECT id, value, is_active FROM code
					WHERE id IN (?)`

	var query string
	var args []interface{}
	var err error
	query, args, err = sqlx.In(queryString, acivitieIDs)
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, args...)
	logengine.GetTelemetryClient().TrackEvent("GetActivitiesByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return nil, err
	}

	defer rows.Close()

	activities := []*entity.Activity{}
	for rows.Next() {
		activity := &entity.Activity{}
		err := rows.Scan(&activity.CodeId, &activity.Description, &activity.IsActive)
		logengine.GetTelemetryClient().TrackEvent("GetActivitiesByID query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			return nil, err
		}
		activities = append(activities, activity)
	}
	return activities, err
}

func GetHcpMealLimit(country int) ([]*entity.HcpMealEntity, error) {
	queryString := `select con.max_limit,c.value from controls con
	inner join code c on  c.id = con.code_id
	where con.category = 'ExpenseLimit' and con."type" = 'Meal'
	and (con.country = $1) and activity_id IS NULL`

	var query string
	var args []interface{}
	var err error
	query, args, err = sqlx.In(queryString, country)
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, args...)
	logengine.GetTelemetryClient().TrackEvent("GetHcpMealLimit query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return nil, err
	}

	defer rows.Close()
	hcpMeals := []*entity.HcpMealEntity{}
	for rows.Next() {
		hcpmeal := &entity.HcpMealEntity{}
		err := rows.Scan(&hcpmeal.MaxLimit, &hcpmeal.Value)
		logengine.GetTelemetryClient().TrackEvent("GetHcpMealLimit query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			return nil, err
		}
		hcpMeals = append(hcpMeals, hcpmeal)
	}
	return hcpMeals, err
}
func GetHcpMealCheck(ActivityId int) bool {
	functionName := "GetHcpMealCheck()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	activityID := strconv.Itoa(ActivityId)
	var check bool
	var value int
	queryString := `select 1 from code where  category = 'ValidateMealHcpAllow' and  $1 = ANY(string_to_array(value,'|')::text[])`
	err := pool.QueryRow(context.Background(), queryString, activityID).Scan(&value)
	if err == nil {
		check = true
	}
	return check

}

func GetCodeValueByID(codeId string) *string {
	functionName := "GetCodeValueById()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `SELECT value from user_roles where id = $1 and is_deleted = false and is_active = true`
	err := pool.QueryRow(context.Background(), queryString, codeId).Scan(&value)
	var codeValue string
	if err == nil {

		codeValue = value
	}
	return &codeValue
}

func ConvertedCurrencyValue(userCountryID int, customerCountryID int, value float64, hcpType string) (float64, error) {
	functionName := "ConvertedCurrencyValue()"
	log.Printf("%s", functionName)
	var customerCountry int
	code := codeController.GetValueKeyCodes()["country"]
	if hcpType == "regional" || hcpType == "internationalothers" || hcpType == "foreign" {
		customerCountry = code["us"].ID
	} else if hcpType == "local" {
		customerCountry = customerCountryID
	}
	if customerCountry == code["phzpc"].ID {
		customerCountry = code["ph"].ID
	}
	if userCountryID == code["phzpc"].ID {
		userCountryID = code["ph"].ID
	}
	if pool == nil {
		pool = GetPool()
	}
	var rate float64
	var country int
	var targetCountry int
	queryString := `select country ,target_country ,target_rate from currency_exchange where 
	((country = $1 and target_country = $2) or (country = $2 and target_country = $1))
	order by date_created desc limit 1`
	err := pool.QueryRow(context.Background(), queryString, userCountryID, customerCountry).Scan(&country, &targetCountry, &rate)
	logengine.GetTelemetryClient().TrackEvent("ConvertedCurrencyValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return 0, err
	}
	var finalValue float64
	if userCountryID != country {
		finalValue = value * rate
	} else {
		finalValue = value / rate
	}
	return finalValue, nil
}

func GetCurrencyByCountry(codeId int) *string {
	functionName := "GetCurrencyByCountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select value from code where value LIKE '%' || left ((select value from code where id = $1),2) || '%' 
	and category = 'Currency'`
	err := pool.QueryRow(context.Background(), queryString, codeId).Scan(&value)
	var codeValue string
	if err == nil {

		codeValue = value
	}
	return &codeValue
}

func GetActivityCodeIDByActivity(activity string) (int, error) {
	functionName := "GetActivityCodeIDByActivity()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value int
	queryString := `select id from code where value = $1 and is_active = true and is_deleted = false and category in ('ActivityType','ActivityEventType')`
	err := pool.QueryRow(context.Background(), queryString, activity).Scan(&value)
	var actValue int
	if err == nil {

		actValue = value
	}
	return actValue, err
}

func GetCodeIDByTitle(title string, category string) (int, error) {
	functionName := "GetCodeIDByTitle()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value int
	queryString := `select id from code where value = $1 and is_active = true and is_deleted = false and category = $2`
	err := pool.QueryRow(context.Background(), queryString, title, category).Scan(&value)
	var idValue int
	if err == nil {

		idValue = value
	}
	return idValue, err
}

func CheckDepartmentPresent(department string) bool {
	functionName := "CheckDepartmentPresent()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from departments where id = $1 and is_active = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, department).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

// func GetDepartmentID(title string) (string, error) {
// 	log.Println("GetDepartmentID()")
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var value string
// 	queryString := `select id from departments where id = $1 and is_active = true `
// 	err := pool.QueryRow(context.Background(), queryString, title).Scan(&value)
// 	if err != nil {
// 		return "", err
// 	}
// 	return value, nil
// }

func InsertApprovalRoleManagementData(entity *entity.ApprovalRoleManagementEntity) *model.UpsertApprovalRoleManagementResponse {
	var inputArgs []interface{}
	querystring := `INSERT INTO approval_role_management (has_level_of_influence,approval_role , group_type , created_by , 
										sequence_no , min_limit , max_limit ,activity_id, country, has_condition,  has_international, department, led_by`

	if entity.AlternateDepartment != nil {
		querystring += `, alternate_department_id,alternate_group_type)
		VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9, $10,$11,$12,$13,$14, $15)`
		inputArgs = append(inputArgs, entity.HasLevelOfInfluence, 0, entity.GroupID, entity.UserID,
			entity.SequenceNo, entity.MinLimit, entity.MaxLimit, entity.ActivityCodeID, entity.CountryCode, entity.HasCondition, entity.HasInternational, entity.Department, *entity.AlternateDepartment, entity.AlternateGroupID, entity.LedByOptions)
	} else {
		querystring += `)
		VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9, $10,$11,$12, $13)`
		inputArgs = append(inputArgs, entity.HasLevelOfInfluence, 0, entity.GroupID, entity.UserID,
			entity.SequenceNo, entity.MinLimit, entity.MaxLimit, entity.ActivityCodeID, entity.CountryCode, entity.HasCondition, entity.HasInternational, entity.Department, entity.LedByOptions)
	}

	_, err := GetPool().Exec(context.Background(), querystring, inputArgs...)
	if err != nil {
		panic(err)
	}

	return &model.UpsertApprovalRoleManagementResponse{
		Error:   false,
		Message: "success",
	}
}

func DeleteApprovalRoleManagementData(entity *entity.ApprovalRoleManagementEntity) *model.UpsertApprovalRoleManagementResponse {
	ctx := context.Background()
	querystring := `
	UPDATE 
		approval_role_management
	SET is_active = false,
		is_deleted = true 
	WHERE id = $1`
	_, err := GetPool().Exec(ctx, querystring, entity.ID)
	if err != nil {
		panic(err)
	}

	return &model.UpsertApprovalRoleManagementResponse{
		Error:   false,
		Message: "success",
	}
}

func UpdateApprovalRoleManagementData(input *model.UpsertApprovalRoleManagementRequest, entity *entity.ApprovalRoleManagementEntity) *model.UpsertApprovalRoleManagementResponse {
	querystring := `UPDATE approval_role_management SET last_modified = now(),`

	var inputArgs []interface{}
	querystring += `min_limit = ? ,`
	inputArgs = append(inputArgs, entity.MinLimit)

	querystring += `max_limit = ? ,`
	inputArgs = append(inputArgs, entity.MaxLimit)

	if input.GroupType != nil {
		querystring += `group_type= ? ,`
		inputArgs = append(inputArgs, entity.GroupID)
	}
	if input.AlternateGroupType != nil {
		querystring += `alternate_group_type= ? ,`
		inputArgs = append(inputArgs, entity.AlternateGroupID)
	}
	if input.SequenceNo != nil {
		querystring += `sequence_no= ? ,`
		inputArgs = append(inputArgs, entity.SequenceNo)
	}
	if input.Activity != nil {
		querystring += `activity_id= ? ,`
		inputArgs = append(inputArgs, entity.ActivityCodeID)
	}
	if input.HasCondition != nil {
		querystring += `has_condition= ?, `
		inputArgs = append(inputArgs, entity.HasCondition)
	}
	if input.HasInternational != nil {
		querystring += `has_international= ? ,`
		inputArgs = append(inputArgs, entity.HasInternational)
	}
	if input.HasLevelOfInfluence != nil {
		querystring += `has_level_of_influence= ? ,`
		inputArgs = append(inputArgs, entity.HasLevelOfInfluence)
	}
	if input.Department != nil {
		querystring += `department= ?,`
		inputArgs = append(inputArgs, entity.Department)
	}

	if input.AlternateDepartment != nil {
		querystring += `alternate_department_id= ?,`
		inputArgs = append(inputArgs, entity.AlternateDepartment)
	}

	querystring += `led_by= ?,`
	inputArgs = append(inputArgs, entity.LedByOptions)

	querystring += `modified_by= ?, `
	inputArgs = append(inputArgs, entity.UserID)
	querystring += `country= ? `
	inputArgs = append(inputArgs, entity.CountryCode)
	querystring += `WHERE id= ? and is_active = true`
	inputArgs = append(inputArgs, entity.ID)
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	_, err := GetPool().Exec(context.Background(), querystring, inputArgs...)
	if err != nil {
		panic(err)
	}

	return &model.UpsertApprovalRoleManagementResponse{
		Error:   false,
		Message: "success",
	}
}

func InsertStatusNotification2(ctx context.Context, tx pgx.Tx, formAnswerId string, actionedBy string, status int, receiverID string) {
	_, err := tx.Exec(context.Background(), `INSERT INTO status_change_notification (form_answer, actioned_by, status, receiver) 
	VALUES($1, $2, $3, $4)`,
		formAnswerId, actionedBy, status, receiverID)
	if err != nil {
		panic(err)
	}
}

func InsertStatusNotification(formAnswerId string, actionedBy string, status int, receiverID string) error {
	functionName := "InsertStatusNotification()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	defer tx.Rollback(context.Background())

	queryString := `INSERT INTO status_change_notification (form_answer, actioned_by, status, receiver) 
	VALUES($1, $2, $3, $4)`
	_, err = tx.Exec(context.Background(), queryString, formAnswerId, actionedBy, status, receiverID)
	logengine.GetTelemetryClient().TrackEvent("InsertStatusNotification query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func InsertEmailLog2(tx pgx.Tx, formAnswerId string, Type int, emailContent string, userID string, receiverID string) {
	logengine.GetTelemetryClient().TrackEvent("InsertEmailLog query called")
	queryString := `
	INSERT INTO email_log (event_id , type_of_email , email_content , created_by, receiver) 
	VALUES($1, $2, $3, $4, $5)`
	_, err := tx.Exec(context.Background(), queryString, formAnswerId, Type, emailContent, userID, receiverID)
	if err != nil {
		panic(err)
	}
}

func InsertEmailLog(formAnswerId string, Type int, emailContent string, userID string, receiverID string) error {
	functionName := "InsertEmailLog()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	defer tx.Rollback(context.Background())

	queryString := `INSERT INTO email_log (event_id , type_of_email , email_content , created_by, receiver) 
	VALUES($1, $2, $3, $4, $5)`
	_, err = tx.Exec(context.Background(), queryString, formAnswerId, Type, emailContent, userID, receiverID)
	logengine.GetTelemetryClient().TrackEvent("InsertEmailLog query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func GetSequenceNoOfApprover(formAnswerID string, ApproverID string) (*entity.ApproverGroupSequenceRole, error) {
	functionName := "GetSequenceNoOfApprover()"
	var ApproverEntity entity.ApproverGroupSequenceRole
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select group_id , sequence_no , user_role_id , department_id, set_number from approvers 
	where form_answer_id = $1 
	and approver_id = $2 and is_active = true and is_deleted = false`

	var groupID int
	var sequenceNo int
	var UserRoleID string
	var departmentID string
	var setNumber int
	err := pool.QueryRow(context.Background(), query, formAnswerID, ApproverID).Scan(&groupID, &sequenceNo, &UserRoleID, &departmentID, &setNumber)
	logengine.GetTelemetryClient().TrackEvent("GetSequenceNoOfApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	ApproverEntity.GroupID = groupID
	ApproverEntity.SequenceNo = sequenceNo
	ApproverEntity.UserRoleID = UserRoleID
	ApproverEntity.DepartmentID = departmentID
	ApproverEntity.SetNumber = setNumber

	return &ApproverEntity, nil
}

func InsertDeligatedApprover(entity *entity.DelegateApproverEntity, response model.DelegateApproverResponse, userID string, isApollo *bool) model.DelegateApproverResponse {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	action := "pending"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	pendingID := codes[action].ID
	delegatedID := codes["delegated"].ID

	querystring := `insert into approvers (group_id, sequence_no, user_role_id, department_id, form_answer_id, approver_id,status,created_by,set_number,is_apollo)
	values ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10)`
	_, err = tx.Exec(context.Background(), querystring, entity.GroupID, entity.SequenceNo, entity.UserRoleID, entity.DepartmentID, entity.FormAnswerID, entity.ApproverID, pendingID, userID, entity.SetNumber, isApollo)
	logengine.GetTelemetryClient().TrackEvent("InsertDeligatedApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to roll back Approver data"
		response.Error = true
		return response
	}
	query := `update approvers set is_active = false , is_deleted = true where form_answer_id  = $1 and approver_id = $2`
	_, err = tx.Exec(context.Background(), query, entity.FormAnswerID, userID)
	logengine.GetTelemetryClient().TrackEvent("InsertDeligatedApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to delete approver in approvers table"
		response.Error = true
		return response
	}
	queryString1 := `INSERT INTO approval_log
	(form_answer, actioned_by, approval_role, status, "comments", "limit _range", is_active, user_role_id,is_apollo)
	VALUES($1, $2, 0, $3, 'Approver Deligated', NULL, true, NULL,$4);
	`
	_, err = tx.Exec(context.Background(), queryString1, entity.FormAnswerID, userID, delegatedID, isApollo)
	logengine.GetTelemetryClient().TrackEvent("InsertDeligatedApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = err.Error()
		response.Error = true
		return response
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit Approver data"
		response.Error = true
		return response
	}
	response.Message = "Approver successfully deligated"
	response.Error = false
	return response
}

func GetCodeValuesForRegionalIHCP(category string) []*model.Values {
	queryString := `select value, title from code where category = $1 
	and value not in ('local','regional') 
	and is_active = true and is_deleted = false`

	codeValues := []*model.Values{}
	rows, err := pool.Query(context.Background(), queryString, category)
	logengine.GetTelemetryClient().TrackEvent("GetActivities query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println(err)
		return codeValues
	}

	defer rows.Close()
	for rows.Next() {
		codeValue := &model.Values{}
		err := rows.Scan(&codeValue.Value, &codeValue.Description)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			return codeValues
		}
		codeValues = append(codeValues, codeValue)
	}

	return codeValues
}

func GetCodeValuesForIndonesia(category string) []*model.Values {
	queryString := `select value, title from code where category = $1 
	and value not in ('handsontrainer') 
	and is_active = true and is_deleted = false`

	codeValues := []*model.Values{}
	rows, err := pool.Query(context.Background(), queryString, category)
	logengine.GetTelemetryClient().TrackEvent("GetCodeValuesForIndonesia query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println(err)
		return codeValues
	}

	defer rows.Close()
	for rows.Next() {
		codeValue := &model.Values{}
		err := rows.Scan(&codeValue.Value, &codeValue.Description)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			return codeValues
		}
		codeValues = append(codeValues, codeValue)
	}

	return codeValues
}
func ConvertedCurrencyValueForApollo(userCountryID int, customerCountryID int, hcpType string) (float64, int, error) {
	functionName := "ConvertedCurrencyValue()"
	log.Printf("%s", functionName)
	var customerCountry int
	code := codeController.GetValueKeyCodes()["country"]
	if hcpType == "regional" || hcpType == "internationalothers" || hcpType == "foreign" {
		customerCountry = code["us"].ID
	} else if hcpType == "local" {
		customerCountry = customerCountryID
	}
	if customerCountry == code["phzpc"].ID {
		customerCountry = code["ph"].ID
	}
	if userCountryID == code["phzpc"].ID {
		userCountryID = code["ph"].ID
	}
	if pool == nil {
		pool = GetPool()
	}
	var rate float64
	var country int
	var targetCountry int
	queryString := `select country ,target_country ,target_rate from currency_exchange where 
	((country = $1 and target_country = $2) or (country = $2 and target_country = $1))
	order by date_created desc limit 1`
	err := pool.QueryRow(context.Background(), queryString, userCountryID, customerCountry).Scan(&country, &targetCountry, &rate)
	logengine.GetTelemetryClient().TrackEvent("ConvertedCurrencyValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return 0, 0, err
	}

	return rate, country, nil
}
func ConvertedCurrencyValueForExpense(convertCountry int, userCountryID *int, value float64) (float64, error) {
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["country"]
	if userCountryID != nil && *userCountryID == code["phzpc"].ID {
		id := code["ph"].ID
		userCountryID = &id
	}
	var rate float64
	var country int
	var targetCountry int
	queryString := `select country ,target_country ,target_rate from currency_exchange where 
	((country = $1 and target_country = $2) or (country = $2 and target_country = $1))
	order by date_created desc limit 1`
	err := pool.QueryRow(context.Background(), queryString, userCountryID, convertCountry).Scan(&country, &targetCountry, &rate)
	logengine.GetTelemetryClient().TrackEvent("ConvertedCurrencyValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return 0, err
	}
	var finalValue float64
	log.Println(rate)
	if rate != 0 {
		finalValue = value * rate
	}

	return finalValue, nil
}
