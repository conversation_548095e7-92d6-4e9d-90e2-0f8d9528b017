package veeva

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
)

func VeevaGetAllCreatedProductData() ([]entity.ProductRecords, error) {
	functionName := "VeevaGetAllCreatedProductData()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllProductData
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		return nil, err
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Country_Code__c+,+Id+,+ZLG_Status__c+,+Name+,+ZLG_Group_Code__c+,+ZLG_Product_Owners_Principals__c+,+LastModifiedDate+,+CreatedDate+FROM+Product_vod__c+WHERE+Product_Type_vod__c='Detail'+AND+CreatedDate+=+` + os.Getenv("VEEVA_FETCH_CREATED_DATA_FOR_PRODUCT_DAYS")
	//url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+FROM+Product_vod__c+WHERE+Id=` + `'a005h00000Va1yFAAR'` + `+limit+1`

	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	bearer := "Bearer " + veevaAuthorizationResponse.Access_token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	defer response.Body.Close()
	responseBody, responseErr := ioutil.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, responseErr
	}
	fmt.Println(string(responseBody))
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)
	if err2 != nil {
		return nil, errors.New(string(responseBody) + url)
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllProductData
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			return nil, errors.New(string(responseForNextRecordBody) + url)
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}
	return ResponseUrlValues.Records, nil
}
func VeevaGetAllModifiedProductData() ([]entity.ProductRecords, error) {
	functionName := "VeevaGetAllModifiedProductData()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllProductData
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		return nil, err
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Country_Code__c+,+Id+,+ZLG_Status__c+,+Name+,+ZLG_Group_Code__c+,+ZLG_Product_Owners_Principals__c+,+LastModifiedDate+,+CreatedDate+FROM+Product_vod__c+WHERE+Product_Type_vod__c='Detail'+AND+LastModifiedDate+=+` + os.Getenv("VEEVA_FETCH_MODIFIED_DATA_FOR_PRODUCT_DAYS")
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	bearer := "Bearer " + veevaAuthorizationResponse.Access_token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, err
	}
	defer response.Body.Close()
	responseBody, responseErr := ioutil.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, responseErr
	}
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)
	if err2 != nil {
		return nil, errors.New(string(responseBody) + url)
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllProductData
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			return nil, errors.New(string(responseForNextRecordBody) + url)
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}
	return ResponseUrlValues.Records, nil
}
