UPDATE code SET title = 'Regional', value = 'regional', description = 'Regional' WHERE id = 26;
UPDATE form SET design = 
$$
[
    {
        "id": "activities-selection",
        "title": "Activities Selection",
        "sections": [
            {
                "id": "activities-selection-sections-1",
                "form": {
                    "id": "activities-selection-section-1-form-1",
                    "group": [
                        {
                            "id": "activities-selection-section-1-form-1-group1",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "type-of-ihcp-activity",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Type of IHCP activity",
                                            "hidden": false,
                                            "source": "code|ActivityType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "type-of-event",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Type of Event",
                                            "hidden": false,
                                            "source": "code|ActivityEventType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        }
                                    ],
                                    "groupId": "activities-selection-section-1-form-1-group1-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                }
                            ],
                            "sequenceNo": 1
                        }
                    ],
                    "title": null,
                    "sequenceNo": 1
                },
                "title": null,
                "childForm": null,
                "sequenceNo": 1
            }
        ],
        "sequenceNo": 1
    },
    {
        "id": "basic-info",
        "title": "Basic Info",
        "sections": [
            {
                "id": "basic-info-section-1",
                "form": {
                    "id": "basic-info-section-1-form-1",
                    "group": [
                        {
                            "id": "basic-info-section-1-form-1-group1",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "event-requestor",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Event Requestor",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "event-owner",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Event owner",
                                            "hidden": false,
                                            "source": "user",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        },
                                        {
                                            "id": "activity-name",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Activity Name",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 3
                                        },
                                        {
                                            "id": "activity-start-date",
                                            "type": "date",
                                            "rules": null,
                                            "title": "Activity Start Date",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 4,
                                            "validations": null,
                                            "validatesource": "controls|StartDate"
                                        },
                                        {
                                            "id": "activity-end-date",
                                            "type": "date",
                                            "rules": null,
                                            "title": "Activity End Date",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 5
                                        },
                                        {
                                            "id": "duration-of-the-activity",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Duration of the Activity",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 6
                                        },
                                        {
                                            "id": "product",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Product",
                                            "hidden": false,
                                            "source": "material",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 7
                                        },
                                        {
                                            "id": "country",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Country",
                                            "hidden": false,
                                            "source": "code|Country",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 8
                                        },
                                        {
                                            "id": "team",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Team",
                                            "hidden": false,
                                            "source": "team",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 9
                                        },
                                        {
                                            "id": "therapeutic-area",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Therapeutic Area",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 10
                                        },
                                        {
                                            "id": "event-organizer",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Event Organizer",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 11
                                        },
                                        {
                                            "id": "venue",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Venue",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 12
                                        },
                                        {
                                            "id": "target-audience",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Target Audience (Specialization)",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 13
                                        },
                                        {
                                            "id": "no-of-attendees",
                                            "type": "number",
                                            "rules": null,
                                            "title": "No. of Attendees",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 14
                                        },
                                        {
                                            "id": "do-you-want-to-add-any-attachment-for-agenda-or-proposal",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-for-agenda-or-proposal-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment for agenda or proposal?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 15
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 16
                                        },
                                        {
                                            "id": "promotional-or-non-promotional-event",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Promotional or non-promotional event",
                                            "hidden": false,
                                            "source": "code|HcpPromotional",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 17
                                        },
                                        {
                                            "id": "virtual-event",
                                            "type": "toggle",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "virtual-event-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Virtual Event",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 18
                                        },
                                        {
                                            "id": "details-of-the-virtual-event",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Details of the Virtual Event",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 19
                                        },
                                        {
                                            "id": "linked-event-id",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Linked Event ID",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 20
                                        }
                                    ],
                                    "groupId": "basic-info-section-1-form-1-group1-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                }
                            ],
                            "sequenceNo": 1
                        }
                    ],
                    "title": null,
                    "sequenceNo": 1
                },
                "title": null,
                "childForm": null,
                "sequenceNo": 1
            }
        ],
        "sequenceNo": 2
    },
    {
        "id": "hcp-engagement-info",
        "title": "HCP Engagement Info",
        "sections": [
            {
                "id": "hcp-engagement-info-section-1",
                "form": {
                    "id": "hcp-engagement-info-section-1-form-1",
                    "group": [
                        {
                            "id": "hcp-engagement-info-section-1-form-1-group1",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "type-of-hcp",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Type of HCP",
                                            "hidden": false,
                                            "source": "code|HcpType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-1",
                                    "groupRules": [
                                        {
                                            "value": "internationalothers",
                                            "actions": [
                                                {
                                                    "action": "hide",
                                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-3"
                                                },
                                                {
                                                    "action": "show",
                                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-4"
                                                },
                                                {
                                                    "action": "show",
                                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-12"
                                                }
                                            ]
                                        }
                                    ],
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "govt-or-non-govt-hcp",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Govt or Non-Govt HCP",
                                            "hidden": false,
                                            "source": "code|HcpGovtType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "speaker-name",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Speaker Name",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-3",
                                    "groupRules": null,
                                    "sequenceNo": 3
                                },
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "speaker-name-text",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Speaker Name",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-4",
                                    "groupRules": null,
                                    "sequenceNo": 4
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "base-type",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Base Type",
                                            "hidden": false,
                                            "source": "code|BaseType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-5",
                                    "groupRules": null,
                                    "sequenceNo": 5
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "specialty",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Specialty",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-6",
                                    "groupRules": null,
                                    "sequenceNo": 6
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "hco-institute-name",
                                            "type": "text",
                                            "rules": null,
                                            "title": "HCO/Institute Name",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-7",
                                    "groupRules": null,
                                    "sequenceNo": 7
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "expert-level",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Expert Level",
                                            "hidden": false,
                                            "source": "code|HCPTier",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-8a",
                                    "groupRules": null,
                                    "sequenceNo": 8
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "expert-level-international-others",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "International Expert Level",
                                            "hidden": true,
                                            "source": "code|ExpertLevelInternational",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-8b",
                                    "groupRules": null,
                                    "sequenceNo": 8
                                },
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "expert-level-text",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Expert Level",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-9",
                                    "groupRules": null,
                                    "sequenceNo": 9
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "preparation-time",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Preparation Time",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-10",
                                    "groupRules": null,
                                    "sequenceNo": 10
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "service-time",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Service Time",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-11",
                                    "groupRules": null,
                                    "sequenceNo": 11
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "hourly-rate",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Hourly Rate",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-12",
                                    "groupRules": null,
                                    "sequenceNo": 12
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "role",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Role",
                                            "hidden": false,
                                            "source": "code|RoleMaxPaidTime",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-13",
                                    "groupRules": null,
                                    "sequenceNo": 13
                                },
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "role-text",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Role",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-14",
                                    "groupRules": null,
                                    "sequenceNo": 14
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "calculated-honorarium",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Calculated/Max Honorarium",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2,
                                            "validations": null,
                                            "validatesource": null
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-15",
                                    "groupRules": null,
                                    "sequenceNo": 15
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "proposed-honorarium",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Proposed Payable Honorarium",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2,
                                            "validations": null,
                                            "validatesource": "controls|Honorarium"
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-16",
                                    "groupRules": null,
                                    "sequenceNo": 16
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-form-1-group1-questions-17",
                                    "groupRules": null,
                                    "sequenceNo": 17
                                }
                            ],
                            "sequenceNo": 1
                        }
                    ],
                    "title": null,
                    "sequenceNo": 1
                },
                "title": null,
                "childForm": {
                    "id": "hcp-engagement-info-section-1-child-form-1",
                    "group": [
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group1",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": null,
                                    "inputs": [
                                        {
                                            "id": "registration-fee",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Registration Fee",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group1-questions-3",
                                    "groupRules": null,
                                    "sequenceNo": 3
                                }
                            ],
                            "sequenceNo": 1
                        },
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group2",
                            "title": "Transportation",
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "ground-transportation",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Ground Transportation",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": null,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "airfare",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Airfare",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": null,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group2-questions-3",
                                    "groupRules": null,
                                    "sequenceNo": 3
                                }
                            ],
                            "sequenceNo": 2
                        },
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group3",
                            "title": "Meal Package",
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "type-of-meal",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Type of Meal",
                                            "hidden": false,
                                            "source": "code|ExpenseLimit",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group3-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "no-of-meal",
                                            "type": "number",
                                            "rules": null,
                                            "title": "No of Meal",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "*",
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "cost-per-meal",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Cost per Meal",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "=",
                                            "sequenceNo": 2,
                                            "validations": null,
                                            "validatesource": "controls|Meal"
                                        },
                                        {
                                            "id": "total-cost",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 3
                                        },
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 4
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 5
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group3-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                }
                            ],
                            "sequenceNo": 3
                        },
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group4",
                            "title": "Accomodation",
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "no-of-days",
                                            "type": "number",
                                            "rules": null,
                                            "title": "No of Days",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "*",
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "cost-per-day",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Cost per Day",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "=",
                                            "sequenceNo": 2
                                        },
                                        {
                                            "id": "total-cost",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 3
                                        },
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": null,
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 4
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 5
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group4-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                }
                            ],
                            "sequenceNo": 4
                        },
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group5",
                            "title": "Others",
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "name-of-expense",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Name of Expense",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group5-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "id": "hcp-engagement-info-section-1-child-form-1-group6",
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "total-cost",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group6-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                },
                                {
                                    "id": "hcp-engagement-info-section-1-child-form-1-group7",
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "do-you-want-to-add-any-attachment",
                                            "type": "checkbox",
                                            "rules": [
                                                {
                                                    "value": "true",
                                                    "actions": [
                                                        {
                                                            "id": "do-you-want-to-add-any-attachment-show",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Do you want to add any attachment?",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "upload",
                                            "type": "button",
                                            "rules": null,
                                            "title": "Upload",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 2
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group7-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 3
                                }
                            ],
                            "sequenceNo": 5
                        },
                        {
                            "id": "hcp-engagement-info-section-1-child-form-1-group8",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "total-cost",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "hcp-engagement-info-section-1-child-form-1-group8-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                }
                            ],
                            "sequenceNo": 6
                        }
                    ],
                    "title": "Expenses",
                    "sequenceNo": 1
                },
                "sequenceNo": 1
            }
        ],
        "sequenceNo": 3
    },
    {
        "id": "event-expenses",
        "title": "Event Expenses",
        "sections": [
            {
                "id": "event-expenses-section-1",
                "form": {
                    "id": "event-expenses-section-1-form-1",
                    "group": [
                        {
                            "id": "event-expenses-section-1-form-1-group1",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "type-of-event-expenses",
                                            "type": "dropdown",
                                            "rules": [
                                                {
                                                    "value": "logisticsmaterials",
                                                    "actions": [
                                                        {
                                                            "id": "event-expenses-section-1-form-1-group3",
                                                            "action": "show"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "value": "accomodation",
                                                    "actions": [
                                                        {
                                                            "id": "event-expenses-section-1-form-1-group2",
                                                            "action": "show"
                                                        }
                                                    ]
                                                },
                                                {
                                                    "value": "meals",
                                                    "actions": [
                                                        {
                                                            "id": "event-expenses-section-1-form-1-group2",
                                                            "action": "show"
                                                        }
                                                    ]
                                                }
                                            ],
                                            "title": "Type of Event Expenses",
                                            "hidden": false,
                                            "source": "code|EventExpenseType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group1-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "type-meeting-package",
                                            "type": "dropdown",
                                            "rules": null,
                                            "title": "Type of Meeting Package",
                                            "hidden": false,
                                            "source": "code|MeetingPackageType",
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group1-questions-2",
                                    "groupRules": [
                                        {
                                            "value": "others",
                                            "actions": [
                                                {
                                                    "action": "show",
                                                    "groupId": "event-expenses-section-1-form-1-group1-questions-3"
                                                }
                                            ]
                                        }
                                    ],
                                    "sequenceNo": 2
                                },
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "meeting-package-others",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Others",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group1-questions-3",
                                    "groupRules": null,
                                    "sequenceNo": 3
                                }
                            ],
                            "sequenceNo": 1
                        },
                        {
                            "id": "event-expenses-section-1-form-1-group2",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "no-of-people",
                                            "type": "number",
                                            "rules": null,
                                            "title": "No of people",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "*",
                                            "sequenceNo": 1
                                        },
                                        {
                                            "id": "fare",
                                            "type": "number",
                                            "rules": null,
                                            "title": "Fee",
                                            "hidden": true,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": "=",
                                            "sequenceNo": 2
                                        },
                                        {
                                            "id": "total-cost",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 3
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group2-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                }
                            ],
                            "sequenceNo": 2
                        },
                        {
                            "id": "event-expenses-section-1-form-1-group3",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": true,
                                    "inputs": [
                                        {
                                            "id": "type-of-transport",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Type of Transport",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group3-questions-1",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                },
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "total-cost",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group3-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 2
                                }
                            ],
                            "sequenceNo": 2
                        },
                        {
                            "id": "event-expenses-section-1-form-1-group4",
                            "title": null,
                            "questions": [
                                {
                                    "title": null,
                                    "hidden": false,
                                    "inputs": [
                                        {
                                            "id": "total-cost",
                                            "type": "text",
                                            "rules": null,
                                            "title": "Total Cost",
                                            "hidden": false,
                                            "source": null,
                                            "values": null,
                                            "readOnly": false,
                                            "operation": null,
                                            "sequenceNo": 1
                                        }
                                    ],
                                    "groupId": "event-expenses-section-1-form-1-group3-questions-2",
                                    "groupRules": null,
                                    "sequenceNo": 1
                                }
                            ],
                            "sequenceNo": 2
                        }
                    ],
                    "title": null,
                    "sequenceNo": 1
                },
                "title": null,
                "childForm": null,
                "sequenceNo": 1
            }
        ],
        "sequenceNo": 4
    }
]
$$
WHERE id = 'acc110f0-becc-4d7f-82fd-4a64e9f626d9';