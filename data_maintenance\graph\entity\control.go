package entity

import (
	"database/sql"

	uuid "github.com/satori/go.uuid"
)

type ControlUpsertInput struct {
	ID       *uuid.UUID
	IsDelete bool
	MaxLimit *int
	Country  int
}

type FmvAuditLogsUpsertInput struct {
	ID                 *uuid.UUID
	IsDelete           bool
	MaxLimit           sql.NullString
	Country            sql.NullString
	Category           sql.NullString
	Type               sql.NullString
	ActivityId         sql.NullString
	CodeId             sql.NullString
	IsActive           bool
	RangeLimit         sql.NullString
	CurrencyCodeLimit  sql.NullString
	MoreThanTwoLecture bool
	Description        sql.NullString
}

type ExportControl struct {
	Error   bool
	Message string
	Data    []Control
}

type Control struct {
	ID          *uuid.UUID
	MaxLimit    int
	Category    string
	Type        string
	Description string
	Status      string
	Country     int
	Limit       int
	PageNo      int
}

type ExportControls struct {
	ID          *uuid.UUID
	MaxLimit    int
	Category    sql.NullString
	Type        sql.NullString
	Description sql.NullString
	Status      sql.NullString
	Country     sql.NullString
}
