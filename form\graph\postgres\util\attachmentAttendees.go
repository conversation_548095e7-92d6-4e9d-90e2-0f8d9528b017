package util

import (
	"bytes"
	"errors"
	"io/ioutil"
	"log"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize"
	wkhtml "github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/ihcp/form/graph/azure"
)

func AttachmentMailAttendeesForEzClaim(BlobUrl string, getMeetingId string, eventName string, startDate string, endDate string) (string, error) {
	pdfg, err := wkhtml.NewPDFGenerator()
	if err != nil {
		return "", err
	}
	var htmlStr string
	var pdfName string
	loc, _ := time.LoadLocation("UTC")
	activityStartDate, _ := strconv.Atoi(startDate)
	activityStartDateTime := time.Unix(int64(activityStartDate), 0)
	activityStartDateUTC := activityStartDateTime.In(loc)
	StartDate := activityStartDateUTC.Format("2006-01-02")
	activityendDate, _ := strconv.Atoi(endDate)
	activityendDateTime := time.Unix(int64(activityendDate), 0)
	activityendDateUTC := activityendDateTime.In(loc)
	EndDate := activityendDateUTC.Format("2006-01-02")
	if BlobUrl != "" {
		_, err = url.ParseRequestURI(strings.TrimSpace(BlobUrl))
		if err != nil {
			return "", err
		}
		excelFile, downloadErr := azure.DownloadFileFromBlobURL(BlobUrl)
		if downloadErr != nil {
			return "", downloadErr
		}
		excelBytes, _ := ioutil.ReadAll(excelFile)
		sheetName := GetSheetName(excelBytes)
		sheet, errorMsg := ParseExcelForeZCLaim(excelBytes, sheetName)
		if errorMsg != "" {
			return "", errors.New(errorMsg)
		}
		htmlStr = `<style>
	.center {
		text-align:center;
		}
		.demo {
			border:1px solid #6409EC;
			border-collapse:collapse;
			padding:5px;
		}
		.demo th {
			border:1px solid #6409EC;
			padding:5px;
			background:#F5BE47;
		}
		.demo td {
			border:1px solid #6409EC;
			padding:5px;
		}
	</style>
	<h3 >` + eventName + "_" + getMeetingId + "_" + StartDate + "_" + EndDate + `</h3>
	<table class="demo" style="width:100%">
		<thead>
		<tr>`
		// for i := 0; i < len(sheet); i++ {
		// 	if i == 0 {
		// 		htmlStr += `<th>Serial No.</th>
		// 	<th>firstName</th>
		// 	<th>lastName</th>
		// 	<th>affiliation</th>
		// 	<th>specialty</th>
		// </tr>`
		// 	} else {

		// 		firstName := strings.TrimSpace(sheet[i][0])
		// 		lastName := strings.TrimSpace(sheet[i][1])
		// 		affiliation := strings.TrimSpace(sheet[i][2])
		// 		specialty := strings.TrimSpace(sheet[i][3])
		// 		htmlStr += `<tr>
		// <td>` + strconv.Itoa(i) + `</td>
		// 	<td>` + firstName + `</td>
		// 	<td>` + lastName + `</td>
		// 	<td>` + affiliation + `</td>
		// 	<td>` + specialty + `</td>
		// </tr>`
		// 	}
		// }
		for i, row := range sheet {
			if i == 0 {
				htmlStr += "<tr>"
				for _, col := range row {
					htmlStr += `<th>` + strings.TrimSpace(col) + `</th>`
				}
				htmlStr += `</tr></thead><tbody>`
			} else {
				htmlStr += `<tr>`
				for _, col := range row {
					htmlStr += `<td>` + strings.TrimSpace(col) + `</td>`
				}
				htmlStr += `</tr></thead>
				<tbody>`
			}
		}
		htmlStr += `</thead>
		<tbody>
		</tbody>
	</table>`

	}
	if BlobUrl != "" {
		pdfg.AddPage(wkhtml.NewPageReader(strings.NewReader(htmlStr)))
		// Create PDF document in internal buffer
		err = pdfg.Create()
		if err != nil {
			return "", err
		}
		blobURL, err := azure.UploadBytesToBlob(pdfg.Buffer().Bytes(), "Attendees-"+getMeetingId+".pdf")
		return blobURL, err
	}
	return pdfName, nil
}
func ParseExcelEZFlow(b []byte, sheetName string) ([][]string, error) {
	log.Println("ParseExcelEZFlow()")
	r := bytes.NewReader(b)
	f, err := excelize.OpenReader(r)
	if err != nil {
		return nil, err
	}
	rows := f.GetRows(sheetName)

	if len(rows) == 0 {
		return nil, errors.New("no row error")
	}

	return rows, nil

}
func FilterBlankValues(row []string) []string {
	var filtered []string
	for _, value := range row {
		if strings.TrimSpace(value) != "" {
			filtered = append(filtered, value)
		}
	}
	return filtered
}
func ReadEmployeeAttendanceExcelFile(b []byte, sheetName string) ([][]string, error, int) {
	log.Println("ReadEmployeeAttendanceExcelFile()")
	f, err := excelize.OpenReader(bytes.NewReader(b))
	if err != nil {
		return nil, err, 0
	}

	rows := f.GetRows(sheetName)

	if len(rows) == 0 {
		return nil, errors.New(`no row error`), 0
	}

	return rows, nil, len(FilterBlankValues(rows[0]))
}
func GetSheetNameForApollo(data []byte) string {
	r := bytes.NewReader(data)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.Error())
	}
	return f.GetSheetName(f.GetSheetIndex("employee"))
}
func ParseExcel(b []byte, sheetName string, totalColumns int) ([][]string, error) {
	log.Println("ParseExcel()")
	r := bytes.NewReader(b)
	f, err := excelize.OpenReader(r)
	if err != nil {
		panic(err)
	}
	rows := f.GetRows(sheetName)
	if len(rows) == 0 || len(rows[0]) != totalColumns {
		return nil, errors.New("format of excel columns is wrong")
	}

	return rows, nil
}
func GetSheetName(data []byte) string {
	r := bytes.NewReader(data)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.Error())
	}
	return f.GetSheetName(f.GetActiveSheetIndex())
}
func ParseExcelForeZCLaim(b []byte, sheetname string) ([][]string, string) {
	log.Println("ParseExcel()")
	r := bytes.NewReader(b)
	f, err := excelize.OpenReader(r)
	if err != nil {
		log.Println(err.Error())
	}
	rows := f.GetRows(sheetname)
	return rows, ""
}
