package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertDepartmentRoles is the resolver for the upsertDepartmentRoles field.
func (r *mutationResolver) UpsertDepartmentRoles(ctx context.Context, input model.DepartmentRolesInput) (*model.UpsertResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetCodeValues", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertDepartmentRoles :" + string(inputJson))
	response := controller.UpsertDepartmentRolesData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertDepartmentRoles", "data_maintenance/UpsertDepartmentRoles", time.Since(start), "200")
	return response, nil
}

// GetDepartmentRole is the resolver for the getDepartmentRole field.
func (r *queryResolver) GetDepartmentRole(ctx context.Context, input *model.DepartmentRoleRequest) (*model.DepartmentRoleResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetDepartmentRole", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetDepartmentRole :" + string(inputJson))
	response := controller.FetchDepartmentRoles(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetDepartmentRole", "data_maintenance/GetDepartmentRole", time.Since(start), "200")
	return response, nil
}
