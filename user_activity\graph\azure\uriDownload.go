package azure

import (
	"bytes"
	"context"
	"fmt"
	"log"
	url2 "net/url"

	"github.com/Azure/azure-storage-blob-go/azblob"
)

func DownloadFileFromBlobURL(url string) (*bytes.Buffer, error) {
	// From the Azure portal, get your Storage account blob service URL endpoint.
	accountKey, accountName, _, _ := GetAzureAccountInfo()

	u, err := url2.Parse(url)
	if err != nil {
		log.Println(err)
	}
	credential, err := azblob.NewSharedKeyCredential(accountName, accountKey)
	if err != nil {
		log.Fatal(err)
		return nil, err
	}
	blobURL := azblob.NewBlobURL(*u, azblob.NewPipeline(credential, azblob.PipelineOptions{}))
	ctx := context.Background()

	response, err := blobURL.Download(ctx, 0, 0, azblob.BlobAccessConditions{}, false, azblob.ClientProvidedKeyOptions{})
	if err != nil {
		log.Fatal(err)
		return nil, err
	}

	blobData := &bytes.Buffer{}
	reader := response.Body(azblob.RetryReaderOptions{})
	// local file download start
	//r := bufio.NewReader(reader)
	//fo, err := os.Create("material.xlsx")
	//if err != nil {
	//	panic(err)
	//}
	//defer func() {
	//	if err := fo.Close(); err != nil {
	//		panic(err)
	//	}
	//}()
	//w := bufio.NewWriter(fo)
	//buf := make([]byte, 1024)
	//for {
	//	n, err := r.Read(buf)
	//	if err != nil && err != io.EOF {
	//		panic(err)
	//	}
	//	if n == 0 {
	//		break
	//	}
	//	if _, err := w.Write(buf[:n]); err != nil {
	//		panic(err)
	//	}
	//}
	//
	//if err = w.Flush(); err != nil {
	//	panic(err)
	//}
	// local file download end
	blobData.ReadFrom(reader)
	defer reader.Close()
	log.Println("url: " + url + " -> size: " + fmt.Sprintf("%+v", blobData.Len()) + " bytes")
	return blobData, nil
}
