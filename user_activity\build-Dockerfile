FROM golang:alpine as build-env

# Set working directory for project
RUN mkdir /app
RUN mkdir /app/code
RUN mkdir /app/user_activity
WORKDIR /app/user_activity

######################################################################################

# Install some dependencies needed to build the project
RUN apk update
RUN apk --no-cache add gcc g++ make git

## Update CA Certificates
RUN apk add --no-cache git ca-certificates && update-ca-certificates

# Download all timezone data
RUN apk add --no-cache tzdata
RUN apk add tzdata

######################################################################################

# Copy go mod and sum files
COPY user_activity/go.mod user_activity/go.sum ./
# COPY local login module
COPY ./login ../login/
# COPY local code module
COPY ./code ../code/

# Add Maintainer Info
LABEL maintainer="Zuellig Pharma"

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download

######################################################################################

# This image builds the server
FROM build-env AS server_builder

COPY ./user_activity .

RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o /go/bin/user_activity

######################################################################################

FROM surnet/alpine-wkhtmltopdf:3.12-0.12.6-small as wkhtmltopdf

#In this last stage, we start from a fresh Alpine image, to reduce the image size and not ship the Go compiler in our production artifacts.
FROM alpine AS projectservice

# Install dependencies for wkhtmltopdf
RUN apk add --no-cache libstdc++ libx11 libxrender libxext libssl3 fontconfig freetype ttf-dejavu ttf-droid ttf-freefont ttf-liberation && apk add --no-cache --virtual .build-deps msttcorefonts-installer && update-ms-fonts && fc-cache -f && rm -rf /tmp/* && apk del .build-deps

# We add the certificates to be able to verify remote instances
RUN apk add ca-certificates

######################################################################################

COPY --from=server_builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Finally we copy the statically compiled Go binary.
COPY --from=server_builder /go/bin/user_activity /go/bin/user_activity

# Copy all timezone data inside the container
COPY --from=server_builder /usr/share/zoneinfo/ /usr/share/zoneinfo/

# Copy wkhtmltopdf files from docker-wkhtmltopdf final image
COPY --from=wkhtmltopdf /bin/wkhtmltopdf /bin/wkhtmltopdf

ENTRYPOINT ["/go/bin/user_activity"]