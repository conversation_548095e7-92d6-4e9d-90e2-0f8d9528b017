package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

func UpdateUploadStatus(uploadId *uuid.UUID, status string, author uuid.UUID) {
	log.Println("UpdateUploadStatus()")
	if pool == nil {
		pool = GetPool()
	}
	updateStmt := "UPDATE upload SET status = $2, modified_by = $3, last_modified = now() WHERE id = $1"
	commandTag, err := pool.Exec(context.Background(), updateStmt, uploadId, status, author)
	logengine.GetTelemetryClient().TrackEvent("GetTeamValues query called")
	if err != nil {
		log.Println(err)
	}
	if commandTag.RowsAffected() != 1 {

		log.Println("UpdateUploadStatus: Fail to update upload status with id: " + uploadId.String())
	}
}

func InsertUploadValidationErrors(input *model.ExcelUploadRequest, validation *model.ValidationResult, uploadId *uuid.UUID, author uuid.UUID) {
	log.Println("InsertUploadValidationErrors()")
	// Update upload status to Fail.
	if pool == nil {
		pool = GetPool()
	}
	if uploadId != nil {

		UpdateUploadStatus(uploadId, "Fail", author)

		querystring := "INSERT INTO upload_log (upload, row, message, created_by) VALUES($1, $2, $3, $4)"
		for _, validationError := range validation.ExcelValidationMessages {
			commandTag, err := pool.Exec(context.Background(), querystring, uploadId, validationError.Row, validationError.Message, author)

			if err != nil {
				log.Println(err)
			}
			if commandTag.RowsAffected() != 1 {
				log.Println("No row inserted into uploads")
			}
		}
	} else {
		log.Println("InsertUploadValidationErrors(): Upload id is empty")
	}

}
