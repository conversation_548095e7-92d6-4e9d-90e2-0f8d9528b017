package postgres

import (
	"context"
	"errors"
	"log"
	"strings"

	codeController "github.com/ihcp/code/controller"
	codeEntity "github.com/ihcp/code/entity"
	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/model"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func UpsertExcelCustomer(entities []*entity.CustomerExcelInput, uploadId *uuid.UUID) {
	functionName := "UpsertExcelCustomer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()
	cmslClasses := codes["cmslclass"]
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	for index, item := range entities {
		if err != nil {
			log.Printf("%s - Error: Failed to begin transaction", functionName)
			continue
		}
		if *(item.ID) == uuid.Nil && (CheckSpeakernamevalid(item.Name, item.CountryNo, item.SpDesc, item.City, item.Organization)) {
			err := createCustomer(tx, item)
			if err != nil {
				return
			}
			if item.TeamMemberID != nil {
				handleTeamAndTeamMember(tx, item, cmslClasses)
			}
		} else if *(item.ID) != uuid.Nil && ((CheckSpeakernamevalid(item.Name, item.CountryNo, item.SpDesc, item.City, item.Organization)) || !item.IsActive) {
			if item.ID != nil && item.TeamName == "" && item.Position == "" && item.EmployeeName == "" && item.Name == "" && item.SpDesc == "" && item.CMSLClass == "" && item.City == "" && item.Gender == "" && item.VeevareferenceId == "" && item.Organization == "" && item.AccountType == "" {
				err := deleteCustomer(tx, item)
				if err != nil {
					return
				}
			} else {
				err = updateCustomer(tx, item, index, uploadId)
				if err != nil {
					return
				}
			}
			// } else {
			// 	err := deleteCustomer(tx, item)
			// 	if err != nil {
			// 		return
			// 	}
			// }

		}
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: Failed to commit customer data", functionName)
	}
}

func CheckSpeakernamevalid(Speakername string, country int, Specialty string, city string, organization string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	query := `select 1 from customer c where lower(c."name") =$1 and country =$2 and is_active =true and is_deleted =false and REPLACE(lower(c.city),' ','') = $3 and REPLACE(lower(c.v_sp_desc),' ','') = $4 and REPLACE(lower(c.organization),' ','') = $5`
	err := pool.QueryRow(context.Background(), query, strings.ToLower(Speakername), country, strings.ToLower(strings.ReplaceAll(city, " ", "")), strings.ToLower(strings.ReplaceAll(Specialty, " ", "")), strings.ToLower(strings.ReplaceAll(organization, " ", ""))).Scan(&hasValue)
	if err == nil {
		return false
	}
	return true
}

func createCustomer(tx pgx.Tx, item *entity.CustomerExcelInput) error {
	functionName := "createCustomer()"
	log.Println(functionName)
	queryString := `INSERT INTO customer (name,country,city,customer_number,gender,v_sp_desc,is_active,is_deleted,created_by,speaker_weight,cmsl_class,veeva_reference_id,organization,account_type) VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13,$14) RETURNING(id)`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.Name, item.CountryNo, item.City, item.CustomerNo, item.GenderID, item.SpDesc, item.IsActive, item.IsDeleted, item.Author, item.SpeakerWeight, item.CMSLClass, item.VeevareferenceId, item.Organization, item.AccountType)
	err := tx.QueryRow(context.Background(), queryString, inputArgs...).Scan(&item.ID)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}

func updateCustomer(tx pgx.Tx, item *entity.CustomerExcelInput, index int, uploadId *uuid.UUID) error {
	functionName := "updateCustomer()"
	log.Println(functionName)
	var validationResult model.ValidationResult
	queryString := `UPDATE customer set name=$2,city=$3,customer_number=$4,gender=$5,v_sp_desc=$6,is_active=$7,modified_by=$8,last_modified=now(),country=$9, speaker_weight = $10, cmsl_class = $11,veeva_reference_id = $12,organization=$13,account_type=$14 where id=$1`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.ID, item.Name, item.City, item.CustomerNo, item.GenderID, item.SpDesc, item.IsActive, item.Author, item.CountryNo, item.SpeakerWeight, item.CMSLClass, item.VeevareferenceId, item.Organization, item.AccountType)
	commandTag, err := tx.Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	if commandTag.RowsAffected() != 1 {
		err = errors.New("invalid id")
		errorMessage := &model.ExcelValidationMessage{Row: (index + 1), Message: err.Error()}
		validationResult.ExcelValidationMessages = append(validationResult.ExcelValidationMessages, errorMessage)
		InsertUploadValidationErrors(nil, &validationResult, uploadId, uuid.UUID{})
		return err
	}
	return err
}

func deleteCustomer(tx pgx.Tx, item *entity.CustomerExcelInput) error {
	functionName := "deleteCustomer()"
	log.Println(functionName)
	var err error
	// teamMemberCustomerQuery := `UPDATE team_member_customer
	// set is_active = false, is_deleted = true, modified_by = $2,last_modified = now()
	// where customer = $1`

	// _, err := tx.Exec(context.Background(), teamMemberCustomerQuery, inputArgs...)
	// if err != nil {
	// 	log.Printf("%s - error: {%s}", functionName, err.Error())
	// 	return err
	// }
	customerQuery := `UPDATE customer 
	set is_active = $3, is_deleted = $4, modified_by = $2, last_modified = now()
	where customer.id = $1`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.ID, item.Author, item.IsActive, item.IsDeleted)
	_, err = tx.Exec(context.Background(), customerQuery, inputArgs...)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}

func handleTeamAndTeamMember(tx pgx.Tx, entity *entity.CustomerExcelInput, cmslClasses map[string]codeEntity.Code) error {
	cmslClassID := cmslClasses[entity.CMSLClass].ID
	return UpsertTeamMemberCustomerForExcel(tx, mapTeamMemberCustomerFromCustomer(*entity.TeamMemberID, *entity.ID, cmslClassID), *entity.Author)
}

func mapTeamMemberCustomerFromCustomer(teamMemberId, customerId uuid.UUID, cmslClass int) *entity.TeamMemberCustomer {

	return &entity.TeamMemberCustomer{
		ID:         nil,
		TeamMember: &teamMemberId,
		Customer:   &customerId,
		CMSLClass:  cmslClass,
		IsActive:   false,
		IsDeleted:  false,
	}
}

// func CheckCustomerNumberUnique(customerNumber string, customerID string) bool {
// 	functionName := "CheckCustomerNumberUnique()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var result bool
// 	if customerID != "" {
// 		if !CheckCustomerNumberForID(customerNumber, customerID) {
// 			queryString := `select 1 from customer where id <> $2 AND customer_number = $1 AND is_deleted = false`
// 			var hasValue int
// 			err := pool.QueryRow(context.Background(), queryString, customerNumber, customerID).Scan(&hasValue)
// 			if err == nil {
// 				result = true
// 			}
// 		}
// 	} else {
// 		queryString := `select 1 from customer where customer_number = $1 AND is_deleted = false`
// 		var hasValue int
// 		err := pool.QueryRow(context.Background(), queryString, customerNumber).Scan(&hasValue)
// 		if err == nil {
// 			result = true
// 		}
// 	}
// 	return result
// }

//	func CheckCustomerNumberForID(customerNumber string, customerID string) bool {
//		functionName := "CheckCustomerNumberForID()"
//		log.Println(functionName)
//		if pool == nil {
//			pool = GetPool()
//		}
//		var result bool
//		queryString := `select 1 from customer where id = $2 AND customer_number = $1 AND is_deleted = false`
//		var hasValue int
//		err := pool.QueryRow(context.Background(), queryString, customerNumber, customerID).Scan(&hasValue)
//		if err == nil {
//			result = true
//		}
//		return result
//	}
func GetUserRoleValue(roleValue string) (string, error) {
	functionName := "GetUserRoleValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `select ur.id::text from user_roles ur 
	where ur.value =$1 and is_active =true and is_deleted =false `
	var hasValue string
	err := pool.QueryRow(context.Background(), query, roleValue).Scan(&hasValue)
	if err != nil {
		return "", err
	}
	return hasValue, nil
}
