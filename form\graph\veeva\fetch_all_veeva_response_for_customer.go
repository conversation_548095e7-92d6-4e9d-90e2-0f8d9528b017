package veeva

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
)

func VeevaGetAllCreatedDataForCustomer() ([]entity.CustomerInputForVeevaAccount, []entity.Records, error) {
	functionName := "VeevaGetAllCreatedDataForCustomer()"
	log.Println(functionName)
	var ResponseUrlValues entity.VeevaGetAllDataForCustomer
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		return nil, nil, err
	}
	method := "GET"
	var body []byte
	var days string = "last_n_days:1"
	if s := os.Getenv("VEEVA_FETCH_CREATED_DATA_DAYS"); s != "" {
		days = s
	}
	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=Select+Account.ID+,+Account.Primary_Country_NWK__c+,+Account.ZLG_HCP_Weight__c+,+Account.Name+,+Account.ZLG_Sales_Segment__c+,+Account.Primary_City_QS__c +,+Account.RecordTypeId+,+Account.KOL_vod__c+,+    Account.CreatedDate+,+Account.LastModifiedDate+,+Account.Gender_vod__c+,+    Account.Status__c        +,+Account.Primary_Address_QS__c+    ,+Account.Primary_Zip_Postal_code_QS__c+,+Account.Primary_State_QS__c    +,+Account.FirstName    +,+Account.Primary_Parent_vod__r.Name+,+Account.Specialty_2_QS__c+,+Account.LastName+,+Account.Specialty_1_QS__r.Name+,+Account.PersonEmail    +,+Account.External_ID_vod__c+FROM+Account+where+Account.CreatedDate+=+` + days
	request, err := http.NewRequest(method, strings.ReplaceAll(url, " ", ""), bytes.NewBuffer(body))
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, nil, err
	}
	bearer := "Bearer " + veevaAuthorizationResponse.Access_token
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	log.Println(fmt.Sprintf(">>> Requesting HCP data created from %s ", days))
	response, err := client.Do(request)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err.Error())
		return nil, nil, err
	}
	defer response.Body.Close()
	responseBody, responseErr := ioutil.ReadAll(response.Body)
	if responseErr != nil {
		logengine.GetTelemetryClient().TrackException(responseErr.Error())
		return nil, nil, responseErr
	}
	err2 := json.Unmarshal(responseBody, &ResponseUrlValues)
	if err2 != nil {
		return nil, nil, err2
	}
	for ResponseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + ResponseUrlValues.NextRecordsURL
		var ResponseUrlNextValues entity.VeevaGetAllDataForCustomer
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, nil, err
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return nil, nil, err
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, responseErr := ioutil.ReadAll(responseForNextRecord.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return nil, nil, responseErr
		}
		err3 := json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err3 != nil {
			return nil, nil, err3
		}
		ResponseUrlValues.Records = append(ResponseUrlValues.Records, ResponseUrlNextValues.Records...)
		ResponseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}
	responseEntity, err := VeevaAllDataStore(ResponseUrlValues.Records, url, method, bearer, veevaInstanceURL, body)
	if err != nil {
		return nil, nil, err
	}
	return responseEntity, ResponseUrlValues.Records, nil

}
func VeevaGetAllModifiedDataForCustomer() ([]entity.CustomerInputForVeevaAccount, []entity.Records, error) {
	functionName := "VeevaGetAllModifiedDataForCustomer()"
	log.Println(functionName)

	var responseUrlValues entity.VeevaGetAllDataForCustomer
	veevaAuthorizationResponse, err := GetAuthRest()
	if err != nil {
		panic(err)
	}

	method := "GET"
	var body []byte
	var days string = "last_n_days:1"
	if s := os.Getenv("VEEVA_FETCH_CREATED_DATA_DAYS"); s != "" {
		days = s
	}

	veevaInstanceURL := GetVeevaBaseURL(veevaAuthorizationResponse.Instance_url)
	url := veevaInstanceURL + `/services/data/v54.0/query?q=Select+Account.ID+,+Account.Primary_Country_NWK__c+,+Account.ZLG_HCP_Weight__c+,+Account.Name+,+Account.ZLG_Sales_Segment__c    +,+Account.RecordTypeId+,+Account.Primary_City_QS__c +,+Account.RecordType.Name   +,+Account.KOL_vod__c+,+    Account.CreatedDate+,+Account.LastModifiedDate+,+Account.Gender_vod__c+,+    Account.Status__c       +,+Account.Primary_Address_QS__c+    ,+Account.Primary_Zip_Postal_code_QS__c+,+Account.Primary_State_QS__c    +,+Account.FirstName    +,+Account.Primary_Parent_vod__r.Name+,+Account.Specialty_2_QS__c+,+Account.LastName+,+Account.Specialty_1_QS__r.Name+,+Account.PersonEmail    +,+Account.External_ID_vod__c+FROM+Account+where+Account.LastModifiedDate+=+` + days
	request, err := http.NewRequest(method, strings.ReplaceAll(url, " ", ""), bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}

	bearer := "Bearer " + veevaAuthorizationResponse.Access_token

	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	log.Println(fmt.Sprintf(">>> Requesting HCP data updated from %s ", days))
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	if err := json.Unmarshal(responseBody, &responseUrlValues); err != nil {
		panic(err)
	}

	f := func(nextRecordURL string) *entity.VeevaGetAllDataForCustomer {
		url = veevaInstanceURL + nextRecordURL
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		if err != nil {
			panic(err)
		}

		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			panic(err)
		}
		defer responseForNextRecord.Body.Close()

		responseForNextRecordBody, err := io.ReadAll(responseForNextRecord.Body)
		if err != nil {
			panic(err)
		}

		var resp entity.VeevaGetAllDataForCustomer
		if err := json.Unmarshal(responseForNextRecordBody, &resp); err != nil {
			panic(err)
		}

		return &resp
	}

	for responseUrlValues.NextRecordsURL != "" {
		res := f(responseUrlValues.NextRecordsURL)
		responseUrlValues.Records = append(responseUrlValues.Records, res.Records...)
		responseUrlValues.NextRecordsURL = res.NextRecordsURL
	}

	responseEntity, _ := VeevaAllDataStore(responseUrlValues.Records, url, method, bearer, veevaInstanceURL, body)
	return responseEntity, responseUrlValues.Records, nil
}
func VeevaAllDataStore(input []entity.Records, url string, method string, bearer string, veevaInstanceURL string, body []byte) ([]entity.CustomerInputForVeevaAccount, error) {
	var responseEntity []entity.CustomerInputForVeevaAccount
	codes := codeController.GetValueKeyCodes()
	genders := codes["gender"]
	country := codes["country"]
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	var countryValue string
	var Gender int
	var status bool
	for _, value := range input {
		var entityCustomer entity.CustomerInputForVeevaAccount
		if strings.TrimSpace(value.PrimaryCountryNWKC) == "" {
			continue
		}
		if strings.TrimSpace(value.PrimaryCountryNWKC) == "PH" {
			countryValue = "phzpc"
			if value.SpeakerWeight != 0 {
				entityCustomer.SpeakerWeight = value.SpeakerWeight
			}
		} else {
			countryValue = strings.ToLower(value.PrimaryCountryNWKC)
		}
		if strings.ToLower(strings.TrimSpace(value.GenderVodC)) == "male" || strings.ToLower(strings.TrimSpace(value.GenderVodC)) == "m" {
			value.GenderVodC = "M"
		} else if strings.ToLower(strings.TrimSpace(value.GenderVodC)) == "female" || strings.ToLower(strings.TrimSpace(value.GenderVodC)) == "f" {
			value.GenderVodC = "F"
		} else {
			value.GenderVodC = "NA"
		}
		var cityNameResponse entity.FetchCityNameFromVeevaAccount
		url = veevaInstanceURL + `/services/data/v54.0/query?q=Select+Address_vod__c.City_vod__c+FROM+Address_vod__c+where+Address_vod__c.Account_vod__c+=+'` + value.ID + `'`
		requestForCityName, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		if err != nil {
			return responseEntity, err
		}
		requestForCityName.Header.Add("Authorization", bearer)
		requestForCityName.Header.Set("Content-Type", "application/json")
		responseForCityName, err := client.Do(requestForCityName)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return responseEntity, err
		}
		defer responseForCityName.Body.Close()
		responseForCityNameBody, responseErr := ioutil.ReadAll(responseForCityName.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return responseEntity, responseErr
		}
		err3 := json.Unmarshal(responseForCityNameBody, &cityNameResponse)
		if err3 != nil {
			return responseEntity, err3
		}
		for _, city := range cityNameResponse.Records {
			cityName := city.CityVodC
			entityCustomer.City = cityName
		}
		//RecordType fetch
		var RecordTypeNameResponse entity.FetchRecordTypeNameFromVeevaAccount
		url = veevaInstanceURL + `/services/data/v54.0/query?q=Select+Name+FROM+RecordType+where+Id+=+'` + value.RecordTypeId + `'`
		requestForRecordTypeName, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		if err != nil {
			return responseEntity, err
		}
		requestForRecordTypeName.Header.Add("Authorization", bearer)
		requestForRecordTypeName.Header.Set("Content-Type", "application/json")
		responseForRecordTypeName, err := client.Do(requestForRecordTypeName)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err.Error())
			return responseEntity, err
		}
		defer responseForCityName.Body.Close()
		responseForRecordTypeNameBody, responseErr := ioutil.ReadAll(responseForRecordTypeName.Body)
		if responseErr != nil {
			logengine.GetTelemetryClient().TrackException(responseErr.Error())
			return responseEntity, responseErr
		}
		err4 := json.Unmarshal(responseForRecordTypeNameBody, &RecordTypeNameResponse)
		if err4 != nil {
			return responseEntity, err4
		}
		for _, RecordType := range RecordTypeNameResponse.Records {
			RecordTypeName := RecordType.Name
			entityCustomer.RecordTypeName = strings.ReplaceAll(RecordTypeName, "_vod", "")
		}
		Gender = genders[value.GenderVodC].ID
		entityCustomer.GenderID = Gender
		if strings.TrimSpace(value.StatusC) == "Active" {
			status = true
			entityCustomer.IsActive = status
		} else {
			status = false
			entityCustomer.IsActive = status
		}
		entityCustomer.SpDesc = strings.TrimSpace(value.Specialty1QSr.Name)
		entityCustomer.OrganizationName = strings.TrimSpace(value.PrimaryParentVodr.Name)
		entityCustomer.Country = country[countryValue].ID
		entityCustomer.CustomerNo = strings.TrimSpace(value.ExternalIDVodC)
		entityCustomer.VeevareferenceId = value.ID[:len(value.ID)-3]
		entityCustomer.IsDeleted = false
		entityCustomer.Name = strings.TrimSpace(value.Name)
		entityCustomer.CMSLClass = strings.TrimSpace(value.ZLGSalesSegmentC)
		log.Println("Requesting HPC info for: ", value.Name, value.CreatedDate, value.ExternalIDVodC, entityCustomer.VeevareferenceId)

		responseEntity = append(responseEntity, entityCustomer)
	}
	return responseEntity, nil
}
