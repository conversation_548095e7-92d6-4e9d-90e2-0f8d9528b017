INSERT INTO event_type_access_permission (user_role,country,is_active,is_deleted,created_by,last_modified,modified_by,event_type,user_role_id) VALUES
	 ('{68,69,46,71,72}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,92,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'productmanager' or value = 'salesmanager' or value = 'marketaccessmanager' or value = 'medicalmanager'  and is_active = true and is_deleted = false))),
	 ('{47}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,96,(select array(select id from user_roles where value = 'bumanager' and is_active = true and is_deleted = false))),
	 ('{68,69,46,71,72}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,98,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'productmanager' or value = 'salesmanager' or value = 'marketaccessmanager' or value = 'medicalmanager'  and is_active = true and is_deleted = false))),
	 ('{68,69,46,71,72}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,94,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'productmanager' or value = 'salesmanager' or value = 'marketaccessmanager' or value = 'medicalmanager'  and is_active = true and is_deleted = false))),
	 ('{68,69,46,71,72}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,86,(select array(select id from user_roles where value = 'salesrepresentative' or  value = 'productmanager' or value = 'salesmanager' or value = 'marketaccessmanager' or value = 'medicalmanager' and is_active = true and is_deleted = false))),
	 ('{47,46,69}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,70,(select array(select id from user_roles where value = 'bumanager' or  value = 'salesmanager' or value = 'productmanager' and is_active = true and is_deleted = false))),
	 ('{69,46,71,72}',7,true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,93,(select array(select id from user_roles where value = 'productmanager' or value = 'salesmanager' or value = 'marketaccessmanager' or value = 'medicalmanager' and is_active = true and is_deleted = false)));