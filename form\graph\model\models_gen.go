// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

type ActivityFilter struct {
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"isActive,omitempty"`
}

type ActivityInput struct {
	ID          *string `json:"id,omitempty"`
	Description *string `json:"description,omitempty"`
	IsActive    *bool   `json:"isActive,omitempty"`
	IsDeleted   *bool   `json:"isDeleted,omitempty"`
}

type ActivityModel struct {
	ID          string `json:"id"`
	Description string `json:"description"`
	IsActive    bool   `json:"isActive"`
	Country     string `json:"country"`
}

type ActivityModelsResponse struct {
	Error   bool             `json:"error"`
	Message string           `json:"message"`
	Data    []*ActivityModel `json:"data"`
}

type ActivitySelection struct {
	ID          string `json:"id"`
	Description string `json:"description"`
}

type ActivitySelectionsResponse struct {
	Error   bool                 `json:"error"`
	Message string               `json:"message"`
	Data    []*ActivitySelection `json:"data"`
}

type ApprovalRoleData struct {
	ID                  string   `json:"id"`
	GroupType           string   `json:"groupType"`
	GroupTitle          string   `json:"groupTitle"`
	SequenceNo          int      `json:"sequenceNo"`
	MinLimit            *int     `json:"minLimit,omitempty"`
	MaxLimit            *int     `json:"maxLimit,omitempty"`
	Activity            string   `json:"activity"`
	ActivityTitle       string   `json:"activityTitle"`
	CountryTitle        string   `json:"countryTitle"`
	CountryValue        string   `json:"countryValue"`
	HasCondition        bool     `json:"hasCondition"`
	HasInternational    bool     `json:"hasInternational"`
	HasLevelOfInfluence bool     `json:"hasLevelOfInfluence"`
	Department          *string  `json:"department,omitempty"`
	DepartmentID        *string  `json:"departmentId,omitempty"`
	LedBy               []string `json:"ledBy,omitempty"`
}

type ApprovalRoles struct {
	ActivityName string   `json:"activityName"`
	Group        []*Group `json:"group"`
}

type AttachmentCategory struct {
	Description *string  `json:"description,omitempty"`
	URL         []string `json:"url"`
}

type AttachmentCategoryForEzClaim struct {
	Description *string  `json:"description,omitempty"`
	URL         []string `json:"url"`
}

type AttachmentCategoryForEzClaimData struct {
	Description  *string  `json:"description,omitempty"`
	URL          []string `json:"url"`
	IsValid      bool     `json:"isValid"`
	ErrorMessage *string  `json:"errorMessage,omitempty"`
}

type BankInformation struct {
	BankName          string `json:"bankName"`
	BankBranch        string `json:"bankBranch"`
	BankAccountNumber string `json:"bankAccountNumber"`
	BankAccountHolder string `json:"bankAccountHolder"`
}

type CommonResponse struct {
	Success bool    `json:"success"`
	Message *string `json:"message,omitempty"`
}

type EventAudit struct {
	AnswerJSON            string                `json:"answerJSON"`
	EventType             string                `json:"eventType"`
	EventName             string                `json:"eventName"`
	MeetingID             string                `json:"meetingID"`
	EventStatus           string                `json:"eventStatus"`
	ReportingDBStatus     string                `json:"reportingDBStatus"`
	IsExceptionalApproval bool                  `json:"isExceptionalApproval"`
	RequesterName         string                `json:"requesterName"`
	RequestUsername       string                `json:"requestUsername"`
	CompletionDate        string                `json:"completionDate"`
	VeevaSyncStatus       string                `json:"veevaSyncStatus"`
	VeevaSyncTime         string                `json:"veevaSyncTime"`
	VeevaSyncError        string                `json:"veevaSyncError"`
	VeevaReferenceID      string                `json:"veevaReferenceID"`
	EventLogs             []*EventAuditLog      `json:"eventLogs,omitempty"`
	EventApprovers        []*EventAuditApprover `json:"eventApprovers,omitempty"`
}

type EventAuditApprover struct {
	UserID          string `json:"userID"`
	Username        string `json:"username"`
	FullName        string `json:"fullName"`
	RoleName        string `json:"roleName"`
	Status          string `json:"status"`
	StatusUpdatedAt string `json:"statusUpdatedAt"`
	Active          bool   `json:"active"`
	IsApollo        bool   `json:"isApollo"`
}

type EventAuditLog struct {
	UpdatedAt string `json:"updatedAt"`
	Status    string `json:"status"`
	IsApollo  bool   `json:"isApollo"`
}

type HcpMealInput struct {
	Input []*MealInput `json:"input"`
}

type MealInput struct {
	MealType      string  `json:"mealType"`
	MealTotalCost float64 `json:"mealTotalCost"`
	MealNo        float64 `json:"mealNo"`
	ActivityID    int     `json:"activityId"`
}

type Mutation struct {
}

type NumberofEMPData struct {
	MeetingID  string `json:"meetingId"`
	EmpNumbers int    `json:"empNumbers"`
}

type Query struct {
}

type RequestorResponse struct {
	ApproverName        string `json:"approverName"`
	ApproverComment     string `json:"approverComment"`
	ApproverCommentDate int    `json:"approverCommentDate"`
}

type RoleTypes struct {
	RoleType *string `json:"RoleType,omitempty"`
}

type Signatory struct {
	ZPRepresentative       string `json:"ZPRepresentative"`
	ZPRepresentativeTitle  string `json:"ZPRepresentativeTitle"`
	HCORepresentative      string `json:"HCORepresentative"`
	HCORepresentativeTitle string `json:"HCORepresentativeTitle"`
	HCOAbbr                string `json:"HCOAbbr"`
}

type USDValueRequest struct {
	Currency      string  `json:"currency"`
	ExpenseAmount float64 `json:"expenseAmount"`
}

type USDValueResponse struct {
	Error            bool   `json:"error"`
	Message          string `json:"message"`
	ExpenseAmountUsd string `json:"expenseAmountUSD"`
}

type UpdateEventApprovalFromMaptoolInput struct {
	EventID        string  `json:"eventID"`
	Approver       string  `json:"approver"`
	ApprovalStatus string  `json:"approvalStatus"`
	Reason         *string `json:"reason,omitempty"`
}

type UpsertResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UserByApprovalRole struct {
	DepartmentName string           `json:"departmentName"`
	SequenceNo     int              `json:"sequenceNo"`
	DepartmentType string           `json:"departmentType"`
	DepartmentID   string           `json:"departmentID"`
	UserSelection  []*UserSelection `json:"userSelection"`
	HighLoi        *bool            `json:"highLoi,omitempty"`
}

type UserRoles struct {
	SearchItem *string `json:"searchItem,omitempty"`
}

type UserSelection struct {
	Description string  `json:"description"`
	Hint        string  `json:"hint"`
	Value       string  `json:"value"`
	RoleID      *string `json:"roleId,omitempty"`
}

type ValidateHcpMealResponse struct {
	Error              bool                 `json:"error"`
	ValidationMessages []*ValidationMessage `json:"ValidationMessages,omitempty"`
}

type ValidationMessage struct {
	Message string `json:"message"`
}

type Actions struct {
	ID     *string `json:"id,omitempty"`
	Action *string `json:"action,omitempty"`
}

type ActionsAnswers struct {
	ID     *string `json:"id,omitempty"`
	Action *string `json:"action,omitempty"`
}

type ActionsInput struct {
	ID     *int    `json:"id,omitempty"`
	Action *string `json:"action,omitempty"`
}

type ActivityInputs struct {
	Activities          []string `json:"activities"`
	TotalExpense        float64  `json:"totalExpense"`
	HasHCPEngagement    *bool    `json:"hasHCPEngagement,omitempty"`
	HasInternational    *bool    `json:"hasInternational,omitempty"`
	HasLevelOfInfluence *bool    `json:"hasLevelOfInfluence,omitempty"`
	FormAnswerID        *string  `json:"formAnswerID,omitempty"`
}

type AllAttendanceVeevaAccountDetailsData struct {
	ID                *string `json:"id,omitempty"`
	DateCreated       *string `json:"dateCreated,omitempty"`
	Specialty         *string `json:"specialty,omitempty"`
	Name              *string `json:"name,omitempty"`
	EventID           *string `json:"eventId,omitempty"`
	ContactType       *string `json:"contactType,omitempty"`
	AccountName       *string `json:"accountName,omitempty"`
	AccountveevaID    *string `json:"accountveevaId,omitempty"`
	AttendanceveevaID *string `json:"attendanceveevaId,omitempty"`
	Systemcode        *string `json:"systemcode,omitempty"`
	Organization      *string `json:"organization,omitempty"`
	IsHcp             *bool   `json:"isHcp,omitempty"`
	IsSpeaker         *bool   `json:"isSpeaker,omitempty"`
	FirstName         *string `json:"firstName,omitempty"`
	LastName          *string `json:"lastName,omitempty"`
}

type AllProductDetails struct {
	Description *string `json:"description,omitempty"`
	Value       *string `json:"value,omitempty"`
}

type AllVeevaAccountDetailsData struct {
	ID               *string  `json:"id,omitempty"`
	CustomerID       *string  `json:"customerId,omitempty"`
	DateCreated      *string  `json:"dateCreated,omitempty"`
	Method           *string  `json:"method,omitempty"`
	City             *string  `json:"city,omitempty"`
	Name             *string  `json:"name,omitempty"`
	SpDesc           *string  `json:"spDesc,omitempty"`
	Country          *string  `json:"country,omitempty"`
	Gender           *string  `json:"gender,omitempty"`
	IsActive         *bool    `json:"isActive,omitempty"`
	CMSLClass        *string  `json:"cMSLClass,omitempty"`
	IsDeleted        *bool    `json:"isDeleted,omitempty"`
	CustomerNo       *string  `json:"customerNo,omitempty"`
	ErrorMessage     *string  `json:"errorMessage,omitempty"`
	SpeakerWeight    *float64 `json:"speakerWeight,omitempty"`
	VeevareferenceID *string  `json:"veevareferenceId,omitempty"`
}

type ApolloApprovalData struct {
	ApprovalRole            *string `json:"approvalRole,omitempty"`
	ActionedBy              string  `json:"actionedBy"`
	ApproverActiveDirectory string  `json:"approverActiveDirectory"`
	ApproverSeq             int     `json:"approverSeq"`
	ActionedByRole          string  `json:"actionedByRole"`
	Status                  string  `json:"status"`
	DateCreated             int     `json:"dateCreated"`
	Comment                 string  `json:"comment"`
}

type ApolloApprovalTrailFormSubmissionResponse struct {
	Error    bool                  `json:"error"`
	Message  string                `json:"message"`
	Approval []*ApolloApprovalData `json:"approval"`
}

type ApolloFormAnswerIDInput struct {
	FormAnswerID string  `json:"formAnswerId"`
	EventID      *string `json:"eventId,omitempty"`
}

type ApprovalData struct {
	ApprovalRole   *string `json:"approvalRole,omitempty"`
	ActionedBy     string  `json:"actionedBy"`
	ActionedByRole string  `json:"actionedByRole"`
	Status         string  `json:"status"`
	DateCreated    int     `json:"dateCreated"`
	Comment        string  `json:"comment"`
}

type ApprovalLog struct {
	ID             string `json:"id"`
	FormAnsID      string `json:"formAnsId"`
	EventID        string `json:"eventId"`
	ActionedBy     string `json:"actionedBy"`
	ActionedByName string `json:"actionedByName"`
	StatusValue    string `json:"statusValue"`
	StatusTitle    string `json:"statusTitle"`
	DateCreated    int    `json:"dateCreated"`
	Comment        string `json:"comment"`
	UserRoleValue  string `json:"userRoleValue"`
	UserRoleTitle  string `json:"userRoleTitle"`
}

type ApprovalLogResponse struct {
	Error          bool           `json:"error"`
	Message        string         `json:"message"`
	GetApprovalLog []*ApprovalLog `json:"getApprovalLog"`
}

type ApprovalResponse struct {
	Error         bool             `json:"error"`
	Message       string           `json:"message"`
	ApprovalRoles []*ApprovalRoles `json:"approvalRoles"`
}

type ApprovalRole struct {
	UserID       string `json:"userId"`
	RoleID       string `json:"roleId"`
	SequenceNo   int    `json:"sequenceNo"`
	DepartmentID string `json:"departmentId"`
}

type ApprovalRoleSelectionForExceptionalInput struct {
	FormID        string          `json:"formId"`
	ApprovalRoles []*ApprovalRole `json:"approvalRoles"`
	IsApollo      *bool           `json:"isApollo,omitempty"`
}

type ApprovalRolesInput struct {
	ID                  *string  `json:"id,omitempty"`
	SearchItem          *string  `json:"searchItem,omitempty"`
	GroupType           *string  `json:"groupType,omitempty"`
	SequenceNo          *int     `json:"sequenceNo,omitempty"`
	MinLimit            *int     `json:"minLimit,omitempty"`
	MaxLimit            *int     `json:"maxLimit,omitempty"`
	Activity            *string  `json:"activity,omitempty"`
	HasCondition        *bool    `json:"hasCondition,omitempty"`
	HasInternational    *bool    `json:"hasInternational,omitempty"`
	HasLevelOfInfluence *bool    `json:"hasLevelOfInfluence,omitempty"`
	Department          *string  `json:"department,omitempty"`
	LedBy               []string `json:"ledBy,omitempty"`
}

type ApprovalRolesResponse struct {
	Error         bool                `json:"error"`
	Message       string              `json:"message"`
	ApprovalRoles []*ApprovalRoleData `json:"approvalRoles"`
}

type ApprovalRolesUserSelectionResponse struct {
	Error               bool                  `json:"error"`
	Message             string                `json:"message"`
	UsersByapprovalRole []*UserByApprovalRole `json:"usersByapprovalRole"`
}

type ApprovalTrailFormSubmissionResponse struct {
	Error    bool            `json:"error"`
	Message  string          `json:"message"`
	Approval []*ApprovalData `json:"approval"`
}

type ApproverData struct {
	ApproverID   string `json:"approverID"`
	ApproverName string `json:"approverName"`
	UserRole     string `json:"userRole"`
}

type ApproverformAnsID struct {
	FormAnsID string `json:"formAnsId"`
}

type ApproverformAnsResponse struct {
	Name           string  `json:"name"`
	ApprovalID     string  `json:"approvalID"`
	ApprovalValue  *string `json:"approvalValue,omitempty"`
	ApprovalTitle  *string `json:"approvalTitle,omitempty"`
	GroupValue     string  `json:"groupValue"`
	GroupTitle     string  `json:"groupTitle"`
	SequenceNo     int     `json:"sequenceNo"`
	DepartmentName *string `json:"departmentName,omitempty"`
	DepartmentID   *string `json:"departmentId,omitempty"`
}

type ApproversByType struct {
	UserID         string `json:"userId"`
	RoleID         string `json:"roleId"`
	SequenceNo     int    `json:"sequenceNo"`
	DepartmentID   string `json:"departmentId"`
	DepartmentName string `json:"departmentName"`
	UserName       string `json:"userName"`
}

type Attachment struct {
	Type       *string               `json:"type,omitempty"`
	Categories []*AttachmentCategory `json:"categories"`
}

type AttachmentCategoryResponse struct {
	Description *string   `json:"description,omitempty"`
	URL         []*string `json:"url"`
}

type AttachmentForEzClaim struct {
	Type       *string                         `json:"type,omitempty"`
	Categories []*AttachmentCategoryForEzClaim `json:"categories"`
}

type AttachmentForEzClaimData struct {
	Type       *string                             `json:"type,omitempty"`
	Categories []*AttachmentCategoryForEzClaimData `json:"categories"`
}

type AttachmentResponse struct {
	Type       *string                       `json:"type,omitempty"`
	Categories []*AttachmentCategoryResponse `json:"categories"`
}

type BasicInfoDateValidationRequest struct {
	ID                         *string                           `json:"id,omitempty"`
	IsChangeRequest            *bool                             `json:"isChangeRequest,omitempty"`
	ActivityEvent              *string                           `json:"activityEvent,omitempty"`
	EventType                  *string                           `json:"eventType,omitempty"`
	StartDate                  string                            `json:"startDate"`
	VirtualEventDetails        string                            `json:"virtualEventDetails"`
	EndUnixTime                string                            `json:"endUnixTime"`
	StartUnixTime              string                            `json:"startUnixTime"`
	BoardOfDirectoryValidation []*BoardOfDirectoryDataValidation `json:"boardOfDirectoryValidation,omitempty"`
}

type BasicInfoDateValidationResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type BoardOfDirectoryData struct {
	Name     *string `json:"name,omitempty"`
	Position *string `json:"position,omitempty"`
	Question *string `json:"question,omitempty"`
	Remarks  *string `json:"remarks,omitempty"`
}

type BoardOfDirectoryDataValidation struct {
	Name     *string `json:"name,omitempty"`
	Position *string `json:"position,omitempty"`
	Question *string `json:"question,omitempty"`
	Remarks  *string `json:"remarks,omitempty"`
}

type CalculateEventExpenseData struct {
	TotalEventExpenseInLocalCurrency string `json:"totalEventExpenseInLocalCurrency"`
	TotalEventExpenseInUsd           string `json:"totalEventExpenseInUsd"`
}

type CalculateExpenseData struct {
	TotalCostInLocalCurrency string `json:"totalCostInLocalCurrency"`
	TotalCostInUsd           string `json:"totalCostInUsd"`
	CostPerUnitInUsd         string `json:"costPerUnitInUsd"`
}

type CalculateExpenseRequest struct {
	NoOfUnits   int     `json:"noOfUnits"`
	CostPerUnit float64 `json:"costPerUnit"`
}

type CalculateExpenseResponse struct {
	Error                bool                  `json:"error"`
	Message              string                `json:"message"`
	CalculateExpenseData *CalculateExpenseData `json:"calculateExpenseData"`
}

type CalculateHcpExpenseData struct {
	TotalHcpExpenseInLocalCurrency string `json:"totalHcpExpenseInLocalCurrency"`
	TotalHcpExpenseInUsd           string `json:"totalHcpExpenseInUsd"`
}

type CalculateTotalHcpExpenseRequest struct {
	FormID string `json:"formId"`
}

type CalculatetotalEventExpenseRequest struct {
	FormID string `json:"formId"`
}

type ChangeApprovalRequest struct {
	FormAnswerID       string `json:"formAnswerId"`
	ChangeApprovalType string `json:"changeApprovalType"`
}

type ChangeRequestInput struct {
	FormAnswerID      string    `json:"formAnswerID"`
	ActivityStartDate *string   `json:"activityStartDate,omitempty"`
	ActivityEndDate   *string   `json:"activityEndDate,omitempty"`
	Duration          *string   `json:"duration,omitempty"`
	NoOfHcp           *string   `json:"noOfHCP,omitempty"`
	NoOfNonHcp        *string   `json:"noOfNonHCP,omitempty"`
	TypeOfHcp         *string   `json:"typeOfHCP,omitempty"`
	Roles             []*string `json:"roles,omitempty"`
	Venue             *string   `json:"venue,omitempty"`
	VirtualEvent      *bool     `json:"virtualEvent,omitempty"`
	GovtNonGovtHcp    *string   `json:"govtNonGovtHcp,omitempty"`
	Remarks           *string   `json:"remarks,omitempty"`
	SponsoredHcp      *string   `json:"sponsoredHcp,omitempty"`
	Speciality        *string   `json:"speciality,omitempty"`
	Hco               *string   `json:"hco,omitempty"`
}

type ChangeRequestOutput struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type ConversionAmountRequest struct {
	ProposedPayableHonorariumAmount float64 `json:"proposedPayableHonorariumAmount"`
}

type ConversionAmountResponse struct {
	Error                                          bool   `json:"error"`
	Message                                        string `json:"message"`
	ProposedPayableHonorariumAmountInLocalCurrency string `json:"proposedPayableHonorariumAmountInLocalCurrency"`
	ProposedPayableHonorariumAmountInUsd           string `json:"proposedPayableHonorariumAmountInUsd"`
}

type DelegateApproverInput struct {
	FormAnswerID string `json:"formAnswerID"`
	ApproverID   string `json:"approverID"`
	IsApollo     *bool  `json:"isApollo,omitempty"`
}

type DelegateApproverResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type DeleteDraftEventsInput struct {
	MeetingIds []string `json:"meetingIds"`
}

type DeleteDraftEventsResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type Dropdown struct {
	Description string `json:"description"`
	Value       string `json:"value"`
}

type EventIDData struct {
	Description string `json:"description"`
	Value       string `json:"value"`
}

type ExcelExtractTrailDataRequest struct {
	FormAnsID *string `json:"formAnsId,omitempty"`
	StartDate *string `json:"startDate,omitempty"`
	EndDate   *string `json:"endDate,omitempty"`
	IsExcel   *bool   `json:"isExcel,omitempty"`
}

type ExcelExtractTrailDataResponse struct {
	Error   bool    `json:"error"`
	Message string  `json:"message"`
	URL     *string `json:"url,omitempty"`
}

type ExcelUploadForSponsoredHcpEngagementRequest struct {
	FileName   string `json:"fileName"`
	URL        string `json:"url"`
	ActivityID int    `json:"activityId"`
}

type ExcelUploadForSponsoredHcpEngagementResponse struct {
	Error   bool                          `json:"error"`
	Message string                        `json:"message"`
	Data    []*SponsoredHcpEngagementData `json:"data,omitempty"`
}

type ExcelUploadRequestForEmployeeAttendance struct {
	FileName     string   `json:"fileName"`
	URL          []string `json:"url"`
	FormAnswerID string   `json:"formAnswerId"`
}

type ExcelUploadResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type ExcelValidationMessage struct {
	Row     int    `json:"row"`
	Message string `json:"message"`
}

type ExceptionalDetailsInput struct {
	ID                            string    `json:"id"`
	DetailsOfRequest              *string   `json:"detailsOfRequest,omitempty"`
	IhcpOtherLocalPolicies        *string   `json:"ihcpOtherLocalPolicies,omitempty"`
	MoreComments                  *string   `json:"moreComments,omitempty"`
	ExceptionalApprovalFileUpload []*string `json:"exceptionalApprovalFileUpload,omitempty"`
	ScopeOfExceptionalRequest     *string   `json:"scopeOfExceptionalRequest,omitempty"`
}

type ExceptionalDetailsResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FetchAllProductRequest struct {
	ProductOwnerName string `json:"productOwnerName"`
}

type FetchAllProductResponse struct {
	Error             bool                 `json:"error"`
	Message           string               `json:"message"`
	AllProductDetails []*AllProductDetails `json:"allProductDetails"`
}

type FetchapproverDataByFormAnsID struct {
	Error    bool                       `json:"error"`
	Message  string                     `json:"message"`
	Approval []*ApproverformAnsResponse `json:"approval"`
}

type FormAnswerAttachment struct {
	FormAnswerID string        `json:"formAnswerId"`
	Attachments  []*Attachment `json:"attachments"`
	IsApollo     *bool         `json:"isApollo,omitempty"`
}

type FormAnswerAttachmentForEzClaim struct {
	FormAnswerID string                  `json:"formAnswerId"`
	Attachments  []*AttachmentForEzClaim `json:"attachments"`
}

type FormAnswerAttachmentResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FormAnswerAttachmentResponseForEzClaim struct {
	FormAnswerID string                      `json:"formAnswerId"`
	Error        bool                        `json:"error"`
	Message      string                      `json:"message"`
	Attachments  []*AttachmentForEzClaimData `json:"attachments"`
}

type FormAnswerByEventIDInput struct {
	EventID string `json:"eventId"`
}

type FormAnswerByEventIDResponse struct {
	Error   bool         `json:"error"`
	Message string       `json:"message"`
	Data    *EventIDData `json:"data,omitempty"`
}

type FormAnswerIDInput struct {
	FormAnswerID string  `json:"formAnswerId"`
	EventID      *string `json:"eventId,omitempty"`
}

type FormAnswerInput struct {
	FormID  string  `json:"formId"`
	EventID *string `json:"eventId,omitempty"`
}

type FormAnswerInputForApollo struct {
	FormID  string  `json:"formId"`
	EventID *string `json:"eventId,omitempty"`
}

type FormAnswerInputForLinkedEvent struct {
	FormID  string  `json:"formId"`
	EventID *string `json:"eventId,omitempty"`
}

type FormAnswerResponse struct {
	Error                         bool                    `json:"error"`
	Message                       string                  `json:"message"`
	RequestorCountry              *string                 `json:"requestorCountry,omitempty"`
	RequestorCurrency             *string                 `json:"requestorCurrency,omitempty"`
	DateCreated                   *int                    `json:"dateCreated,omitempty"`
	MeetingID                     *string                 `json:"meetingId,omitempty"`
	Answers                       []*FormTabsAnswer       `json:"answers"`
	Attachments                   []*AttachmentResponse   `json:"attachments"`
	IsExceptionalApproval         bool                    `json:"isExceptionalApproval"`
	DetailsOfRequest              *string                 `json:"detailsOfRequest,omitempty"`
	IhcpOtherLocalPolicies        *string                 `json:"ihcpOtherLocalPolicies,omitempty"`
	MoreComments                  *string                 `json:"moreComments,omitempty"`
	ExceptionalApprovalFileUpload []*string               `json:"exceptionalApprovalFileUpload,omitempty"`
	ScopeOfExceptionalRequest     *string                 `json:"scopeOfExceptionalRequest,omitempty"`
	IsChangeRequest               *bool                   `json:"isChangeRequest,omitempty"`
	ChangeRequestSummary          *string                 `json:"changeRequestSummary,omitempty"`
	ChangeRequestType             *string                 `json:"changeRequestType,omitempty"`
	IsRecall                      *bool                   `json:"isRecall,omitempty"`
	RecallSummary                 *string                 `json:"recallSummary,omitempty"`
	ConversionRate                *float64                `json:"ConversionRate,omitempty"`
	LineSecretaryReturnSummary    *string                 `json:"lineSecretaryReturnSummary,omitempty"`
	BoardOfDirectory              []*BoardOfDirectoryData `json:"boardOfDirectory,omitempty"`
	BankInformation               *BankInformation        `json:"bankInformation,omitempty"`
	Signatory                     *Signatory              `json:"signatory,omitempty"`
}

type FormAnswerResponseForApollo struct {
	Error                         bool                    `json:"error"`
	Message                       string                  `json:"message"`
	RequestorCountry              *string                 `json:"requestorCountry,omitempty"`
	RequestorCurrency             *string                 `json:"requestorCurrency,omitempty"`
	DateCreated                   *int                    `json:"dateCreated,omitempty"`
	MeetingID                     *string                 `json:"meetingId,omitempty"`
	Answers                       []*FormTabsAnswer       `json:"answers"`
	Attachments                   []*AttachmentResponse   `json:"attachments"`
	IsExceptionalApproval         bool                    `json:"isExceptionalApproval"`
	DetailsOfRequest              *string                 `json:"detailsOfRequest,omitempty"`
	IhcpOtherLocalPolicies        *string                 `json:"ihcpOtherLocalPolicies,omitempty"`
	MoreComments                  *string                 `json:"moreComments,omitempty"`
	ExceptionalApprovalFileUpload []*string               `json:"exceptionalApprovalFileUpload,omitempty"`
	ScopeOfExceptionalRequest     *string                 `json:"scopeOfExceptionalRequest,omitempty"`
	IsChangeRequest               *bool                   `json:"isChangeRequest,omitempty"`
	ChangeRequestSummary          *string                 `json:"changeRequestSummary,omitempty"`
	ChangeRequestType             *string                 `json:"changeRequestType,omitempty"`
	IsRecall                      *bool                   `json:"isRecall,omitempty"`
	RecallSummary                 *string                 `json:"recallSummary,omitempty"`
	LineSecretaryReturnSummary    *string                 `json:"lineSecretaryReturnSummary,omitempty"`
	BoardOfDirectory              []*BoardOfDirectoryData `json:"boardOfDirectory,omitempty"`
	TypeOfEngagements             []*string               `json:"typeOfEngagements,omitempty"`
	ConversionRate                *float64                `json:"ConversionRate,omitempty"`
	OwnerActiveDirectory          *string                 `json:"ownerActiveDirectory,omitempty"`
	EventStatusTitle              *string                 `json:"eventStatusTitle,omitempty"`
	EventStatusValue              *string                 `json:"eventStatusValue,omitempty"`
	RequestorName                 *string                 `json:"requestorName,omitempty"`
}

type FormAnswerResponseForLinkedEvent struct {
	Error                         bool                    `json:"error"`
	Message                       string                  `json:"message"`
	RequestorCountry              *string                 `json:"requestorCountry,omitempty"`
	RequestorCurrency             *string                 `json:"requestorCurrency,omitempty"`
	DateCreated                   *int                    `json:"dateCreated,omitempty"`
	MeetingID                     *string                 `json:"meetingId,omitempty"`
	Answers                       []*FormTabsAnswer       `json:"answers"`
	Attachments                   []*AttachmentResponse   `json:"attachments"`
	IsExceptionalApproval         bool                    `json:"isExceptionalApproval"`
	DetailsOfRequest              *string                 `json:"detailsOfRequest,omitempty"`
	IhcpOtherLocalPolicies        *string                 `json:"ihcpOtherLocalPolicies,omitempty"`
	MoreComments                  *string                 `json:"moreComments,omitempty"`
	ExceptionalApprovalFileUpload []*string               `json:"exceptionalApprovalFileUpload,omitempty"`
	ScopeOfExceptionalRequest     *string                 `json:"scopeOfExceptionalRequest,omitempty"`
	IsChangeRequest               *bool                   `json:"isChangeRequest,omitempty"`
	ChangeRequestSummary          *string                 `json:"changeRequestSummary,omitempty"`
	IsRecall                      *bool                   `json:"isRecall,omitempty"`
	RecallSummary                 *string                 `json:"recallSummary,omitempty"`
	EventRequestorName            *string                 `json:"eventRequestorName,omitempty"`
	BoardOfDirectory              []*BoardOfDirectoryData `json:"boardOfDirectory,omitempty"`
	ConversionRate                *float64                `json:"ConversionRate,omitempty"`
}

type FormAnswerSubmissionRequest struct {
	ID                   *string                `json:"id,omitempty"`
	IsDraft              bool                   `json:"isDraft"`
	TotalCost            float64                `json:"totalCost"`
	ParentEventID        *string                `json:"parentEventId,omitempty"`
	Answers              []*FormTabsAnswerInput `json:"answers"`
	IsChangeRequest      *bool                  `json:"isChangeRequest,omitempty"`
	ChangeRequestSummary *string                `json:"changeRequestSummary,omitempty"`
	IsApollo             *bool                  `json:"isApollo,omitempty"`
}

type FormAnswerSubmissionResponse struct {
	Error      bool   `json:"error"`
	Message    string `json:"message"`
	FormAnsID  string `json:"formAnsId"`
	FormAnsSeq string `json:"formAnsSeq"`
}

type FormAnswerforcancel struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FormApprovalInput struct {
	ID                    string  `json:"id"`
	Action                string  `json:"action"`
	Comment               *string `json:"comment,omitempty"`
	IsExceptionalApprover *bool   `json:"isExceptionalApprover,omitempty"`
	IsCompliance          *bool   `json:"isCompliance,omitempty"`
	IsApollo              *bool   `json:"isApollo,omitempty"`
}

type FormApprovalResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FormSectionAnswer struct {
	ID         *string                   `json:"id,omitempty"`
	Title      *string                   `json:"title,omitempty"`
	SequenceNo *int                      `json:"sequenceNo,omitempty"`
	Form       *FormSectionContentAnswer `json:"form,omitempty"`
	ChildForm  *FormSectionContentAnswer `json:"childForm,omitempty"`
}

type FormSectionAnswerInput struct {
	ID         *string                        `json:"id,omitempty"`
	Title      *string                        `json:"title,omitempty"`
	SequenceNo *int                           `json:"sequenceNo,omitempty"`
	Form       *FormSectionContentAnswerInput `json:"form,omitempty"`
	ChildForm  *FormSectionContentAnswerInput `json:"childForm,omitempty"`
}

type FormSectionContent struct {
	ID         *string          `json:"id,omitempty"`
	Title      *string          `json:"title,omitempty"`
	SequenceNo *int             `json:"sequenceNo,omitempty"`
	Group      []*QuestionGroup `json:"group,omitempty"`
}

type FormSectionContentAnswer struct {
	ID          *string                 `json:"id,omitempty"`
	Title       *string                 `json:"title,omitempty"`
	SequenceNo  *int                    `json:"sequenceNo,omitempty"`
	GroupAnswer []*QuestionGroupAnswers `json:"groupAnswer,omitempty"`
}

type FormSectionContentAnswerInput struct {
	ID          *string                `json:"id,omitempty"`
	Title       *string                `json:"title,omitempty"`
	SequenceNo  *int                   `json:"sequenceNo,omitempty"`
	GroupAnswer []*QuestionGroupAnswer `json:"groupAnswer,omitempty"`
}

type FormSectionContentInput struct {
	Title      *string               `json:"title,omitempty"`
	SequenceNo *int                  `json:"sequenceNo,omitempty"`
	Group      []*QuestionGroupInput `json:"group,omitempty"`
}

type FormSectionInput struct {
	Title      *string                  `json:"title,omitempty"`
	SequenceNo *int                     `json:"sequenceNo,omitempty"`
	Form       *FormSectionContentInput `json:"form,omitempty"`
	ChildForm  *FormSectionContentInput `json:"childForm,omitempty"`
}

type FormSections struct {
	ID         *string             `json:"id,omitempty"`
	Title      *string             `json:"title,omitempty"`
	SequenceNo *int                `json:"sequenceNo,omitempty"`
	Form       *FormSectionContent `json:"form,omitempty"`
	ChildForm  *FormSectionContent `json:"childForm,omitempty"`
}

type FormSubmissionActivity struct {
	FormAnsID             string `json:"formAnsId"`
	StartDate             string `json:"startDate"`
	EndDate               string `json:"endDate"`
	Duration              string `json:"duration"`
	IsExceptionalApprover bool   `json:"isExceptionalApprover"`
}

type FormSubmissionApprovalForAllEventResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FormSubmissionResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FormTabs struct {
	ID         string          `json:"id"`
	Title      string          `json:"title"`
	SequenceNo int             `json:"sequenceNo"`
	Sections   []*FormSections `json:"sections,omitempty"`
}

type FormTabsAnswer struct {
	ID            *string              `json:"id,omitempty"`
	Title         *string              `json:"title,omitempty"`
	SequenceNo    *int                 `json:"sequenceNo,omitempty"`
	SectionAnswer []*FormSectionAnswer `json:"sectionAnswer"`
}

type FormTabsAnswerInput struct {
	ID            *string                   `json:"id,omitempty"`
	Title         *string                   `json:"title,omitempty"`
	SequenceNo    *int                      `json:"sequenceNo,omitempty"`
	SectionAnswer []*FormSectionAnswerInput `json:"sectionAnswer"`
}

type FormTabsInput struct {
	Title      *string             `json:"title,omitempty"`
	SequenceNo *int                `json:"sequenceNo,omitempty"`
	Sections   []*FormSectionInput `json:"sections,omitempty"`
}

type FormTemplateResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Design  []*FormTabs `json:"design"`
}

type FormTemplateSubmitRequest struct {
	Design []*FormTabsInput `json:"design"`
}

type FormTemplateSubmitResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type GetActivitiesAndEventSelection struct {
	Status            []*Dropdown `json:"status,omitempty"`
	ActivityType      []*Dropdown `json:"activityType,omitempty"`
	EventType         []*Dropdown `json:"eventType,omitempty"`
	Team              []*Dropdown `json:"team,omitempty"`
	Product           []*Dropdown `json:"product,omitempty"`
	ProductOwner      []*Dropdown `json:"productOwner,omitempty"`
	RequestorList     []*Dropdown `json:"requestorList,omitempty"`
	LineSecretaryList []*Dropdown `json:"lineSecretaryList,omitempty"`
}

type GetActivitiesAndEventSelectionAllDropDownValues struct {
	MeetingMode              []*Dropdown `json:"meetingMode,omitempty"`
	Country                  []*Dropdown `json:"country,omitempty"`
	ExpertLevelInternational []*Dropdown `json:"expertLevelInternational,omitempty"`
	ExpertLevel              []*Dropdown `json:"expertLevel,omitempty"`
	Role                     []*Dropdown `json:"role,omitempty"`
	TypeOfEventExpenses      []*Dropdown `json:"typeOfEventExpenses,omitempty"`
}

type GetActivitiesAndEventSelectionAllDropDownValuesResponse struct {
	Error                           bool                                             `json:"error"`
	Message                         string                                           `json:"message"`
	GetActivitiesAndEventsSelection *GetActivitiesAndEventSelectionAllDropDownValues `json:"getActivitiesAndEventsSelection,omitempty"`
}

type GetActivitiesAndEventsSelectionResponse struct {
	Error                           bool                            `json:"error"`
	Message                         string                          `json:"message"`
	GetActivitiesAndEventsSelection *GetActivitiesAndEventSelection `json:"getActivitiesAndEventsSelection"`
}

type GetAllEventIDListDetails struct {
	Value       string `json:"value"`
	Description string `json:"description"`
}

type GetAllEventIDListRequest struct {
	SearchItem *string `json:"searchItem,omitempty"`
	FormID     *string `json:"formId,omitempty"`
}

type GetAllEventIDListResponse struct {
	Error                    bool                        `json:"error"`
	Message                  string                      `json:"message"`
	GetAllEventIDListDetails []*GetAllEventIDListDetails `json:"getAllEventIdListDetails"`
}

type GetAttachmentZipInput struct {
	Attachment []string `json:"attachment,omitempty"`
	EventID    string   `json:"eventId"`
}

type GetAttachmentZipResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	URL     string `json:"url"`
}

type GetDurationFormatRequest struct {
	StartDate int `json:"startDate"`
	EndDate   int `json:"endDate"`
}

type GetDurationFormatResponse struct {
	Error          bool   `json:"error"`
	Message        string `json:"message"`
	DurationFormat string `json:"durationFormat"`
}

type GetEmployeeManagementFilterDropdown struct {
	ActionBy                 []*Dropdown `json:"actionBy,omitempty"`
	ActionType               []*Dropdown `json:"actionType,omitempty"`
	RequestorActiveDirectory []*Dropdown `json:"requestorActiveDirectory,omitempty"`
}

type GetEmployeeManagementFilterResponse struct {
	Error                               bool                                 `json:"error"`
	Message                             string                               `json:"message"`
	GetEmployeeManagementFilterDropdown *GetEmployeeManagementFilterDropdown `json:"getEmployeeManagementFilterDropdown"`
}

type GetFmvAuditlogsData struct {
	Description string `json:"description"`
	MaxLimit    string `json:"maxLimit"`
	Date        string `json:"date"`
	Currency    string `json:"currency"`
	Status      string `json:"status"`
	CreatedBy   string `json:"createdBy"`
}

type GetFmvAuditlogsRequest struct {
	Limit  *int `json:"limit,omitempty"`
	PageNo *int `json:"pageNo,omitempty"`
}

type GetFmvAuditlogsResponse struct {
	Error               bool                   `json:"error"`
	Message             string                 `json:"message"`
	DataCount           int                    `json:"dataCount"`
	GetFmvAuditlogsData []*GetFmvAuditlogsData `json:"getFmvAuditlogsData"`
}

type GetNotificationStatus struct {
	ID           string `json:"id"`
	FormAnswerID string `json:"formAnswerID"`
	Message      string `json:"message"`
	IsRead       bool   `json:"isRead"`
	DateCreated  int    `json:"dateCreated"`
}

type GetNotificationStatusForUserResponse struct {
	Error                 bool                     `json:"error"`
	Message               string                   `json:"message"`
	GetNotificationStatus []*GetNotificationStatus `json:"getNotificationStatus"`
}

type GetPDFBlobURLRequest struct {
	FormID               string  `json:"FormID"`
	CountryCurrency      string  `json:"CountryCurrency"`
	UsdCurrency          string  `json:"UsdCurrency"`
	ConvertRate          float64 `json:"convertRate"`
	StartDate            string  `json:"startDate"`
	EndDate              string  `json:"endDate"`
	HcpTotalExpense      string  `json:"hcpTotalExpense"`
	EventTotalExpense    string  `json:"eventTotalExpense"`
	TotalCost            string  `json:"totalCost"`
	MeetingID            string  `json:"meetingId"`
	HcpTotalExpenseUsd   string  `json:"hcpTotalExpenseUSD"`
	EventTotalExpenseUsd string  `json:"eventTotalExpenseUSD"`
	Submission           string  `json:"submission"`
}

type GetPDFBlobURLResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	URL     string `json:"url"`
}

type GetRequestorForCountryInput struct {
	SearchItem *string `json:"searchItem,omitempty"`
}

type GetRequestorForCountryResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Data    []*Dropdown `json:"data,omitempty"`
}

type GetSponsorshipAgreementPDFBlobURLRequest struct {
	FormID          string   `json:"formID"`
	CountryCurrency *string  `json:"countryCurrency,omitempty"`
	UsdCurrency     *string  `json:"usdCurrency,omitempty"`
	ConvertRate     *float64 `json:"convertRate,omitempty"`
}

type GetTypeApprovalListInput struct {
	FormAnswerID string `json:"formAnswerID"`
	Type         string `json:"type"`
}

type GetTypeApprovalListResponse struct {
	Error       bool               `json:"error"`
	Message     string             `json:"message"`
	UsersByType []*ApproversByType `json:"usersByType"`
}

type GetUserRoles struct {
	ID    string `json:"id"`
	Title string `json:"title"`
	Value string `json:"value"`
}

type GetUserRolesByapprover struct {
	Error   bool            `json:"error"`
	Message string          `json:"message"`
	Data    []*GetUserRoles `json:"data"`
}

type Group struct {
	GroupName string   `json:"groupName"`
	Roles     []*Roles `json:"roles"`
}

type GroupActions struct {
	GroupID *string `json:"groupId,omitempty"`
	Action  *string `json:"action,omitempty"`
}

type GroupActionsInput struct {
	GroupID *int    `json:"groupId,omitempty"`
	Action  *string `json:"action,omitempty"`
}

type GroupAnswer struct {
	ID         *string        `json:"id,omitempty"`
	Title      *string        `json:"title,omitempty"`
	SequenceNo *int           `json:"sequenceNo,omitempty"`
	Source     *string        `json:"source,omitempty"`
	Answers    []*InputAnswer `json:"answers,omitempty"`
}

type GroupAnswers struct {
	ID         *string         `json:"id,omitempty"`
	Title      *string         `json:"title,omitempty"`
	SequenceNo *int            `json:"sequenceNo,omitempty"`
	Source     *string         `json:"source,omitempty"`
	Answers    []*InputAnswers `json:"answers,omitempty"`
}

type GroupRuleInput struct {
	Value   *string              `json:"value,omitempty"`
	Actions []*GroupActionsInput `json:"actions,omitempty"`
}

type GroupRules struct {
	Value   *string         `json:"value,omitempty"`
	Actions []*GroupActions `json:"actions,omitempty"`
}

type Hcp struct {
	Country        *string `json:"Country,omitempty"`
	HCPName        *string `json:"HCPName,omitempty"`
	HCPVeevaID     *string `json:"HCPVeevaId,omitempty"`
	TerritoryID    *string `json:"TerritoryId,omitempty"`
	HCOName        *string `json:"HCOName,omitempty"`
	HCOId          *string `json:"HCOId,omitempty"`
	Province       *string `json:"Province,omitempty"`
	City           *string `json:"City,omitempty"`
	Specialty      *string `json:"Specialty,omitempty"`
	ProductName    *string `json:"ProductName,omitempty"`
	Classification *string `json:"Classification,omitempty"`
}

type HcpEngagementValidationData struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	VeevaID      string `json:"veevaID"`
	Specialty    string `json:"specialty"`
	Organization string `json:"organization"`
	Valid        bool   `json:"valid"`
	ErrorMessage string `json:"errorMessage"`
}

type HcpEngagementValidationRequest struct {
	Hcps []*HcpsData `json:"hcps"`
}

type HcpEngagementValidationResponse struct {
	Error                       bool                           `json:"error"`
	Message                     string                         `json:"message"`
	HcpEngagementValidationData []*HcpEngagementValidationData `json:"hcpEngagementValidationData"`
}

type HcpLoiRequest struct {
	Answer1 string `json:"answer1"`
	Answer2 string `json:"answer2"`
	Answer3 string `json:"answer3"`
	Answer4 string `json:"answer4"`
	Answer5 string `json:"answer5"`
}

type HcpLoiResponse struct {
	Error            bool   `json:"error"`
	Message          string `json:"message"`
	LevelOfInfluence string `json:"levelOfInfluence"`
}

type HcpSpeakerRequest struct {
	SpeakerName string `json:"speakerName"`
}

type HcpSpeakerResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type HcpsData struct {
	VeevaID      string `json:"veevaID"`
	Name         string `json:"name"`
	Specialty    string `json:"specialty"`
	Organization string `json:"organization"`
}

type HonorariumDetails struct {
	ExpertLevel              *string      `json:"expertLevel,omitempty"`
	PreparationTime          *int         `json:"preparationTime,omitempty"`
	ServiceTime              *int         `json:"serviceTime,omitempty"`
	Roles                    []*RoleTypes `json:"roles,omitempty"`
	HcpType                  *string      `json:"hcpType,omitempty"`
	NoOfAttendees            *int         `json:"noOfAttendees,omitempty"`
	BaseType                 *string      `json:"baseType,omitempty"`
	CustomerID               *string      `json:"customerID,omitempty"`
	HourlyRate               *int         `json:"hourlyRate,omitempty"`
	ExpertLevelInternational *string      `json:"expertLevelInternational,omitempty"`
	MoreThanTwoMeeting       *bool        `json:"moreThanTwoMeeting,omitempty"`
}

type InputAnswer struct {
	ID         *string          `json:"id,omitempty"`
	Title      *string          `json:"title,omitempty"`
	Type       *string          `json:"type,omitempty"`
	SequenceNo *int             `json:"sequenceNo,omitempty"`
	Source     *string          `json:"source,omitempty"`
	Values     []*ValuesAnswers `json:"values,omitempty"`
}

type InputAnswers struct {
	ID         *string         `json:"id,omitempty"`
	Title      *string         `json:"title,omitempty"`
	Type       *string         `json:"type,omitempty"`
	SequenceNo *int            `json:"sequenceNo,omitempty"`
	Source     *string         `json:"source,omitempty"`
	Values     []*ValuesAnswer `json:"values,omitempty"`
}

type InputGroup struct {
	GroupID    *string       `json:"groupId,omitempty"`
	SequenceNo *int          `json:"sequenceNo,omitempty"`
	Title      *string       `json:"title,omitempty"`
	GroupRules []*GroupRules `json:"groupRules,omitempty"`
	Hidden     *bool         `json:"hidden,omitempty"`
	Inputs     []*Inputs     `json:"inputs,omitempty"`
}

type InputGroupInput struct {
	GroupID    *int              `json:"groupId,omitempty"`
	SequenceNo *int              `json:"sequenceNo,omitempty"`
	Title      *string           `json:"title,omitempty"`
	GroupRules []*GroupRuleInput `json:"groupRules,omitempty"`
	Hidden     *bool             `json:"hidden,omitempty"`
	Inputs     []*QuestionInput  `json:"inputs,omitempty"`
}

type Inputs struct {
	ID             *string            `json:"id,omitempty"`
	Hidden         *bool              `json:"hidden,omitempty"`
	Rules          []*Rules           `json:"rules,omitempty"`
	Title          *string            `json:"title,omitempty"`
	Type           *string            `json:"type,omitempty"`
	ReadOnly       *bool              `json:"readOnly,omitempty"`
	SequenceNo     *int               `json:"sequenceNo,omitempty"`
	Source         *string            `json:"source,omitempty"`
	Operation      *string            `json:"operation,omitempty"`
	Values         []*Values          `json:"values,omitempty"`
	Validatesource *string            `json:"validatesource,omitempty"`
	Validations    []*ValidationInput `json:"validations,omitempty"`
}

type LevelOfInfluenceValidationRequest struct {
	LevelOfInfluence  string `json:"levelOfInfluence"`
	LoiquestionAnswer string `json:"loiquestionAnswer"`
}

type LevelOfInfluenceValidationResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type Limit struct {
	Limit                   *int         `json:"limit,omitempty"`
	PageNo                  *int         `json:"pageNo,omitempty"`
	SearchItem              *string      `json:"searchItem,omitempty"`
	SortItem                []*SortItems `json:"sortItem,omitempty"`
	TypeOfActivityFilter    []int        `json:"typeOfActivityFilter,omitempty"`
	TypeOfEventFilter       []int        `json:"typeOfEventFilter,omitempty"`
	StatusFilter            []string     `json:"statusFilter,omitempty"`
	TotalCostRange          []int        `json:"totalCostRange,omitempty"`
	DateFilter              *string      `json:"dateFilter,omitempty"`
	DateFilterStart         *string      `json:"dateFilterStart,omitempty"`
	DateFilterEnd           *string      `json:"dateFilterEnd,omitempty"`
	DateCreatedFilterStart  *string      `json:"dateCreatedFilterStart,omitempty"`
	DateCreatedFilterEnd    *string      `json:"dateCreatedFilterEnd,omitempty"`
	ExceptionalStatusFilter []string     `json:"exceptionalStatusFilter,omitempty"`
}

type MaxHonorariumResponse struct {
	Error         bool    `json:"error"`
	Message       string  `json:"message"`
	MaxHonorarium float64 `json:"maxHonorarium"`
}

type NotificationKeyLimit struct {
	Limit  *int    `json:"limit,omitempty"`
	LastID *string `json:"lastID,omitempty"`
}

type OwnerDataRequest struct {
	UserID string `json:"userId"`
}

type OwnerDataResponse struct {
	Error                bool   `json:"error"`
	Message              string `json:"message"`
	OwnerActiveDirectory string `json:"ownerActiveDirectory"`
	OwnerFullName        string `json:"ownerFullName"`
}

type PendingApproval struct {
	ID                    string   `json:"id"`
	TypeOfActivity        []string `json:"typeOfActivity"`
	TypeOfEvent           []string `json:"typeOfEvent"`
	SubmitedDate          int      `json:"submitedDate"`
	RequestorName         string   `json:"requestorName"`
	ApprovalStatus        string   `json:"approvalStatus"`
	ApprovalStatusValue   string   `json:"approvalStatusValue"`
	TotalCost             float64  `json:"totalCost"`
	EventStartDate        int      `json:"eventStartDate"`
	EventEndDate          int      `json:"eventEndDate"`
	DepartmentName        string   `json:"departmentName"`
	ActivityName          string   `json:"activityName"`
	ProductName           string   `json:"productName"`
	CountryTitle          string   `json:"countryTitle"`
	CountryValue          string   `json:"countryValue"`
	CurrencyTitle         string   `json:"currencyTitle"`
	CurrencyValue         string   `json:"currencyValue"`
	EventID               string   `json:"eventId"`
	IsExceptionalApprover *bool    `json:"isExceptionalApprover,omitempty"`
	ChangeRequestSummary  *string  `json:"changeRequestSummary,omitempty"`
	IsApproverAccess      *bool    `json:"isApproverAccess,omitempty"`
	IsRecall              *bool    `json:"isRecall,omitempty"`
}

type PendingApprovalResponse struct {
	Error        bool               `json:"error"`
	Message      string             `json:"message"`
	MaxTotalCost float64            `json:"maxTotalCost"`
	TotalCount   int                `json:"totalCount"`
	LastPage     *int               `json:"lastPage,omitempty"`
	Data         []*PendingApproval `json:"data"`
}

type QuestionGroup struct {
	ID         *string       `json:"id,omitempty"`
	SequenceNo *int          `json:"sequenceNo,omitempty"`
	Title      *string       `json:"title,omitempty"`
	Inputs     []*InputGroup `json:"inputs,omitempty"`
}

type QuestionGroupAnswer struct {
	ID              *string        `json:"id,omitempty"`
	SequenceNo      *int           `json:"sequenceNo,omitempty"`
	Title           *string        `json:"title,omitempty"`
	QuestionAnswers []*GroupAnswer `json:"questionAnswers,omitempty"`
}

type QuestionGroupAnswers struct {
	ID              *string         `json:"id,omitempty"`
	SequenceNo      *int            `json:"sequenceNo,omitempty"`
	Title           *string         `json:"title,omitempty"`
	QuestionAnswers []*GroupAnswers `json:"questionAnswers,omitempty"`
}

type QuestionGroupInput struct {
	SequenceNo *int               `json:"sequenceNo,omitempty"`
	Title      *string            `json:"title,omitempty"`
	Questions  []*InputGroupInput `json:"questions,omitempty"`
}

type QuestionInput struct {
	ID         *int          `json:"id,omitempty"`
	Title      *string       `json:"title,omitempty"`
	Type       *string       `json:"type,omitempty"`
	SequenceNo *int          `json:"sequenceNo,omitempty"`
	Rules      []*RuleInput  `json:"rules,omitempty"`
	Source     *string       `json:"source,omitempty"`
	Hidden     *bool         `json:"hidden,omitempty"`
	ReadOnly   *bool         `json:"readOnly,omitempty"`
	Operation  *string       `json:"operation,omitempty"`
	Values     []*ValueInput `json:"values,omitempty"`
}

type RecallFormApprovalInput struct {
	FormAnsID string  `json:"formAnsId"`
	Action    string  `json:"action"`
	Comment   *string `json:"comment,omitempty"`
	IsApollo  *bool   `json:"isApollo,omitempty"`
}

type RecallFormApprovalResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type RequestAllAttendanceVeevaAccountDetails struct {
	Limit           *int    `json:"limit,omitempty"`
	Page            *int    `json:"page,omitempty"`
	FormAnswerID    *string `json:"formAnswerId,omitempty"`
	Name            *string `json:"name,omitempty"`
	DateCreatedFrom *string `json:"dateCreatedFrom,omitempty"`
	DateCreatedTo   *string `json:"dateCreatedTo,omitempty"`
	EventID         *string `json:"eventId,omitempty"`
}

type RequestAllVeevaAccountDetails struct {
	Limit *int `json:"limit,omitempty"`
	Page  *int `json:"page,omitempty"`
}

type RequesterInput struct {
	Limit                   *int         `json:"limit,omitempty"`
	PageNo                  *int         `json:"pageNo,omitempty"`
	Status                  *string      `json:"status,omitempty"`
	IsAdmin                 *bool        `json:"isAdmin,omitempty"`
	IsExcel                 *bool        `json:"isExcel,omitempty"`
	ExceptionalStatusFilter []string     `json:"exceptionalStatusFilter,omitempty"`
	SearchItem              *string      `json:"searchItem,omitempty"`
	SortItem                []*SortItems `json:"sortItem,omitempty"`
	TypeOfActivityFilter    []int        `json:"typeOfActivityFilter,omitempty"`
	TypeOfEventFilter       []int        `json:"typeOfEventFilter,omitempty"`
	StatusFilter            []string     `json:"statusFilter,omitempty"`
	TotalCostRange          []int        `json:"totalCostRange,omitempty"`
	DateFilterStart         *string      `json:"dateFilterStart,omitempty"`
	DateFilterEnd           *string      `json:"dateFilterEnd,omitempty"`
	Product                 *string      `json:"product,omitempty"`
	Team                    *string      `json:"team,omitempty"`
	Requestor               *string      `json:"requestor,omitempty"`
	ProductOwner            *string      `json:"productOwner,omitempty"`
	DateCreatedFilterStart  *string      `json:"dateCreatedFilterStart,omitempty"`
	DateCreatedFilterEnd    *string      `json:"dateCreatedFilterEnd,omitempty"`
	RecallActionType        *string      `json:"recallActionType,omitempty"`
}

type RequesterSubmittedFormAnswerList struct {
	RequestorName            string               `json:"requestorName"`
	ID                       string               `json:"id"`
	RequestorActiveDirectory string               `json:"requestorActiveDirectory"`
	TypeOfActivity           []string             `json:"typeOfActivity"`
	TypeOfEvent              []string             `json:"typeOfEvent"`
	SubmitedDate             int                  `json:"submitedDate"`
	ApprovalStatus           string               `json:"approvalStatus"`
	ApprovalStatusValue      string               `json:"approvalStatusValue"`
	TotalCost                float64              `json:"totalCost"`
	EventStartDate           int                  `json:"eventStartDate"`
	EventEndDate             int                  `json:"eventEndDate"`
	DepartmentName           string               `json:"departmentName"`
	ActivityName             string               `json:"activityName"`
	ProductName              string               `json:"productName"`
	CountryTitle             string               `json:"countryTitle"`
	CountryValue             string               `json:"countryValue"`
	CurrencyTitle            string               `json:"currencyTitle"`
	CurrencyValue            string               `json:"currencyValue"`
	EventID                  string               `json:"eventId"`
	Limit                    *int                 `json:"limit,omitempty"`
	PageNo                   *int                 `json:"pageNo,omitempty"`
	IsOwner                  *bool                `json:"isOwner,omitempty"`
	IsExceptionalApprover    *bool                `json:"isExceptionalApprover,omitempty"`
	ProductOwner             *string              `json:"productOwner,omitempty"`
	IsRecallAccess           *bool                `json:"isRecallAccess,omitempty"`
	Response                 []*RequestorResponse `json:"response"`
}

type RequesterSubmittedFormAnswerListResponse struct {
	Error        bool                                `json:"error"`
	Message      string                              `json:"message"`
	URL          string                              `json:"url"`
	MaxTotalCost float64                             `json:"maxTotalCost"`
	TotalCount   int                                 `json:"totalCount"`
	LastPage     *int                                `json:"lastPage,omitempty"`
	Data         []*RequesterSubmittedFormAnswerList `json:"data"`
}

type RequestorActionInput struct {
	ID       string  `json:"id"`
	Comment  *string `json:"comment,omitempty"`
	IsApollo *bool   `json:"isApollo,omitempty"`
}

type ResponseAllAttendanceVeevaAccountDetails struct {
	Error   bool                                    `json:"error"`
	Message string                                  `json:"message"`
	Data    []*AllAttendanceVeevaAccountDetailsData `json:"data,omitempty"`
}

type ResponseAllVeevaAccountDetails struct {
	Error   bool                          `json:"error"`
	Message string                        `json:"message"`
	Data    []*AllVeevaAccountDetailsData `json:"data,omitempty"`
}

type ResponseStatusChangeNotification struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type RoleSelection struct {
	FormID               string          `json:"formId"`
	ApprovalRoles        []*ApprovalRole `json:"approvalRoles"`
	IsChangeRequest      *bool           `json:"isChangeRequest,omitempty"`
	ChangeRequestSummary *string         `json:"changeRequestSummary,omitempty"`
	IsApollo             *bool           `json:"isApollo,omitempty"`
}

type Roles struct {
	Role       string `json:"role"`
	SequenceNo *int   `json:"sequenceNo,omitempty"`
}

type RuleInput struct {
	Value   *string         `json:"value,omitempty"`
	Actions []*ActionsInput `json:"actions,omitempty"`
}

type Rules struct {
	Value   *string    `json:"value,omitempty"`
	Actions []*Actions `json:"actions,omitempty"`
}

type RulesAnswer struct {
	Value   *string           `json:"value,omitempty"`
	Actions []*ActionsAnswers `json:"actions,omitempty"`
}

type SameApproverList struct {
	Error   bool            `json:"error"`
	Message string          `json:"message"`
	Data    []*ApproverData `json:"data"`
}

type SearchHcp struct {
	Country            string   `json:"Country"`
	HCPNameList        []string `json:"HCPNameList,omitempty"`
	TerritoryIDList    []string `json:"TerritoryIdList,omitempty"`
	HCONameList        []string `json:"HCONameList,omitempty"`
	ProvinceList       []string `json:"ProvinceList,omitempty"`
	Cities             []string `json:"Cities,omitempty"`
	Specialties        []string `json:"Specialties,omitempty"`
	ProductNameList    []string `json:"ProductNameList,omitempty"`
	ClassificationList []string `json:"ClassificationList,omitempty"`
}

type SearchHCPResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	Data    []*Hcp `json:"data,omitempty"`
	Total   int    `json:"total"`
}

type SortItems struct {
	Columns *string `json:"columns,omitempty"`
	Order   *string `json:"order,omitempty"`
}

type SponsoredExpenseData struct {
	Registrationfee        *string                   `json:"registrationfee,omitempty"`
	Registrationremark     *string                   `json:"registrationremark,omitempty"`
	Groundtransportation   *string                   `json:"groundtransportation,omitempty"`
	Airfare                *string                   `json:"airfare,omitempty"`
	Transportremark        *string                   `json:"transportremark,omitempty"`
	Typeofmeal0            *string                   `json:"typeofmeal0,omitempty"`
	Nom0                   *int                      `json:"nom0,omitempty"`
	Costpermeal0           *string                   `json:"costpermeal0,omitempty"`
	Typeofmeal1            *string                   `json:"typeofmeal1,omitempty"`
	Nom1                   *int                      `json:"nom1,omitempty"`
	Costpermeal1           *string                   `json:"costpermeal1,omitempty"`
	Typeofmeal2            *string                   `json:"typeofmeal2,omitempty"`
	Nom2                   *int                      `json:"nom2,omitempty"`
	Costpermeal2           *string                   `json:"costpermeal2,omitempty"`
	Typeofmeal3            *string                   `json:"typeofmeal3,omitempty"`
	Nom3                   *int                      `json:"nom3,omitempty"`
	Costpermeal3           *string                   `json:"costpermeal3,omitempty"`
	MealExpenses           []*SponsoredMealExpenses  `json:"mealExpenses,omitempty"`
	Mealremark             *string                   `json:"mealremark,omitempty"`
	Nodaccomodation        *int                      `json:"nodaccomodation,omitempty"`
	Costperdayaccomodation *string                   `json:"costperdayaccomodation,omitempty"`
	Accomodationremark     *string                   `json:"accomodationremark,omitempty"`
	Otherexpenses          []*SponsoredOtherexpenses `json:"otherexpenses,omitempty"`
	Otherremark            *string                   `json:"otherremark,omitempty"`
}

type SponsoredHcpEngagementData struct {
	Typeofengagement                                                                       *string                 `json:"typeofengagement,omitempty"`
	Individualcategory                                                                     *string                 `json:"individualcategory,omitempty"`
	Govtornongovthcp                                                                       *string                 `json:"govtornongovthcp,omitempty"`
	Remarks                                                                                *string                 `json:"remarks,omitempty"`
	Speakername                                                                            *string                 `json:"speakername,omitempty"`
	Specialty                                                                              *string                 `json:"specialty,omitempty"`
	Hcoinstitutename                                                                       *string                 `json:"hcoinstitutename,omitempty"`
	SpeakerID                                                                              *string                 `json:"speakerID,omitempty"`
	Speakernamevalue                                                                       *string                 `json:"speakernamevalue,omitempty"`
	Preparationtime                                                                        *string                 `json:"preparationtime,omitempty"`
	Servicetime                                                                            *string                 `json:"servicetime,omitempty"`
	Morethan2lecturesmeetings                                                              *string                 `json:"morethan2lecturesmeetings,omitempty"`
	Expenses                                                                               []*SponsoredExpenseData `json:"expenses,omitempty"`
	Doesthehcpinfluenceinpublichealthpolicyorlawsorregulations                             *string                 `json:"doesthehcpinfluenceinpublichealthpolicyorlawsorregulations,omitempty"`
	Doesthehcpinfluenceinpublichealthpolicyorlawsorregulationsremarks                      *string                 `json:"doesthehcpinfluenceinpublichealthpolicyorlawsorregulationsremarks,omitempty"`
	Doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilar         *string                 `json:"doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilar,omitempty"`
	Doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilarremarks  *string                 `json:"doesthehcpinfluenceinpurchasingorreimbursementorpricingordrugapprovalorsimilarremarks,omitempty"`
	Doesthehcpserveoninternationalnongovernmentalhealthorganization                        *string                 `json:"doesthehcpserveoninternationalnongovernmentalhealthorganization,omitempty"`
	Doesthehcpserveoninternationalnongovernmentalhealthorganizationremarks                 *string                 `json:"doesthehcpserveoninternationalnongovernmentalhealthorganizationremarks,omitempty"`
	Tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernment        *string                 `json:"tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernment,omitempty"`
	Tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernmentremarks *string                 `json:"tothebestofyourknowledgedoesthehcporanimmediatefamilymemberworkforthegovernmentremarks,omitempty"`
	Whatisthehcpsphereofinfluence                                                          *string                 `json:"whatisthehcpsphereofinfluence,omitempty"`
	Levelofinfluence                                                                       *string                 `json:"levelofinfluence,omitempty"`
	Question1                                                                              *string                 `json:"question1,omitempty"`
	Remarks1                                                                               *string                 `json:"remarks1,omitempty"`
}

type SponsoredMealExpenses struct {
	Typeofmeal0  *string `json:"typeofmeal0,omitempty"`
	Nom0         *int    `json:"nom0,omitempty"`
	Costpermeal0 *string `json:"costpermeal0,omitempty"`
	Typeofmeal1  *string `json:"typeofmeal1,omitempty"`
	Nom1         *int    `json:"nom1,omitempty"`
	Costpermeal1 *string `json:"costpermeal1,omitempty"`
	Typeofmeal2  *string `json:"typeofmeal2,omitempty"`
	Nom2         *int    `json:"nom2,omitempty"`
	Costpermeal2 *string `json:"costpermeal2,omitempty"`
	Typeofmeal3  *string `json:"typeofmeal3,omitempty"`
	Nom3         *int    `json:"nom3,omitempty"`
	Costpermeal3 *string `json:"costpermeal3,omitempty"`
}

type SponsoredOtherexpenses struct {
	Nameofexpenses  *string `json:"nameofexpenses,omitempty"`
	Totalcostsother *string `json:"totalcostsother,omitempty"`
}

type StatusChangeNotificationInput struct {
	NotificationID []string `json:"NotificationID"`
}

type TotalEventExpenseData struct {
	TypeOfEventExpense string     `json:"typeOfEventExpense"`
	TotalCost          []*float64 `json:"totalCost"`
}

type TotalEventExpenseRequest struct {
	TotalEventExpense []*TotalEventExpenseData `json:"totalEventExpense"`
}

type TotalEventExpenseResponse struct {
	Error                     bool                       `json:"error"`
	Message                   string                     `json:"message"`
	CalculateEventExpenseData *CalculateEventExpenseData `json:"calculateEventExpenseData"`
}

type TotalEvents struct {
	RequestorTotalPending  *int `json:"requestorTotalPending,omitempty"`
	RequestorTotalApproved *int `json:"requestorTotalApproved,omitempty"`
	RequestorTotalRejected *int `json:"requestorTotalRejected,omitempty"`
	ApproverTotalPending   *int `json:"approverTotalPending,omitempty"`
	ApproverTotalApproved  *int `json:"approverTotalApproved,omitempty"`
	ApproverTotalRejected  *int `json:"approverTotalRejected,omitempty"`
}

type TotalEventsResponse struct {
	Error   bool         `json:"error"`
	Message string       `json:"message"`
	Data    *TotalEvents `json:"data,omitempty"`
}

type TotalHcpExpense struct {
	PoposedHonorarium             float64    `json:"poposedHonorarium"`
	RegistrationFeeTotalcost      float64    `json:"registrationFeeTotalcost"`
	GroundTransportationTotalcost float64    `json:"groundTransportationTotalcost"`
	AirfareTotalcost              float64    `json:"airfareTotalcost"`
	MealTotalcost                 []*float64 `json:"mealTotalcost"`
	AccomodationTotalCost         float64    `json:"accomodationTotalCost"`
	OthersTotalCost               []*float64 `json:"othersTotalCost"`
}

type TotalHcpExpenseRequest struct {
	TotalHcpExpense []*TotalHcpExpense `json:"totalHcpExpense"`
}

type TotalHcpExpenseResponse struct {
	Error                   bool                     `json:"error"`
	Message                 string                   `json:"message"`
	CalculateHcpExpenseData *CalculateHcpExpenseData `json:"calculateHcpExpenseData"`
}

type UpdateApproveEventsResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpdateCompletedEventsInput struct {
	FormAnsID string `json:"formAnsId"`
	Status    string `json:"status"`
}

type UpdateCompletedEventsResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpdateEventStatusCompletedToApprovedInput struct {
	MeetingIds []string `json:"meetingIds"`
}

type UpdateEventStatusPendingToApprovedInput struct {
	MeetingIds []string `json:"meetingIds"`
}

type UpdateNumberofEMPInput struct {
	Data []*NumberofEMPData `json:"data"`
}

type UpsertApprovalRoleManagementRequest struct {
	ID                  *string  `json:"id,omitempty"`
	IsDeleted           *bool    `json:"isDeleted,omitempty"`
	Activity            *string  `json:"activity,omitempty"`
	GroupType           *string  `json:"groupType,omitempty"`
	SequenceNo          *int     `json:"sequenceNo,omitempty"`
	MinLimit            *int     `json:"minLimit,omitempty"`
	MaxLimit            *int     `json:"maxLimit,omitempty"`
	HasCondition        *bool    `json:"hasCondition,omitempty"`
	HasInternational    *bool    `json:"hasInternational,omitempty"`
	HasLevelOfInfluence *bool    `json:"hasLevelOfInfluence,omitempty"`
	Department          *string  `json:"department,omitempty"`
	AlternateDepartment *string  `json:"alternateDepartment,omitempty"`
	AlternateGroupType  *string  `json:"alternateGroupType,omitempty"`
	LedBy               []string `json:"ledBy,omitempty"`
}

type UpsertApprovalRoleManagementResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpsertRequest struct {
	IsDeleted *bool   `json:"isDeleted,omitempty"`
	ID        *string `json:"id,omitempty"`
	IsActive  *bool   `json:"isActive,omitempty"`
}

type UserformAnsID struct {
	FormAnsID string `json:"formAnsId"`
	IsApollo  *bool  `json:"isApollo,omitempty"`
}

type ValidationInput struct {
	ControlID          string    `json:"controlID"`
	ActivityID         []*string `json:"activityID,omitempty"`
	CodeID             *int      `json:"codeID,omitempty"`
	Country            *int      `json:"country,omitempty"`
	ComparisonOperator string    `json:"comparisonOperator"`
	Category           string    `json:"category"`
	Description        *string   `json:"description,omitempty"`
	Limit              *float64  `json:"limit,omitempty"`
	Enabled            bool      `json:"enabled"`
	Type               *string   `json:"type,omitempty"`
}

type ValidationResult struct {
	Error                   bool                      `json:"error"`
	ValidationTimeTaken     string                    `json:"validationTimeTaken"`
	ExcelValidationMessages []*ExcelValidationMessage `json:"excelValidationMessages,omitempty"`
}

type ValidationResultForSponsoredHcp struct {
	Error                   bool   `json:"error"`
	ExcelValidationMessages string `json:"excelValidationMessages"`
}

type ValueInput struct {
	Description *string `json:"description,omitempty"`
	Value       *string `json:"value,omitempty"`
}

type Values struct {
	Description string `json:"description"`
	Value       string `json:"value"`
}

type ValuesAnswer struct {
	ID          *string `json:"id,omitempty"`
	Description *string `json:"description,omitempty"`
	Value       *string `json:"value,omitempty"`
	ValueInUsd  *string `json:"valueInUsd,omitempty"`
}

type ValuesAnswers struct {
	ID          *string `json:"id,omitempty"`
	Description string  `json:"description"`
	Value       string  `json:"value"`
}
