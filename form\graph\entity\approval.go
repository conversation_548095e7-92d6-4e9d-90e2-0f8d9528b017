package entity

import (
	"database/sql"

	uuid "github.com/satori/go.uuid"
)

type Approval struct {
	ActivityName string `json:"activity_name"`
	GroupName    string `json:"group_name"`
	ApprovalRole string `json:"approval_role"`
}

type UserRole struct {
	ID    string
	Value string
	Title string
}

type ApprovalRoles struct {
	ID                  *uuid.UUID
	ActivityName        string
	ActivityTitle       string
	GroupName           string
	GroupTitle          string
	ApprovalRole        string
	ApprovalRoleTitle   string
	SequenceNo          int
	MinLimit            sql.NullInt32
	MaxLimit            sql.NullInt32
	CountryTitle        string
	CountryValue        string
	HasCondition        bool
	HasInternational    bool
	HasLevelOfInfluence bool
	Department          *string
	DepartmentID        *uuid.UUID
	LedBy               []string
}

type ApprovalRoleManagementValues struct {
	ActivityID     int
	GroupType      int
	ApprovalRoleID int
	SequenceNo     int
	MinLimit       sql.NullInt32
	MaxLimit       sql.NullInt32
}

type GetApprovalRoleManagementMaxMinId struct {
	ID       *uuid.UUID
	MinLimit sql.NullInt32
	MaxLimit sql.NullInt32
}
type ApprovalTemp struct {
	RoleName   string
	RoleType   string
	SequenceNo string
	ID         int
}

type UserByApprovalRole struct {
	RoleName      string          `json:"roleName"`
	SequenceNo    int             `json:"sequenceNo"`
	UserSelection []UserSelection `json:"userSelection"`
}

type UserSelection struct {
	Description string         `json:"description"`
	Hint        sql.NullString `json:"hint"`
	Value       string         `json:"id"`
}

type TempUserSelection struct {
	SequenceNo  sql.NullInt32
	RoleName    string
	RoleID      int
	Description sql.NullString
	Hint        sql.NullString
	Value       sql.NullString
	RoleType    string
}

type TempUserByActivity struct {
	Description sql.NullString
	Hint        sql.NullString
	Value       sql.NullString
}

type ExpenseValue struct {
	ZPExpenseMin          int
	ZPExpenseMax          int
	SponsorshipExpenseMin int
	SponsorshipExpenseMax int
}

type ApprovalRoleManagementEntity struct {
	ID                  *uuid.UUID
	UserID              string
	ActivityID          string
	GroupID             int
	AlternateGroupID    int
	SequenceNo          int
	IsDeleted           bool
	MinLimit            *int
	MaxLimit            *int
	ActivityCodeID      int
	CountryCode         int
	HasCondition        bool
	HasInternational    bool
	HasLevelOfInfluence bool
	Department          string
	AlternateDepartment *string
	LedByOptions        []string
}
type ApprovalRoleSelections struct {
	UserRole      string
	UserRoleTitle string
	RoleType      int
	Department    string
	SequenceNo    int
	UserID        sql.NullString
	UserFullName  sql.NullString
	OfflineCheck  sql.NullBool
	HighLoi       bool
}

type DelegateApproverEntity struct {
	FormAnswerID string
	ApproverID   string
	SequenceNo   int
	GroupID      int
	UserRoleID   string
	DepartmentID string
	SetNumber    int
}

type FetchDelegateApproverEntity struct {
	ApproverID   string
	ApproverName string
	UserRole     string
}
