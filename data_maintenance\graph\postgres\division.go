package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func GetDivisionByNameAndCountry(country int, divisionName string) (*entity.Division, error) {
	functionName := "GetDivisionByNameAndCountry()"
	log.Printf("%s - Inputs: %s and %s", functionName, country, divisionName)
	if pool == nil {
		pool = GetPool()
	}

	entityResponse := new(entity.Division)
	query := `SELECT id,name,country from division where country = $1 AND name = $2 AND is_deleted = false AND is_active = true`

	err := pool.QueryRow(context.Background(), query, country, divisionName).Scan(&entityResponse.ID, &entityResponse.Name, &entityResponse.Country)
	logengine.GetTelemetryClient().TrackEvent("GetDivisionByNameAndCountry query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: {%s}", functionName, err.Error())
		return nil, err
	}

	return entityResponse, nil
}

func UpsertDivisionForExcel(tx pgx.Tx, entity *entity.Division) (uuid.UUID, error) {
	functionName := "UpsertDivision()"
	query := `INSERT INTO division (name,country,is_active,created_by) VALUES($1,$2,$3,$4) RETURNING(id)`
	var divisionId uuid.UUID
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.Name, entity.Country, true, uuid.Nil)
	err := tx.QueryRow(context.Background(), query, inputArgs...).Scan(&divisionId)
	logengine.GetTelemetryClient().TrackEvent("UpsertDivisionForExcel query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}

	return divisionId, err
}

func deleteDivision(id uuid.UUID) {
	functionName := "deleteDivision"
	logengine.GetTelemetryClient().TrackEvent("deleteDivision query called")
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: Failed to begin transaction", functionName)
		return
	}
	defer tx.Rollback(context.Background())
	queryMaterial := `update material set is_active=false and is_deleted=true where division=$1`
	_, err = tx.Exec(context.Background(), queryMaterial, id)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return
	}
	queryDivision := `update division set is_active=false and is_deleted=true where id=$1`
	_, err = tx.Exec(context.Background(), queryDivision, id)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: Failed to commit material data", functionName)
	}
}

func GetDivisionIdFromMaterialId(materialId uuid.UUID) *uuid.UUID {
	functionName := "GetDivisionIdFromMaterialId()"
	if pool == nil {
		pool = GetPool()
	}
	var divisionID *uuid.UUID
	queryString := `SELECT division from material where id = $1`
	err := pool.QueryRow(context.Background(), queryString, materialId).Scan(&divisionID)
	logengine.GetTelemetryClient().TrackEvent("GetDivisionIdFromMaterialId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return &uuid.Nil
	}
	return divisionID
}
