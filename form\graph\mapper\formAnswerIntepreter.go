package mapper

import (
	"bytes"
	"fmt"
	"golang.org/x/text/currency"
	"html/template"
	"log"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/ihcp/form/graph/model"
	"github.com/shopspring/decimal"

	wkhtml "github.com/SebastiaanKlippert/go-wkhtmltopdf"
)

type BankAccountInfo struct {
	BankName          string
	BankBranch        string
	BankAccountNumber string
	BankAccountHolder string
}

type SignatoryInformation struct {
	HCORepresentative      string
	HCORepresentativeTitle string
}

type BoardOfDirector struct {
	Name     string
	Position string
	Question string
	Remark   string
}

type BankInformation struct {
	*BankAccountInfo
	*SignatoryInformation
}

type EventSponsorshipAgreementInformation struct {
	DateOfAgreement         string
	DateOfAgreementDatetime time.Time
	HCOName                 string
	Country                 string
	EventName               string
	EventLocation           string
	EventStartAt            string
	EventEndAt              string
	LedBy                   string

	Amount                    decimal.Decimal
	Currency                  string
	HCOChairmanName           string
	IncludeLogisticFacilities bool
	Expense                   []*Expense
	PaidSeparatelyToHCO       []string
	BankInformation           *BankInformation
	HCPNumber                 int
	ZPRepresentative          string
	ZPRepresentativeTitle     string
}

type Expense struct {
	PackageTitle   string
	PackageDetails string
	PackageAmount  decimal.Decimal
	IsPaidToHCO    bool
	ExpenseType    string
}

var (
	BankQuestionID      string = "basic-info-section-1-form-1-group2-bank"
	SignatoryQuestionID string = "basic-info-section-1-form-1-group2-signatory"
)

// Convert string to CamelCase with the first letter capitalized
//func toCamelCase(s string) string {
//	words := strings.Fields(s)
//	for i, word := range words {
//		words[i] = strings.Title(word)
//	}
//	return strings.Join(words, "")
//}

func extractSingleValueFromAnswer(a []*model.ValuesAnswer) string {
	if len(a) == 0 {
		return ""
	}
	return *a[0].Value
}

func extractMultipleValueFromAnswer(a []*model.ValuesAnswer) []string {
	if len(a) == 0 {
		return []string{}
	}

	rs := []string{}
	for i := range a {
		rs = append(rs, *a[i].Value)
	}

	return rs
}

func GetBoardOfDirector(d *model.FormTabsAnswer) *BoardOfDirector {
	rs := &BoardOfDirector{}
	if len(d.SectionAnswer[0].Form.GroupAnswer) < 2 {
		return rs
	}

	bankQuestions := d.SectionAnswer[0].Form.GroupAnswer[1].QuestionAnswers
	for i := range bankQuestions {
		q := bankQuestions[i]
		if q != nil && q.ID != nil && *q.ID == "basic-info-section-1-form-1-group21" {
			for j := range q.Answers {
				data := extractSingleValueFromAnswer(q.Answers[j].Values)
				if *q.Answers[j].ID == `name` {
					rs.Name = data
				} else if *q.Answers[j].ID == `position` {
					rs.Position = data
				} else if *q.Answers[j].ID == `question-1` {
					rs.Question = data
				} else if *q.Answers[j].ID == `remarks-1` {
					rs.Remark = data
				}
			}
		}
	}

	return rs
}

func GetBankData(d *model.FormTabsAnswer) (b *BankInformation) {
	bankInformation := &BankInformation{
		&BankAccountInfo{},
		&SignatoryInformation{},
	}
	defer func() {
		if err := recover(); err != nil {
			log.Println(err, "When event bank data is not valid")
			b = bankInformation
		}
	}()

	bankQuestions := d.SectionAnswer[0].Form.GroupAnswer[1].QuestionAnswers
	for i := range bankQuestions {
		q := bankQuestions[i]

		if q == nil || q.ID == nil {
			continue
		}

		if *q.ID == "basic-info-section-1-form-1-group2-bank" {
			for j := range q.Answers {
				data := extractSingleValueFromAnswer(q.Answers[j].Values)
				if *q.Answers[j].ID == `bank_name` {
					bankInformation.BankName = data
				} else if *q.Answers[j].ID == `bank_branch` {
					bankInformation.BankBranch = data
				} else if *q.Answers[j].ID == `bank_account_number` {
					bankInformation.BankAccountNumber = data
				} else if *q.Answers[j].ID == `bank_account_holder` {
					bankInformation.BankAccountHolder = data
				}
			}
		}

		if *q.ID == "basic-info-section-1-form-1-group2-signatory" {
			for j := range q.Answers {
				data := extractSingleValueFromAnswer(q.Answers[j].Values)
				if *q.Answers[j].ID == `HCO_representative` {
					bankInformation.HCORepresentative = data
				} else if *q.Answers[j].ID == `HCO_representative_title` {
					bankInformation.HCORepresentativeTitle = data
				}
			}
		}
	}
	fmt.Println(bankInformation, "--------------------------")
	return bankInformation
}

func mapExpenseCodeWithTitle(code string) string {
	m := map[string]string{
		"accommodation":         "Accommodation",
		"agencyfee":             "Agency Fee",
		"equipmentrental":       "Equipment Rental",
		"printingmaterials":     "Printing Materials",
		"logisticsmaterials":    "Transport",
		"eventsetup":            "Event Setup",
		"registrationfee":       "Registration Fee",
		"meals":                 "Meals",
		"venuerental":           "Venue Rental",
		"meetingpackage":        "Meeting Package",
		"funding":               "Funding",
		"proposedfundingamount": "Proposed Funding Amount",
		"sponsorpackage":        "Sponsorship Package",
		"others":                "Others",
	}

	v, found := m[code]
	if found {
		return v
	}
	return code
}

func GetEventInformationForSponsorshipAgreement(d []*model.FormTabsAnswer) *EventSponsorshipAgreementInformation {
	rs := &EventSponsorshipAgreementInformation{
		LedBy:               "N/A",
		DateOfAgreement:     time.Now().Format(`January 2, 2006`),
		HCOName:             "",
		Country:             "",
		EventName:           "",
		EventLocation:       "",
		EventStartAt:        "",
		EventEndAt:          "",
		Amount:              decimal.Zero,
		Currency:            "",
		HCOChairmanName:     "HCO Chairman",
		Expense:             make([]*Expense, 0),
		PaidSeparatelyToHCO: make([]string, 0),
	}

	location, err := time.LoadLocation("Asia/Manila")
	if err != nil {
		panic(err)
	}

	basicSection := d[1].SectionAnswer[0].Form.GroupAnswer[0].QuestionAnswers[0].Answers //"id": "basic-info-section-1-form-1-group1-questions-1",
	for i := range basicSection {
		q := basicSection[i]
		if q == nil || q.ID == nil {
			continue
		}

		if *q.ID == "activity-name" {
			rs.EventName = extractSingleValueFromAnswer(q.Values)
		} else if *q.ID == "venue" {
			rs.EventLocation = extractSingleValueFromAnswer(q.Values)
		} else if *q.ID == "country" {
			rs.Country = extractSingleValueFromAnswer(q.Values)
		} else if *q.ID == "sponsorship-to-hco-requesting-hco" {
			rs.HCOName = extractSingleValueFromAnswer(q.Values)
		} else if *q.ID == "activity-start-date" {
			unixTimeStr := extractSingleValueFromAnswer(q.Values)
			i, err := strconv.ParseInt(unixTimeStr, 10, 64)
			if err != nil {
				panic(err)
			}

			rs.EventStartAt = time.Unix(i, 0).In(location).Format(`January 2, 2006`)
			fmt.Println(rs.EventStartAt)
		} else if *q.ID == "activity-end-date" {
			unixTimeStr := extractSingleValueFromAnswer(q.Values)
			i, err := strconv.ParseInt(unixTimeStr, 10, 64)
			if err != nil {
				panic(err)
			}

			rs.EventEndAt = time.Unix(i, 0).In(location).Format(`January 2, 2006`)
		} else if *q.ID == "led-by" {
			rs.LedBy = extractSingleValueFromAnswer(q.Values)
		}

	}

	expenseQuestions := d[3].SectionAnswer[0].Form.GroupAnswer
	for i := range expenseQuestions {
		q := expenseQuestions[i]
		if q == nil {
			continue
		}

		isSponsorPackage := false
		amount := decimal.Zero
		expense := Expense{}

		for j := range q.QuestionAnswers {
			q2 := q.QuestionAnswers[j]
			if q2 == nil {
				continue
			}

			t := *q2.Answers[0].Title

			if t == `Expenses` { // expense total cost
				for k := range q2.Answers {
					val := extractSingleValueFromAnswer(q2.Answers[k].Values)
					d, err := decimal.NewFromString(val)
					if err == nil {
						// panic(err)
						amount = amount.Add(d)
					}
				}
				expense.PackageAmount = amount
			} else if t == `Type of Event Expenses` {
				val := extractSingleValueFromAnswer(q2.Answers[0].Values)
				expense.ExpenseType = mapExpenseCodeWithTitle(val)
				if val == `sponsorpackage` {
					isSponsorPackage = true
				}
			} else if t == `Package Title` {
				pkgTitle := extractSingleValueFromAnswer(q2.Answers[0].Values)
				pkgDetails := extractSingleValueFromAnswer(q2.Answers[1].Values)
				expense.PackageTitle = pkgTitle
				expense.PackageDetails = pkgDetails
			} else if t == `Expense will be paid to HCO` {
				expense.IsPaidToHCO = extractSingleValueFromAnswer(q2.Answers[0].Values) == "true"
			}
		}

		if isSponsorPackage {
			expense.PackageAmount = amount
			rs.Amount = rs.Amount.Add(amount)
			rs.Expense = append(rs.Expense, &expense)
		}
		if expense.IsPaidToHCO {
			rs.PaidSeparatelyToHCO = append(rs.PaidSeparatelyToHCO, expense.ExpenseType)
		}
	}

	hcpSection := d[2].SectionAnswer[0].Form.GroupAnswer
	count := 0
	for i := range hcpSection {
		q := hcpSection[i]
		if q != nil && q.ID != nil && *q.ID == "hcp-engagement-info-section-1-form-1-group1" {
			var role string
			for j := range q.QuestionAnswers {
				if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-14" {
					data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
					role = data
				}
			}

			if strings.Contains(role, `speaker`) || strings.Contains(role, `moderator`) {
				count++
			}
		}
	}
	rs.HCPNumber = count

	return rs
}

func ParseSponsorshipAgreementHTML(d *EventSponsorshipAgreementInformation) *bytes.Buffer {
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}

	rootPath := path.Dir(e)
	var t *template.Template
	t, err = template.ParseFiles(rootPath + "/templates/sponsorship_agreement.html")
	if err != nil {
		absPath, err := filepath.Abs("graph/postgres/util/templates/sponsorship_agreement.html")
		if err != nil {
			panic(err)
		}

		t, err = template.ParseFiles(absPath)
		if err != nil {
			panic(err)
		}

	}

	var (
		packageTitle   string
		packageDetails []string
		packageAmount  string
	)

	for i := range d.Expense {
		packageTitle = d.Expense[i].PackageTitle
		packageDetails = strings.Split(d.Expense[i].PackageDetails, "\n")
		packageAmount = d.Expense[i].PackageAmount.String()
		break // ONLY GET THE FIRST ONE
	}

	var buffer bytes.Buffer
	t.Execute(&buffer, struct {
		Country                string
		DateOfAgreement        string
		HCOName                string
		EventName              string
		EventStartDate         string
		EventEndDate           string
		EventLocation          string
		Currency               string
		TotalAmount            string
		BankName               string
		BankBranch             string
		AccountNumber          string
		AccountHolder          string
		PreferredCourt         string
		HCORepresentative      string
		HCORepresentativeTitle string
		ZPRepresentative       string
		ZPRepresentativeTitle  string

		PackageTitle        string
		PackageDetails      []string
		PackageAmount       string
		PaidSeparatelyToHCO []string
	}{
		Country:                d.Country,
		DateOfAgreement:        d.DateOfAgreement,
		HCOName:                d.HCOName,
		EventName:              d.EventName,
		EventStartDate:         d.EventStartAt,
		EventEndDate:           d.EventEndAt,
		EventLocation:          d.EventLocation,
		Currency:               strings.ToUpper(d.Currency),
		TotalAmount:            formatMoney(d.Amount.String()),
		BankName:               d.BankInformation.BankName,
		BankBranch:             d.BankInformation.BankBranch,
		AccountNumber:          d.BankInformation.BankAccountNumber,
		AccountHolder:          d.BankInformation.BankAccountHolder,
		PreferredCourt:         "the Philippines",
		PackageTitle:           packageTitle,
		PackageAmount:          formatMoney(packageAmount),
		PackageDetails:         packageDetails,
		HCORepresentative:      d.BankInformation.SignatoryInformation.HCORepresentative,
		HCORepresentativeTitle: d.BankInformation.SignatoryInformation.HCORepresentativeTitle,
		PaidSeparatelyToHCO:    d.PaidSeparatelyToHCO,
		ZPRepresentativeTitle:  d.ZPRepresentativeTitle,
		ZPRepresentative:       d.ZPRepresentative,
	})

	return &buffer
}

func formatMoney(s string) string {
	if s == `` {
		s = `0`
	}

	a, err := strconv.ParseFloat(s, 64)
	if err != nil {
		panic(err)
	}
	amount := currency.GBP.Amount(a)
	amountStr := fmt.Sprintf(`%v`, amount)
	return amountStr[4:]
}

func SponsorshipAgrHeaderFooter(page *wkhtml.PageReader, meetingID *string) {
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	page.HeaderHTML.Set(rootPath + "/templates/header.html")
	page.FooterHTML.Set(rootPath + "/templates/footer.html")
	/// Local test
	//headerPath, err := filepath.Abs("graph/postgres/util/templates/header.html")
	//if err != nil {
	//	panic(err)
	//}
	//footerPath, err := filepath.Abs("graph/postgres/util/templates/footer.html")
	//if err != nil {
	//	panic(err)
	//}
	//page.HeaderHTML.Set(headerPath)
	//page.FooterHTML.Set(footerPath)
	// Get the current date and time
	currentDate := time.Now()
	formattedDate := currentDate.Format("02. Jan. 2006")
	monthAbbr := currentDate.Format("Jan")
	finalFormattedDate := fmt.Sprintf("Effective %s. %s.%d (Version 1)\neZFlow %s", formattedDate[:2], monthAbbr, currentDate.Year(), *meetingID)

	page.FooterLeft.Set(finalFormattedDate)
	page.FooterFontSize.Set(6)
	page.FooterSpacing.Set(3)
	page.FooterRight.Set("[page]")
}

type BasicInfo struct {
	*BankAccountInfo
	*SignatoryInformation
	*BoardOfDirector
	ActivityName             string
	LedBy                    string
	MeetingObjective         string
	Client                   string
	Product                  string
	TherapeuticArea          string
	ProductTargetAudience    string
	PromotionValue           string
	Team                     string
	Country                  string
	NameOfHCO                string
	MaterialCode             string
	NoOfEmployee             string
	Remark                   string
	RequestingHCO            string
	Recipient                string
	AgendaDocument           []string
	AttachmentForProposal    []string
	LinkedEventID            string
	HCONature                string
	contactPersonName        string
	grantPurpose             string
	DDQDocuments             string
	RequestDocuments         string
	ScreeningDocuments       string
	RedFlagDetails           string
	RedFlagResolutionDetails string
	GovernmentDetails        string
	ZPBenefit                string
	ZPBenefitDetails         string
}

// TODO
func GetMajorBasicInfoFromAnswer(d *model.FormTabsAnswer) *BasicInfo {
	var (
		ledBy                 string
		activityName          string
		meetingObjective      string
		client                string
		product               string
		therapeuticArea       string
		productTargetAudience string
		promotionValue        string
		team                  string
		country               string

		nameOfHCO     string
		materialCode  string
		noOfEmployee  string
		remark        string
		requestingHCO string

		recipient          string
		agendaAttachment   []string
		proposalAttachment []string
		linkedEventID      string

		HCONature                string
		contactPersonName        string
		grantPurpose             string
		DDQDocuments             string
		RequestDocuments         string
		ScreeningDocuments       string
		RedFlagDetails           string
		RedFlagResolutionDetails string
		GovernmentDetails        string
		ZPBenefit                string
		ZPBenefitDetails         string
	)

	basicQuestions := d.SectionAnswer[0].Form.GroupAnswer[0].QuestionAnswers
	for i := range basicQuestions {
		q := basicQuestions[i]
		if q == nil || q.ID == nil {
			continue
		}
		if q != nil && *q.ID == "basic-info-section-1-form-1-group1-questions-1" {
			for j := range q.Answers {
				data := extractSingleValueFromAnswer(q.Answers[j].Values)
				if *q.Answers[j].ID == `led-by` {
					ledBy = data
				} else if *q.Answers[j].ID == `activity-name` {
					activityName = data
				} else if *q.Answers[j].ID == `meeting-objective` {
					meetingObjective = data
				} else if *q.Answers[j].ID == `product-owner` {
					client = data
				} else if *q.Answers[j].ID == `product` {
					product = data
				} else if *q.Answers[j].ID == `therapeutic-area` {
					therapeuticArea = data
				} else if *q.Answers[j].ID == `target-audience` {
					productTargetAudience = data
				} else if *q.Answers[j].ID == `promotional-or-non-promotional-event` {
					promotionValue = data
				} else if *q.Answers[j].ID == `country` {
					country = data
				} else if *q.Answers[j].ID == `team` {
					team = data
				} else if *q.Answers[j].ID == `event-organizer` {
					nameOfHCO = data
				} else if *q.Answers[j].ID == `material-code-number` {
					materialCode = data
				} else if *q.Answers[j].ID == `employee-no` {
					noOfEmployee = data
				} else if *q.Answers[j].ID == `general-remarks` {
					remark = data
				} else if *q.Answers[j].ID == `sponsorship-to-hco-requesting-hco` {
					requestingHCO = data
				} else if *q.Answers[j].ID == `do-you-want-to-add-any-attachment-for-agenda-or-proposal` {
					agendaAttachment = extractMultipleValueFromAnswer(q.Answers[j].Values)
				} else if *q.Answers[j].ID == `proposal-attachments` {
					proposalAttachment = extractMultipleValueFromAnswer(q.Answers[j].Values)
				} else if *q.Answers[j].ID == `sponsorship-to-hco-recipient` {
					recipient = data
				} else if *q.Answers[j].ID == `linked-event-id` {
					linkedEventID = data
				} else if *q.Answers[j].ID == `nature-of-hcohci` {
					nameOfHCO = data
				} else if *q.Answers[j].ID == `name-of-contact-person` {
					contactPersonName = data
				} else if *q.Answers[j].ID == `purpose-of-grant` {
					grantPurpose = data
				} else if *q.Answers[j].ID == `upload-documents-of-ddq` {
					DDQDocuments = data
				} else if *q.Answers[j].ID == `upload-documents-of-request-letter` {
					RequestDocuments = data
				} else if *q.Answers[j].ID == `upload-documents-of-screening-report` {
					ScreeningDocuments = data
				} else if *q.Answers[j].ID == `details-of-the-red-flags` {
					RedFlagDetails = data
				} else if *q.Answers[j].ID == `has-the-red-flag-been-resolved-provide-more-details` {
					RedFlagResolutionDetails = data
				} else if *q.Answers[j].ID == `details-of-the-recipient-owned-or-controlled-by-a-government` {
					GovernmentDetails = data
				} else if *q.Answers[j].ID == `in-return-will-zuellig-pharma-be-benefiting-from-this-grant?` {
					ZPBenefit = data
				} else if *q.Answers[j].ID == `details-of-the-zuellig-pharma-be-benefiting-from-this-grant` {
					ZPBenefitDetails = data
				}
			}
		}
	}

	//fmt.Println(activityName, "------------")
	//fmt.Println(meetingObjective, "------------")
	//fmt.Println(client, "------------")
	//fmt.Println(product, "------------")
	//fmt.Println(therapeuticArea, "------------")
	//fmt.Println(promotionValue, "------------")
	//fmt.Println(country, "------------country")
	//fmt.Println(team, "------------team")
	//fmt.Println(productTargetAudience, "------------productTargetAudience")
	//fmt.Println(nameOfHCO, "------------nameOfHCO")
	//fmt.Println(materialCode, "------------materialCode")
	//fmt.Println(noOfEmployee, "------------noOfEmployee")
	//fmt.Println(remark, "------------remark")
	//fmt.Println(requestingHCO, "------------requestingHCO")
	//fmt.Println(agendaAttachment, "------------agendaAttachment")
	//fmt.Println(proposalAttachment, "------------proposalAttachment")
	//fmt.Println(recipient, "------------recipient")
	//fmt.Println(linkedEventID, "------------linkedEventID")
	b := GetBankData(d)
	fmt.Println("===", b)
	return &BasicInfo{
		LedBy:                    ledBy,
		BankAccountInfo:          b.BankAccountInfo,
		SignatoryInformation:     b.SignatoryInformation,
		BoardOfDirector:          GetBoardOfDirector(d),
		ActivityName:             activityName,
		MeetingObjective:         meetingObjective,
		Client:                   client,
		Product:                  product,
		TherapeuticArea:          therapeuticArea,
		ProductTargetAudience:    productTargetAudience,
		PromotionValue:           promotionValue,
		Team:                     team,
		Country:                  country,
		NameOfHCO:                nameOfHCO,
		MaterialCode:             materialCode,
		NoOfEmployee:             noOfEmployee,
		Remark:                   remark,
		RequestingHCO:            requestingHCO,
		Recipient:                recipient,
		AgendaDocument:           agendaAttachment,
		AttachmentForProposal:    proposalAttachment,
		LinkedEventID:            linkedEventID,
		HCONature:                HCONature,
		contactPersonName:        contactPersonName,
		grantPurpose:             grantPurpose,
		DDQDocuments:             DDQDocuments,
		RequestDocuments:         RequestDocuments,
		ScreeningDocuments:       ScreeningDocuments,
		RedFlagDetails:           RedFlagDetails,
		RedFlagResolutionDetails: RedFlagResolutionDetails,
		GovernmentDetails:        GovernmentDetails,
		ZPBenefit:                ZPBenefit,
		ZPBenefitDetails:         ZPBenefitDetails,
	}
}

type HCPInfo struct {
	EngagementType   string
	HCPType          string
	Name             string
	Role             string
	LevelOfInfluence string
}

func GetHCPInfo(d *model.FormTabsAnswer) (rs []*HCPInfo) {
	basicQuestions := d.SectionAnswer[0].Form.GroupAnswer
	for i := range basicQuestions {
		q := basicQuestions[i]

		hcp := HCPInfo{}
		for j := range q.QuestionAnswers {
			if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-19" {
				data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
				hcp.EngagementType = data
			} else if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-1" {
				data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
				hcp.HCPType = data
			} else if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-4" {
				data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
				hcp.Name = data
			} else if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-14" {
				data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
				hcp.Role = data
			} else if *q.QuestionAnswers[j].ID == "hcp-engagement-info-section-1-form-1-group1-questions-21" {
				data := extractSingleValueFromAnswer(q.QuestionAnswers[j].Answers[0].Values)
				hcp.LevelOfInfluence = data
			}
		}
		rs = append(rs, &hcp)
	}
	//}
	return rs
}
