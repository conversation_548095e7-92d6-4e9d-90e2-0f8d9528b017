package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/model"
)

// AuthenticateUser is the resolver for the authenticateUser field.
func (r *queryResolver) AuthenticateUser(ctx context.Context, input model.UserCredentialsInput) (*model.UserAuthenticationResponse, error) {
	return r.LoginController.AuthenticateUser(ctx, input)
	//controller.AuthenticateUser(input)
}

// RegenerateJwtTokenWithCountry is the resolver for the regenerateJwtTokenWithCountry field.
func (r *queryResolver) RegenerateJwtTokenWithCountry(ctx context.Context, input model.RegenerateJwtTokenWithCountryRequest) (*model.UserAuthenticationResponse, error) {
	return controller.RegenerateJWTToken(&ctx, input)
}

// ProxyLoginUser is the resolver for the proxyLoginUser field.
func (r *queryResolver) ProxyLoginUser(ctx context.Context, input model.ProxyUserCredentialsInput) (*model.ProxyUserAuthenticationResponse, error) {
	return controller.ProxyAuthenticateUser(&ctx, input)
}
