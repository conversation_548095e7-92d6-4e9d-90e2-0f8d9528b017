package mapper

import (
	"errors"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func ExportEmployeeInputModelToEntity(input *model.EmployeeRequest, userUUID uuid.UUID) (*entity.ExportEmployeeExcel, error) {
	var outEntity entity.ExportEmployeeExcel
	sortElements := []entity.SortingElements{}
	outEntity.IsExcel = input.IsExcel
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid

	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New(err.Error())

	}
	outEntity.Country = countryID
	if input.IsActive != nil {
		outEntity.IsActive = input.IsActive
	}
	if input.Team != nil {
		outEntity.Team = strings.TrimSpace(*(input).Team)
	}
	if input.Position != nil {
		outEntity.Position = strings.TrimSpace(*(input).Position)
	}
	if input.EmployeeCode != nil {
		outEntity.EmployeeCode = strings.TrimSpace(*(input).EmployeeCode)
	}
	if input.ActiveDirectoryName != nil {
		outEntity.ActiveDirectoryName = strings.TrimSpace(*(input).ActiveDirectoryName)
	}
	if input.FirstName != nil {
		outEntity.FirstName = strings.TrimSpace(*(input).FirstName)
	}
	if input.LastName != nil {
		outEntity.LastName = strings.TrimSpace(*(input).LastName)
	}
	if input.VeevaReferenceID != nil {
		outEntity.VeevaReferenceId = strings.TrimSpace(*(input).VeevaReferenceID)
	}
	if input.Role != nil {
		// code := codeController.GetTitleKeyCodes()["userrole"]
		// role := code[*input.Role].ID
		// log.Println(role)
		userRole, err := postgres.GetUserRoleIdByUserRole(strings.TrimSpace(*(input).Role))
		if err != nil {
			return nil, errors.New(err.Error())

		}
		outEntity.Role = userRole
	}
	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = strings.TrimSpace(*(input).SearchItem)
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements
	return &outEntity, nil
}

func ExportEmployeeAuditEntityToModel(input []*model.EmployeeAuditLogsData) []entity.EmployeeExcelData {
	var responseExcel []entity.EmployeeExcelData
	for _, item := range input {
		resExcel := entity.EmployeeExcelData{}
		resExcel.Data = append(resExcel.Data, item.ID)
		resExcel.Data = append(resExcel.Data, item.UserID)
		resExcel.Data = append(resExcel.Data, item.Country)
		resExcel.Data = append(resExcel.Data, item.FirstName)
		resExcel.Data = append(resExcel.Data, item.LastName)
		resExcel.Data = append(resExcel.Data, item.ActiveDirectoryName)
		resExcel.Data = append(resExcel.Data, item.Email)
		resExcel.Data = append(resExcel.Data, item.EmployeeCode)
		resExcel.Data = append(resExcel.Data, item.RoleTitle)
		resExcel.Data = append(resExcel.Data, item.ActionType)
		resExcel.Data = append(resExcel.Data, item.ActionDate)
		resExcel.Data = append(resExcel.Data, item.ActionBy)
		responseExcel = append(responseExcel, resExcel)
	}
	return responseExcel

}

func ExportEmployeeInfoEntityToModel(input []entity.FetchEmployee) ([]*model.Employee, []*entity.EmployeeExcelData) {
	var response []*model.Employee
	var responseExcel []*entity.EmployeeExcelData
	code := codeController.GetIdKeyCodes()["country"]
	for _, item := range input {
		res := new(model.Employee)
		resExcel := new(entity.EmployeeExcelData)
		res.ID = item.ID.String()
		res.Country = code[int(item.Country.Int32)].Title.String
		if item.Team.String != "" {
			team := item.Team.String
			res.Team = &team
		} else {
			res.Team = nil
		}
		if item.Position.String != "" {
			position := item.Position.String
			res.Position = &position
		} else {
			res.Position = nil
		}

		res.FirstName = item.FirstName.String
		res.LastName = item.LastName.String
		if item.EmployeeCode.String != "" {
			employeeCode := item.EmployeeCode.String
			res.EmployeeCode = &employeeCode
		} else {
			res.EmployeeCode = nil
		}
		res.ActiveDirectoryName = item.ActiveDirectory.String
		if item.RoleTitle.String != "" {
			res.RoleTitle = item.RoleTitle.String
		}
		if item.RoleValue.String != "" {
			res.RoleValue = item.RoleValue.String
		}
		res.IsActive = item.IsActive
		if item.EmailFetch.String != "" {
			email := item.EmailFetch.String
			res.Email = &email
		} else {
			res.Email = nil
		}
		if item.TeamID.String != "" {
			teamID := item.TeamID.String
			res.TeamID = &teamID
		} else {
			res.TeamID = nil
		}
		res.VeevaReferenceID = item.VeevaReferenceId.String
		response = append(response, res)

		resExcel.Data = append(resExcel.Data, item.ID.String())
		resExcel.Data = append(resExcel.Data, code[int(item.Country.Int32)].Title.String)
		resExcel.Data = append(resExcel.Data, item.Team.String)
		resExcel.Data = append(resExcel.Data, item.Position.String)
		resExcel.Data = append(resExcel.Data, item.FirstName.String)
		resExcel.Data = append(resExcel.Data, item.LastName.String)
		resExcel.Data = append(resExcel.Data, item.EmployeeCode.String)
		if item.RoleValue.String != "" {
			resExcel.Data = append(resExcel.Data, item.RoleValue.String)
		}
		resExcel.Data = append(resExcel.Data, item.ActiveDirectory.String)
		resExcel.Data = append(resExcel.Data, item.EmailFetch.String)
		if item.IsActive {
			resExcel.Data = append(resExcel.Data, "Y")
		} else {
			resExcel.Data = append(resExcel.Data, "N")
		}
		resExcel.Data = append(resExcel.Data, item.VeevaReferenceId.String)
		responseExcel = append(responseExcel, resExcel)

	}
	return response, responseExcel
}

func MapEmployeeModelToEntity(inputModel *model.EmployeeInput, userUUID uuid.UUID, country int) (*entity.Employee, error) {
	var entity entity.Employee

	isDeleted := false

	if inputModel.ID != nil {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err != nil {
			return nil, errors.New("Employee ID format is invalid!")
		}
		entity.ID = &uuid
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			entity.IsDeleted = isDeleted
		}

		entity.CountryID = country

		if inputModel.EmpCode != nil {
			entity.EmployeeCode = *inputModel.EmpCode
		}
		if inputModel.Email != nil {
			entity.Email = *inputModel.Email
		}

		if inputModel.FirstName != nil {
			entity.FirstName = *inputModel.FirstName
		}

		if inputModel.LastName != nil {
			entity.LastName = *inputModel.LastName
		}

		if inputModel.TeamID != nil && strings.TrimSpace(*inputModel.TeamID) != "" {
			entity.Team = *inputModel.TeamID
		}

		if inputModel.VPosition != nil {
			entity.Position = *inputModel.VPosition
		}
		if inputModel.ActiveDirectoryName != nil {
			if strings.TrimSpace(*inputModel.ActiveDirectoryName) != "" {
				if postgres.CheckActivDirNameInUserForACountry(*inputModel.ActiveDirectoryName, inputModel.ID, country) {
					return nil, errors.New("Employee active directory name already exists in this country")
				}
				entity.ActiveDirectory = *inputModel.ActiveDirectoryName
			}
		}

		var role = inputModel.Role
		if role != nil {
			userRole, err := postgres.GetUserRoleIdByUserRole(*inputModel.Role)
			if err != nil {
				entity.UserRoleID = nil
			} else {
				entity.UserRoleID = &userRole
			}
		}
	} else {
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			entity.IsDeleted = isDeleted
		}
		countryID, err := postgres.GetUserCountryByID(userUUID)
		if err != nil {
			return nil, errors.New("Country does not exist")
		}
		entity.CountryID = countryID

		if inputModel.EmpCode != nil {
			if strings.TrimSpace(*inputModel.EmpCode) != "" {
				entity.EmployeeCode = *inputModel.EmpCode
			}
		} else {
			return nil, errors.New("Employee code can not be empty")
		}

		if inputModel.FirstName != nil {
			if strings.TrimSpace(*inputModel.FirstName) != "" {
				entity.FirstName = *inputModel.FirstName
			}
		} else {
			return nil, errors.New("Employee first name can not be empty")
		}
		if inputModel.LastName != nil {
			entity.LastName = *inputModel.LastName
		}

		if inputModel.TeamID != nil {
			if strings.TrimSpace(*inputModel.TeamID) != "" {
				entity.Team = *inputModel.TeamID
			}

		}
		if inputModel.Email != nil {
			entity.Email = *inputModel.Email
		}

		if inputModel.VPosition != nil {
			entity.Position = *inputModel.VPosition
		}
		if inputModel.ActiveDirectoryName != nil {
			if strings.TrimSpace(*inputModel.ActiveDirectoryName) != "" {
				if postgres.CheckActivDirNameInUserForACountry(*inputModel.ActiveDirectoryName, inputModel.ID, country) {
					return nil, errors.New("Employee active directory name already exists in this country")
				}
				entity.ActiveDirectory = *inputModel.ActiveDirectoryName
			}
		} else {
			return nil, errors.New("Employee active directory name can not be empty")
		}
		var role = inputModel.Role
		if role != nil {
			if inputModel.Role != nil {
				userRole, err := postgres.GetUserRoleIdByUserRole(*inputModel.Role)
				if err != nil {
					entity.UserRoleID = nil
				} else {
					entity.UserRoleID = &userRole
				}
			}

		} else {
			return nil, errors.New("Employee role can not be empty")
		}
	}

	return &entity, nil
}

func EmployeeByRoleInputModelToEntity(input *model.EmployeeRoleRequest) (string, error) {
	var roleID string
	var err error
	if input.Role != "" {
		role := strings.TrimSpace(input.Role)
		// codes := codeController.GetValueKeyCodes()["userrole"]
		roleID, err = postgres.GetRoleIDByValueInUserRoles(role)
		if err != nil {
			return "", errors.New("invalid employee role")
		}
	} else {
		return "", errors.New("Employee role cannot be blank")
	}
	return roleID, nil
}

func EmployeeByRoleInfoEntityToModel(input []entity.Employee) []*model.Employee {
	var response []*model.Employee
	for _, item := range input {
		res := new(model.Employee)
		res.ID = item.ID.String()
		res.FirstName = item.FirstName
		res.LastName = item.LastName
		res.ActiveDirectoryName = item.ActiveDirectory
		response = append(response, res)
	}
	return response

}

func ExportEmployeeAuditLogsInputModelToEntity(input model.EmployeeAuditLogsRequest, country *int) (*entity.EmployeeAuditLogsEntity, error) {
	var outEntity entity.EmployeeAuditLogsEntity
	sortElements := []entity.SortingElements{}
	var uid uuid.UUID
	var err error
	if input.UserID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).UserID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.UserID = &uid

	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if country != nil {
		outEntity.Country = *country
	}
	if input.ActionBy != nil {
		outEntity.ActionBy = *(input).ActionBy
	}
	if input.ActionType != nil {
		outEntity.ActionType = *(input).ActionType
	}
	if input.ActiveDirectoryName != nil {
		outEntity.ActiveDirectoryName = *(input).ActiveDirectoryName
	}
	if input.FirstName != nil {
		outEntity.FirstName = *(input).FirstName
	}
	if input.LastName != nil {
		outEntity.LastName = *(input).LastName
	}
	if input.Email != nil {
		outEntity.Email = *(input).Email
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = strings.TrimSpace(*(input).SearchItem)
	}
	if input.ActionStartDate != nil && *input.ActionStartDate != "" {
		outEntity.ActionStartDate = strings.TrimSpace(*(input).ActionStartDate)
	}
	if input.ActionEndDate != nil && *input.ActionEndDate != "" {
		outEntity.ActionEndDate = strings.TrimSpace(*(input).ActionEndDate)
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements
	return &outEntity, nil

}
