package entity

import (
	"database/sql"
	"strconv"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

type MaterialUpsertInput struct {
	ID           *uuid.UUID
	GroupCode    string
	GroupName    string
	CountryName  string
	DivisionName *string
	IsActive     bool
	IsDeleted    bool
	CountryCode  int
	ClientId     *uuid.UUID
}
type MaterialExcelInput struct {
	ID           *uuid.UUID
	GroupCode    string
	GroupName    string
	CountryName  string
	CountryNo    int
	DivisionName string
	IsActive     bool
	IsDeleted    bool
}

type MaterialExcelResponse struct {
	ID               string
	Country          string
	GroupCode        string
	GroupName        string
	Active           string
	VeevaReferenceID string
	ClientName       string
}
type MaterialExcelData struct {
	RowNo int
	Data  []interface{}
}

type MaterialExcel struct {
	ID               *uuid.UUID
	Country          sql.NullInt32
	Division         sql.NullString
	GroupCode        string
	GroupName        string
	MaterialCode     int
	IsActive         bool
	VeevaReferenceID sql.NullString
	ClientName       sql.NullString
}

type ExportMaterialExcel struct {
	IsExcel          bool       `json:"isExcel"`
	ID               *uuid.UUID `json:"id"`
	SearchItem       string
	Country          int    `json:"country"`
	Division         string `json:"division"`
	GroupCode        string `json:"groupCode"`
	GroupName        string `json:"groupName"`
	Status           string `json:"status"`
	Limit            int    `json:"limit"`
	Offset           int    `json:"offset"`
	IsActive         *bool
	VeevaReferenceID string
	ClientName       string
	Sort             []SortingElements
}

type MaterialInstructionExcel struct {
	Column    string
	Mandatory string
	Limit     string
}

func (o *MaterialExcelInput) ValidateMaterialExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(o.ID) != uuid.Nil && o.CountryName == "" && o.DivisionName == "" && o.GroupCode == "" && o.GroupName == "" {
		o.IsDeleted = true
		o.IsActive = false
	} else {
		codes := codeController.GetTitleKeyCodes()["country"]
		_, countryExists := codes[o.CountryName]
		if !countryExists {
			errorMessage := &model.ExcelValidationMessage{Row: (row), Message: "Row " + strconv.Itoa(row) + ": Country does not exist"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.DivisionName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": Division Name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.GroupCode == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": Group Code is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.GroupName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: "Row " + strconv.Itoa(row) + ": GroupName is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
	}
}

func (o *MaterialUpsertInput) ValidateMaterialUpsertData(result *model.UpsertMaterialResponse) {

	validationMessages := []*model.ValidationMessage{}

	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
		}
		result.ValidationErrors = append(result.ValidationErrors, validationMessages...)
	}

}
