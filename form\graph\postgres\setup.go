package postgres

import (
	"context"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/jackc/pgx/v4/pgxpool"
)

var pool *pgxpool.Pool
var maxRetry int64

func InitDbPool() {
	host := os.Getenv("IHCP_HOST")
	port := os.Getenv("IHCP_PORT")
	user := os.Getenv("IHCP_USER")
	password := os.Getenv("IHCP_PASSWORD")
	dbname := os.Getenv("IHCP_DB_NAME")
	sslMode := os.Getenv("IHCP_SSLMODE")
	searchPath := os.Getenv("IHCP_SEARCH_PATH")

	maxOpenConnection, err := strconv.Atoi(os.Getenv("IHCP_MAX_CONN"))
	if err != nil {
		panic(err)
	}
	maxIdleTime, err := strconv.Atoi(os.<PERSON>env("IHCP_MAX_IDLE_TIME"))
	if err != nil {
		panic(err)
	}
	maxConnectionLifetime, err := strconv.Atoi(os.Getenv("IHCP_MAX_LIFETIME"))
	if err != nil {
		panic(err)
	}
	healthcheckPeriod, err := strconv.Atoi(os.Getenv("IHCP_HEALTHCHECK_PREIOD"))
	if err != nil {
		panic(err)
	}

	databaseUrl := "host=" + host + " port=" + port + " user=" + user + " password=" + password + " dbname=" + dbname + " sslmode=" + sslMode + " search_path=" + searchPath
	config, err := pgxpool.ParseConfig(databaseUrl)
	if err != nil {
		panic(err)
	}
	config.MaxConns = int32(maxOpenConnection)
	config.MaxConnLifetime = time.Duration(maxConnectionLifetime) * time.Minute
	config.HealthCheckPeriod = time.Duration(healthcheckPeriod) * time.Minute
	config.MaxConnIdleTime = time.Duration(maxIdleTime) * time.Minute

	pool, err = pgxpool.ConnectConfig(context.Background(), config)

	if err != nil {
		poolConnectionRefresh, err := strconv.ParseUint(os.Getenv("POOL_CONNECTION_INTERVAL"), 10, 64)
		if err != nil {
			poolConnectionRefresh = 15
		}

		retryMaxTime, err := strconv.ParseInt(os.Getenv("MAX_RETRY"), 10, 64)
		if err != nil {
			retryMaxTime = 10
		}

		if maxRetry < retryMaxTime {
			maxRetry++
			log.Print("Could not connect to Postgres.", err)
			time.Sleep(time.Second * time.Duration(poolConnectionRefresh))
			InitDbPool()
		}
	}
	log.Println("Postgres connected!")
}

func GetPool() *pgxpool.Pool {
	if pool != nil {
		return pool
	}
	InitDbPool()
	return pool
}
