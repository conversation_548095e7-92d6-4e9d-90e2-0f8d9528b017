package postgres

import (
	"context"
	"database/sql"
	"errors"
	"log"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
)

func GetTierHonorariumLimit(input model.HonorariumDetails, countryCode *int) (entity.HLimits, error) {
	functionName := "GetTierHonorariumLimit"
	log.Println(functionName)
	var limitEntity entity.HLimits
	var category string
	var controlCategory string
	var value string
	var rows pgx.Rows
	var err error
	if pool == nil {
		pool = GetPool()
	}
	countryID := *countryCode
	var tierLevel string
	var inputargs []interface{}
	if *input.HcpType == "regional" {
		if input.ExpertLevelInternational == nil {
			log.Println("error")
			return entity.HLimits{}, err
		}
		var roles []string
		tierLevel = *input.ExpertLevelInternational
		category = "ExpertLevelInternational"
		controlCategory = "RegionalHonorarium"
		for _, role := range input.Roles {
			if *role.RoleType == "speaker" {
				value = "speaker"
			} else if *role.RoleType == "moderator" {
				value = "moderator"
			} else if *role.RoleType == "advisoryboardmember" {
				value = "advisoryboardmember"
			}
			roles = append(roles, value)
		}
		var queryString string
		if input.MoreThanTwoMeeting != nil {
			queryString = `select category, max(max_limit) from controls where type in (?) and category = ? and is_deleted = false AND is_active = true and (country = ? or country is Null) 
		and code_id in (select id from code where category = ? and value = ?) and more_than_two_lecture = ? group by category`
			inputargs = append(inputargs, roles, controlCategory, countryID, category, tierLevel, *input.MoreThanTwoMeeting)
		} else {
			queryString = `select category, max(max_limit) from controls where type in (?) and category = ? and is_deleted = false AND is_active = true and (country = ? or country is Null) 
		and code_id in (select id from code where category = ? and value = ?) group by category`
			inputargs = append(inputargs, roles, controlCategory, countryID, category, tierLevel)
		}
		query, args, err := sqlx.In(queryString, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return entity.HLimits{}, err
		}
		queryString = sqlx.Rebind(sqlx.DOLLAR, query)
		rows, err = pool.Query(context.Background(), queryString, args...)

	} else {
		if input.ExpertLevel == nil {
			log.Println("error")
			return entity.HLimits{}, err
		}
		tierLevel = *input.ExpertLevel
		category = "HCPTier"
		value = "Honorarium"
		queryString := `select category,max_limit from controls where type = $4 and is_deleted = false AND is_active = true and (country = $1 or country is Null) 
	and code_id in (select id from code where category = $2 and value = $3)`
		rows, err = pool.Query(context.Background(), queryString, countryID, category, tierLevel, value)
	}
	logengine.GetTelemetryClient().TrackEvent("GetTierHonorariumLimit query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return entity.HLimits{}, err
	}
	for rows.Next() {
		err := rows.Scan(
			&limitEntity.Category,
			&limitEntity.Limit,
		)
		if err != nil {
			log.Printf("%s - Error: %s", functionName, err.Error())
			return entity.HLimits{}, err
		}
	}
	return limitEntity, nil
}

func GetTierHonorariumLimitForInternational(input model.HonorariumDetails) (entity.HLimits, error) {
	functionName := "GetTierHonorariumLimitForInternational"
	log.Println(functionName)
	var limitEntity entity.HLimits
	var category string
	var value string
	var rows pgx.Rows
	var tierLevel string
	var controlCategory string
	var err error
	if pool == nil {
		pool = GetPool()
	}
	if input.ExpertLevelInternational == nil {
		log.Println("error")
		return entity.HLimits{}, err
	}
	tierLevel = *input.ExpertLevelInternational
	category = "ExpertLevelInternational"
	controlCategory = "InternationalOthersHonorarium"
	var roles []string
	for _, role := range input.Roles {
		if *role.RoleType == "speaker" {
			value = "speaker"
		} else if *role.RoleType == "moderator" {
			value = "moderator"
		} else if *role.RoleType == "advisoryboardmember" {
			value = "advisoryboardmember"
		}
		roles = append(roles, value)
	}
	var inputargs []interface{}
	var queryString string
	if input.MoreThanTwoMeeting != nil {
		queryString = `select category,max(max_limit) from controls where type in (?) and category = ? and is_deleted = false AND is_active = true  
	and code_id in (select id from code where category = ? and value = ?) and more_than_two_lecture = ?  group by category`
		inputargs = append(inputargs, roles, controlCategory, category, tierLevel, *input.MoreThanTwoMeeting)
	} else {
		queryString = `select category,max(max_limit) from controls where type in (?) and category = ? and is_deleted = false AND is_active = true  
	and code_id in (select id from code where category = ? and value = ?) group by category`
		inputargs = append(inputargs, roles, controlCategory, category, tierLevel)
	}

	query, args, err := sqlx.In(queryString, inputargs...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return entity.HLimits{}, err
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err = pool.Query(context.Background(), queryString, args...)
	logengine.GetTelemetryClient().TrackEvent("GetTierHonorariumLimitForInternational query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return entity.HLimits{}, err
	}
	for rows.Next() {
		err := rows.Scan(
			&limitEntity.Category,
			&limitEntity.Limit,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return entity.HLimits{}, err
		}
	}
	return limitEntity, nil
}

func GetTierHonorariumLimitForRegional(input model.HonorariumDetails, countryCode *int) (entity.HLimits, error) {
	functionName := "GetTierHonorariumLimitForRegional"
	log.Println(functionName)
	var limitEntity entity.HLimits
	var category string
	var value string
	var rows pgx.Rows
	var err error
	if pool == nil {
		pool = GetPool()
	}

	countryID := *countryCode
	if input.ExpertLevel == nil {
		log.Println("error")
		return entity.HLimits{}, err
	}
	tierLevel := input.ExpertLevel

	category = "HCPTier"
	value = "Honorarium"
	queryString := `select category,max_limit from controls where type = $4 and is_deleted = false AND is_active = true and (country = $1 or country is Null) 
	and code_id in (select id from code where category = $2 and value = $3)`
	rows, err = pool.Query(context.Background(), queryString, countryID, category, tierLevel, value)
	logengine.GetTelemetryClient().TrackEvent("GetTierHonorariumLimitForRegional query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return entity.HLimits{}, err
	}
	for rows.Next() {
		err := rows.Scan(
			&limitEntity.Category,
			&limitEntity.Limit,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return entity.HLimits{}, err
		}
	}
	return limitEntity, nil
}

func FetchRoleMaxLimit(input model.HonorariumDetails, countryCode *int) (float64, error) {
	functionName := "FetchRoleMaxLimit"
	log.Println(functionName)

	var roleList []string

	for _, role := range input.Roles {
		tmpRole := role
		roleList = append(roleList, *tmpRole.RoleType)
	}

	countryID := *countryCode
	var roleLimit entity.HLimits
	var queryString string
	var rows pgx.Rows
	var err error

	if pool == nil {
		pool = GetPool()
	}
	log.Printf("FetchRoleMaxLimit roles length:%v", len(roleList))
	if len(roleList) < 1 {
		log.Printf("%s - Error: %s", functionName, "Roles array is empty")
		return 0.0, errors.New("Error:" + functionName + " roles array is empty")
	} else if len(roleList) > 1 {
		queryString = `select category,max_limit from controls where category in ('EngagementLimit') and (country = $1 or country is Null) and code_id is NULL;`
		rows, err = pool.Query(context.Background(), queryString, countryID)
	} else {
		role := roleList[0]
		queryString = `select category,max_limit from controls where category in ('EngagementLimit') 
		and (country = $1 or country is Null) and code_id in (select id from code where category = 'RoleMaxPaidTime' 
		and  value =$2);`
		rows, err = pool.Query(context.Background(), queryString, countryID, role)
	}
	logengine.GetTelemetryClient().TrackEvent("GetTierHonorariumLimitForRegional query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return 0.0, err
	}
	for rows.Next() {
		err := rows.Scan(
			&roleLimit.Category,
			&roleLimit.Limit,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return 0.0, err
		}
	}
	log.Printf("FetchRoleMaxLimit Limit :%v", roleLimit.Limit)
	return roleLimit.Limit, nil
}

func GetInbaseOrOutbaseValue(entity *entity.BaseTypeEntity) (int, error) {
	functionName := "GetInbaseOrOutbaseValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value int
	query := `select max_limit from controls where "type" = $1
	and (CAST($2 AS int) <@ int4range(range_limit)) 
	and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, entity.BaseType, entity.NoOfAttendees).Scan(&value)
	logengine.GetTelemetryClient().TrackEvent("GetInbaseOrOutbaseValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return value, nil
}

func GetInbaseOrOutbaseValueForOtherRoles(entity *entity.BaseTypeEntity) (int, error) {
	functionName := "GetInbaseOrOutbaseValueForOtherRoles()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value int
	query := `select c.max_limit from controls c
	inner join code on c.code_id = code.id 
	where c.category = 'BaseType'
	and "type" = $1
	and code.value = $2
	and c.is_active = true and c.is_deleted = false`
	err := pool.QueryRow(context.Background(), query, entity.BaseType, entity.Role).Scan(&value)
	logengine.GetTelemetryClient().TrackEvent("GetInbaseOrOutbaseValueForOtherRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return value, nil
}

func GetLowerLimitForRange1() (int, error) {
	functionName := "GetLowerLimitForRange1()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var lowerLimit int
	query := `SELECT lower(int4range(range_limit)) from controls where description = 'Range 1'`
	err := pool.QueryRow(context.Background(), query).Scan(&lowerLimit)
	logengine.GetTelemetryClient().TrackEvent("GetLowerLimitForRange1 query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return lowerLimit, nil
}

func GetupperLimitForRange1() (int, error) {
	functionName := "GetupperLimitForRange1()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var upperLimit int
	query := `SELECT upper(int4range(range_limit)) from controls where description = 'Range 1'`
	err := pool.QueryRow(context.Background(), query).Scan(&upperLimit)
	logengine.GetTelemetryClient().TrackEvent("GetupperLimitForRange1 query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}

	return upperLimit, nil
}

func GetUSDConversionForCountries(countryID string) (float64, error) {
	functionName := "GetUSDConversionForCountries()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	codes := codeController.GetValueKeyCodes()["country"]
	usdValue := codes["us"].ID
	var amount float64
	query := `select target_rate from currency_exchange where 
	country = (select id from code where value = left ($1,2) and category = 'Country' and is_active =true ) 
	and target_country = $2 order by date_created desc limit 1`

	err := pool.QueryRow(context.Background(), query, countryID, usdValue).Scan(&amount)
	logengine.GetTelemetryClient().TrackEvent("GetUSDConversionForCountries query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return amount, nil
}

func CheckCurrencyCodeLimitInControlsUsd(countryID int) bool {
	functionName := "CheckCurrencyCodeLimitInControlsUsd()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	action := "usd"
	codes := codeController.GetValueKeyCodes()["currency"]
	usdCode := codes[action].ID
	var result bool
	querystring := `select 1 from controls where country = $1 and currency_code_limit = $2 and activity_id is null`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, countryID, usdCode).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func GetCountryByCustomerID(userID *string) (int, error) {
	functionName := "GetCountryByCustomerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `select country from customer where id = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetCountryByCustomerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}
func ValidationHcpEngagment(country *int) map[string]entity.SpeakerValidation {
	log.Println("ValidationHcpEngagment postgres()")
	if pool == nil {
		pool = GetPool()
	}
	SpeakerValidationRes := make(map[string]entity.SpeakerValidation)
	querystring := `	WITH fa AS 
	(	
	SELECT fa.id,fa.event_code as event_code ,fa.event_seq as event_seq ,
	fa.answers	from form_answers fa
	), groups AS 
	(
		SELECT id,event_code   ,event_seq , arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
		arr(item_object)
	), tab_section AS (
		SELECT id,event_code ,event_seq ,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
	), group_questions AS (
		SELECT id,event_code ,event_seq , arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT id,event_code ,event_seq , arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS 
	(
		SELECT id,event_code ,event_seq , arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'id' IN ('speaker-name','proposed-honorarium') or arr.item_object->>'title' IN ('Expenses')
	),fetch_details as (
		select id,
		unnest ((select array(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id in( 'proposed-honorarium') AND questions.id = fa.id )) )as proposed_honorarium,
		unnest ((select array(SELECT qn_values#>>'{0,id}' FROM  questions WHERE qn_id = 'speaker-name' AND questions.id = fa.id))) as customer_id
		from fa 
	)
	select c."name",count(c."name" ),sum(case when fd.proposed_honorarium='' then 0 else fd.proposed_honorarium::int end)  from fetch_details fd 
	left join customer c on c.id::text =fd.customer_id::text
	inner join form_answers fa2 on fa2.id =fd.id
	where  fa2.date_created between date_trunc('year', current_date) and (date_trunc('year',current_date)+'1year') and fa2.country =$1
	GROUP BY c."name"  
    HAVING COUNT(c."name") > 1`
	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
	}
	for rows.Next() {
		var resSpeakerName sql.NullString
		var resTotalUseOfSpeaker sql.NullInt32
		var resTotalAmountOfSpeaker sql.NullInt32
		rows.Scan(
			&resSpeakerName,
			&resTotalUseOfSpeaker,
			&resTotalAmountOfSpeaker,
		)
		value := entity.SpeakerValidation{
			UseLimit:         int(resTotalUseOfSpeaker.Int32),
			TotalAmountLimit: int(resTotalAmountOfSpeaker.Int32),
		}
		SpeakerValidationRes[resSpeakerName.String] = value
	}
	return SpeakerValidationRes
}
func GetValidationData(country *int) (int, int, error) {
	log.Println("GetValidationData postgres()")
	if pool == nil {
		pool = GetPool()
	}
	totalAmount := 0
	useLimit := 0
	query := `select cv.total_amount_limit ,cv.use_limit  from customer_validation cv 
	where cv.country =$1 and cv.is_active =true and cv.is_deleted =false`
	err := pool.QueryRow(context.Background(), query, country).Scan(&totalAmount, &useLimit)
	if err != nil {
		return useLimit, totalAmount, err
	}
	return useLimit, totalAmount, nil
}
