CREATE TABLE "form_answers_relation" (
	id uuid primary key NOT NULL DEFAULT public.uuid_generate_v4(),
	form_answer uuid NOT NULL,
	parent uuid not null,
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	created_by uuid NOT NULL,
	date_created timestamptz NOT NULL DEFAULT now(),
	modified_by uuid NULL,
	last_modified timestamptz NULL
);

ALTER TABLE form_answers_relation ADD CONSTRAINT fk_form_answers FOREIGN KEY (form_answer) REFERENCES form_answers(id);
ALTER TABLE form_answers_relation ADD CONSTRAINT fk_parent FOREIGN KEY (parent) REFERENCES form_answers(id);

