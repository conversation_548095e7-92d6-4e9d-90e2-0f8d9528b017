-- fmv_controls_audit_logs definition

-- Drop table

-- DROP TABLE fmv_controls_audit_logs;

CREATE TABLE fmv_controls_audit_logs (
	id uuid NOT NULL DEFAULT uuid_generate_v4(),
	description text NULL,
	max_limit numeric NULL,
	country int8 NULL,
	created_by uuid NOT NULL,
	date_created timestamptz NOT NULL DEFAULT now(),
	modified_by uuid NULL,
	last_modified timestamptz NULL,
	category text NULL,
	"type" text NULL,
	activity_id uuid NULL,
	code_id int8 NULL,
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	range_limit int4range NULL,
	currency_code_limit int4 NULL,
	more_than_two_lecture bool NULL DEFAULT false,
	CONSTRAINT fmv_controls_pkey PRIMARY KEY (id)
);

-- Permissions

ALTER TABLE fmv_controls_audit_logs OWNER TO ezflowdevadmin;
GRANT ALL ON TABLE fmv_controls_audit_logs TO ezflowdevadmin;


-- fmv_controls_audit_logs foreign keys

ALTER TABLE fmv_controls_audit_logs ADD CONSTRAINT fk_fmv_controls_activity FOREIGN KEY (activity_id) REFERENCES activity(id);
ALTER TABLE fmv_controls_audit_logs ADD CONSTRAINT fk_fmv_controls_country_code FOREIGN KEY (country) REFERENCES code(id);
ALTER TABLE fmv_controls_audit_logs ADD CONSTRAINT fk_fmv_controls_currency_code_limit FOREIGN KEY (currency_code_limit) REFERENCES code(id);
ALTER TABLE fmv_controls_audit_logs ADD control_id uuid NULL;