
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8ff310e5-c318-4ab0-b274-001ccb441df6'::uuid, 'Half day for local event', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:07:26.980', NULL, NULL, 'LocalExpense', 'Honorarium', '65e653da-4d0e-4fa0-a03a-ef44508079de'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('7d8cafd8-722f-4109-865f-76ece024fd23'::uuid, 'One day for local event', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:08:31.941', NULL, NULL, 'LocalExpense', 'Honorarium', '65e653da-4d0e-4fa0-a03a-ef44508079de'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('f4595855-891b-422e-aff5-1586fae3340a'::uuid, 'One day for international symposiums', 35000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:09:51.653', NULL, NULL, 'LocalExpense', 'Honorarium', '257efdaf-05d8-4840-adcb-4245b57a5bf7'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8c598dc2-1d64-402e-abff-2539904635da'::uuid, 'Serve on Advisory Board', 35000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 22:02:55.422', NULL, NULL, 'LocalExpense', 'Honorarium', 'b5bbc5ad-b38d-4be5-9b50-da7dfd6b3261'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5308ac60-0031-4167-9739-eb7706432dca'::uuid, 'Other consultancy engagements', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 22:03:46.351', NULL, NULL, 'LocalExpense', 'Honorarium', 'f50ec3ca-e155-441f-bd1e-42eabe12e86c'::uuid, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('7163ade6-ecd7-4e1c-a967-aa454dddffe7'::uuid, 'Specialist Tier 2', 4000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:16.614', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 104, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('3ee064b7-1de0-487e-adaa-99796631a3ec'::uuid, 'Specialist Tier 3', 3000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:00.807', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 103, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8ff77d72-c274-4dc6-bc01-a644f582377c'::uuid, 'Specialist Tier 1', 5000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:29.617', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 105, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5961b2b1-ccf8-4e06-b782-6256dbeeb085'::uuid, 'General Tier', 2000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:54.032', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 106, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5836ba79-1f7d-495a-9fb4-2c105bb75b25'::uuid, 'Speaker', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 07:46:39.086', NULL, NULL, 'RoleMaxPaidTime', 'Honorarium', NULL, 124, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('*************-44e1-9bdc-ca77a59698de'::uuid, 'Advisory Board Member', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 08:03:54.986', NULL, NULL, 'EngagementLimit', 'Role', NULL, 127, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('d5611f07-0454-467c-8de5-f5fad067eeb9'::uuid, 'Moderator', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 08:05:21.239', NULL, NULL, 'EngagementLimit', 'Role', NULL, 125, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('441a15a7-04af-4c4a-929b-27654e84f6b3'::uuid, 'Multiple Role', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 09:16:33.891', NULL, NULL, 'EngagementLimit', 'MultipleRole', NULL, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('2bcbe117-1451-44d4-8dbb-cfb068425b99'::uuid, 'Day-to-day promotional activities', 500, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-14 06:55:07.476', NULL, NULL, 'ExpenseLimit', 'Meal', 'f37cefb7-3283-49fe-aecd-066e61a4035f'::uuid, 34, true, false, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('adb384de-ae90-4760-a579-35af101f56b8'::uuid, 'Speaker', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-11-20 12:08:05.911', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('185d9254-4c1a-454e-a726-2b4aa9803a5c'::uuid, 'Speaker', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-11-20 12:20:32.046', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs 
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('06d9f8bb-**************-2881eb7dff5d'::uuid, 'Meals', 2500, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-11-05 13:27:18.556', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 132, true, false, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8ff310e5-c318-4ab0-b274-001ccb441df6'::uuid, 'Half day for local event', 15000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:07:26.980', NULL, NULL, 'LocalExpense', 'Honorarium', '65e653da-4d0e-4fa0-a03a-ef44508079de'::uuid, NULL, false, true, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('7d8cafd8-722f-4109-865f-76ece024fd23'::uuid, 'One day for local event', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:08:31.941', NULL, NULL, 'LocalExpense', 'Honorarium', '65e653da-4d0e-4fa0-a03a-ef44508079de'::uuid, NULL, false, true, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('f4595855-891b-422e-aff5-1586fae3340a'::uuid, 'One day for international symposiums', 35000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 21:09:51.653', NULL, NULL, 'LocalExpense', 'Honorarium', '257efdaf-05d8-4840-adcb-4245b57a5bf7'::uuid, NULL, false, true, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8c598dc2-1d64-402e-abff-2539904635da'::uuid, 'Serve on Advisory Board', 35000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 22:02:55.422', NULL, NULL, 'LocalExpense', 'Honorarium', 'b5bbc5ad-b38d-4be5-9b50-da7dfd6b3261'::uuid, NULL, false, true, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs 
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5308ac60-0031-4167-9739-eb7706432dca'::uuid, 'Other consultancy engagements', 20000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-11 22:03:46.351', NULL, NULL, 'LocalExpense', 'Honorarium', 'f50ec3ca-e155-441f-bd1e-42eabe12e86c'::uuid, NULL, false, true, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('2bcbe117-1451-44d4-8dbb-cfb068425b99'::uuid, 'Day-to-day promotional activities', 500, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-14 06:55:07.476', NULL, NULL, 'ExpenseLimit', 'Meal', 'f37cefb7-3283-49fe-aecd-066e61a4035f'::uuid, 34, false, true, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('7163ade6-ecd7-4e1c-a967-aa454dddffe7'::uuid, 'Specialist Tier 2', 5500, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:16.614', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 104, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('3ee064b7-1de0-487e-adaa-99796631a3ec'::uuid, 'Specialist Tier 3', 3500, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:00.807', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 103, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('8ff77d72-c274-4dc6-bc01-a644f582377c'::uuid, 'Specialist Tier 1', 7000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-12 20:50:29.617', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 105, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5836ba79-1f7d-495a-9fb4-2c105bb75b25'::uuid, 'Speaker', 40000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 07:46:39.086', NULL, NULL, 'RoleMaxPaidTime', 'Honorarium', NULL, 124, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('*************-44e1-9bdc-ca77a59698de'::uuid, 'Advisory Board Member', 40000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 08:03:54.986', NULL, NULL, 'EngagementLimit', 'Role', NULL, 127, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('d5611f07-0454-467c-8de5-f5fad067eeb9'::uuid, 'Moderator', 25000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 08:05:21.239', NULL, NULL, 'EngagementLimit', 'Role', NULL, 125, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('441a15a7-04af-4c4a-929b-27654e84f6b3'::uuid, 'Multiple Role', 40000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-10-30 09:16:33.891', NULL, NULL, 'EngagementLimit', 'MultipleRole', NULL, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('adb384de-ae90-4760-a579-35af101f56b8'::uuid, 'Speaker', 40000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-11-20 12:08:05.911', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, 44, false);
INSERT INTO fmv_controls_audit_logs 
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('185d9254-4c1a-454e-a726-2b4aa9803a5c'::uuid, 'Speaker', 40000, 9, '52dad980-4b26-411f-96a3-83483ad143a9'::uuid, '2020-11-20 12:20:32.046', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, 44, false);
