package mapper

import (
	"strconv"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
)

func MapActivityEntitiesToModels(entities []*entity.Activity) []*model.ActivityModel {
	models := []*model.ActivityModel{}
	for _, activityEntity := range entities {
		activityModel := &model.ActivityModel{
			ID:          (*activityEntity.ID).String(),
			Description: activityEntity.Description,
			IsActive:    *activityEntity.IsActive,
			Country:     *activityEntity.CountryTitle,
		}
		models = append(models, activityModel)
	}
	return models
}

func HcpMealValidation(entities []*entity.HcpMealEntity, inputs *model.HcpMealInput, response *model.ValidateHcpMealResponse, Country int) {
	var input = inputs.Input
	models := []*model.ValidationMessage{}

	for _, item := range input {
		model := &model.ValidationMessage{}
		for _, val := range entities {

			if postgres.CheckCurrencyCodeLimitInControlsUsd(Country) {
				codes := codeController.GetIdKeyCodes()["country"]
				countryValue := codes[Country].Value
				amount, _ := postgres.GetUSDConversionForCountries(countryValue)
				if amount != 0 {
					item.MealTotalCost = item.MealTotalCost * amount
				}
			}
			meal_price := item.MealTotalCost / item.MealNo
			if val.Value == item.MealType {
				if val.MaxLimit < meal_price {
					response.Error = true
					model.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") has exceeded the limit!"

				} else {
					response.Error = false
					model.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") is within the limit!"
				}
				models = append(models, model)
				response.ValidationMessages = models
				break
			} else if val.Value != item.MealType {
				if val.Value == "meals" {
					if val.MaxLimit < meal_price {
						response.Error = true
						model.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") has exceeded the limit!"
					} else {
						response.Error = false
						model.Message = item.MealType + " (" + strconv.FormatFloat(item.MealTotalCost, 'f', 2, 64) + ") is within the limit!"
					}
					models = append(models, model)
					response.ValidationMessages = models
				}

			}

		}
	}
}
