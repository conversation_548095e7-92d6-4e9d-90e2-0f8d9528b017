package controller

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"runtime/debug"

	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/teams_service"
	"github.com/ihcp/form/graph/veeva"
)

type UpdateFormAnswerInput struct {
	MeetingID     string `json:"meetingID"`
	RequesterName string `json:"requesterName"`
	OwnerName     string `json:"ownerName"`
	ProductName   string `json:"productName"`
	ClientName    string `json:"clientName"`
}

func UpdateFormAnswers(ctx context.Context, options []map[string]interface{}) (*model.CommonResponse, error) {
	if len(options) == 0 {
		return nil, errors.New("no input provided")
	}

	if len(options) > 10 {
		return nil, errors.New("no more than 10 events at a time")
	}

	list := make([]UpdateFormAnswerInput, len(options))
	for i, v := range options {
		form := UpdateFormAnswerInput{}
		f, err := json.Marshal(v)
		if err != nil {
			panic(err)
		}

		if err := json.Unmarshal(f, &form); err != nil {
			panic(err)
		}

		if form.MeetingID == "" {
			return nil, errors.New("meetingID is required")
		}

		list[i] = form
	}

	var maxWorker int = 3
	workBucket := make(chan struct{}, maxWorker)

	_, err := veeva.GetAuthRest()
	if err != nil {
		teams_service.Send(context.Background(), "--- Err: Sync Events To Veeva.\n Veeva API authentication has failed:", err.Error(), `UpdateFormAnswers`)
		return nil, errors.New("veeva API authentication has failed")
	}

	for i := range list {
		workBucket <- struct{}{}
		go func() {
			defer func() { <-workBucket }()
			ctx := context.Background()
			pool := postgres.GetPool()
			tx, err := pool.Begin(ctx)
			if err != nil {
				panic(err)
			}
			defer tx.Rollback(ctx)

			defer func() {
				if err := recover(); err != nil {
					teams_service.SendMsg(ctx, `UPDATING FORM ANSWERS `, fmt.Sprintf(`ID: %v - Update has failed with error: %v \n %v`, list[i].MeetingID, err, string(debug.Stack())), "UpdateEventFormAnswers")
				}
			}()

			_, formAnswer, _ := postgres.GetAnswerByMeetingID(ctx, tx, list[i].MeetingID)
			if formAnswer == nil {
				teams_service.SendMsg(ctx, `UPDATING FORM ANSWERS `, fmt.Sprintf(`ID: %v - mettingID not found`, list[i].MeetingID), "UpdateEventFormAnswers")
				return
			}
			oldStr, _ := json.Marshal(formAnswer)

			// -
			ownerUser := postgres.GetUserByNameOrByADName(ctx, pool, list[i].OwnerName, list[i].MeetingID)
			requesterUser := postgres.GetUserByNameOrByADName(ctx, pool, list[i].RequesterName, list[i].MeetingID)
			var msg string

			//fmt.Println(ownerUser, requesterUser, "-------------")
			// ------------------------
			if ownerUser != nil && requesterUser != nil {
				postgres.UpdateAnswersRequesterAndOwner(ctx, tx, list[i].MeetingID, &postgres.UpdateAnswersRequesterAndOwnerInput{
					OwnerUUID:     ownerUser.UUID.String(),
					OwnerAD:       ownerUser.ActiveDirectory,
					OwnerName:     ownerUser.FullName,
					RequesterUUID: requesterUser.UUID.String(),
					RequesterAD:   requesterUser.ActiveDirectory,
					RequesterName: requesterUser.FullName,
				})

				msg += "\n Updated event requester and owner"
			}
			// ------------------------

			if list[i].ClientName != "" && list[i].ProductName != "" {
				postgres.UpdateAnswersClientAndProduct(ctx, tx, list[i].MeetingID, map[string]interface{}{
					"client": map[string]interface{}{
						"value": list[i].ClientName,
					},
					"product": map[string]interface{}{
						"value": list[i].ProductName,
					},
				})
				msg += "\n Updated event client and product"
			}
			// ------------------------

			formID, answer, status := postgres.GetAnswerByMeetingID(ctx, tx, list[i].MeetingID)
			if answer == nil {
				panic("Something went wrong. This should never be nil")
			}

			//newStr, _ := json.Marshal(answer)
			log.Println(fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - %s - OLD", list[i].MeetingID, msg), string(oldStr), "UpdateEventFormAnswers")
			//teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - %s - NEW", list[i].MeetingID, msg), string(newStr), "UpdateEventFormAnswers")

			postgres.UpsertVeevaEventLogs(context.Background(), formID.String(),
				false, false, "", "", "MANUALLY UPDATED. RSYNC")

			if err := tx.Commit(ctx); err != nil {
				panic(err)
			}

			err = postgres.InsertIntoVeeva(formID.String(), status)
			if err != nil {

				teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - %s - HAS FAILED WITH ERR: ", list[i].MeetingID, msg), err.Error(), "Re-sync to Veeva")
				return
			}
			teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - Has Been Synced To Veeva Successfully", list[i].MeetingID), "", "UpdateEventFormAnswers")
		}()

	}

	return &model.CommonResponse{
		Success: true,
	}, nil
}

func UpdateEventStatusPendingToApproved(ctx context.Context, input model.UpdateEventStatusPendingToApprovedInput) *model.CommonResponse {
	pool := postgres.GetPool()
	formAnswerIDs := postgres.GetEventIDsFromMeetingIds(ctx, pool, input.MeetingIds)
	if len(formAnswerIDs) == 0 {
		return &model.CommonResponse{
			Success: true,
			Message: StringPtr("no event was found"),
		}
	}
	formAnswerList := postgres.FetchMultiFormAnswersStatus(ctx, pool, formAnswerIDs)

	for _, fa := range formAnswerList {
		if fa.Status != 57 {
			return &model.CommonResponse{
				Success: false,
				Message: StringPtr("only pending events can be fast approved"),
			}
		}
	}
	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	postgres.UpdateMultiEventStatusToApproved(formAnswerIDs, tx)

	for _, id := range formAnswerIDs {
		postgres.UpdateEventStartTimeAndEndTimeToNow(tx, id)
	}

	txErr := tx.Commit(ctx)
	if txErr != nil {
		log.Printf("%s - Error: %s ", "UpdateApproveEvents()", txErr.Error())
	}

	for i := range formAnswerIDs {
		err = postgres.InsertIntoVeeva(formAnswerIDs[i], "approved")
		if err != nil {
			teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - HAS FAILED WITH ERR: ", formAnswerIDs[i]), err.Error(), "Re-sync to Veeva")
			return &model.CommonResponse{
				Success: false,
				Message: StringPtr("Update success. Syncing failed."),
			}
		}
		teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - Has Been Synced To Veeva Successfully", formAnswerIDs[i]), "", "UpdateEventFormAnswers")
	}

	return &model.CommonResponse{
		Success: true,
	}
}

func UpdateApproveEvents(ctx context.Context, input model.UpdateEventStatusCompletedToApprovedInput) *model.CommonResponse {
	pool := postgres.GetPool()
	formAnswerIDs := postgres.GetEventIDsFromMeetingIds(ctx, pool, input.MeetingIds)
	if len(formAnswerIDs) == 0 {
		return &model.CommonResponse{
			Success: true,
			Message: StringPtr("no event was found"),
		}
	}
	formAnswerList := postgres.FetchMultiFormAnswersStatus(ctx, pool, formAnswerIDs)

	for _, fa := range formAnswerList {
		if fa.Status != 67 {
			return &model.CommonResponse{
				Success: false,
				Message: StringPtr("only completed events can be reverted back to approved"),
			}
		}
	}
	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	postgres.UpdateMultiEventStatusToApproved(formAnswerIDs, tx)

	txErr := tx.Commit(ctx)
	if txErr != nil {
		log.Printf("%s - Error: %s ", "UpdateApproveEvents()", txErr.Error())
	}

	return &model.CommonResponse{
		Success: true,
	}
}

const SupportAPIToken string = `do with care`

func AuthenticateSupportAPI(token string) bool {
	if token != SupportAPIToken {
		return false
	}
	return true
}

func StringPtr(s string) *string {
	return &s
}

func DeleteDraftEvents(ctx context.Context, input model.DeleteDraftEventsInput) *model.CommonResponse {
	pool := postgres.GetPool()

	formAnswerIDs := postgres.GetEventIDsFromMeetingIds(ctx, pool, input.MeetingIds)
	if len(formAnswerIDs) == 0 {
		return &model.CommonResponse{
			Success: true,
			Message: StringPtr("no event was found"),
		}
	}

	formAnswerList := postgres.FetchMultiFormAnswersStatus(ctx, pool, formAnswerIDs)
	for _, fa := range formAnswerList {
		if fa.Status != 66 {
			return &model.CommonResponse{
				Success: false,
				Message: StringPtr(fmt.Sprintf("only draft events can be deleted - event: %s - status %d - meetingId: %s", fa.Id, fa.Status, fa.MeetingID)),
			}
		}
	}

	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	postgres.DeleteMultiDraftEvent(ctx, tx, formAnswerIDs)

	if err := tx.Commit(ctx); err != nil {
		panic(err)
	}

	return &model.CommonResponse{
		Success: true,
	}
}

func GetAuditEvent(ctx context.Context, options map[string]interface{}) (*model.EventAudit, error) {
	meetingID, found := options[`meetingID`]
	if !found {
		return nil, errors.New("invalid meeting id")
	}

	rs := postgres.GetAuditEvent(ctx, postgres.GetPool(), meetingID.(string))
	if rs == nil {
		return nil, errors.New("event not found")
	}

	return rs, nil
}

func SyncEventToVeeva(ctx context.Context, eventCode string, options map[string]interface{}) (*model.CommonResponse, error) {
	eventId := postgres.GetEventByEventCode(eventCode)
	fmt.Println(eventId, "----- Options: ", options)

	if eventId == "" {
		if len(options) != 0 {
			if v, f := options["bypass_approval"]; f && v.(bool) == true {
				if err := postgres.CreateEventLog(eventCode); err != nil {
					e := err.Error()
					log.Println(eventId, "----- Err: ", e)
					return &model.CommonResponse{
						Success: false,
						Message: &e,
					}, nil
				}
			}

			eventId = postgres.GetEventByEventCode(eventCode)
		} else {
			return &model.CommonResponse{
				Success: false,
				Message: func() *string {
					s := `err_event_not_found_or_not_approved`
					return &s
				}(),
			}, nil
		}
	}

	_, err := veeva.GetAuthRest()
	if err != nil {
		return &model.CommonResponse{
			Success: false,
			Message: func() *string {
				s := err.Error()
				return &s
			}(),
		}, nil
	}

	status := postgres.GetStatusForEachEvent2(eventId)
	log.Println(`--- Trying to sync event: `, eventId, status)
	postgres.UpsertVeevaEventLogs(context.Background(), eventId,
		false, false, "", "", "MANUALLY UPDATED. RSYNC")

	if err := postgres.InsertIntoVeeva(eventId, status); err != nil {
		teams_service.Send(context.Background(), fmt.Sprintf(`--- Syncing Event Error: %v %v Err: %v`, eventId, status, err), "", "")
		return &model.CommonResponse{
			Success: false,
			Message: func() *string {
				s := err.Error()
				return &s
			}(),
		}, nil
	}

	log.Println(`--- Event has been synced successfully: `, eventId)
	return &model.CommonResponse{
		Success: true,
	}, nil
}

func UpdateNumberofEMP(ctx context.Context, input model.UpdateNumberofEMPInput) *model.CommonResponse {
	pool := postgres.GetPool()
	var meetingIds []string

	// extract meeting IDs from the input
	for _, data := range input.Data {
		meetingIds = append(meetingIds, data.MeetingID)
	}
	formAnswerIDs := postgres.GetEventIDsFromMeetingIds(ctx, pool, meetingIds)

	if len(formAnswerIDs) == 0 {
		return &model.CommonResponse{
			Success: true,
			Message: StringPtr("no event was found"),
		}
	}
	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)
	for _, data := range input.Data {
		postgres.UpdateNumberofEMP(tx, data.MeetingID, data.EmpNumbers)
	}

	txErr := tx.Commit(ctx)
	if txErr != nil {
		log.Printf("%s - Error: %s ", "UpdateNumberofEMP()", txErr.Error())
	}

	for i := range formAnswerIDs {
		err = postgres.InsertIntoVeeva(formAnswerIDs[i], "approved")
		if err != nil {
			teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - HAS FAILED WITH ERR: ", formAnswerIDs[i]), err.Error(), "Re-sync to Veeva")
			return &model.CommonResponse{
				Success: false,
				Message: StringPtr("Update success. Syncing failed."),
			}
		}
		teams_service.SendMsg(ctx, fmt.Sprintf("UPDATING FORM ANSWERS - ID: %s - Has Been Synced To Veeva Successfully", formAnswerIDs[i]), "", "UpdateNumberofEMP")
	}

	return &model.CommonResponse{
		Success: true,
	}
}
