ALTER TABLE test.customer ADD  "country" int NOT NULL;
ALTER TABLE test.customer ADD CONSTRAINT fk_country FOREIGN KEY (country) REFERENCES code(id);

ALTER TABLE test.employee ADD "country" int NOT NULL;
ALTER TABLE test.employee ADD CONSTRAINT fk_country FOREIGN KEY (country) REFERENCES code(id);

ALTER TABLE test.material ADD  "country" int NOT NULL;
ALTER TABLE test.material ADD CONSTRAINT fk_country FOREIGN KEY (country) REFERENCES code(id);