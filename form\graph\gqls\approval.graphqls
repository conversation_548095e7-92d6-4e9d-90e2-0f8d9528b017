type roles{
    role: String!
    sequenceNo: Int
}

type group{
    groupName: String!
    roles: [roles!]!
}

type ApprovalRoles{
    activityName: String!
    group: [group!]!
}

type approvalResponse {
    error: Boolean!
    message: String!
    approvalRoles:[ApprovalRoles!]!
}

input upsertRequest {
    isDeleted: Boolean
    id: String
    isActive: Boolean

}

type ApprovalRoleData{
    id : String!
    groupType : String!
    groupTitle : String!
    sequenceNo : Int!
    minLimit : Int
    maxLimit : Int
    activity : String!
    activityTitle : String!
    countryTitle: String!
    countryValue: String!
    hasCondition: Boolean!
    hasInternational: Boolean!
    hasLevelOfInfluence: Boolean!
    department: String
    departmentId: String
    ledBy: [String!]
}

type approvalRolesResponse {
    error: Boolean!
    message: String!
    approvalRoles:[ApprovalRoleData!]!
}

input approvalRolesInput {
    id: String
    searchItem: String
    groupType : String
    sequenceNo : Int
    minLimit : Int
    maxLimit : Int
    activity : String
    hasCondition: Boolean
    hasInternational: Boolean
    hasLevelOfInfluence: Boolean
    department: String
    ledBy: [String!]
}


input upsertApprovalRoleManagementRequest {
    id: String
    isDeleted: Boolean
    activity: String
    groupType: String
    sequenceNo: Int
    minLimit: Int
    maxLimit: Int
    hasCondition: Boolean
    hasInternational: Boolean
    hasLevelOfInfluence:Boolean
    department: String
    alternateDepartment: String
    alternateGroupType: String
    ledBy: [String!]
}


type upsertApprovalRoleManagementResponse {
  error: Boolean!
  message: String!
}

type getUserRoles{
    id: String!
    title: String!
    value: String!
}

type getUserRolesByapprover {
    error: Boolean!
    message: String!
    data: [getUserRoles]!
}

input UserRoles{
    searchItem: String
}

input delegateApproverInput{
    formAnswerID: String!
    approverID: String!
    isApollo:Boolean
}

type delegateApproverResponse{
    error: Boolean!
    message: String!
}

type approverData{
    approverID: String!
    approverName: String!
    userRole: String!
}
type sameApproverList{
    error: Boolean!
    message: String!
    data: [approverData]!


}

extend type Query {
    getApprovalRoleManagement(input: approvalRolesInput): approvalRolesResponse!
    getUserRolesForapprover(input: UserRoles): getUserRolesByapprover!
    getApproversOfSameRole: sameApproverList!
}
extend type Mutation {
    upsertApprovalRoles(input: upsertRequest): approvalResponse!
    upsertApprovalRoleManagement(input: upsertApprovalRoleManagementRequest): upsertApprovalRoleManagementResponse!
    delegateApprover(input: delegateApproverInput!): delegateApproverResponse!
}
