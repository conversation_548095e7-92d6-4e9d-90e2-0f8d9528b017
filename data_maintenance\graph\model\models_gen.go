// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

type ClientInput struct {
	ID         *string `json:"id,omitempty"`
	IsDelete   *bool   `json:"isDelete,omitempty"`
	ClientName *string `json:"clientName,omitempty"`
	IsActive   *bool   `json:"isActive,omitempty"`
}

type Control struct {
	ID          string `json:"id"`
	MaxLimit    int    `json:"maxLimit"`
	Category    string `json:"category"`
	Type        string `json:"type"`
	Description string `json:"description"`
	Status      string `json:"status"`
	Country     string `json:"country"`
}

type ControlInput struct {
	ID       *string `json:"id,omitempty"`
	IsDelete *bool   `json:"isDelete,omitempty"`
	MaxLimit *int    `json:"maxLimit,omitempty"`
}

type ControlRequest struct {
	ID          *string `json:"id,omitempty"`
	Category    *string `json:"category,omitempty"`
	Type        *string `json:"type,omitempty"`
	Description *string `json:"description,omitempty"`
	Status      *string `json:"status,omitempty"`
	Limit       *int    `json:"limit,omitempty"`
	PageNo      *int    `json:"PageNo,omitempty"`
}

type ControlResponse struct {
	Error      bool       `json:"error"`
	Message    string     `json:"message"`
	TotalCount *int       `json:"TotalCount,omitempty"`
	Data       []*Control `json:"data"`
}

type CountryCurrency struct {
	ID                   string      `json:"id"`
	ApprovalRole         *string     `json:"ApprovalRole,omitempty"`
	CountryDescription   string      `json:"CountryDescription"`
	CountryValue         string      `json:"CountryValue"`
	CurrencyDescription  string      `json:"CurrencyDescription"`
	CurrencyValue        string      `json:"CurrencyValue"`
	ConversionRate       *float64    `json:"ConversionRate,omitempty"`
	IsChangeRequestAllow bool        `json:"isChangeRequestAllow"`
	IsMonitoring         *bool       `json:"isMonitoring,omitempty"`
	AccessControl        *Permission `json:"AccessControl,omitempty"`
}

type Customer struct {
	ID               string  `json:"id"`
	Country          string  `json:"country"`
	CountryValue     string  `json:"countryValue"`
	Team             string  `json:"team"`
	VPosition        string  `json:"vPosition"`
	EmpFirstName     string  `json:"empFirstName"`
	EmpLastName      string  `json:"empLastName"`
	CustomerNumber   string  `json:"customerNumber"`
	CustomerName     string  `json:"customerName"`
	VSpDesc          string  `json:"vSpDesc"`
	CmslClass        string  `json:"cmslClass"`
	City             string  `json:"city"`
	Gender           string  `json:"gender"`
	SpeakerWeight    float64 `json:"SpeakerWeight"`
	IsActive         bool    `json:"isActive"`
	Organization     string  `json:"organization"`
	AccountType      string  `json:"accountType"`
	VeevaReferenceID string  `json:"veevaReferenceId"`
}

type CustomerExcelRequest struct {
	IsExcel          bool             `json:"isExcel"`
	IsForm           *bool            `json:"isForm,omitempty"`
	ID               *string          `json:"id,omitempty"`
	SearchItem       *string          `json:"searchItem,omitempty"`
	Team             *string          `json:"team,omitempty"`
	VPosition        *string          `json:"vPosition,omitempty"`
	EmpFirstName     *string          `json:"empFirstName,omitempty"`
	EmpLastName      *string          `json:"empLastName,omitempty"`
	CustomerNumber   *string          `json:"customerNumber,omitempty"`
	CustomerName     *string          `json:"customerName,omitempty"`
	VSpDesc          *string          `json:"vSpDesc,omitempty"`
	CmslClass        *string          `json:"cmslClass,omitempty"`
	City             *string          `json:"city,omitempty"`
	Gender           *string          `json:"gender,omitempty"`
	Limit            *int             `json:"limit,omitempty"`
	PageNo           *int             `json:"pageNo,omitempty"`
	IsActive         *bool            `json:"isActive,omitempty"`
	VeevaReferenceID *string          `json:"veevaReferenceId,omitempty"`
	Organization     *string          `json:"organization,omitempty"`
	AccountType      *string          `json:"accountType,omitempty"`
	Sort             []*SortingInputs `json:"sort,omitempty"`
}

type CustomerExcelResponse struct {
	Error      bool        `json:"error"`
	Message    string      `json:"message"`
	URL        string      `json:"url"`
	TotalCount int         `json:"totalCount"`
	Data       []*Customer `json:"data"`
}

type CustomerInfoRequest struct {
	ID               *string          `json:"id,omitempty"`
	SearchItem       *string          `json:"searchItem,omitempty"`
	Team             *string          `json:"team,omitempty"`
	VPosition        *string          `json:"vPosition,omitempty"`
	EmpFirstName     *string          `json:"empFirstName,omitempty"`
	EmpLastName      *string          `json:"empLastName,omitempty"`
	CustomerNumber   *string          `json:"customerNumber,omitempty"`
	CustomerName     *string          `json:"customerName,omitempty"`
	VSpDesc          *string          `json:"vSpDesc,omitempty"`
	CmslClass        *string          `json:"cmslClass,omitempty"`
	City             *string          `json:"city,omitempty"`
	Gender           *string          `json:"gender,omitempty"`
	Limit            *int             `json:"limit,omitempty"`
	PageNo           *int             `json:"pageNo,omitempty"`
	IsActive         *bool            `json:"isActive,omitempty"`
	VeevaReferenceID *string          `json:"veevaReferenceId,omitempty"`
	Organization     *string          `json:"organization,omitempty"`
	AccountType      *string          `json:"accountType,omitempty"`
	Sort             []*SortingInputs `json:"sort,omitempty"`
	CustomerIds      []*string        `json:"customerIds,omitempty"`
}

type CustomerInput struct {
	ID             *string  `json:"id,omitempty"`
	IsDeleted      *bool    `json:"isDeleted,omitempty"`
	Team           *string  `json:"team,omitempty"`
	VPosition      *string  `json:"vPosition,omitempty"`
	ActiveDirName  *string  `json:"activeDirName,omitempty"`
	CustomerNumber *string  `json:"customerNumber,omitempty"`
	CustomerName   *string  `json:"customerName,omitempty"`
	VSpDesc        *string  `json:"vSpDesc,omitempty"`
	CmslClass      *string  `json:"cmslClass,omitempty"`
	City           *string  `json:"city,omitempty"`
	Gender         *string  `json:"gender,omitempty"`
	SpeakerWeight  *float64 `json:"SpeakerWeight,omitempty"`
	Organization   *string  `json:"organization,omitempty"`
	AccountType    *string  `json:"accountType,omitempty"`
}

type Department struct {
	ID         string `json:"id"`
	Department string `json:"department"`
}

type DepartmentInput struct {
	ID         *string `json:"id,omitempty"`
	Department *string `json:"Department,omitempty"`
	IsDeleted  *bool   `json:"isDeleted,omitempty"`
}

type DepartmentRole struct {
	ID         string            `json:"id"`
	Department string            `json:"department"`
	UserRole   []*UserRolesValue `json:"userRole"`
}

type DepartmentRoleRequest struct {
	Department *string `json:"department,omitempty"`
	UserRole   *string `json:"userRole,omitempty"`
}

type DepartmentRoleResponse struct {
	Error   bool              `json:"error"`
	Message string            `json:"message"`
	Data    []*DepartmentRole `json:"data"`
}

type DepartmentRolesInput struct {
	ID         *string   `json:"id,omitempty"`
	IsDelete   *bool     `json:"isDelete,omitempty"`
	Department *string   `json:"department,omitempty"`
	UserRole   []*string `json:"userRole,omitempty"`
}

type Employee struct {
	ID                  string  `json:"id"`
	Country             string  `json:"country"`
	Team                *string `json:"team,omitempty"`
	ActiveDirectoryName string  `json:"activeDirectoryName"`
	FirstName           string  `json:"firstName"`
	LastName            string  `json:"lastName"`
	Position            *string `json:"position,omitempty"`
	EmployeeCode        *string `json:"employeeCode,omitempty"`
	Email               *string `json:"email,omitempty"`
	RoleTitle           string  `json:"roleTitle"`
	RoleValue           string  `json:"roleValue"`
	TeamID              *string `json:"teamId,omitempty"`
	IsActive            bool    `json:"isActive"`
	VeevaReferenceID    string  `json:"veevaReferenceId"`
}

type EmployeeAuditLogsData struct {
	ID                  string `json:"id"`
	UserID              string `json:"userId"`
	Country             string `json:"country"`
	ActiveDirectoryName string `json:"activeDirectoryName"`
	FirstName           string `json:"firstName"`
	LastName            string `json:"lastName"`
	EmployeeCode        string `json:"employeeCode"`
	Email               string `json:"email"`
	RoleTitle           string `json:"roleTitle"`
	ActionBy            string `json:"actionBy"`
	ActionType          string `json:"actionType"`
	ActionDate          string `json:"actionDate"`
}

type EmployeeAuditLogsRequest struct {
	Limit               *int             `json:"limit,omitempty"`
	PageNo              *int             `json:"pageNo,omitempty"`
	ActionBy            *string          `json:"actionBy,omitempty"`
	ActionType          *string          `json:"actionType,omitempty"`
	ActionStartDate     *string          `json:"actionStartDate,omitempty"`
	ActionEndDate       *string          `json:"actionEndDate,omitempty"`
	ActiveDirectoryName *string          `json:"activeDirectoryName,omitempty"`
	FirstName           *string          `json:"firstName,omitempty"`
	LastName            *string          `json:"lastName,omitempty"`
	Email               *string          `json:"email,omitempty"`
	UserID              *string          `json:"userId,omitempty"`
	SearchItem          *string          `json:"searchItem,omitempty"`
	IsExcel             bool             `json:"isExcel"`
	Sort                []*SortingInputs `json:"sort,omitempty"`
}

type EmployeeAuditLogsResponse struct {
	Error      bool                     `json:"error"`
	Message    string                   `json:"message"`
	TotalCount int                      `json:"totalCount"`
	URL        string                   `json:"url"`
	Data       []*EmployeeAuditLogsData `json:"data"`
}

type EmployeeInput struct {
	ID                  *string `json:"id,omitempty"`
	IsDelete            *bool   `json:"isDelete,omitempty"`
	TeamID              *string `json:"teamID,omitempty"`
	VPosition           *string `json:"vPosition,omitempty"`
	FirstName           *string `json:"firstName,omitempty"`
	LastName            *string `json:"lastName,omitempty"`
	EmpCode             *string `json:"empCode,omitempty"`
	ActiveDirectoryName *string `json:"activeDirectoryName,omitempty"`
	Email               *string `json:"email,omitempty"`
	Role                *string `json:"role,omitempty"`
}

type EmployeeRequest struct {
	IsExcel             bool             `json:"isExcel"`
	ID                  *string          `json:"id,omitempty"`
	SearchItem          *string          `json:"searchItem,omitempty"`
	Team                *string          `json:"team,omitempty"`
	Position            *string          `json:"position,omitempty"`
	EmployeeCode        *string          `json:"employeeCode,omitempty"`
	ActiveDirectoryName *string          `json:"activeDirectoryName,omitempty"`
	Role                *string          `json:"role,omitempty"`
	FirstName           *string          `json:"firstName,omitempty"`
	LastName            *string          `json:"lastName,omitempty"`
	Limit               *int             `json:"limit,omitempty"`
	PageNo              *int             `json:"pageNo,omitempty"`
	IsActive            *bool            `json:"isActive,omitempty"`
	VeevaReferenceID    *string          `json:"veevaReferenceId,omitempty"`
	Sort                []*SortingInputs `json:"sort,omitempty"`
}

type EmployeeResponse struct {
	Error      bool        `json:"error"`
	Message    string      `json:"message"`
	URL        string      `json:"url"`
	TotalCount int         `json:"totalCount"`
	Data       []*Employee `json:"data"`
}

type EmployeeRoleRequest struct {
	Role string `json:"role"`
}

type EventTypeAccessPermission struct {
	UserRoles      []*UserRole `json:"userRoles"`
	EventTypeName  string      `json:"eventTypeName"`
	EventTypeValue string      `json:"eventTypeValue"`
	ID             string      `json:"id"`
	Country        *string     `json:"country,omitempty"`
	CountryValue   *string     `json:"countryValue,omitempty"`
}

type EventTypeAccessPermissionInput struct {
	EventType *string `json:"eventType,omitempty"`
}

type EventTypeAccessPermissionResponse struct {
	Error   bool                         `json:"error"`
	Message string                       `json:"message"`
	Data    []*EventTypeAccessPermission `json:"data"`
}

type EventTypeAccessPermissionUpsertInput struct {
	ID        *string   `json:"id,omitempty"`
	IsDelete  *bool     `json:"isDelete,omitempty"`
	EventType *string   `json:"eventType,omitempty"`
	UserRole  []*string `json:"userRole,omitempty"`
}

type ExceptionalApprovalManagementInput struct {
	ID          *string `json:"id,omitempty"`
	UserID      *string `json:"userId,omitempty"`
	IsDelete    *bool   `json:"isDelete,omitempty"`
	SequencenNo *int    `json:"sequencenNo,omitempty"`
	GroupType   *string `json:"groupType,omitempty"`
}

type ExceptionalApprovalManagementResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type FetchExceptionalApprovalManagementInput struct {
	UserID       *string `json:"userId,omitempty"`
	IsCompliance *bool   `json:"isCompliance,omitempty"`
}

type FetchExceptionalApprovalManagementResponse struct {
	Error   bool                  `json:"error"`
	Message string                `json:"message"`
	Data    []*UserByApprovalRole `json:"data"`
}

type GetDepartmentInput struct {
	ID         *string `json:"id,omitempty"`
	Department *string `json:"department,omitempty"`
}

type GetDepartmentResponse struct {
	Error       bool          `json:"error"`
	Message     string        `json:"message"`
	Departments []*Department `json:"departments,omitempty"`
}

type GetOfflineDateResponse struct {
	Error       bool           `json:"error"`
	Message     string         `json:"message"`
	OfflineDate []*OfflineDate `json:"offlineDate"`
}

type Material struct {
	ID               string  `json:"id"`
	Country          *string `json:"country,omitempty"`
	Division         *string `json:"division,omitempty"`
	GroupCode        string  `json:"groupCode"`
	GroupName        string  `json:"groupName"`
	Status           string  `json:"status"`
	IsActive         bool    `json:"isActive"`
	ClientName       string  `json:"clientName"`
	VeevaReferenceID string  `json:"veevaReferenceId"`
}

type MaterialInput struct {
	ID         *string `json:"id,omitempty"`
	IsDelete   *bool   `json:"isDelete,omitempty"`
	Division   *string `json:"division,omitempty"`
	GroupCode  *string `json:"groupCode,omitempty"`
	GroupName  *string `json:"groupName,omitempty"`
	ClientName *string `json:"clientName,omitempty"`
	IsActive   *bool   `json:"isActive,omitempty"`
}

type MaterialRequest struct {
	IsExcel          bool             `json:"isExcel"`
	ID               *string          `json:"id,omitempty"`
	SearchItem       *string          `json:"searchItem,omitempty"`
	Division         *string          `json:"division,omitempty"`
	GroupCode        *string          `json:"groupCode,omitempty"`
	GroupName        *string          `json:"groupName,omitempty"`
	Status           *string          `json:"status,omitempty"`
	Limit            *int             `json:"limit,omitempty"`
	PageNo           *int             `json:"pageNo,omitempty"`
	IsActive         *bool            `json:"isActive,omitempty"`
	VeevaReferenceID *string          `json:"veevaReferenceId,omitempty"`
	ClientName       *string          `json:"clientName,omitempty"`
	Sort             []*SortingInputs `json:"sort,omitempty"`
}

type MaterialResponse struct {
	Error      bool        `json:"error"`
	Message    string      `json:"message"`
	URL        string      `json:"url"`
	TotalCount int         `json:"totalCount"`
	Data       []*Material `json:"data"`
}

type Mutation struct {
}

type Permission struct {
	IsApprover          *bool `json:"isApprover,omitempty"`
	IsRequestor         *bool `json:"isRequestor,omitempty"`
	ReadOnlySubmissions *bool `json:"readOnlySubmissions,omitempty"`
	MasterData          *bool `json:"masterData,omitempty"`
	IsAdmin             *bool `json:"isAdmin,omitempty"`
	SuperAdmin          *bool `json:"superAdmin,omitempty"`
}

type Query struct {
}

type SortingInputs struct {
	Column *string `json:"column,omitempty"`
	Sort   *string `json:"sort,omitempty"`
}

type TeamInput struct {
	ID          *string `json:"id,omitempty"`
	IsDelete    *bool   `json:"isDelete,omitempty"`
	TeamName    string  `json:"teamName"`
	TeamCountry string  `json:"TeamCountry"`
}

type UpsertClientResponse struct {
	Error            bool                 `json:"error"`
	Message          string               `json:"message"`
	ValidationErrors []*ValidationMessage `json:"validationErrors,omitempty"`
}

type UpsertControlResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpsertCustomerResponse struct {
	Error            bool                 `json:"error"`
	Message          string               `json:"message"`
	ValidationErrors []*ValidationMessage `json:"validationErrors,omitempty"`
}

type UpsertDepartmentResponse struct {
	Error            bool                 `json:"error"`
	Message          string               `json:"message"`
	ValidationErrors []*ValidationMessage `json:"validationErrors,omitempty"`
}

type UpsertEventTypeAccessPermissionResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpsertMaterialResponse struct {
	Error            bool                 `json:"error"`
	Message          string               `json:"message"`
	ValidationErrors []*ValidationMessage `json:"validationErrors,omitempty"`
}

type UpsertOfflineDateResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UpsertTeamResponse struct {
	Error            bool                 `json:"error"`
	Message          string               `json:"message"`
	ValidationErrors []*ValidationMessage `json:"validationErrors,omitempty"`
}

type UserAuthenticationResponse struct {
	Error     bool         `json:"error"`
	Message   string       `json:"message"`
	ErrorCode int          `json:"errorCode"`
	Data      *UserDetails `json:"data,omitempty"`
}

type UserByApprovalRole struct {
	DepartmentName *string          `json:"departmentName,omitempty"`
	SequenceNo     *int             `json:"sequenceNo,omitempty"`
	DepartmentType *string          `json:"departmentType,omitempty"`
	DepartmentID   *string          `json:"departmentID,omitempty"`
	UserSelection  []*UserSelection `json:"userSelection,omitempty"`
}

type UserCredentialsInput struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

type UserDetails struct {
	ActiveDirectoryName string             `json:"ActiveDirectoryName"`
	JwtToken            string             `json:"JwtToken"`
	CountryCurrency     []*CountryCurrency `json:"countryCurrency"`
}

type UserRole struct {
	UserRoleTitle string `json:"userRoleTitle"`
	UserRoleValue string `json:"userRoleValue"`
}

type UserRoleRequest struct {
	ID       *string `json:"id,omitempty"`
	UserRole *string `json:"userRole,omitempty"`
}

type UserRoleResponse struct {
	Error   bool         `json:"error"`
	Message string       `json:"message"`
	Data    []*UserRoles `json:"data"`
}

type UserRoles struct {
	ID          string `json:"id"`
	UserRole    string `json:"userRole"`
	Title       string `json:"Title"`
	Description string `json:"description"`
}

type UserRolesInput struct {
	ID          *string `json:"id,omitempty"`
	IsDelete    *bool   `json:"isDelete,omitempty"`
	UserRole    *string `json:"userRole,omitempty"`
	Description *string `json:"description,omitempty"`
}

type UserSelection struct {
	Description *string `json:"description,omitempty"`
	Hint        *string `json:"hint,omitempty"`
	Value       *string `json:"value,omitempty"`
	RoleID      *string `json:"roleId,omitempty"`
}

type AzureExcelUploadResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
	URL     string `json:"url"`
}

type Client struct {
	ID         string  `json:"id"`
	Country    *string `json:"country,omitempty"`
	Status     string  `json:"status"`
	IsActive   bool    `json:"isActive"`
	ClientName string  `json:"clientName"`
}

type ClientRequest struct {
	IsExcel    bool             `json:"isExcel"`
	ID         *string          `json:"id,omitempty"`
	Status     *string          `json:"status,omitempty"`
	SearchItem *string          `json:"searchItem,omitempty"`
	Limit      *int             `json:"limit,omitempty"`
	PageNo     *int             `json:"pageNo,omitempty"`
	IsActive   *bool            `json:"isActive,omitempty"`
	ClientName *string          `json:"clientName,omitempty"`
	Sort       []*SortingInputs `json:"sort,omitempty"`
}

type ClientResponse struct {
	Error      bool      `json:"error"`
	Message    string    `json:"message"`
	URL        string    `json:"url"`
	TotalCount int       `json:"totalCount"`
	Data       []*Client `json:"data"`
}

type CodeData struct {
	ID    int    `json:"id"`
	Title string `json:"title"`
	Value string `json:"value"`
}

type CodeInput struct {
	Category string `json:"category"`
}

type CodeResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Data    []*CodeData `json:"data"`
}

type ExcelStatusRequest struct {
	Type *string `json:"type,omitempty"`
}

type ExcelTemplateRequest struct {
	TemplateType string `json:"templateType"`
}

type ExcelUploadRequest struct {
	FileName string `json:"fileName"`
	URL      string `json:"url"`
}

type ExcelUploadResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type ExcelValidationMessage struct {
	Row     int    `json:"row"`
	Message string `json:"message"`
}

type OfflineDate struct {
	ID        string `json:"ID"`
	StartDate *int   `json:"startDate,omitempty"`
	EndDate   *int   `json:"endDate,omitempty"`
}

type OfflineDateInput struct {
	ID        *string `json:"id,omitempty"`
	StartDate int     `json:"startDate"`
	EndDate   int     `json:"endDate"`
	IsDeleted *bool   `json:"is_deleted,omitempty"`
}

type ProxyCountryCurrency struct {
	ID                   string           `json:"id"`
	ApprovalRole         *string          `json:"ApprovalRole,omitempty"`
	CountryDescription   string           `json:"CountryDescription"`
	JwtToken             string           `json:"JwtToken"`
	CountryValue         string           `json:"CountryValue"`
	CurrencyDescription  string           `json:"CurrencyDescription"`
	CurrencyValue        string           `json:"CurrencyValue"`
	ConversionRate       *float64         `json:"ConversionRate,omitempty"`
	IsChangeRequestAllow bool             `json:"isChangeRequestAllow"`
	IsMonitoring         *bool            `json:"isMonitoring,omitempty"`
	AccessControl        *ProxyPermission `json:"AccessControl,omitempty"`
}

type ProxyPermission struct {
	IsApprover          *bool `json:"isApprover,omitempty"`
	IsRequestor         *bool `json:"isRequestor,omitempty"`
	ReadOnlySubmissions *bool `json:"readOnlySubmissions,omitempty"`
	MasterData          *bool `json:"masterData,omitempty"`
	IsAdmin             *bool `json:"isAdmin,omitempty"`
	SuperAdmin          *bool `json:"superAdmin,omitempty"`
}

type ProxyUserAuthenticationResponse struct {
	Error     bool              `json:"error"`
	Message   string            `json:"message"`
	ErrorCode int               `json:"errorCode"`
	Data      *ProxyUserDetails `json:"data,omitempty"`
}

type ProxyUserCredentialsInput struct {
	Username string `json:"username"`
}

type ProxyUserDetails struct {
	ActiveDirectoryName string                  `json:"ActiveDirectoryName"`
	CountryCurrency     []*ProxyCountryCurrency `json:"countryCurrency"`
}

type RegenerateJwtTokenWithCountryRequest struct {
	Country string `json:"country"`
}

type RegenerateJwtTokenWithCountryResponse struct {
	Error   bool             `json:"error"`
	Message string           `json:"message"`
	Data    *RegenerateToken `json:"data,omitempty"`
}

type RegenerateToken struct {
	ActiveDirectoryName string `json:"ActiveDirectoryName"`
	JwtToken            string `json:"JwtToken"`
	Country             string `json:"Country"`
}

type TeamData struct {
	ID       string `json:"id"`
	TeamName string `json:"teamName"`
}

type TeamResponse struct {
	Error   bool        `json:"error"`
	Message string      `json:"message"`
	Data    []*TeamData `json:"data"`
}

type UpsertResponse struct {
	Error   bool   `json:"error"`
	Message string `json:"message"`
}

type UserRolesValue struct {
	ID          string `json:"id"`
	Value       string `json:"value"`
	Title       string `json:"title"`
	Description string `json:"description"`
}

type ValidationMessage struct {
	Message string `json:"message"`
}

type ValidationResult struct {
	Error                   bool                      `json:"error"`
	ValidationTimeTaken     string                    `json:"validationTimeTaken"`
	ExcelValidationMessages []*ExcelValidationMessage `json:"excelValidationMessages,omitempty"`
}
