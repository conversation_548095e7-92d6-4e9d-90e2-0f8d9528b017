package postgres

import (
	"context"
	"errors"
	"log"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func GetDetailsFromUserId(userId uuid.UUID) (*entity.FetchUpsertExceptionalApprovalManagement, error) {
	functionName := "GetDetailsFromUserId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var ent entity.FetchUpsertExceptionalApprovalManagement
	querystring := ` select u.user_role_id as urid, u.country as uc, dr.department as drd
	from "user" u 
	inner join department_roles dr on u.user_role_id = dr.userrole  
	where u.id = $1`
	rows, err := pool.Query(context.Background(), querystring, userId)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		rows.Scan(
			&ent.UserRole,
			&ent.Country,
			&ent.Department,
		)
	}
	logengine.GetTelemetryClient().TrackEvent("GetDetailsFromUserId query called")
	return &ent, nil
}

func InsertExceptionalDetails(exceptionInsertEntity *entity.UpsertExceptionalApprovalManagementEntity, userId uuid.UUID) error {
	functionName := "InsertExceptionalDetails()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	defer tx.Rollback(context.Background())
	querystring := `insert into exceptional_approver_management (user_id ,user_role ,country ,department, created_by, date_created, is_active, is_deleted,sequence_no,group_type) values ($1, $2, $3, $4, $5, now(), true, false,$6,$7)`
	_, err1 := tx.Exec(context.Background(), querystring, exceptionInsertEntity.UserID, exceptionInsertEntity.UserRole, exceptionInsertEntity.Country, exceptionInsertEntity.Department, userId, exceptionInsertEntity.SequenceNo, exceptionInsertEntity.GroupId)
	logengine.GetTelemetryClient().TrackEvent("InsertExceptionalDetails query called")
	if err1 != nil {
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return txErr
	}
	return nil

}

func DeleteInsertExceptionalDetails(entity *entity.UpsertExceptionalApprovalManagementEntity) error {
	functionName := "DeleteInsertExceptionalDetails()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	defer tx.Rollback(context.Background())
	querystring := `update exceptional_approver_management set is_active = false, is_deleted = true where id = $1`
	reff, err1 := tx.Exec(context.Background(), querystring, entity.ID)
	logengine.GetTelemetryClient().TrackEvent("DeleteInsertExceptionalDetails query called")
	if err1 != nil {
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return txErr
	}
	flag := reff.RowsAffected()
	if flag == 0 {
		return errors.New("No Such ID exists!")
	}
	return nil
}
func UpdateInsertExceptionalDetails(exceptionInsertEntity *entity.UpsertExceptionalApprovalManagementEntity, userId uuid.UUID) error {
	functionName := "UpdateInsertExceptionalDetails()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	log.Println(exceptionInsertEntity.ID)
	defer tx.Rollback(context.Background())
	querystring := `update exceptional_approver_management set user_id = $1, user_role = $2 , country = $3 , department = $4, updated_by = $5, updated_at = now(), sequence_no=$7,group_type=$8 where id = $6`
	reff, err1 := tx.Exec(context.Background(), querystring, exceptionInsertEntity.UserID, exceptionInsertEntity.UserRole, exceptionInsertEntity.Country, exceptionInsertEntity.Department, userId, exceptionInsertEntity.ID, exceptionInsertEntity.SequenceNo, exceptionInsertEntity.GroupId)
	logengine.GetTelemetryClient().TrackEvent("UpdateInsertExceptionalDetails query called")
	if err1 != nil {
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return txErr
	}
	flag := reff.RowsAffected()
	if flag == 0 {
		return errors.New("No Such Data exists!")
	}
	return nil
}
func FetchExceptionalApprovalManagementDataFromDB(exceptionentity *entity.FetchExceptionDetails) ([]entity.OutputFetchEntity, error) {
	var entityfromdb []entity.OutputFetchEntity
	functionName := "FetchtexceptionalapprovalmanagementdataFromDB()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	countryCode := codeController.GetValueKeyCodes()["country"]
	regionalCountryId := countryCode["rg"].ID
	var inputArgs []interface{}
	querystring := `with temp_table1 as(
		select  distinct(u.id) as id  ,
		concat(u.first_name,' ',u.last_name) as name,
		u.user_role_id,
		dr.department as department  ,
		ur.value as value,arm.group_type ,
		ur.title as title, 
		u.is_active ,u.is_deleted ,
        (select department  from departments d2 where id = dr.department) as dept 
		 from "user" u 
		inner join user_roles ur on ur.id = u.user_role_id and ur.is_active = true and ur.is_deleted = false
		inner join department_roles dr on dr.userrole = ur.id and dr.is_active = true and dr.is_deleted = false 
		inner join approval_role_management arm  on arm.department  =dr.department and arm.is_active =true and arm.is_deleted  =false
		where u.country in (?,?) and arm.country in (?,?) AND dr.department  in (
		select id from departments d 
		where d.department in (
       select  unnest(string_to_array(value,'|')::text[])as dep from code where id = 435 ) ) 
		),offline_data1 as(
		select od.user_id as userid,true as bool_field  from offline_date od 
	    inner join temp_table1 tt on tt.id=od.user_id 
	    where od.is_active =true and od.is_deleted =false and (od.start_date <=NOW()::date and od.end_date >=NOW()::date)
	    ),
		sequence_temp as(
        with temp1 as(
        select  unnest(string_to_array(value,'|')::text[])as dep from code where id = 435 )
		select dep,ROW_NUMBER() OVER(order by (SELECT 1)) as seq from temp1)
		select distinct (tt.id) ,
		tt.name,
		tt.group_type ,
		tt.department,
		tt.user_role_id,
		tt.value,
		tt.title,
		tt.dept,
		st.seq from temp_table1 tt
		left join offline_data1 odb on odb.userid=tt.id 
		inner join sequence_temp st on tt.dept=st.dep where tt.is_active=true and tt.is_deleted=false and odb.bool_field is null `

	inputArgs = append(inputArgs, exceptionentity.UserCountry, regionalCountryId, exceptionentity.UserCountry, regionalCountryId)
	if exceptionentity.UserID != nil {
		querystring += ` and tt.id=? `
		inputArgs = append(inputArgs, exceptionentity.UserID)
	}
	if exceptionentity.IsCompliance != nil {
		if *exceptionentity.IsCompliance {
			querystring += ` and tt.dept='Compliance'`
		} else {
			querystring += ` and tt.dept <> 'Compliance' `
		}
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputArgs...)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		var ele entity.OutputFetchEntity
		err := rows.Scan(
			&ele.UserId,
			&ele.UserName,
			&ele.GroupType,
			&ele.DepartmentId,
			&ele.UserRole,
			&ele.UserRoleValue,
			&ele.UserRoleTitle,
			&ele.DepartmentName,
			&ele.SequenceNo,
		)

		if err != nil {
			return nil, err
		}
		entityfromdb = append(entityfromdb, ele)
	}

	logengine.GetTelemetryClient().TrackEvent("FetchRequesterCountry query called")
	return entityfromdb, nil
}
