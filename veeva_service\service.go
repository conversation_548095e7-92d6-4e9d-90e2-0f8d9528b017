package veeva_service

import (
	"errors"
	"os"
)

type Controller struct {
	// credentials
	configs struct {
		GrandType     string
		ClientId      string
		ClientSecret  string
		UsernameVeeva string
		PasswordVeeva string
		VeevaAuthUrl  string
	}
	// obtain from veeva
	auth *VeevaAuthorizationRestResponse
}

type VeevaAuthorizationRestResponse struct {
	Id          string `json:"id"`
	IssuedAtt   string `json:"issued_at"`
	InstanceURL string `json:"instance_url"`
	Signature   string `json:"signature"`
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
}

func NewVeevaService() (*Controller, error) {
	s := &Controller{
		configs: struct {
			GrandType     string
			ClientId      string
			ClientSecret  string
			UsernameVeeva string
			PasswordVeeva string
			VeevaAuthUrl  string
		}{
			GrandType:     os.Getenv("GRANT_TYPE"),
			ClientId:      os.Getenv("CLIENT_ID"),
			ClientSecret:  os.Getenv("CLIENT_SECRET"),
			UsernameVeeva: os.Getenv("USERNAME_VEEVA"),
			PasswordVeeva: os.Getenv("PASSWORD"),
			VeevaAuthUrl:  os.Getenv("VEEVA_AUTH_URL"),
		},
	}

	if !s.haveCredentials() {
		return nil, errors.New("insufficient credentials")
	}

	return s, nil
}
