INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('34bcac8d-7062-4539-9212-e13fa42d20cd', 'Specialist Tier 2', 3200000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 104, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('f3abb582-5e94-48b4-8eb7-4cd2f1e32161', 'Multiple Engagements/day', 8500000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'LocalExpense', 'Honorarium', '8a959a65-c0e9-4766-9b8f-f8c2a88be7b7', NULL, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('3cab1453-5d25-40f6-b482-343ffd4726e3', 'Specialist Tier 3', 2700000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 103, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('79123368-84a2-441d-a319-a94b306eeecc', 'Advisory Board/day', 11500000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'LocalExpense', 'Honorarium', 'c796b209-bc98-4afb-9bf3-b0fee9c3331d', NULL, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('1381deb3-ba13-4782-8336-200cf3e8659c', 'Speaking at launch symposiums/day', 11000000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'LocalExpense', 'Honorarium', '75812381-5c1e-4001-8974-2301da9488f0', NULL, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('959a5bec-8c33-4042-ad56-414472132938', 'Meals', 1000000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 132, true, false, NULL, 45, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('d26e6cd3-8a12-4e4a-bb1b-deeabed8ce60', 'Day-to-day promotional activities', 350000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'ExpenseLimit', 'Meal', '65775a8a-b396-4f5c-9e5e-897ccf307970', 34, true, false, NULL, 45, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('35aa3cca-25fc-4749-94b4-59280ae80e70', 'Speaker', 11000000, 10, '00000000-0000-0000-0000-000000000000', '2022-12-01 12:15:59.728', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('a0610105-27a0-4e0a-ad72-4b31cc694668', 'Advisory Board Member', 11500000, 10, '00000000-0000-0000-0000-000000000000', '2022-12-01 12:15:59.728', NULL, NULL, 'EngagementLimit', 'Role', NULL, 127, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('040c12ff-fe86-4a6a-bdc2-81df446f887d', 'Moderator', 11000000, 10, '00000000-0000-0000-0000-000000000000', '2022-12-01 12:15:59.728', NULL, NULL, 'EngagementLimit', 'Role', NULL, 125, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('5c862bc3-dacc-4824-bd2e-307c13825370', 'Specialist Tier 1', 3700000, 10, '52dad980-4b26-411f-96a3-83483ad143a9', '2022-12-01 12:15:59.728', NULL, NULL, 'HcpTier', 'Honorarium', NULL, 105, true, false, NULL, NULL, NULL);
INSERT INTO ezflow_client_uat.fmv_controls_audit_logs
(control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture)
VALUES('e0f7bb90-bd6b-49c8-b226-109c5cfdc05a', 'Multiple Role', 11500000, 10, '00000000-0000-0000-0000-000000000000', '2022-12-01 12:15:59.728', NULL, NULL, 'EngagementLimit', 'MultipleRole', NULL, NULL, true, false, NULL, NULL, NULL);
