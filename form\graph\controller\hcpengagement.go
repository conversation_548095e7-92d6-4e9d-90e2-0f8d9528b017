package controller

import (
	"context"
	"io/ioutil"
	"log"
	"net/url"
	"strings"

	"github.com/ihcp/form/graph/azure"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func SponsoredHcpEngagementTemplate(ctx *context.Context, input model.ExcelUploadForSponsoredHcpEngagementRequest) *model.ExcelUploadForSponsoredHcpEngagementResponse {
	uploadResponse := &model.ExcelUploadForSponsoredHcpEngagementResponse{}
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		uploadResponse.Error = true
		Message := "You are not authorized to login please contact your country ezflow admin."
		uploadResponse.Message = Message
		return uploadResponse
	}
	_, err := url.ParseRequestURI(strings.TrimSpace(input.URL))
	if err != nil {
		uploadResponse.Error = true
		message := err.Error()
		uploadResponse.Message = message
		return uploadResponse
	}
	if strings.TrimSpace(input.FileName) == "" {
		uploadResponse.Error = true
		message := "Please provide valid Filename!"
		uploadResponse.Message = message
		return uploadResponse
	}
	excelFile, downloadErr := azure.DownloadFileFromBlobURL(input.URL)
	if downloadErr == nil {
		excelBytes, _ := ioutil.ReadAll(excelFile)
		sheetName := util.GetSheetName(excelBytes)
		if sheetName == "Sponsored" {
			_, err := util.ParseExcel(excelBytes, sheetName, 45)
			if err != nil {
				uploadResponse.Error = true
				uploadResponse.Message = err.Error()
				return uploadResponse
			} else {
				sponsoredData, ValidationMessages := ProcessHcpEngagementSponsoredExcel(input, ctx, excelBytes)
				if ValidationMessages.Error {
					uploadResponse.Error = true
					uploadResponse.Message = ValidationMessages.ExcelValidationMessages
					uploadResponse.Data = nil
					return uploadResponse
				} else {
					uploadResponse.Error = false
					Message := "Excel has been submitted!"
					uploadResponse.Message = Message
					uploadResponse.Data = sponsoredData
					return uploadResponse
				}
			}
		} else {
			uploadResponse.Error = true
			Message := "Incorrect sheet name!"
			uploadResponse.Message = Message
			return uploadResponse
		}
	} else {
		uploadResponse.Error = true
		message := downloadErr.Error()
		uploadResponse.Message = message
		return uploadResponse
	}
}
func ProcessHcpEngagementSponsoredExcel(input model.ExcelUploadForSponsoredHcpEngagementRequest, ctx *context.Context, excelBytes []byte) ([]*model.SponsoredHcpEngagementData, *model.ValidationResultForSponsoredHcp) {
	functionName := "ProcessHcpEngagementSponsoredExcel()"
	userID := auth.GetUserID(*ctx)
	country := auth.GetCountry(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", functionName, err.Error())
		}
	}
	sheetName := util.GetSheetName(excelBytes)
	sheet, err := util.ParseExcel(excelBytes, sheetName, 45)
	if err != nil {
		panic(err)
	}
	var validationResult *model.ValidationResultForSponsoredHcp
	var responseAllSponsoredData []*model.SponsoredHcpEngagementData
	if sheet != nil {
		var entities []*model.SponsoredHcpEngagementData
		var validationResult *model.ValidationResultForSponsoredHcp
		uploadId := postgres.InsertUploadLogsSponsoredExcel(input, sheetName, userUUID)
		if country != nil {
			entities, validationResult = mapper.MapSponsoredHcpExcelToEntities(sheet, userUUID, input.ActivityID, *country)
		}
		upload, err := uuid.FromString(uploadId)
		var uploadUUID *uuid.UUID
		if err != nil {
			log.Println("Failed to parse upload UUID!")
		} else {
			uploadUUID = &upload
		}
		if !validationResult.Error {
			postgres.UpdateUploadStatus(uploadUUID, "Write Success", userUUID)
		}
		return entities, validationResult
	} else {
		validationResult = &model.ValidationResultForSponsoredHcp{Error: true}
		errorMessage := "Excel format is wrong!"
		validationResult.ExcelValidationMessages = errorMessage
		return responseAllSponsoredData, validationResult
	}
}
