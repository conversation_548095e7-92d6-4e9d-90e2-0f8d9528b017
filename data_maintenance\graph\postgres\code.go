package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

func GetCodeDescriptionByValue(category string, value string) *string {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT id, description FROM code WHERE category = $1 AND value = $2 AND is_active = true AND is_delete = false"
	var id int64
	var url string
	var result *string
	err := pool.QueryRow(context.Background(), querystring, category, value).Scan(&id, &url)
	if err == nil {
		result = &url
	}
	return result
}

func GetCodeValues(input *model.CodeInput) ([]entity.FetchCodeValues, error) {
	functionName := "GetCodeValues()"
	log.Println(functionName)
	var response []entity.FetchCodeValues
	if pool == nil {
		pool = GetPool()
	}
	queryString := `SELECT id, title, value FROM code WHERE category = $1 AND is_active = true AND is_deleted = false`
	rows, err := pool.Query(context.Background(), queryString, input.Category)
	logengine.GetTelemetryClient().TrackEvent("GetCodeValues query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var values entity.FetchCodeValues
		rows.Scan(&values.ID, &values.Title, &values.Value)
		response = append(response, values)
	}
	return response, nil
}

func GetCodeIdByValue(value string) *int64 {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT id FROM code WHERE value = $1 AND is_active = true AND is_deleted = false"
	var id int64
	var result *int64
	err := pool.QueryRow(context.Background(), querystring, value).Scan(&id)
	if err == nil {
		result = &id
	}
	return result
}

func GetCodeValueByCatagory(value string) string {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT value FROM code WHERE category = $1 AND is_active = true AND is_deleted = false"
	var val string
	var result string
	err := pool.QueryRow(context.Background(), querystring, value).Scan(&val)
	if err == nil {
		result = val
	}
	return result
}
