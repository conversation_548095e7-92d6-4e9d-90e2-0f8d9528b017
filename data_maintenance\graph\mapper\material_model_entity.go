package mapper

import (
	"errors"
	"regexp"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
	uuid1 "github.com/satori/go.uuid"
)

func MapMaterialModelToEntity(inputModel *model.MaterialInput, userUUID uuid.UUID) (*entity.MaterialUpsertInput, *model.UpsertMaterialResponse) {
	var id *uuid.UUID
	result := &model.UpsertMaterialResponse{Error: false}
	entity := &entity.MaterialUpsertInput{}
	isDeleted := false
	// if inputModel.IsDelete != nil {
	// 	isDeleted = *(inputModel).IsDelete
	// 	entity.IsDeleted = isDeleted
	// }
	patternGroupName := regexp.MustCompile(`^[A-Za-z_/ 0-9]*\([^()]*\)[A-Za-z_/ ]*$`)
	patternGroupCode := regexp.MustCompile(`^[A-Za-z_/ 0-9]*([()]?[A-Za-z_/ ]*)*$`)
	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			id = &uuid
		} else {
			if !result.Error {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
			}
			errorMessage := &model.ValidationMessage{Message: "Material ID format is invalid!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.ID = id
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			if isDeleted == true {
				entity.IsActive = false
			} else {
				entity.IsActive = true
			}
		} else {
			entity.IsActive = true
		}
		countryID, err := postgres.GetUserCountryByID(userUUID)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Country does not exists!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.CountryCode = countryID

		if inputModel.Division != nil {
			entity.DivisionName = inputModel.Division
		}
		if inputModel.GroupCode != nil && strings.TrimSpace(*inputModel.GroupCode) != "" {
			matchGroupCode := patternGroupCode.MatchString(strings.TrimSpace(*inputModel.GroupCode))
			if !matchGroupCode {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Group Code format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
			entity.GroupCode = strings.TrimSpace(*inputModel.GroupCode)
		}
		if inputModel.GroupName != nil && strings.TrimSpace(*inputModel.GroupName) != "" {
			matchGroupName := patternGroupName.MatchString(strings.TrimSpace(*inputModel.GroupName))
			if !matchGroupName {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Group Name format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
			entity.GroupName = strings.TrimSpace(*inputModel.GroupName)
		}

		if inputModel.ClientName != nil {
			clientId, _ := postgres.GetClientNameToClientID(*inputModel.ClientName, entity.CountryCode)
			uuid1, err := uuid1.FromString(clientId)
			if err == nil {
				entity.ClientId = &uuid1
			} else {
				if !result.Error {
					result.Error = true
					result.ValidationErrors = []*model.ValidationMessage{}
				}
				errorMessage := &model.ValidationMessage{Message: "Please choose correct client name!!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
		}
		// if inputModel.IsActive != nil {
		// 	entity.IsActive = *inputModel.IsActive
		// }

	} else {
		if inputModel.IsDelete != nil {
			isDeleted = *(inputModel).IsDelete
			if isDeleted == true {
				entity.IsActive = false
			} else {
				entity.IsActive = true
			}
		} else {
			entity.IsActive = true
		}

		countryID, err := postgres.GetUserCountryByID(userUUID)
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Country does not exists!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		entity.CountryCode = countryID

		if inputModel.Division != nil {
			entity.DivisionName = inputModel.Division
		}
		if inputModel.GroupCode != nil && strings.TrimSpace(*inputModel.GroupCode) != "" {
			matchGroupCode := patternGroupCode.MatchString(strings.TrimSpace(*inputModel.GroupCode))
			if !matchGroupCode {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Group Code format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
			entity.GroupCode = strings.TrimSpace(*inputModel.GroupCode)
		} else {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Group code cannot be blank!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		if inputModel.GroupName != nil && strings.TrimSpace(*inputModel.GroupName) != "" {
			matchGroupName := patternGroupName.MatchString(strings.TrimSpace(*inputModel.GroupName))
			if !matchGroupName {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Group Name format is invalid!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
			entity.GroupName = strings.TrimSpace(*inputModel.GroupName)
		} else {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Group name cannot be blank!"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
		// if inputModel.IsActive != nil {
		// 	entity.IsActive = *inputModel.IsActive
		// } else {

		// 	result.Error = true
		// 	result.ValidationErrors = []*model.ValidationMessage{}
		// 	errorMessage := &model.ValidationMessage{Message: "IsActive code cannot be blank!"}
		// 	result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		// }
		if inputModel.ClientName != nil {
			clientId, _ := postgres.GetClientNameToClientID(*inputModel.ClientName, entity.CountryCode)
			uuid1, err := uuid1.FromString(clientId)
			if err == nil {
				entity.ClientId = &uuid1
			} else {
				if !result.Error {
					result.Error = true
					result.ValidationErrors = []*model.ValidationMessage{}
				}
				errorMessage := &model.ValidationMessage{Message: "Please choose correct client name!!"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			}
		}

	}

	// if entity.IsDeleted {
	// 	if entity.ID == nil {
	// 		result.Error = true
	// 		result.ValidationErrors = []*model.ValidationMessage{}
	// 		errorMessage := &model.ValidationMessage{Message: "Material ID cannot be blank"}
	// 		result.ValidationErrors = append(result.ValidationErrors, errorMessage)
	// 	}
	// }
	entity.ValidateMaterialUpsertData(result)

	return entity, result
}

func ExportMaterialInputModelToEntity(input *model.MaterialRequest, userUUID uuid.UUID) (*entity.ExportMaterialExcel, error) {
	var outEntity entity.ExportMaterialExcel
	sortElements := []entity.SortingElements{}
	outEntity.IsExcel = input.IsExcel
	var uid uuid.UUID
	var err error
	if input.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(input).ID))
		if err != nil {
			return nil, err
		}
	}
	outEntity.ID = &uid
	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New(err.Error())

	}
	outEntity.Country = countryID
	if input.Division != nil {
		outEntity.Division = strings.TrimSpace(*(input).Division)
	}
	if input.IsActive != nil {
		outEntity.IsActive = input.IsActive
	}
	if input.VeevaReferenceID != nil {
		outEntity.VeevaReferenceID = strings.TrimSpace(*(input).VeevaReferenceID)
	}
	if input.ClientName != nil {
		outEntity.ClientName = strings.TrimSpace(*(input).ClientName)
	}
	if input.GroupCode != nil {
		outEntity.GroupCode = strings.TrimSpace(*(input).GroupCode)
	}
	if input.GroupName != nil {
		outEntity.GroupName = strings.TrimSpace(*(input).GroupName)
	}
	if input.Status != nil {
		outEntity.Status = strings.TrimSpace(*(input).Status)
	}
	if input.Limit != nil {
		outEntity.Limit = *input.Limit
	}
	if input.PageNo != nil && *input.PageNo > 0 {
		outEntity.Offset = (*input.PageNo - 1) * outEntity.Limit
	}
	if input.SearchItem != nil && *input.SearchItem != "" {
		outEntity.SearchItem = *input.SearchItem
	}
	if input.Sort != nil {
		for _, val := range input.Sort {
			sortElement := entity.SortingElements{}
			if val.Column != nil {
				if *val.Column == "" {
					return nil, errors.New("Column cannot be blank")
				} else {
					sortElement.Column = *val.Column
				}
			}
			if val.Sort != nil {
				if *val.Sort == "" {
					sortElement.Sort = "asc"
				} else {
					sortElement.Sort = *val.Sort
				}

			}
			sortElements = append(sortElements, sortElement)
		}
	}
	outEntity.Sort = sortElements
	return &outEntity, nil
}

func ExportMaterialInfoEntityToModel(input []entity.MaterialExcel) ([]*model.Material, []*entity.MaterialExcelData) {
	code := codeController.GetIdKeyCodes()["country"]
	var response []*model.Material
	var responseExcel []*entity.MaterialExcelData
	for _, item := range input {
		res := new(model.Material)
		res.ID = item.ID.String()

		if item.Country.Int32 != 0 {
			country := code[int(item.Country.Int32)].Title.String
			res.Country = &country
		} else {
			res.Country = nil
		}
		// res.Country = code[int(item.Country.Int32)].Title.String
		if item.Division.String != "" {
			division := item.Division.String
			res.Division = &division
		} else {
			res.Division = nil
		}
		// res.Division = &item.Division.String
		res.GroupCode = item.GroupCode
		res.GroupName = item.GroupName
		res.IsActive = item.IsActive
		if item.IsActive {
			res.Status = "Active"
		} else {
			res.Status = "InActive"
		}
		res.VeevaReferenceID = item.VeevaReferenceID.String
		res.ClientName = item.ClientName.String
		response = append(response, res)

		resExcel := new(entity.MaterialExcelData)
		resExcel.Data = append(resExcel.Data, item.ID.String())
		resExcel.Data = append(resExcel.Data, code[int(item.Country.Int32)].Title.String)
		resExcel.Data = append(resExcel.Data, item.GroupCode)
		resExcel.Data = append(resExcel.Data, item.GroupName)
		if item.IsActive {
			resExcel.Data = append(resExcel.Data, "Y")
		} else {
			resExcel.Data = append(resExcel.Data, "N")
		}
		resExcel.Data = append(resExcel.Data, item.VeevaReferenceID.String)
		resExcel.Data = append(resExcel.Data, item.ClientName.String)
		responseExcel = append(responseExcel, resExcel)
	}
	return response, responseExcel
}
