package mapper

import (
	"errors"
	"strings"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func HonorariumValidation(inputModel *model.HonorariumDetails) (*entity.BaseTypeEntity, error) {
	var entity entity.BaseTypeEntity
	lowerRange, _ := postgres.GetLowerLimitForRange1()
	upperRange, _ := postgres.GetupperLimitForRange1()
	if inputModel.CustomerID != nil {
		entity.UserID = *inputModel.CustomerID
	} else {
		return nil, errors.New("Please provide customer ID")
	}
	speakerWeight, err := postgres.GetUserWeightByUserID(entity.UserID)
	if speakerWeight == 0 {
		return nil, errors.New("Speaker weight does not exist")
	}
	if err != nil {
		return nil, errors.New(err.Error())
	}
	entity.SpeakerWeight = speakerWeight

	if inputModel.BaseType != nil {
		entity.BaseType = *inputModel.BaseType
	} else {
		return nil, errors.New("Please provide Base Type")
	}
	var roleList []string
	if inputModel.Roles != nil {
		for _, role := range inputModel.Roles {
			tmpRole := role
			roleList = append(roleList, *tmpRole.RoleType)
		}
		for _, val := range roleList {
			entity.Role = val
		}

	} else {
		return nil, errors.New("Please provide Role")
	}

	if entity.Role == "speaker" {
		if inputModel.NoOfAttendees != nil {
			entity.NoOfAttendees = *inputModel.NoOfAttendees
		} else {
			return nil, errors.New("Please provide no. of attendees")
		}
		if entity.NoOfAttendees < lowerRange {
			return nil, errors.New("No. of attendees cannot be less than 5")
		}
		if entity.NoOfAttendees >= lowerRange && entity.NoOfAttendees < upperRange && entity.BaseType == "outbase" {
			return nil, errors.New("Out-base is not applicable for 5-9 attendees")
		}
	}

	return &entity, nil
}
func ValidateAllAttendanceData(inputModel []entity.AllAttendanceVeevaAccountDetailsEntityData) []*model.AllAttendanceVeevaAccountDetailsData {
	var response []*model.AllAttendanceVeevaAccountDetailsData
	var res []*model.AllAttendanceVeevaAccountDetailsData
	checkDuplicate := make(map[entity.AllAttendanceDataValidation]bool)
	for _, val := range inputModel {
		id := val.ID.String
		dateCreated := val.DateCreated.String
		specialty := val.Specialty.String
		name := val.Name.String
		contactType := val.ContactType.String
		eventID := val.EventID.String
		accountName := val.AccountName.String
		accountveevaID := val.AccountveevaID.String
		attendanceveevaID := val.AttendanceveevaID.String
		systemcode := val.Systemcode.String
		organization := val.Organization.String
		ishcp := val.IsHcp
		isSpeaker := val.IsSpeaker
		firstName := val.FirstName.String
		lastName := val.LastName.String
		allData := model.AllAttendanceVeevaAccountDetailsData{
			ID:                &id,
			DateCreated:       &dateCreated,
			Specialty:         &specialty,
			Name:              &name,
			EventID:           &eventID,
			ContactType:       &contactType,
			AccountName:       &accountName,
			AccountveevaID:    &accountveevaID,
			AttendanceveevaID: &attendanceveevaID,
			Systemcode:        &systemcode,
			Organization:      &organization,
			IsHcp:             &ishcp,
			IsSpeaker:         &isSpeaker,
			FirstName:         &firstName,
			LastName:          &lastName,
		}

		dataValidation := entity.AllAttendanceDataValidation{
			Specialty:    specialty,
			Name:         name,
			Organization: organization,
		}
		if _, exists := checkDuplicate[dataValidation]; exists {
			continue
		} else {
			checkDuplicate[dataValidation] = true
			response = append(response, &allData)
		}
	}
	checkDup := make(map[string]bool)
	for _, value := range response {
		if value.AccountveevaID != nil {
			if *value.AccountveevaID != "" {
				id := *value.AccountveevaID
				if _, exists := checkDup[id]; exists {
					continue
				} else {
					checkDup[id] = true
					res = append(res, value)
				}
			} else {
				res = append(res, value)
			}
		}
	}
	if res == nil {
		res = response
	}
	return res
}

func FetchAllAttendanceVeevaAccountDetailsModelToEntity(input *model.RequestAllAttendanceVeevaAccountDetails) (entity.AllAttendanceVeevaInputEntityData, error) {
	var attendeesEntity entity.AllAttendanceVeevaInputEntityData
	if input.FormAnswerID != nil && strings.TrimSpace(*input.FormAnswerID) != "" {
		FormAnswerID, err := uuid.FromString(*input.FormAnswerID)
		if err != nil {
			return attendeesEntity, nil
		} else {
			attendeesEntity.FormAnswerID = FormAnswerID
		}
	}
	if input.Limit != nil && *input.Limit > 0 {
		attendeesEntity.Limit = *input.Limit
		if input.Page != nil && *input.Page > 0 {
			attendeesEntity.Page = *input.Limit * (*input.Page - 1)
		}
	}
	if input.Name != nil && strings.TrimSpace(*input.Name) != "" {
		attendeesEntity.Name = *input.Name
	}
	if input.DateCreatedFrom != nil && strings.TrimSpace(*input.DateCreatedFrom) != "" {
		attendeesEntity.DateCreatedFrom = *input.DateCreatedFrom
	}
	if input.DateCreatedTo != nil && strings.TrimSpace(*input.DateCreatedTo) != "" {
		attendeesEntity.DateCreatedTo = *input.DateCreatedTo
	}
	if input.EventID != nil && strings.TrimSpace(*input.EventID) != "" {
		attendeesEntity.EventID = *input.EventID
	}
	return attendeesEntity, nil
}
