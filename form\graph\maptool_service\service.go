package maptool_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
)

type Service struct {
	baseURL  string
	username string
	password string
}

func NewService() (*Service, error) {
	var endpoint, username, pwd string
	if endpoint = os.Getenv("MAPTOOL_API_URL"); endpoint == "" {
		return nil, errors.New(`unknown MAPTOOL service`)
	}

	if username = os.Getenv("MAPTOOL_USERNAME"); username == "" {
		return nil, errors.New(`unknown MAPTOOL Username`)
	}

	if pwd = os.Getenv("MAPTOOL_PASSWORD"); pwd == "" {
		return nil, errors.New(`unknown MAPTOOL Password`)
	}

	return &Service{
		baseURL:  endpoint,
		username: username,
		password: pwd,
	}, nil
}

type Product struct {
	Client  string `json:"client"`
	Product string `json:"product"`
}

type Approver struct {
	SequenceNo     int    `json:"sequence_no"`
	Email          string `json:"email"`
	Name           string `json:"name"`
	Role           string `json:"role"`
	ApprovalStatus string `json:"approval_status"`
	ActionedAt     string `json:"actioned_at"`
}

type HcpEngagement struct {
	EngagementType            string     `json:"engagement_type"`
	IndividualCategory        string     `json:"individual_category"`
	HcpType                   string     `json:"hcp_type"`
	GovtType                  string     `json:"govt_type"`
	HcpName                   string     `json:"hcp_name"`
	Specialty                 string     `json:"specialty"`
	HcoName                   string     `json:"hco_name"`
	BaseType                  string     `json:"base_type"`
	InfluenceLevel            string     `json:"influence_level"`
	ExpertLevel               string     `json:"expert_level"`
	Role                      string     `json:"role"`
	Remark                    string     `json:"remark"`
	Attachment                []string   `json:"attachment"`
	PreparationTime           string     `json:"preparation_time"`
	ServiceTime               string     `json:"service_time"`
	Currency                  string     `json:"currency"`
	MaxPayableHonorarium      string     `json:"max_payable_honorarium"`
	ProposedPayableHonorarium string     `json:"proposed_payable_honorarium"`
	TotalCost                 string     `json:"total_cost"`
	Expenses                  []*Expense `json:"expenses"`
}

type Expense struct {
	HcpSequence int      `json:"-"`
	ExpenseType string   `json:"expense_type"`
	TotalCost   string   `json:"total_cost"`
	Remark      string   `json:"remark"`
	Description string   `json:"description"`
	Attachment  []string `json:"attachment"`
}

type EventExpense struct {
	Category    string   `json:"category"`
	TotalCost   string   `json:"total_cost"`
	Attachment  []string `json:"attachment"`
	Remark      string   `json:"remark"`
	Description string   `json:"description"`
}

type MapToolEvent struct {
	EventId     string `json:"event_id,omitempty"`
	EzflowId    string `json:"ezflow_id,omitempty"`
	EventType   string `json:"event_type,omitempty"`
	EventName   string `json:"event_name"`
	LedBy       string `json:"led_by"`
	CountryCode string `json:"country_code"`

	TherapeuticArea    string   `json:"therapeutic_area"`
	EventOrganizer     string   `json:"event_organizer"`
	NoOfHCP            string   `json:"no_of_hcp"`
	NoOfNonHCP         string   `json:"no_of_non_hcp"`
	Venue              string   `json:"venue"`
	TargetAudience     string   `json:"target_audience"`
	MaterialCodeNumber string   `json:"material_code_number"`
	MeetingObjective   string   `json:"meeting_objective"`
	NoOfEmployee       string   `json:"no_of_employee"`
	GeneralRemark      string   `json:"general_remark"`
	AttendeeAttachment []string `json:"attendee_attachment"`
	ProposalAttachment []string `json:"proposal_attachment"`

	Status              string           `json:"status"`
	EventStartDate      string           `json:"event_start_date"`
	EventEndDate        string           `json:"event_end_date"`
	EventDuration       float64          `json:"event_duration"`
	MeetingMode         string           `json:"meeting_mode"`
	VirtualEventDetails string           `json:"virtual_event_details"`
	ChangeApprovalType  string           `json:"change_approval_type"`
	RequesterEmail      string           `json:"requester_email"`
	RequesterName       string           `json:"requester_name"`
	EventOwnerEmail     string           `json:"event_owner_email"`
	EventOwnerName      string           `json:"event_owner_name"`
	PrimaryProduct      *Product         `json:"primary_product,omitempty"`
	SecondaryProduct    *Product         `json:"secondary_product,omitempty"`
	TertiaryProduct     *Product         `json:"tertiary_product,omitempty"`
	AgendaAttachment    []string         `json:"agenda_attachment"`
	HcpEngagement       []*HcpEngagement `json:"hcp_engagement"`
	EventExpenses       []*EventExpense  `json:"event_expenses"`
	Approvers           []*Approver      `json:"approvers"`
	TotalCost           string           `json:"total_cost"`
	CreatedAt           string           `json:"created_at,omitempty"`
	UpdatedAt           string           `json:"updated_at"`
}

//type MapToolResponse struct {
//	HttpCode int    `json:"http_code"`
//	Success  bool   `json:"success"`
//	TraceId  string `json:"trace_id"`
//	Error    string `json:"error_msg"`
//}

type MapToolResponse struct {
	Status   int `json:"status"`
	Error    int `json:"error"`
	Messages struct {
		Error string `json:"error"`
	} `json:"messages"`
	EventID string `json:"event_id"`
}

func (s *Service) CreateOrUpdateEventToMaptool(ctx context.Context, event *MapToolEvent, action string) *MapToolResponse {
	var rs *MapToolResponse

	if action == `create` {
		rs = s.CreateEventToMapTool(ctx, event)
	} else if action == `update` {
		eventId := event.EventId

		event.EzflowId = ``
		event.EventType = ``
		event.CreatedAt = ``
		event.EventId = ""
		rs = s.UpdateEventToMapTool(ctx, event, eventId)
	}

	return rs
}

func (s *Service) CreateEventToMapTool(ctx context.Context, event *MapToolEvent) *MapToolResponse {
	fmt.Println(">>> Create Event To Maptool")
	endpoint := s.baseURL + `/api/event/add`
	body, err := json.Marshal(event)
	if err != nil {
		panic(err)
	}

	request, err := http.NewRequest(http.MethodPost, endpoint, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}

	request.Header.Add("Authorization", `Basic `+base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf(`%s:%s`, s.username, s.password))))
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	log.Println("---[MAPTOOL OUT GOING] *Request Header: \n", request.Header, endpoint)
	log.Println("---[MAPTOOL OUT GOING] *Payload: \n", string(body))
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	//responseBody := []byte(`{"success":true,"trace_id":"id-maptooltest-2025-1234555","error_msg":"","http_code":200}`)
	var res MapToolResponse
	if err = json.Unmarshal(responseBody, &res); err != nil {
		panic(err)
	}
	log.Println("---[MAPTOOL OUT GOING] *Response: \n", string(responseBody))

	return &res
}

func (s *Service) UpdateEventToMapTool(ctx context.Context, event *MapToolEvent, eventId string) *MapToolResponse {
	fmt.Println(">>> Update Event To Maptool")
	endpoint := s.baseURL
	endpoint = endpoint + `/api/event/update/` + eventId

	body, err := json.Marshal(event)
	if err != nil {
		panic(err)
	}

	request, err := http.NewRequest(http.MethodPut, endpoint, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}

	request.Header.Add("Authorization", `Basic `+base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf(`%s:%s`, s.username, s.password))))
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	log.Println("---[MAPTOOL OUT GOING] *Request Header: \n", request.Header, endpoint)
	log.Println("---[MAPTOOL OUT GOING] *Payload: \n", string(body))
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}
	//responseBody := []byte(`{"success":true,"trace_id":"","error_msg":""}`)
	var res MapToolResponse
	if err = json.Unmarshal(responseBody, &res); err != nil {
		if string(responseBody) == `""` {
			return &res
		}
		panic(err)
	}
	log.Println("---[MAPTOOL OUT GOING] *Response: \n", string(responseBody))

	return &res
}

type MapToolSyncLog struct {
}
