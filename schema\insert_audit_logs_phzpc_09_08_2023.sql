INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('5e2ffb43-2f97-4fc1-b9e2-189bf506eaa2', 'Speaker', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'EngagementLimit', 'Role', NULL, 124, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('b540b3dc-74ee-48b6-a1c1-e53e17cd0054', 'Advisory Board Member', 23000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'EngagementLimit', 'Role', NULL, 127, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('aa99452b-9daa-47cb-9b54-1eef63eda585', 'Moderator', 23000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'EngagementLimit', 'Role', NULL, 125, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('18e57064-8d84-4c9f-af38-65255585bba8', 'Multiple Engagements/day', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'LocalExpense', 'Honorarium', 'f50ec3ca-e155-441f-bd1e-42eabe12e86c', NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('588d2f60-e569-4fff-b210-9d776df615d7', 'Multiple Role', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'EngagementLimit', 'MultipleRole', NULL, NULL, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('24358f18-c4ce-45e8-a598-679241dad2f4', 'Range 2', 13000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 124, true, false, '[10,50)', NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('74910875-fc8b-4023-9c3a-9f77052c1df5', 'Advisory Board Member', 23000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 127, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('a52700bc-b7b0-49bb-ac5b-fa67bac4a1c8', 'Advisory Board Member', 23000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 127, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('2a276483-f40c-4c29-aed7-8b96155954c7', 'Day-to-day promotional activities', 1500, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'ExpenseLimit', 'Meal', NULL, 34, true, false, NULL, 41, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('c1ee842e-8a82-446e-bd66-b6964ffcbb94', 'Lay-Forum', 13000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 390, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('d7d6a00d-d1a1-4077-926a-d7342d61948d', 'Lay-Forum', 13000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 390, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('6f7126e6-d961-4468-8356-65653a63c321', 'Moderator', 13000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 125, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('6fa044e1-83b0-4ba8-9a07-98cf3d229f46', 'Moderator', 13000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 125, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('e4e42605-205f-4937-9dc9-365028723026', 'Module Development', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 391, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('055ecce1-79fa-4f79-be90-ac1cc58dc3c4', 'Module Development', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 391, true, false, NULL, NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('88b86157-e5c6-43d4-8400-dc8b44be6e21', 'Range 3', 23000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 124, true, false, '[50,)', NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('da7cec6b-4e97-4331-8ca3-3bf5f4340046', 'Range 3', 28000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 124, true, false, '[50,)', NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('fe1651de-9129-4b58-9d01-a8b1fbec61c4', 'Range 2', 18000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'outbase', NULL, 124, true, false, '[10,50)', NULL, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('d0aafc9a-526b-42d9-8b2a-5af018f4dea2', 'Day-to-day promotional activities', 1000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'ExpenseLimit', 'Meal', '171dbdf5-1eee-4ef9-b087-537d6c126465', 34, true, false, NULL, 41, false);
INSERT INTO fmv_controls_audit_logs (control_id, description, max_limit, country, created_by, date_created, modified_by, last_modified, category, "type", activity_id, code_id, is_active, is_deleted, range_limit, currency_code_limit, more_than_two_lecture) VALUES('1f8bca32-b10f-424d-aa55-2a20486878d7', 'Range 1', 10000, 400, '52dad980-4b26-411f-96a3-83483ad143a9', '2020-12-14 14:00:32.181', NULL, NULL, 'BaseType', 'inbase', NULL, 124, true, false, '[5,10)', NULL, false);