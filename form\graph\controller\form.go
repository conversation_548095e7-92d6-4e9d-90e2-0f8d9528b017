package controller

import (
	"archive/zip"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/maptool_service"
	"github.com/jackc/pgx/v4"
	"io"
	"io/ioutil"
	"log"
	"math"
	"os"
	"path/filepath"
	"reflect"
	"slices"
	"strconv"
	"strings"
	"sync"

	"time"

	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/azure"
	"github.com/ihcp/form/graph/constants"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/form/graph/veeva"
	"github.com/jackc/pgtype"

	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func RecallFormSubmissionForApproval(ctx *context.Context, inputModel model.RecallFormApprovalInput) *model.RecallFormApprovalResponse {
	var recallFormApprovalResponse model.RecallFormApprovalResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	userCountry := auth.GetCountry(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		recallFormApprovalResponse.Error = true
		recallFormApprovalResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &recallFormApprovalResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			recallFormApprovalResponse.Error = true
			recallFormApprovalResponse.Message = err.Error()
			return &recallFormApprovalResponse
		}
	}
	entity, err := mapper.RecallMapSubmissionFormApprovalModelToEntity(&inputModel, userUUID, approvalRole)
	if err != nil {
		recallFormApprovalResponse.Error = true
		recallFormApprovalResponse.Message = err.Error()
		return &recallFormApprovalResponse
	}
	err, message := postgres.RecallInsertApprovalLog(entity, inputModel.IsApollo)
	if err != nil {
		recallFormApprovalResponse.Error = true
		recallFormApprovalResponse.Message = err.Error()
		return &recallFormApprovalResponse
	}

	// FOR APL MAPTOOL
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(*ctx, nil, inputModel.FormAnsID)
	}
	//

	recallFormApprovalResponse.Message = message
	return &recallFormApprovalResponse
}

func GetNotificationStatus(ctx context.Context, input *model.NotificationKeyLimit) *model.GetNotificationStatusForUserResponse {
	var response model.GetNotificationStatusForUserResponse
	userID := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	var limit int
	var lastID string
	if input != nil {
		if input.LastID != nil {
			lastID = *input.LastID
		}
		if input.Limit != nil && *input.Limit > 0 {
			limit = *input.Limit
		}
	}
	var data []*model.GetNotificationStatus

	notificationStatusList, err := postgres.GetNotificationStatus(*userID, limit, lastID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	data, err = mapper.ListNotificationStatusEntityToModel(notificationStatusList, *userID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.Error = false
	response.GetNotificationStatus = data

	return &response
}

func SaveFormTemplate(ctx context.Context, input model.FormTemplateSubmitRequest) *model.FormTemplateSubmitResponse {
	var response model.FormTemplateSubmitResponse
	formEntity, err := mapper.FormDesignModelToEntity(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	err = postgres.CreateFormTemplate(formEntity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.Error = false
	response.Message = "Template stored successfully !!"
	return &response
}

func FetchFormTemplate(ctx context.Context) *model.FormTemplateResponse {
	if err := checkUserAuthorization(ctx); err != nil {
		return &model.FormTemplateResponse{
			Error:   true,
			Message: err.Error(),
		}
	}
	userID := auth.GetUserID(ctx)
	userRoleID := auth.GetApprovalRole(ctx)
	countryID := auth.GetCountry(ctx)

	//a := `c7fcd83d-d5a4-4295-af67-eb67f91bbe2f`
	//userID := &a
	//c := `b4042617-b12b-4f59-b4b0-1b9449335f49`
	//userRoleID := &c
	//d := 9
	//countryID := &d

	if !postgres.CheckApprovalRoleFromRolesPermissionForRequestor(*userRoleID) {
		return &model.FormTemplateResponse{
			Error:   true,
			Message: "User does not have requester access",
		}
	}

	formTemplate := postgres.FetchFormTemplate(ctx)
	if formTemplate == nil {
		return &model.FormTemplateResponse{
			Error:   true,
			Message: `no template found`,
		}

	}
	data := mapper.FormDesignEntityToModel(formTemplate, *userID, *userRoleID)
	data = setActivitySelectionListForUser(ctx, *userRoleID, *countryID, data)
	return &model.FormTemplateResponse{
		Error:  false,
		Design: data,
	}
}

func setActivitySelectionListForUser(ctx context.Context, userRoleID string, country int, data []*model.FormTabs) []*model.FormTabs {
	var finalActivityTypeList, finalActivityEventList []*model.Values
	userEventAccessList := postgres.GetUserEventAccessControl(ctx, userRoleID, country)
	m := make(map[string]bool)
	for i := range userEventAccessList {
		m[userEventAccessList[i].Description] = true
	}

	//-----------

	defaultActivityTypeList := data[0].Sections[0].Form.Group[0].Inputs[0].Inputs[0].Values
	for i := range defaultActivityTypeList {
		d := defaultActivityTypeList[i].Description
		if d == "ZP - Hosted Event" {
			finalActivityTypeList = append(finalActivityTypeList, defaultActivityTypeList[i])
			continue
		}

		if _, f := m[d]; f {
			finalActivityTypeList = append(finalActivityTypeList, defaultActivityTypeList[i])
		}
	}
	data[0].Sections[0].Form.Group[0].Inputs[0].Inputs[0].Values = finalActivityTypeList

	//-----------

	defaultActivityEventList := data[0].Sections[0].Form.Group[0].Inputs[0].Inputs[1].Values
	for i := range defaultActivityEventList {
		d := defaultActivityEventList[i].Description
		if _, f := m[d]; f {
			finalActivityEventList = append(finalActivityEventList, defaultActivityEventList[i])
		}
	}
	data[0].Sections[0].Form.Group[0].Inputs[0].Inputs[1].Values = finalActivityEventList

	return data
}

func FetchFormAnswer(ctx context.Context, inputModel model.FormAnswerInput) *model.FormAnswerResponse {
	var response model.FormAnswerResponse
	userId := auth.GetUserID(ctx)
	userCountry := auth.GetCountry(ctx)
	var loggedInId uuid.UUID
	var e *entity.ExceptionDetailsEntity
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	} else {
		loggedInId, err = uuid.FromString(*userId)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}
	userRoleID := auth.GetApprovalRole(ctx)
	entity, err := mapper.FormAnswerFetchModelToEntity(&inputModel, userRoleID, &loggedInId, *userCountry)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	requesterCountry := postgres.FetchRequesterCountry(inputModel.FormID)
	requesterCurrency, boardOfDirectors, ConversionRate, _, _ := postgres.FetchRequesterCurrency(inputModel.FormID)
	e, _ = postgres.FetchExceptionalDetails(inputModel.FormID)
	if e == nil {
		return &model.FormAnswerResponse{
			Error:   true,
			Message: `formAnswerId not found`,
		}
	}

	formAnswer, errString := postgres.FetchFormAnswers(&loggedInId, entity, userRoleID)
	if errString != "" {
		response.Error = true
		response.Message = errString
		return &response
	}

	data := mapper.FormAnswerDesignEntityToModel(formAnswer, requesterCountry)
	attachments, boardOfDirectorsDataRes := mapper.FormAnswerAttachmentEntityToModel(formAnswer, boardOfDirectors)
	createdDate, _, _, err := postgres.GetDateCreatedByFormAnswerId(entity.FormId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	dateCreated := util.GetTimeUnixTimeStamp(createdDate.Time)
	meetingID := util.ConvertToEventID(e.EventCode, e.EventSeq)
	response.DateCreated = &dateCreated
	response.Answers = data
	response.Attachments = attachments
	response.RequestorCountry = &requesterCountry
	response.RequestorCurrency = &requesterCurrency
	response.IsExceptionalApproval = *e.IsExceptionalApproval
	response.DetailsOfRequest = e.DetailsOfRequest
	response.IhcpOtherLocalPolicies = e.IhcpOtherLocalPolicies
	response.MoreComments = e.MoreComments
	response.ExceptionalApprovalFileUpload = e.ExceptionalApprovalFileUpload
	response.ScopeOfExceptionalRequest = e.ScopeOfExceptionalRequest
	response.IsChangeRequest = e.IsChangeRequest
	response.ChangeRequestSummary = e.ChangeRequestSummary
	response.ChangeRequestType = e.ChangeRequestType
	response.MeetingID = &meetingID
	response.ConversionRate = &ConversionRate
	response.IsRecall = e.IsRecall
	response.RecallSummary = e.RecallSummary
	response.BoardOfDirectory = boardOfDirectorsDataRes

	b := mapper.GetBankData(data[1])
	if b != nil {
		response.BankInformation = &model.BankInformation{
			BankName:          b.BankAccountInfo.BankName,
			BankBranch:        b.BankAccountInfo.BankBranch,
			BankAccountNumber: b.BankAccountInfo.BankAccountNumber,
			BankAccountHolder: b.BankAccountInfo.BankAccountHolder,
		}
		response.Signatory = &model.Signatory{
			HCORepresentative:      b.SignatoryInformation.HCORepresentative,
			HCORepresentativeTitle: b.SignatoryInformation.HCORepresentativeTitle,
		}
	}

	return &response
}

func SubmitFormAnswer(ctx context.Context, input model.FormAnswerSubmissionRequest) *model.FormAnswerSubmissionResponse {
	if err := checkUserAuthorization(ctx); err != nil {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	userId := auth.GetUserID(ctx)
	userUUID, err := uuid.FromString(*userId)
	if err != nil {
		panic(err)
	}

	if input.TotalCost < 0 {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: "Total cost cannot be in negative",
		}
	}

	if input.ParentEventID != nil {
		uuid, err := uuid.FromString(*input.ParentEventID)
		if err != nil {
			panic(err)
		}

		if !postgres.IsFormIDExists(&uuid) {
			return &model.FormAnswerSubmissionResponse{
				Error:   true,
				Message: "Parent event ID does not exists",
			}
		}
	}

	if input.IsApollo == nil {
		f := false
		input.IsApollo = &f
	}

	userRoleID := auth.GetApprovalRole(ctx)
	countryID := auth.GetCountry(ctx)
	if !input.IsDraft && !postgres.CheckApproverPresentInApprovers(input.ID) {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: "Please select approvers for this event.",
		}
	}

	if !postgres.CheckApprovalRoleFromRolesPermissionForRequestor(*userRoleID) {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: "user does not have requestor access",
		}
	}

	eventCode := generateEventCode(*userRoleID, *countryID)

	entity, err := mapper.FormAnswerModelToEntity(ctx, &userUUID, input.Answers, *input.IsApollo)
	if err != nil {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	tx, err := postgres.GetPool().Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	if input.ID == nil {
		f := false
		input.IsChangeRequest = &f
		formAnswerID, eventSequence := postgres.CreateFormAnswer(ctx, tx, entity, input, eventCode)
		if input.ParentEventID != nil {
			postgres.InsertFormAnswerRelation(ctx, formAnswerID, tx, entity, input)
		}

		adminData := postgres.GetAdminAnswerListForReport(ctx, tx, formAnswerID)
		hcpCostList, hcpExpenseList := postgres.GetAllHcpExpenses(ctx, tx, formAnswerID)
		postgres.InsertReportingDB(ctx, tx, *adminData, hcpCostList, hcpExpenseList)

		if err := tx.Commit(ctx); err != nil {
			panic(err)
		}

		return &model.FormAnswerSubmissionResponse{
			Error:      false,
			Message:    "Answers successfully submitted !!",
			FormAnsID:  formAnswerID,
			FormAnsSeq: eventCode + "-" + eventSequence,
		}

	}
	if !postgres.CheckFormAnswerIDStatusReturnOrDraftOrApproved(ctx, tx, input.ID) {
		return &model.FormAnswerSubmissionResponse{
			Error:   true,
			Message: "Form answer cannot be modified as it is not in draft or returned or approved state.",
		}
	}

	if input.ParentEventID != nil {
		if !postgres.IsFormIDExistsInFormAnswerRelation(ctx, tx, input.ID) {
			postgres.InsertFormAnswerRelation(ctx, *input.ID, tx, entity, input)
		} else {
			postgres.UpdateFormAnswerRelation(ctx, tx, entity, input)
		}
	} else {
		if postgres.IsFormIDExistsInFormAnswerRelation(ctx, tx, input.ID) {
			postgres.DeleteFormAnswerRelation(ctx, tx, entity, input)
		}
	}

	formID, eventID := postgres.UpdateFormAnswer(ctx, tx, entity, input)
	adminData := postgres.GetAdminAnswerListForReport(ctx, tx, formID)
	hcpCostList, hcpExpenseList := postgres.GetAllHcpExpenses(ctx, tx, formID)

	if postgres.CheckFormAnswerIdPresentInReportingDatabase(ctx, tx, formID) {
		postgres.UpdateReportingDB(ctx, tx, *adminData, hcpCostList, hcpExpenseList)
	} else {
		postgres.InsertReportingDB(ctx, tx, *adminData, hcpCostList, hcpExpenseList)
	}

	if err := tx.Commit(ctx); err != nil {
		panic(err)
	}

	return &model.FormAnswerSubmissionResponse{
		Error:      false,
		Message:    "Answers successfully submitted !!",
		FormAnsID:  formID,
		FormAnsSeq: eventID,
	}
}

func SendEventToMapTool(ctx context.Context, tx pgx.Tx, formAnswerID string) {
	d := postgres.GetEventDetails(ctx, formAnswerID)
	if d == nil {
		panic("should never be nil")
	}
	if d.EventType.String != "Commercial" && d.EventType.String != "Product Presentation" {
		return
	}

	fmt.Println(">>>  APL event. Try sending to map tool.")
	action := `create`
	mtPostgres := postgres.MapTool{}

	// 1. Check event id in maptool_event_logs
	l := mtPostgres.GetMaptoolEventLogByFormAnswerID(ctx, formAnswerID)
	if (l != nil && l.SyncedAt != nil) || (l != nil && l.TraceId != "") {
		action = `update`
	}

	mtPostgres.UpsertMaptoolEventLog(ctx, nil, formAnswerID, ``, `{}`, `{}`, false)
	mtSvc, err := maptool_service.NewService()
	if err != nil {
		mtPostgres.UpsertMaptoolEventLog(ctx, nil, formAnswerID, ``, `{}`, fmt.Sprintf(`{"error":"%s"}`, err.Error()), false)
	}

	formatDatetime := func(unixStr string) time.Time {
		i, err := strconv.ParseInt(unixStr, 10, 64)
		if err != nil {
			panic(err)
		}
		return time.Unix(i, 0)
	}

	// --
	var primaryP, secondaryP, tertiaryP *maptool_service.Product
	if len(d.ClientProductList) >= 1 {
		primaryP = &maptool_service.Product{
			Client:  d.ClientProductList[0].Client,
			Product: d.ClientProductList[0].Product,
		}
	}
	if len(d.ClientProductList) >= 2 {
		secondaryP = &maptool_service.Product{
			Client:  d.ClientProductList[1].Client,
			Product: d.ClientProductList[1].Product,
		}
	}
	if len(d.ClientProductList) >= 3 {
		tertiaryP = &maptool_service.Product{
			Client:  d.ClientProductList[2].Client,
			Product: d.ClientProductList[2].Product,
		}
	}

	obj := &maptool_service.MapToolEvent{
		EventId:  d.EventId.String,
		EzflowId: formAnswerID,
		EventType: fmt.Sprintf(`%s%s`, d.ActivityType.String,
			func() string {
				if len(d.EventType.String) != 0 {
					return fmt.Sprintf(` - %s`, d.EventType.String)
				}
				return ""
			}()),
		EventName:           d.ActivityName.String,
		LedBy:               d.LedBy.String,
		CountryCode:         d.Country.String,
		Status:              d.Status.String,
		EventStartDate:      formatDatetime(d.ActivityStartDate).UTC().String(),
		EventEndDate:        formatDatetime(d.ActivityEndDate).UTC().String(),
		EventDuration:       formatDatetime(d.ActivityEndDate).Sub(formatDatetime(d.ActivityStartDate)).Hours(),
		MeetingMode:         d.MeetingMode.String,
		VirtualEventDetails: d.VirtualEventDetails.String,
		AgendaAttachment:    d.AgendaAttachment,
		PrimaryProduct:      primaryP,
		SecondaryProduct:    secondaryP,
		TertiaryProduct:     tertiaryP,
		Venue:               d.Venue.String,

		TherapeuticArea: d.TherapeuticArea.String,
		EventOrganizer:  d.EventOrganizer.String,
		NoOfHCP:         d.NoOfHCP.String,
		NoOfNonHCP:      d.NoOfNonHCP.String,
		NoOfEmployee:    d.NoOfEmployee.String,

		TargetAudience:     d.TargetAudience.String,
		MaterialCodeNumber: d.MaterialCodeNumber.String,
		MeetingObjective:   d.MeetingObjective.String,
		GeneralRemark:      d.GeneralRemark.String,
		AttendeeAttachment: d.AttendeeAttachment,
		ProposalAttachment: d.ProposalAttachment,

		ChangeApprovalType: d.ChangeApprovalType.String,
		RequesterEmail:     d.RequestorEmail.String,
		RequesterName:      d.RequestorName.String,
		EventOwnerEmail:    d.OwnerEmail.String,
		EventOwnerName:     d.OwnerName.String,
		HcpEngagement:      nil,
		EventExpenses:      nil,
		Approvers:          mtPostgres.GetEventApprovers(ctx, formAnswerID),
		TotalCost:          "0",
		CreatedAt:          d.CreatedAt.UTC().String(),
		UpdatedAt:          d.UpdatedAt.UTC().String(),
	}

	var eventTotal float64
	var hcpList []*maptool_service.HcpEngagement
	for i := range d.HCP {

		var totalExpense float64
		for _, obj := range d.HCP[i].Expenses {
			s, err := strconv.ParseFloat(obj.TotalCost, 64)
			if err != nil {
				panic(err)
			}
			totalExpense += s
		}

		ProposedPayableHonorariumFloat, err := strconv.ParseFloat(d.HCP[i].ProposedPayableHonorarium, 64)
		if err != nil {
			panic(err)
		}

		totalCost := totalExpense + ProposedPayableHonorariumFloat

		hcpList = append(hcpList, &maptool_service.HcpEngagement{
			EngagementType:            d.HCP[i].EngagementType,
			IndividualCategory:        d.HCP[i].IndividualCategory,
			HcpType:                   d.HCP[i].HcpType,
			GovtType:                  d.HCP[i].GovtType,
			HcpName:                   d.HCP[i].HcpName,
			Specialty:                 d.HCP[i].Specialty,
			HcoName:                   d.HCP[i].HcoName,
			BaseType:                  d.HCP[i].BaseType,
			InfluenceLevel:            d.HCP[i].InfluenceLevel,
			ExpertLevel:               d.HCP[i].ExpertLevel,
			Role:                      d.HCP[i].Role,
			Remark:                    d.HCP[i].Remark,
			Attachment:                d.HCP[i].Attachment,
			PreparationTime:           d.HCP[i].PreparationTime,
			ServiceTime:               d.HCP[i].ServiceTime,
			Currency:                  d.HCP[i].Currency,
			MaxPayableHonorarium:      d.HCP[i].MaxPayableHonorarium,
			ProposedPayableHonorarium: d.HCP[i].ProposedPayableHonorarium,
			TotalCost:                 fmt.Sprintf(`%f`, totalCost),
			Expenses:                  d.HCP[i].Expenses,
		})
		eventTotal += totalCost
	}
	obj.HcpEngagement = hcpList

	var expenses []*maptool_service.EventExpense
	for i := range d.EventExpense {
		expenses = append(expenses, &maptool_service.EventExpense{
			Category:    d.EventExpense[i].Category,
			TotalCost:   d.EventExpense[i].TotalCost,
			Attachment:  d.EventExpense[i].Attachment,
			Remark:      d.EventExpense[i].Remark,
			Description: d.EventExpense[i].Description,
		})

		cost, err := strconv.ParseFloat(d.EventExpense[i].TotalCost, 64)
		if err != nil {
			panic(err)
		}
		eventTotal += cost
	}
	obj.EventExpenses = expenses
	obj.TotalCost = fmt.Sprintf(`%f`, eventTotal)

	rs := mtSvc.CreateOrUpdateEventToMaptool(ctx, obj, action)

	var success bool
	if rs.EventID != "" || rs.Error == 0 || rs.Status == 200 || rs.Status == 201 || rs.Status == 409 {
		success = true
		if rs.EventID == "" {
			rs.EventID = obj.EventId
		}
	}
	payloadJSON, _ := json.Marshal(*obj)
	respJSON, _ := json.Marshal(rs)
	mtPostgres.UpsertMaptoolEventLog(ctx, nil, formAnswerID, rs.EventID, string(payloadJSON), string(respJSON), success)
}

func SubmitApprovalRole(ctx context.Context, input model.RoleSelection) *model.FormApprovalResponse {
	err := checkUserAuthorization(ctx)
	if err != nil {
		return &model.FormApprovalResponse{
			Error:   true,
			Message: err.Error(),
		}
	}
	var response model.FormApprovalResponse
	userCountry := auth.GetCountry(ctx)

	//
	var ent pgtype.JSONB
	userId := auth.GetUserID(ctx)

	createdBy, err := uuid.FromString(*userId)
	if err != nil {
		panic(err)
	}

	entity, err := mapper.SubmitApprovalRoleModelToEntity(&createdBy, input.ApprovalRoles, input.FormID, *userCountry, input.IsChangeRequest)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	emailData, _ := postgres.GetFormAnswerDetails(input.FormID)
	if len(emailData) == 0 {
		response.Error = true
		response.Message = `err_form_answer_details_not_found`
		return &response
	}

	data := mapper.SetEmailData(emailData)
	isFirstTimeSetApprover := postgres.CheckFormAnswerIDInApprovers(input.FormID)
	if !isFirstTimeSetApprover { // event exists
		if postgres.CheckApproverPresentInApproversWithStatusReturn(input.FormID) || entity.IsChangeRequest {
			err = postgres.SoftDeleteApprovers(*userId, input.FormID, entity, data, input.ChangeRequestSummary, input.IsApollo)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			code := codeController.GetValueKeyCodes()["approvedstatus"]
			resubmittedStatus := code["resubmitted"].ID
			log.Println("resubmittedStatus", resubmittedStatus)
			ent, err = postgres.FetchFormAnswer(input.FormID)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			err = postgres.CreateFormAnswerLogs(ctx, ent, &createdBy, input.FormID, resubmittedStatus, input.IsApollo)

		} else {
			response.Error = true
			response.Message = "Approvers for this form answer already exists"
			return &response
		}
	} else {
		err = postgres.CreateApprovalRoles(ctx, entity, input.ChangeRequestSummary, input.FormID, *userId, data, input.IsApollo)
		if err != nil {
			panic(err)
		}
		code := codeController.GetValueKeyCodes()["approvedstatus"]
		//status := code["pending"].ID
		pendingStatus := code["pending"].ID
		d := postgres.FetchFormAnswer2(input.FormID)
		if d == nil {
			panic(`something went wrong. This should never be nil`)
		}
		err = postgres.CreateFormAnswerLogs(ctx, *d, &createdBy, input.FormID, pendingStatus, input.IsApollo)
	}

	// FOR APL MAPTOOL
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, input.FormID)
	}
	//

	response.Error = false
	response.Message = "Answers successfully submitted !!"
	return &response

}

func ApprovalRoleSelectionForExceptionalLogic(ctx context.Context, input model.ApprovalRoleSelectionForExceptionalInput) *model.FormApprovalResponse {
	var response model.FormApprovalResponse
	userId := auth.GetUserID(ctx)
	userCountry := auth.GetCountry(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	entity, err := mapper.ApprovalRoleSelectionForExceptionalModelToEntity(input.ApprovalRoles, input.FormID, *userCountry)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	emaidData, err := postgres.GetFormAnswerDetails(input.FormID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	data := mapper.SetEmailData(emaidData)
	err = postgres.CreateApprovalRolesForExceptional(entity, input.FormID, *userId, data, input.IsApollo)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	// FOR APL MAPTOOL
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, input.FormID)
	}
	//

	response.Error = false
	response.Message = "Answers successfully submitted !!"
	return &response

}

func FetchPendingApprovals(ctx *context.Context, input *model.Limit) *model.PendingApprovalResponse {
	var response model.PendingApprovalResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	var Page int
	var limit int
	var searchItem string
	var sortItems []string
	var activityFilter []string
	var statusFilter []string
	var totalCostRange []int
	var eventFilter []string
	var dateFilter string
	var dateFilterStart string
	var dateFilterEnd string
	var dateCreatedFilterStart string
	var dateCreatedFilterEnd string
	var exceptionalStatusFilter []string

	if input != nil {
		if input.Limit != nil && *input.Limit > 0 {
			limit = *input.Limit
			if input.PageNo != nil && *input.PageNo > 0 {
				Page = *input.Limit * (*input.PageNo - 1)
			}
		}
		if input.SearchItem != nil {
			searchItem = *input.SearchItem
		}
		if input.TotalCostRange != nil {
			if len(input.TotalCostRange) != 2 {
				response.Error = true
				response.Message = "The range is incorrect"
				return &response
			}
			totalCostRange = input.TotalCostRange
		}

		if input.SortItem != nil {
			sortItems = mapper.SortItemModelToEntity(input.SortItem)
		}

		if input.StatusFilter != nil {
			statusFilter = input.StatusFilter
		}

		if input.ExceptionalStatusFilter != nil {
			exceptionalStatusFilter = input.ExceptionalStatusFilter
		}

		if input.DateFilterStart != nil {
			dateFilterStart = *input.DateFilterStart
		}
		if input.DateFilterEnd != nil {
			dateFilterEnd = *input.DateFilterEnd
		}
		if input.DateCreatedFilterStart != nil {
			dateCreatedFilterStart = *input.DateCreatedFilterStart
		}
		if input.DateCreatedFilterEnd != nil {
			dateCreatedFilterEnd = *input.DateCreatedFilterEnd
		}
		if input.TypeOfActivityFilter != nil {
			codes := codeController.GetIdKeyCodes()["activitytype"]
			for _, value := range input.TypeOfActivityFilter {
				activityValue := codes[value].Title.String
				activityFilter = append(activityFilter, activityValue)
			}
		}
		if input.TypeOfEventFilter != nil {
			codes := codeController.GetIdKeyCodes()["activityeventtype"]
			for _, value := range input.TypeOfEventFilter {
				eventValue := codes[value].Title.String
				eventFilter = append(eventFilter, eventValue)
			}
		}
		if input.DateFilter != nil {
			dateFilter = *input.DateFilter
		}
	}
	var data []*model.PendingApproval

	if postgres.CheckApprovalRoleFromRolesPermission(*approvalRole) {
		count, counterr := postgres.GetPendingApprovalCount(*userID, Page, limit, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilter, sortItems, dateFilterStart, dateFilterEnd, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if counterr != nil {
			response.Error = true
			response.Message = counterr.Error()
			return &response
		}

		pendingApprovalList, maxTotalCost, err := postgres.GetPendingApproval(*userID, Page, limit, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilter, sortItems, dateFilterStart, dateFilterEnd, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		data = mapper.ListPendingApprovalEntityToModel(pendingApprovalList, *userID, *approvalRole)
		lastpage := LastPageCount(count, limit)
		response.LastPage = &lastpage
		response.Error = false
		response.Data = data
		response.TotalCount = count
		response.MaxTotalCost = maxTotalCost
	} else {
		response.Error = true
		response.Message = "User does not have approver access!"
	}

	return &response
}

func PaginatePendingApproval(x []*model.PendingApproval, skip int, size int) []*model.PendingApproval {
	if skip > len(x) {
		skip = len(x)
	}

	end := skip + size
	if end > len(x) {
		end = len(x)
	}

	return x[skip:end]
}

func FetchEmployeeManagementFilter(ctx *context.Context) *model.GetEmployeeManagementFilterResponse {
	var response model.GetEmployeeManagementFilterResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var actionBy []entity.Dropdown
	var actionType []entity.Dropdown
	var requestorActiveDirectory []entity.Dropdown
	var err error
	requestorActiveDirectory, err = postgres.FetchActiveDirectoryValue(country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	actionBy, err = postgres.FetchActionByMasterDataAccessValue(country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	actionType = append(actionType, entity.Dropdown{
		Value:       "Modified",
		Description: "Modified",
	}, entity.Dropdown{
		Value:       "Created",
		Description: "Created",
	}, entity.Dropdown{
		Value:       "Activate",
		Description: "Activate",
	}, entity.Dropdown{
		Value:       "Deactivate",
		Description: "Deactivate",
	})
	response.GetEmployeeManagementFilterDropdown = mapper.EmployeeManagementFilterEntityToModel(requestorActiveDirectory, actionBy, actionType)
	return &response
}

func FetchRequesterSubmittedFormAnswer(ctx *context.Context, input *model.RequesterInput) *model.RequesterSubmittedFormAnswerListResponse {
	var response model.RequesterSubmittedFormAnswerListResponse
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	// codes := codeController.GetValueKeyCodes()["userrole"]
	// financeID := codes["finance"].ID
	AdminID := postgres.GetRoleIDByValueInUserRoles("admin")
	superAdminID := postgres.GetRoleIDByValueInUserRoles("superadmin")
	financeID := postgres.GetRoleIDByValueInUserRoles("finance")
	clusterComplianceID := postgres.GetRoleIDByValueInUserRoles("clustercomplianceofficer")
	support := postgres.GetRoleIDByValueInUserRoles("support")
	var Page int
	var limit int
	var status string
	var searchItem string
	var sortItems []string
	var activityFilter []string
	var eventFilter []string
	var statusFilter []string
	var totalCostRange []int
	var dateFilterStart string
	var dateFilterEnd string
	var product string
	var productOwner string
	var team string
	var requestor string
	var dateCreatedFilterStart string
	var dateCreatedFilterEnd string
	var exceptionalStatusFilter []string
	var recallActionType string
	isAdmin := false
	isExcel := false
	if input != nil {
		if input.Status != nil {
			status = *input.Status
		}
		if input.Limit != nil && *input.Limit > 0 {
			limit = *input.Limit
			if input.PageNo != nil && *input.PageNo > 0 {
				Page = *input.Limit * (*input.PageNo - 1)
			}
		}
		if input.RecallActionType != nil {
			recallActionType = *input.RecallActionType
		}
		if input.IsAdmin != nil {
			isAdmin = *input.IsAdmin
		}
		if input.IsExcel != nil {
			isExcel = *input.IsExcel
		}
		if input.DateFilterStart != nil {
			dateFilterStart = *input.DateFilterStart
		}
		if input.DateFilterEnd != nil {
			dateFilterEnd = *input.DateFilterEnd
		}
		if input.DateCreatedFilterStart != nil {
			dateCreatedFilterStart = *input.DateCreatedFilterStart
		}
		if input.DateCreatedFilterEnd != nil {
			dateCreatedFilterEnd = *input.DateCreatedFilterEnd
		}
		if input.ExceptionalStatusFilter != nil {
			exceptionalStatusFilter = input.ExceptionalStatusFilter
		}
		if input.TotalCostRange != nil {
			if len(input.TotalCostRange) != 2 {
				response.Error = true
				response.Message = "The range is incorrect"
				return &response
			}
			totalCostRange = input.TotalCostRange
		}
		if input.SearchItem != nil {
			searchItem = *input.SearchItem
		}
		if input.SortItem != nil {
			sortItems = mapper.SortItemModelToEntityForRequestor(input.SortItem)
		}
		if input.StatusFilter != nil {
			statusFilter = input.StatusFilter
		}
		if input.Product != nil {
			product = *input.Product
		}
		if input.ProductOwner != nil {
			productOwner = *input.ProductOwner
		}
		if input.Team != nil {
			team = *input.Team
		}
		if input.Requestor != nil {
			requestor = *input.Requestor
		}
		if input.TypeOfActivityFilter != nil {
			codes := codeController.GetIdKeyCodes()["activitytype"]
			for _, value := range *&input.TypeOfActivityFilter {
				activityValue := codes[value].Title.String
				activityFilter = append(activityFilter, activityValue)
			}
		}
		if input.TypeOfEventFilter != nil {
			codes := codeController.GetIdKeyCodes()["activityeventtype"]
			for _, value := range *&input.TypeOfEventFilter {
				eventValue := codes[value].Title.String
				eventFilter = append(eventFilter, eventValue)
			}
		}
	}
	var data []*model.RequesterSubmittedFormAnswerList
	var dataExcel []*entity.RequestorFormAnswerListExcel
	if financeID == *approvalRole {
		// userCountry, err := postgres.GetCountryByUserID(userID)
		financeCount, countErr := postgres.GetFinanceFormAnswerListCount(*userCountry, Page, limit, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if countErr != nil {
			response.Error = true
			response.Message = countErr.Error()
			return &response
		}
		formSubmissionList, maxTotalCost, err := postgres.GetFinanceFormAnswerList(*userCountry, Page, limit, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		commentsList, err := postgres.GetRequestorResponse(*userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}

		if isExcel {
			dataExcel = mapper.ExportRequestorInfoForAdminEntityToExcelModel(formSubmissionList)
			url, err := createExportRequestorExcel(dataExcel)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			response.URL = url
		} else {
			data = mapper.ListRequestorResponseForAdminEntityToModel(formSubmissionList, commentsList)
			response.Data = data
			response.MaxTotalCost = maxTotalCost
		}
		lastPage := LastPageCount(financeCount, limit)
		response.LastPage = &lastPage
		response.TotalCount = financeCount
		response.Error = false
	} else if (AdminID == *approvalRole || clusterComplianceID == *approvalRole || superAdminID == *approvalRole || support == *approvalRole) && isAdmin {
		// userCountry, err := postgres.GetCountryByUserID(userID)
		adminCount, countErr := postgres.GetAdminAnswerListCount(*userCountry, *userID, Page, limit, *userID, status, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if countErr != nil {
			response.Error = true
			response.Message = countErr.Error()
			return &response
		}

		if isExcel {
			formSubmissionList, err := postgres.FetchAdminDataForReport(*userCountry, Page, limit, *userID, status, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			dataExcel = mapper.ExportRequestorInfoForAdminEntityToExcelModel(formSubmissionList)
			url, err := createExportRequestorExcel(dataExcel)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			response.URL = url
		} else {
			var maxTotalCost float64
			maxTotalCost, err := postgres.FetchMaxTotalCostForAdmin(*userID, *userCountry)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			formSubmissionList, err := postgres.FetchAdminDataForReport(*userCountry, Page, limit, *userID, status, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			data = mapper.ListRequestorResponseForAdminEntityToModel(formSubmissionList, nil)
			response.Data = data
			response.MaxTotalCost = maxTotalCost
		}
		lastPage := LastPageCount(adminCount, limit)
		response.LastPage = &lastPage
		response.TotalCount = adminCount
		response.Error = false

	} else if postgres.CheckApprovalRoleFromRolesPermissionForRequestor(*approvalRole) {
		fmt.Println("--------------------- here --------------------")
		requestorCount, countErr := postgres.GetRequestorResponseFormAnswerListCount(*userCountry, *userID, Page, limit, status, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter)
		if countErr != nil {
			response.Error = true
			response.Message = countErr.Error()
			return &response
		}
		formSubmissionList, maxTotalCost, err := postgres.GetRequestorResponseFormAnswerList(*userCountry, *userID, Page, limit, status, searchItem, activityFilter, eventFilter, statusFilter, totalCostRange, dateFilterStart, dateFilterEnd, sortItems, isExcel, product, productOwner, team, requestor, dateCreatedFilterStart, dateCreatedFilterEnd, exceptionalStatusFilter, recallActionType)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		commentsList, err := postgres.GetRequestorResponse(*userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		if isExcel {
			dataExcel = mapper.ExportRequestorInfoEntityToExcelModel(formSubmissionList)
			url, err := createExportRequestorExcel(dataExcel)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			response.URL = url
		} else {
			data = mapper.ListRequestorResponseEntityToModel(formSubmissionList, commentsList, *userID)
			response.Data = data
			response.MaxTotalCost = maxTotalCost
		}
		lastPage := LastPageCount(requestorCount, limit)
		response.LastPage = &lastPage
		response.TotalCount = requestorCount
		response.Error = false
	} else {
		response.Error = true
		response.Message = "User does not have requestor access!"
	}
	return &response
}

func LastPageCount(Count int, limit int) int {
	var lastPage int
	if limit > 0 {
		if Count%limit == 0 {
			lastPage = int(Count / limit)
		} else if Count%limit > 0 {
			lastPage = int(Count/limit) + 1
		}
	} else {
		lastPage = 1
	}
	return lastPage
}
func createExportRequestorExcel(input []*entity.RequestorFormAnswerListExcel) (string, error) {
	if len(input) == 0 {
		return "", errors.New("no data found")
	}
	sheetName := "requestorExcel"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Meeting ID", "Event Name", "Type of Activity", "Type of Event", "Requestor Name", "Event Owner", "Date Applied", "Start Date", "End Date", "Product Name", "Venue", "HCO (Event Organizer)", "Gov/ Non Gov/ Self Employed HCP", "HCP's Name", "Specialty", "Institution Name (HCP Affiliation)", "Type of Expenses", "Cost per Expense", "Total Cost", "Status", "Completion Date", "Client", "Requesting Hco", "Recipient", "Meeting Mode", "Virtual Event Details", "Type Of Engagement", "Level Of Influence", "Board Of Directory Name", "Board Of Directory Position", "Board Of Directory Question(Will this individual be involved in any decisions regarding these Company-promoted products in the coming 6-12 months?)", "Board Of Directory Remarks"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	// log.Println(input.Data)
	for index, item := range input {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "Reports-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	//if err := f.SaveAs(filename); err != nil {
	//	println(err.Error())
	//}
	//return "", nil

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}

func FormSubmissionForApproval(ctx context.Context, input *model.FormApprovalInput) *model.FormApprovalResponse {
	if err := checkUserAuthorization(ctx); err != nil {
		return &model.FormApprovalResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	var formApprovalResponse model.FormApprovalResponse

	userID := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	c := auth.GetCountry(ctx)
	country := *c
	userUUID, err := uuid.FromString(*userID)
	if err != nil {
		panic(err)
	}

	entity, err := mapper.MapSubmissionFormApprovalModelToEntity(input, userUUID, approvalRole, country)
	if err != nil {
		return &model.FormApprovalResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	// -------------------------
	err, message := postgres.InsertApprovalLog(ctx, entity, input.IsApollo)
	if err != nil {
		return &model.FormApprovalResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	//action := "pending"
	//codes := codeController.GetValueKeyCodes()["approvedstatus"]
	//pendingStatusCode := codes[action].ID
	pendingStatusCode := 57
	if entity.ApprovedStatus == pendingStatusCode {
		emailData, err := postgres.GetFormAnswerDetails(entity.FormAnswerID.String())
		if err != nil {
			return &model.FormApprovalResponse{
				Error:   true,
				Message: err.Error(),
			}
		}

		data := mapper.SetEmailData(emailData)
		approverID, _ := postgres.GetNextApproverFromFromAnswerID(entity.FormAnswerID.String())
		_ = postgres.InsertStatusNotification(entity.FormAnswerID.String(), *userID, pendingStatusCode, approverID)
		if approverID != "" {
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			approverStatus := emailCode["requireapproval"].ID
			email, err := postgres.GetEmailIDFromUserID(approverID)
			if err != nil {
				log.Printf("%s - Error: %s ", err.Error())
			} else {
				approverEmailContent := util.SendEmailScenarioOne(email, data)
				newApproverEmailContent := strip.StripTags(approverEmailContent)
				_ = postgres.InsertEmailLog(entity.FormAnswerID.String(), approverStatus, newApproverEmailContent, *userID, approverID)
			}

		}
	}

	formApprovalResponse.Message = message
	if input.Action == "return" {
		code := codeController.GetValueKeyCodes()["approvedstatus"]
		status := code["return"].ID
		receiverID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(entity.FormAnswerID)
		notificationErr := postgres.InsertStatusNotification(entity.FormAnswerID.String(), *userID, status, receiverID)
		if notificationErr != nil {
			formApprovalResponse.Error = true
			formApprovalResponse.Message = notificationErr.Error()
			return &formApprovalResponse
		}
		emailData, err := postgres.GetFormAnswerDetails(entity.FormAnswerID.String())
		if err != nil {
			formApprovalResponse.Error = true
			formApprovalResponse.Message = err.Error()
			return &formApprovalResponse
		}
		data := mapper.SetEmailData(emailData)
		email, _ := postgres.GetEmailIDFromFormAnswerID(entity.FormAnswerID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		} else {
			emailContent := util.SendEmailScenarioSix(email, data)
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			requestorStatus := emailCode["approverreturned"].ID
			newEmailContent := strip.StripTags(emailContent)
			_ = postgres.InsertEmailLog(entity.FormAnswerID.String(), requestorStatus, newEmailContent, entity.ApprovedBy.String(), receiverID)

			emailOwner, ownerId, err2 := postgres.GetOwnersEmailIDFromFormAnswerID(entity.FormAnswerID)
			if err2 != nil {
				log.Printf("Error: %s ", err2.Error())
			} else {
				if emailOwner != "" {
					if emailOwner != email {
						emailOwnerContent := util.SendEmailScenarioSix(emailOwner, data)
						newOwnerEmailContent := strip.StripTags(emailOwnerContent)
						_ = postgres.InsertEmailLog(entity.FormAnswerID.String(), requestorStatus, newOwnerEmailContent, entity.ApprovedBy.String(), ownerId)
					}
				}
			}
		}

	}

	if input.Action == "approved" {
		if postgres.GetFormAnswerStatusByFormAnswerID(entity.FormAnswerID.String()) {
			code := codeController.GetValueKeyCodes()["approvedstatus"]
			status := code["finalapproved"].ID
			receiverID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(entity.FormAnswerID)
			_ = postgres.InsertStatusNotification(entity.FormAnswerID.String(), *userID, status, receiverID)
		}
	}

	if country == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, entity.FormAnswerID.String())
	}

	return &formApprovalResponse
}
func FetchFormAnswerDetailsForLinkedEvent(ctx context.Context, inputModel model.FormAnswerInputForLinkedEvent) *model.FormAnswerResponseForLinkedEvent {
	var response model.FormAnswerResponseForLinkedEvent
	userId := auth.GetUserID(ctx)
	var exceptionDetailsEntity *entity.ExceptionDetailsEntity
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	approvalRole := auth.GetApprovalRole(ctx)
	entity, err := mapper.FormAnswerDetailsForLinkedModelToEntity(&inputModel, approvalRole)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	requesterCountry := postgres.FetchRequesterCountry(inputModel.FormID)
	requesterCurrency, boardOfDirectors, ConversionRate, _, _ := postgres.FetchRequesterCurrency(inputModel.FormID)
	exceptionDetailsEntity, err = postgres.FetchExceptionalDetails(inputModel.FormID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
	}
	entityexception := *exceptionDetailsEntity
	formAnswer, requestorName, errString := postgres.FetchFormAnswersForLinkedEvents(entity, approvalRole)
	if errString != "" {
		response.Error = true
		response.Message = errString
		return &response
	}
	data := mapper.FormAnswerDesignEntityToModel(formAnswer, requesterCountry)
	attachments, boardOfDirectorsDataRes := mapper.FormAnswerAttachmentEntityToModel(formAnswer, boardOfDirectors)
	createdDate, _, _, err := postgres.GetDateCreatedByFormAnswerId(entity.FormId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	dateCreated := util.GetTimeUnixTimeStamp(createdDate.Time)
	meetingID := util.ConvertToEventID(entityexception.EventCode, entityexception.EventSeq)
	response.DateCreated = &dateCreated
	response.Answers = data
	response.EventRequestorName = &requestorName
	response.Attachments = attachments
	response.RequestorCountry = &requesterCountry
	response.RequestorCurrency = &requesterCurrency
	response.IsExceptionalApproval = *entityexception.IsExceptionalApproval
	response.DetailsOfRequest = entityexception.DetailsOfRequest
	response.IhcpOtherLocalPolicies = entityexception.IhcpOtherLocalPolicies
	response.MoreComments = entityexception.MoreComments
	response.ExceptionalApprovalFileUpload = entityexception.ExceptionalApprovalFileUpload
	response.ScopeOfExceptionalRequest = entityexception.ScopeOfExceptionalRequest
	response.IsChangeRequest = entityexception.IsChangeRequest
	response.ChangeRequestSummary = entityexception.ChangeRequestSummary
	response.MeetingID = &meetingID
	response.ConversionRate = &ConversionRate
	response.IsRecall = exceptionDetailsEntity.IsRecall
	response.RecallSummary = exceptionDetailsEntity.RecallSummary
	response.BoardOfDirectory = boardOfDirectorsDataRes
	return &response
}
func RequestorActionSubmission(ctx *context.Context, inputModel model.RequestorActionInput) *model.FormApprovalResponse {
	var formApprovalResponse model.FormApprovalResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var err error
	if userID == nil && approvalRole == nil {
		formApprovalResponse.Error = true
		formApprovalResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &formApprovalResponse
	}
	entity, err := mapper.MapRequestorActionModelToEntity(&inputModel, userID, approvalRole)
	if err != nil {
		formApprovalResponse.Error = true
		formApprovalResponse.Message = err.Error()
		return &formApprovalResponse
	}
	err, message := postgres.UpdateRequestorResponse(entity, inputModel.IsApollo)
	if err != nil {
		formApprovalResponse.Error = true
		formApprovalResponse.Message = err.Error()
		return &formApprovalResponse
	}
	emailData, err := postgres.GetFormAnswerDetails(entity.FormAnswerID.String())
	if err != nil {
		formApprovalResponse.Error = true
		formApprovalResponse.Message = err.Error()
		return &formApprovalResponse
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["pendingresponse"].ID
	approverID, _ := postgres.GetRequestorResponseByFormID(entity.FormAnswerID)
	notificationErr := postgres.InsertStatusNotification(entity.FormAnswerID.String(), *userID, status, approverID)
	if notificationErr != nil {
		formApprovalResponse.Error = true
		formApprovalResponse.Message = notificationErr.Error()
		return &formApprovalResponse
	}
	data := mapper.SetEmailData(emailData)
	email, err := postgres.GetEmailIDFromUserID(approverID)
	if err != nil {
		log.Printf("%s - Error: %s ", err.Error())
	} else {
		emailContent := util.SendEmailScenarioThree(email, data)
		emailCode := codeController.GetValueKeyCodes()["typeofemail"]
		requestorStatus := emailCode["requireapproval"].ID
		newEmailContent := strip.StripTags(emailContent)
		_ = postgres.InsertEmailLog(entity.FormAnswerID.String(), requestorStatus, newEmailContent, *userID, approverID)
	}
	formApprovalResponse.Message = message
	return &formApprovalResponse
}

func SaveFormAnswerAttachments(ctx *context.Context, input model.FormAnswerAttachment) *model.FormAnswerAttachmentResponse {
	var formAnswerAttachmentResponse model.FormAnswerAttachmentResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &formAnswerAttachmentResponse
	}
	approvalName, err := postgres.GetRoleNameFromId(*approvalRole)
	if err != nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = err.Error()
		return &formAnswerAttachmentResponse
	}
	var entity entity.FormAttachmentInput
	if strings.TrimSpace(input.FormAnswerID) != "" {
		userId, err := uuid.FromString(input.FormAnswerID)
		if err != nil {
			formAnswerAttachmentResponse.Error = true
			formAnswerAttachmentResponse.Message = "Form answer ID format is invalid!"
			return &formAnswerAttachmentResponse
		}
		if approvalName != "clustercomplianceofficer" && approvalName != "admin" {
			if !postgres.IsFormIDApproved(&userId) {
				formAnswerAttachmentResponse.Error = true
				formAnswerAttachmentResponse.Message = "Form answer is not Approved"
				return &formAnswerAttachmentResponse
			}
		}

		// entities.FormAnswerID = userId
		entity.FormAnswerId = userId
	} else {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = "Form answer ID cannot be blank!"
		return &formAnswerAttachmentResponse
	}

	if postgres.IsFormAnswerExistsInFormAnswerAttachment(entity.FormAnswerId) {
		err := postgres.DeleteFormAnswerAttachments(entity.FormAnswerId)
		if err != nil {
			formAnswerAttachmentResponse.Error = true
			formAnswerAttachmentResponse.Message = "failed to delete the data in formAnswerAttachment table"
			return &formAnswerAttachmentResponse
		}
	}

	var formAnswerAttachmentIds []string
	for _, attachment := range input.Attachments {
		action1 := *attachment.Type
		entity.Type = action1
		for _, description := range attachment.Categories {
			action2 := *description.Description
			entity.Description = action2
			for _, url := range description.URL {
				entity.Url = url
				formAnswerAttachmentId, err := postgres.InsertFormAnswerAttachment(entity, input.IsApollo)
				if err != nil {
					formAnswerAttachmentResponse.Error = true
					formAnswerAttachmentResponse.Message = "failed to add data in formAnswerAttachment table"
					return &formAnswerAttachmentResponse
				}
				formAnswerAttachmentIds = append(formAnswerAttachmentIds, formAnswerAttachmentId)
			}
		}
	}
	formAnswerAttachmentResponse.Message = "successfully saved attachment !"
	return &formAnswerAttachmentResponse
}

func UploadFormAnswerAttachments(ctx *context.Context, input model.FormAnswerAttachment) *model.FormAnswerAttachmentResponse {
	var formAnswerAttachmentResponse model.FormAnswerAttachmentResponse
	userID := auth.GetUserID(*ctx)
	countryID := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var err error

	if userID == nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &formAnswerAttachmentResponse
	}
	approvalName, err := postgres.GetRoleNameFromId(*approvalRole)
	if err != nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = err.Error()
		return &formAnswerAttachmentResponse
	}
	entity, err := mapper.FormAnswerAttachmentModelToEntity(&input, approvalName)
	if err != nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = err.Error()
		return &formAnswerAttachmentResponse
	}
	requestorID, answer, _ := postgres.GetCreatedByIDFromFormAnswerID(entity.FormAnswerID)
	err, message := postgres.InsertFormAnswerAttachmentId(entity, answer, userID, input.IsApollo)
	if err != nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = err.Error()
		return &formAnswerAttachmentResponse
	}
	// approverID, _ := postgres.GetIDOfApproversFromFormAnswerID(entity.FormAnswerID)
	var approverID []string
	approverID = append(approverID, requestorID)
	emailOwner, ownerId, err2 := postgres.GetOwnersEmailIDFromFormAnswerID(entity.FormAnswerID)
	if err2 != nil {
		log.Printf("Error: %s ", err2.Error())
	} else {
		if ownerId != requestorID {
			approverID = append(approverID, ownerId)
		}
	}

	FinanceID, _ := postgres.GetIDOfFinanceFromLoggedinUserCountry(*countryID)
	approverID = append(approverID, FinanceID...)
	code := codeController.GetValueKeyCodes()["formanswertype"]
	status := code["completed"].ID
	for _, value := range approverID {
		notificationErr := postgres.InsertStatusNotification(entity.FormAnswerID.String(), *userID, status, value)
		if notificationErr != nil {
			formAnswerAttachmentResponse.Error = true
			formAnswerAttachmentResponse.Message = notificationErr.Error()
			return &formAnswerAttachmentResponse
		}
	}

	if postgres.IsFormIDCompleted(&entity.FormAnswerID) {
		emailData, err := postgres.GetFormAnswerDetails(entity.FormAnswerID.String())
		if err != nil {
			formAnswerAttachmentResponse.Error = true
			formAnswerAttachmentResponse.Message = err.Error()
			return &formAnswerAttachmentResponse
		}
		var allEmail []string
		data := mapper.SetEmailData(emailData)
		email, err1 := postgres.GetEmailIDFromFormAnswerID(entity.FormAnswerID)
		// allEmail, _ := postgres.GetEmailIDOfApproversFromFormAnswerID(entity.FormAnswerID)
		// financeEmail, err3 := postgres.GetEmailIDOfAllFinanceOfLoggedinUserCountry(*countryID)
		if err1 != nil {
			log.Printf("%s - Error: %s ", err1.Error())
		} else {
			allEmail = append(allEmail, email)
		}
		if emailOwner != "" {
			if emailOwner != email {
				allEmail = append(allEmail, emailOwner)
			}
		}
		if len(allEmail) < 1 {
			log.Printf("%s - Error: No email found")
		} else {
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			requestorStatus := emailCode["formcompleted"].ID
			var newEmailContent string
			for _, value := range allEmail {
				emailContent := util.EventCompletionEmailToApprovers(value, data)
				newEmailContent = strip.StripTags(emailContent)

			}
			for _, receiver := range approverID {
				_ = postgres.InsertEmailLog(entity.FormAnswerID.String(), requestorStatus, newEmailContent, *userID, receiver)
			}
		}

	}
	formAnswerAttachmentResponse.Message = message
	return &formAnswerAttachmentResponse
}

func FetchApproverDataByFormAnsId(ctx context.Context, input model.ApproverformAnsID) *model.FetchapproverDataByFormAnsID {
	var response model.FetchapproverDataByFormAnsID
	approvalRole := auth.GetApprovalRole(ctx)
	userId := auth.GetUserID(ctx)
	countryID := auth.GetCountry(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	code := codeController.GetValueKeyCodes()["approvedstatus"]
	cancelStatus := code["cancelled"].ID

	// code = codeController.GetValueKeyCodes()["userrole"]
	// adminRole := code["admin"].ID
	adminRole := postgres.GetRoleIDByValueInUserRoles("admin")

	requestorID, err := postgres.GetrequestorIDByFormAnsID(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if requestorID != *userId {
		if adminRole != *approvalRole {
			response.Error = true
			response.Message = "User do not have access"
			return &response
		}
	}

	status, err := postgres.GetFormAnsStatus(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	FormAnscountryId, err := postgres.GetCountryByFormAnsID(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if FormAnscountryId != *countryID {
		response.Error = true
		response.Message = "User does not belong to event country"
		return &response
	}

	var approver []entity.FetchApprovalRoleByFormAnsIDModelToEntity
	if cancelStatus == status {
		approver, err = postgres.GetApprovalByFormAnsIDForReturn(input)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	} else {
		approver, err = postgres.GetApprovalByFormAnsID(input)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}

	}
	outputEntity := mapper.GetApprovalRoleByFormAnsID(approver)
	response.Approval = outputEntity
	return &response
}

func FetchApprovalRolesByActivities(ctx context.Context, inputModel model.ActivityInputs) *model.ApprovalRolesUserSelectionResponse {
	var response model.ApprovalRolesUserSelectionResponse
	userId := auth.GetUserID(ctx)
	countryID := auth.GetCountry(ctx)
	userRoleID := auth.GetApprovalRole(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	formID := *inputModel.FormAnswerID
	f := postgres.GetFormAnswerByID(formID)
	if f == nil {
		return &model.ApprovalRolesUserSelectionResponse{
			Error:   true,
			Message: "formAnswerId not found",
		}
	}
	codes := codeController.GetIdKeyCodes()["country"]
	countryValue := codes[*countryID].Value
	localCurrency, err := postgres.GetUSDConversionForCountries(countryValue)
	if err != nil {
		log.Printf("%s - Error: %s", err.Error())
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	inputEntity, err := mapper.GetApprovalRolesInputModelToEntity(ctx, &inputModel, *userRoleID, *countryID, localCurrency, f.LedBy)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	approvalRoleSelections, _ := postgres.GetApprovalRolesActivities(inputEntity)

	res, err := mapper.UserRoleSelectionEntityToModel(approvalRoleSelections, *userId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	if len(res.UsersByapprovalRole) == 0 {
		return res
	}

	// When it's PH, is change request, and status draft (approved->draft)
	if (f.Country == 400 && f.StatusCode == 66) && (f.IsChangeRequest && f.ChangeRequestType != "major") {
		changeApprovalType := f.ChangeRequestType
		if changeApprovalType == "" {
			changeApprovalType = `minor`
		}
		//fmt.Println("++++++++++++++++++", changeApprovalType)
		current, prev := postgres.GetFormAnswerEntityByID_TwoMostRecentCopies(formID)
		currentJSON := current.Design.Get()
		prevJSON := prev.Design.Get()
		aa := currentJSON.([]interface{})
		bb := prevJSON.([]interface{})

		hcpSectionA := aa[2]
		hcpSectionB := bb[2]
		dataA := mapper.FormAnswerDesignEntityToModel(current, "phzpc")
		dataB := mapper.FormAnswerDesignEntityToModel(prev, "phzpc")

		//fmt.Println(reflect.DeepEqual(hcpSectionA, hcpSectionB))
		isThereAnyMajorChangeInHCP := false
		if changeApprovalType == `minor` && !reflect.DeepEqual(hcpSectionA, hcpSectionB) {
			currentHCPList := mapper.GetHCPInfo(dataA[2]) // current
			prevHCPList := mapper.GetHCPInfo(dataB[2])    // prev
			m := make(map[string]string)
			for _, v := range prevHCPList {
				m[v.Name] = v.LevelOfInfluence
			}

			for i := 0; i < len(currentHCPList); i++ {
				name := currentHCPList[i].Name
				levelOfInfluence := currentHCPList[i].LevelOfInfluence
				prevLevelOfInfluence, f := m[name]
				if (levelOfInfluence == "High" && !f) || (levelOfInfluence == "High" && f && prevLevelOfInfluence != levelOfInfluence) {
					//changeApprovalType = "major"
					isThereAnyMajorChangeInHCP = true
					break
				}
			}
			//fmt.Println(len(res.UsersByapprovalRole), len(l))
			//fmt.Println(len(res.UsersByapprovalRole))
		}

		//fmt.Println("------------------------------------ run ", changeApprovalType)

		if changeApprovalType == `minor` && isThereAnyMajorChangeInHCP == false {
			res.UsersByapprovalRole = res.UsersByapprovalRole[:1]
			return res
		} else if changeApprovalType == `minor` && isThereAnyMajorChangeInHCP == true {
			listOfApprovers := res.UsersByapprovalRole
			res.UsersByapprovalRole = res.UsersByapprovalRole[:1]
			for _, v := range listOfApprovers[1:] {
				v1 := v
				fmt.Println(v1.DepartmentName, *v1.HighLoi)
				if v.HighLoi != nil && *v.HighLoi == true {
					res.UsersByapprovalRole = append(res.UsersByapprovalRole, v1)
				}
			}
		}
	}

	return res
}

func generateEventCode(userRoleID string, countryCode int) string {
	requesterDesc := postgres.GetUserRoleDescByCatergoryID(userRoleID)
	country := postgres.GetCodeDescByCatergoryID("Country", countryCode)
	return country + "-" + requesterDesc + "-" + time.Now().Format("20060102")

}
func FetchTotalEvents(ctx *context.Context) *model.TotalEventsResponse {
	var response model.TotalEventsResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var data *model.TotalEvents
	if postgres.CheckApprovalRoleFromRolesPermissionForBothRequestorApprovers(*approvalRole) {
		totalEvenApproverData, totalEvenRequestorData, err := postgres.FetchTotalEventForApproverandRequestor(*userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		data = mapper.TotalEventEntityToModel(totalEvenApproverData, totalEvenRequestorData)
		if data == nil {
			response.Error = true
			response.Message = "No data exists"
			return &response
		} else {
			response.Error = false
			response.Data = data
		}
	} else if postgres.CheckApprovalRoleFromRolesPermission(*approvalRole) {
		totalEvenApproverData, err := postgres.FetchTotalEventForApprover(*userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		data = mapper.TotalEventForApproverEntityToModel(totalEvenApproverData)
		if data == nil {
			response.Error = true
			response.Message = "No data exists"
			return &response
		} else {
			response.Error = false
			response.Data = data
		}
	} else {
		totalEvenRequestorData, err := postgres.FetchTotalEventForRequestor(*userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		data = mapper.TotalEventForRequestorEntityToModel(totalEvenRequestorData)
		if data == nil {
			response.Error = true
			response.Message = "No data exists"
			return &response
		} else {
			response.Error = false
			response.Data = data
		}

	}
	return &response
}

func FetchApprovalTrailDataForAnActivity(ctx context.Context, inputModel model.FormAnswerIDInput) *model.ApprovalTrailFormSubmissionResponse {
	var response model.ApprovalTrailFormSubmissionResponse
	userId := auth.GetUserID(ctx)
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	entity, err := mapper.FetchApprovalRoleModelToEntity(&inputModel)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	var data []*model.ApprovalData
	approvalTrailDetails, err := postgres.GetApprovalTrailDataForANActivity(entity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	data = mapper.FetchApprovalTrailValuesEntityToModel(approvalTrailDetails, entity)
	response.Approval = data
	return &response
}

func FormAnswerIDByEventID(ctx context.Context, inputModel model.FormAnswerByEventIDInput) *model.FormAnswerByEventIDResponse {
	var response model.FormAnswerByEventIDResponse
	userId := auth.GetUserID(ctx)
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	entity, err := mapper.FormAnswerIDByEventIDModelToEntity(&inputModel)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	approvalTrailDetails, err := postgres.GetFormAnswerIdByEvent(entity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	data := mapper.FormAnswerIDByEventIDEntityToModel(*approvalTrailDetails)
	response.Data = data
	return &response
}

func GetApprovalLogData(ctx context.Context, inputModel model.FormAnswerInput) *model.ApprovalLogResponse {
	var response model.ApprovalLogResponse
	userId := auth.GetUserID(ctx)
	var data []*model.ApprovalLog
	var dataForRequestor []*model.ApprovalLog
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	entity1, err := mapper.FetchApprovalLogModelToEntity(&inputModel)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	wgMain := &sync.WaitGroup{}
	errorChannelMain := make(chan error, 2)
	wgMain.Add(1)
	go func(input *entity.FetchApprovalTrailEntity, wgMain *sync.WaitGroup) {
		defer wgMain.Done()
		var err error
		approvalLogDetails, err := postgres.GetApprovalLogDataForActivity(input)
		if err != nil {
			errorChannelMain <- err
		}
		data = mapper.FetchApprovalLogValuesEntityToModel(approvalLogDetails)

	}(entity1, wgMain)
	wgMain.Add(1)
	go func(input *entity.FetchApprovalTrailEntity, wgMain *sync.WaitGroup) {
		defer wgMain.Done()
		var err error
		approvalLogDetailsForRequester, err := postgres.GetApprovalLogDataForActivityForRequester(input)
		if err != nil {
			errorChannelMain <- err
		}
		dataForRequestor = mapper.FetchApprovalTrailValuesEntityToModelForRequestor1(approvalLogDetailsForRequester)
	}(entity1, wgMain)
	go func() {
		wgMain.Wait()
		close(errorChannelMain)
	}()

	var lastErrMain error
	for eachError := range errorChannelMain {
		if eachError != nil {
			if lastErrMain == nil {
				lastErrMain = errors.New(eachError.Error())
			} else {
				lastErrMain = errors.New(lastErrMain.Error() + "\n" + eachError.Error())
			}
		}
	}
	if lastErrMain != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	data = append(data, dataForRequestor...)
	response.GetApprovalLog = data
	return &response
}

func UpdateStatusChangeNotification(ctx context.Context, input *model.StatusChangeNotificationInput) *model.ResponseStatusChangeNotification {
	var response model.ResponseStatusChangeNotification
	userID := auth.GetUserID(ctx)
	if userID == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	for _, value := range input.NotificationID {
		receiver, err := postgres.FetchreceiverFromNotification(value)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}

		entity, err := mapper.CheckForStatusChangeNotification(value, receiver, *userID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		HasReadChanges := postgres.UpdateHasReadInStatusChangeNotification(*entity)
		if HasReadChanges != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}
	response.Error = false
	response.Message = "Status Updated"
	return &response
}

func UpdateFormAnswerActivityDate(ctx context.Context, input model.FormSubmissionActivity) *model.FormSubmissionResponse {
	var response model.FormSubmissionResponse
	userId := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	country := auth.GetCountry(ctx)

	var err error
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	} else {
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}
	// action := "admin"
	// codes := codeController.GetValueKeyCodes()["userrole"]
	// userID := codes[action].ID
	userID := postgres.GetRoleIDByValueInUserRoles("admin")
	clusterComplianceOfficer := postgres.GetRoleIDByValueInUserRoles("clustercomplianceofficer")

	if *approvalRole == userID {
		response.Error = false
	} else if *approvalRole == clusterComplianceOfficer {
		response.Error = false
	} else {
		response.Error = true
		response.Message = "Only admin and cluster compliance officer can take action !!"
		return &response
	}

	entity, err := mapper.UpdateFormAnswerActivityDateModelToEntity(input, *country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	FormSubmissionChanges := postgres.FetchFormAnswerActivityDate(*entity, input, *userId)
	if FormSubmissionChanges != nil {
		response.Error = true
		response.Message = FormSubmissionChanges.Error()
		return &response
	}
	err = postgres.UpdateFormAnswerActivityDate(*entity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	err = postgres.UpdateReportingDbActivityDate(*entity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	errException := postgres.UpdateFormAnswerException(entity)
	if errException != nil {
		response.Error = true
		response.Message = errException.Error()
		return &response
	}
	errException = postgres.UpdateReportingDbException(entity)
	if errException != nil {
		response.Error = true
		response.Message = errException.Error()
		return &response
	}
	statusEvent := postgres.GetStatusForEachEvent(input.FormAnsID)

	func(formAnswerId string) {
		if _, err := veeva.GetAuthRest(); err != nil {
			panic(err)
		}
		if err := postgres.InsertIntoVeeva(formAnswerId, statusEvent); err != nil {
			panic(err)
		}
	}(input.FormAnsID)

	response.Error = false
	response.Message = "successfully Updated form answer!!"
	return &response
}

func UpdateCompletedEvents(ctx context.Context, input model.UpdateCompletedEventsInput) *model.UpdateCompletedEventsResponse {
	var response model.UpdateCompletedEventsResponse
	userId := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)

	var err error
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	userID := postgres.GetRoleIDByValueInUserRoles("admin")
	clusterComplianceOfficer := postgres.GetRoleIDByValueInUserRoles("clustercomplianceofficer")

	if *approvalRole == userID {
		response.Error = false
	} else if *approvalRole == clusterComplianceOfficer {
		response.Error = false
	} else {
		response.Error = true
		response.Message = "Only admin and cluster compliance officer can take action !!"
		return &response
	}

	if input.FormAnsID == "" {
		response.Error = true
		response.Message = "Form AnswerID can not be blank !!"
		return &response
	}

	if input.Status == "" {
		response.Error = true
		response.Message = "Status can not be blank !!"
		return &response
	}

	currentStatus, err := postgres.FetchFormAnswersForCancel(input.FormAnsID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	if input.Status == "pending" || input.Status == "approved" {
		if currentStatus != 67 {
			response.Error = true
			response.Message = "Only completed events can be reverted back to pending or approved !!"
			return &response
		} else {
			if input.Status == "pending" {
				err = postgres.UpdateEventStatusFromCompletedToPending(input.FormAnsID)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
			} else if input.Status == "approved" {
				err = postgres.UpdateEventStatusFromCompletedToApproved(input.FormAnsID)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
			}
		}
	} else if input.Status == "completed" {
		err = postgres.UpdateEventStatusFromPendingToCompleted(input.FormAnsID)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}

	// FOR APL MAPTOOL
	userCountry := auth.GetCountry(ctx)
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, input.FormAnsID)
	}
	//

	response.Error = false
	response.Message = "Successfully updated form answer!!"
	return &response
}
func ExcelExtractTrailDataController(ctx *context.Context, input model.ExcelExtractTrailDataRequest) *model.ExcelExtractTrailDataResponse {
	var response model.ExcelExtractTrailDataResponse
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	AdminID := postgres.GetRoleIDByValueInUserRoles(os.Getenv("ADMIN"))
	clusterComplianceID := postgres.GetRoleIDByValueInUserRoles(os.Getenv("CCO"))
	var dateFilterStart string
	var dateFilterEnd string
	var formId uuid.UUID
	isExcel := false
	if input.IsExcel != nil {
		isExcel = *input.IsExcel
	}
	if input.StartDate != nil {
		dateFilterStart = *input.StartDate
	}
	if input.EndDate != nil {
		dateFilterEnd = *input.EndDate
	}
	if input.FormAnsID != nil && strings.TrimSpace(*input.FormAnsID) != "" {
		uuid, err := uuid.FromString(*input.FormAnsID)
		if err != nil {
			response.Error = true
			response.Message = "FormanswerId format Invalid!!"
			return &response
		}
		formId = uuid
	}
	if AdminID == *approvalRole || clusterComplianceID == *approvalRole {
		if isExcel {
			auditTrailData, err := postgres.ExcelExtractTrailDataFromDB(*userCountry, formId, dateFilterStart, dateFilterEnd)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			log.Println(auditTrailData)
			dataExcel := mapper.ExportAuditTrailEntityToExcelModel(auditTrailData)
			url, err := createExportAuditTrailExcel(dataExcel)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			response.URL = &url
		}
	} else {
		response.Error = true
		response.Message = "User does not have requestor access!"
	}
	return &response
}
func createExportAuditTrailExcel(input []*entity.RequestorFormAnswerListExcel) (string, error) {

	if len(input) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "requestorExcel"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)
	firstRow := []string{"Meeting ID", "Approver Name", "User Role", "Date", "Comment", "Action"}

	// Populate excel 1 row header columns
	// typeInfo := reflect.TypeOf(entity.RequestorFormAnswerListExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < len(firstRow); fieldIndex++ {
		headerRow = append(headerRow, firstRow[fieldIndex])
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	// log.Println(input.Data)
	for index, item := range input {
		rowNumber := index + 2
		// log.Println(item.Data[10])
		// s := item.Data[10]
		// log.Println(len(s))
		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "Reports-" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }

	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}
func CancelFormAnsID(ctx context.Context, input model.UserformAnsID) *model.FormAnswerforcancel {
	var response model.FormAnswerforcancel
	userCountry := auth.GetCountry(ctx)
	userId := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	} else {
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}
	adminID := postgres.GetRoleIDByValueInUserRoles(os.Getenv("ADMIN"))
	clusterComplianceOfficer, _ := postgres.GetRoleIDByValueInUserRolesCcO(os.Getenv("CCO"))
	if *approvalRole == adminID || *approvalRole == clusterComplianceOfficer || postgres.Checkformidanduserid(input.FormAnsID, *userId) {
		response.Error = false
	} else {
		response.Error = true
		response.Message = "Only admin and cluster compliance officer and requestor can take action !!"
		return &response
	}
	status, err := postgres.FetchFormAnswersForCancel(input.FormAnsID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	//action := "draft"
	//codes := codeController.GetValueKeyCodes()["approvedstatus"]
	//	draftaction := codes[action].ID

	actionforcomplete := "completed"
	codesforcomplte := codeController.GetValueKeyCodes()["formanswertype"]
	completes := codesforcomplte[actionforcomplete].ID

	actionforrejected := "rejected"
	codesforrejected := codeController.GetValueKeyCodes()["approvedstatus"]
	rejected := codesforrejected[actionforrejected].ID

	if status == completes {
		response.Error = false
		response.Message = "Event completed status, can not be cancelled"
		return &response
	}
	// if status == draftaction {
	// 	response.Error = false
	// 	response.Message = "Event draft status, can not be cancelled"
	// 	return &response
	// }
	if status == rejected {
		response.Error = false
		response.Message = "Event rejected status, can not be cancelled"
		return &response
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	CancelledStatus := code["cancelled"].ID
	var createdBy uuid.UUID
	if userId != nil {
		createdBy, _ = uuid.FromString(*userId)
	}
	answerJson, _ := postgres.FetchAnswersJsonForCancel(input.FormAnsID)
	answer := pgtype.JSONB{}
	_ = answer.Set(answerJson)
	postgres.CreateFormAnswerLogs(ctx, answer, &createdBy, input.FormAnsID, CancelledStatus, input.IsApollo)
	cancelerr := postgres.UpdateFormAnswerId(&input)
	if cancelerr != nil {
		response.Error = true
		response.Message = err.Error()
	} else {
		checkEventExistInVeevaLog, _ := postgres.VeevaEventLogChecker(input.FormAnsID)

		if checkEventExistInVeevaLog {
			postgres.InsertIntoVeeva(input.FormAnsID, "Cancelled")
		}
		response.Error = false
		response.Message = "Event succesfully canceled"
	}

	// FOR APL MAPTOOL
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, input.FormAnsID)
	}
	//

	return &response
}

func checkUserAuthorization(ctx context.Context) error {
	userID := auth.GetUserID(ctx)
	userRoleID := auth.GetApprovalRole(ctx)
	if userID == nil || userRoleID == nil {
		return errors.New("you are not authorized to login please contact your country ezflow admin")
	}

	return nil
}

func fetchCountryOptions() []entity.Dropdown {
	return []entity.Dropdown{
		{
			Value:       "vn",
			Description: "Vietnam",
		}, {
			Value:       "th",
			Description: "Thailand",
		}, {
			Value:       "my",
			Description: "Malaysia",
		}, {
			Value:       "sg",
			Description: "Singapore",
		}, {
			Value:       "ph",
			Description: "Philippines",
		}, {
			Value:       "id",
			Description: "Indonesia",
		}, {
			Value:       "mm",
			Description: "Myanmar",
		}, {
			Value:       "tw",
			Description: "Taiwan",
		}, {
			Value:       "hk",
			Description: "Hong Kong",
		}, {
			Value:       "mo",
			Description: "Macau",
		}, {
			Value:       "kr",
			Description: "Korea",
		}, {
			Value:       "kh",
			Description: "Cambodia",
		}, {
			Value:       "bn",
			Description: "Brunei",
		}, {
			Value:       "au",
			Description: "Australia",
		}, {
			Value:       "nz",
			Description: "New Zealand",
		}, {
			Value:       "jp",
			Description: "Japan",
		}, {
			Value:       "cn",
			Description: "China",
		}, {
			Value:       "in",
			Description: "India",
		}, {
			Value:       "us",
			Description: "USA",
		}, {
			Value:       "ca",
			Description: "Canada",
		}, {
			Value:       "fr",
			Description: "French",
		}, {
			Value:       "it",
			Description: "Italy",
		}, {
			Value:       "uk",
			Description: "United Kingdom",
		}, {
			Value:       "sz",
			Description: "Switzerland",
		},
	}
	//return postgres.FetchCodeDescriptionAndValueForOther("Country")
}

func FetchActivitiesAndEventsSelectionForAllDropdownValue(ctx context.Context) *model.GetActivitiesAndEventSelectionAllDropDownValuesResponse {
	err := checkUserAuthorization(ctx)
	if err != nil {
		return &model.GetActivitiesAndEventSelectionAllDropDownValuesResponse{
			Error:                           true,
			Message:                         err.Error(),
			GetActivitiesAndEventsSelection: nil,
		}
	}

	meetingMode := postgres.FetchCodeDescriptionAndValueForOther("MeetingMode")
	fetchRole := postgres.FetchCodeDescriptionAndValueForOther("RoleMaxPaidTime")
	fetchExpertLevel := postgres.FetchCodeDescriptionAndValueForOther("HCPTier")
	fetchExpertLevelInternational := postgres.FetchCodeDescriptionAndValueForOther("ExpertLevelInternational")
	fetchCountry := fetchCountryOptions()
	fetchTypeOfEventExpenses := postgres.FetchCodeDescriptionAndValueForOther("EventExpenseType")

	return &model.GetActivitiesAndEventSelectionAllDropDownValuesResponse{
		GetActivitiesAndEventsSelection: mapper.CodeDescriptionAndValueEntityToModelForAllDropdownValues(
			meetingMode,
			fetchRole,
			fetchExpertLevelInternational,
			fetchCountry,
			fetchExpertLevel,
			fetchTypeOfEventExpenses),
	}
}

func FetchActivitiesAndEventsSelection(ctx context.Context) *model.GetActivitiesAndEventsSelectionResponse {
	var response model.GetActivitiesAndEventsSelectionResponse
	userID := auth.GetUserID(ctx)
	userRoleID := auth.GetApprovalRole(ctx)
	countryID := auth.GetCountry(ctx)
	if userID == nil || userRoleID == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		response.GetActivitiesAndEventsSelection = &model.GetActivitiesAndEventSelection{}
		return &response
	}

	//a := `c7fcd83d-d5a4-4295-af67-eb67f91bbe2f`
	//userID := &a
	//c := `b4042617-b12b-4f59-b4b0-1b9449335f49`
	//userRoleID := &c
	//d := 10
	//countryID := &d

	status := "ApprovedStatus"
	status1 := "FormAnswerType"
	activity := "ActivityType"
	event := "ActivityEventType"
	var fetchStatus []entity.Dropdown
	var fetchTeam []entity.Dropdown
	var fetchProduct []entity.Dropdown
	var fetchActivity []entity.DropdownIDAndDescription
	var fetchEvent []entity.DropdownIDAndDescription
	var fetchProductOwner []entity.Dropdown

	var err error
	fetchStatus, err = postgres.FetchCodeDescriptionAndValue(status, status1)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchTeam, err = postgres.FetchTeamDescriptionAndValue(*countryID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchProduct, err = postgres.FetchProductDescriptionAndValue(*countryID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchActivity, err = postgres.FetchCodeIDAndDescription(activity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchEvent, err = postgres.FetchCodeIDAndDescription(event)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchProductOwner, err = postgres.FetchProductOwnerValueAndDescription(*countryID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchRequesters, err := postgres.FetchRequestorValueAndDescription(*countryID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.GetActivitiesAndEventsSelection = mapper.CodeDescriptionAndValueEntityToModel(fetchStatus, fetchActivity, fetchEvent, fetchTeam, fetchProduct, fetchProductOwner, fetchRequesters)
	setActivityListForUser(ctx, *userRoleID, *countryID, response.GetActivitiesAndEventsSelection)
	return &response
}

func setActivityListForUser(ctx context.Context, userRoleID string, country int, d *model.GetActivitiesAndEventSelection) {
	var finalActivityTypeList, finalActivityEventList []*model.Dropdown
	userEventAccessList := postgres.GetUserEventAccessControl(ctx, userRoleID, country)
	m := make(map[string]bool)
	for i := range userEventAccessList {
		m[userEventAccessList[i].Description] = true
	}

	//-----------

	defaultActivityTypeList := d.ActivityType
	for i := range defaultActivityTypeList {
		d := defaultActivityTypeList[i].Description
		if d == "ZP - Hosted Event" {
			finalActivityTypeList = append(finalActivityTypeList, defaultActivityTypeList[i])
			continue
		}

		if _, f := m[d]; f {
			finalActivityTypeList = append(finalActivityTypeList, defaultActivityTypeList[i])
		}
	}
	d.ActivityType = finalActivityTypeList

	//-----------

	defaultActivityEventList := d.EventType
	for i := range defaultActivityEventList {
		d := defaultActivityEventList[i].Description
		if _, f := m[d]; f {
			finalActivityEventList = append(finalActivityEventList, defaultActivityEventList[i])
		}
	}
	d.EventType = finalActivityEventList
}

func FetchRequestorDataForCountry(ctx *context.Context, input *model.GetRequestorForCountryInput) *model.GetRequestorForCountryResponse {
	var response model.GetRequestorForCountryResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var fetchRequestor []entity.Dropdown

	var err error
	fetchRequestor, err = postgres.FetchRequestorDescriptionAndValue(*country, input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.Data = mapper.CodeDescriptionAndValueForRequestorEntityToModel(fetchRequestor)
	return &response
}

func FetchTypeApprovalList(ctx *context.Context, input model.GetTypeApprovalListInput) *model.GetTypeApprovalListResponse {
	var response model.GetTypeApprovalListResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)

	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	entity, err := mapper.FetchTypeApprovalListModelToEntity(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	if strings.EqualFold(entity.Type, "ActivityStartDateEndDate") || strings.EqualFold(entity.Type, "NoOfHcpNonHcp") || strings.EqualFold(entity.Type, "TypeOfHcpSponsored") {
		fetchSingleApprover, err := postgres.FetchTypeApprovalListForFirstApprover(entity)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.UsersByType = mapper.FetchTypeApprovalListEntityToModel(fetchSingleApprover)

	} else if strings.EqualFold(entity.Type, "TypeOfHcpLocal") || strings.EqualFold(entity.Type, "Venue") || strings.EqualFold(entity.Type, "VirtualEvent") {
		fetchAllApprover, err := postgres.FetchTypeApprovalListForAllApprover(entity)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.UsersByType = mapper.FetchTypeApprovalListEntityToModel(fetchAllApprover)

	}
	return &response
}

func FetchAttachmentZip(ctx *context.Context, input *model.GetAttachmentZipInput) *model.GetAttachmentZipResponse {
	var response model.GetAttachmentZipResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	// country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	if input != nil {
		if input.Attachment != nil && len(input.Attachment) > 0 {
			response.URL, _ = CreateFolder(input.Attachment, userID, input.EventID)
		}
	}
	return &response
}

func CreateFolder(files []string, userAD *string, eventId string) (string, error) {
	dirName := "Attachment" + "-" + eventId + "-" + time.Now().Format("20060102150405")
	err := os.Mkdir(dirName, 0755)
	log.Println(err)

	for _, item := range files {
		valid, fileName := util.ValidateURL(item)
		if valid {
			file, downldErr := azure.DownloadFileFromBlobURL(item)
			if downldErr == nil {
				fileBytes, _ := ioutil.ReadAll(file)
				err = ioutil.WriteFile(dirName+"/"+fileName, fileBytes, 0644)
			} else {
				continue
			}
		}
	}

	dir, _ := os.Getwd()
	err = Zipit(dirName, dir+"/"+dirName+".zip")
	zipByte, _ := ioutil.ReadFile(dir + "/" + dirName + ".zip")
	blobURL, err := azure.UploadBytesToBlobZip(zipByte, dirName+".zip")
	errd := os.RemoveAll(dir + "/" + dirName)
	errf := os.RemoveAll(dir + "/" + dirName + ".zip")
	if err != nil || errd != nil || errf != nil {
		return "", err
	}
	return blobURL, nil
}

func Zipit(source, target string) error {
	zipfile, err := os.Create(target)
	if err != nil {
		return err
	}
	defer zipfile.Close()

	archive := zip.NewWriter(zipfile)
	defer archive.Close()

	info, err := os.Stat(source)
	if err != nil {
		return nil
	}

	var baseDir string
	if info.IsDir() {
		baseDir = filepath.Base(source)
	}

	filepath.Walk(source, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		if baseDir != "" {
			header.Name = filepath.Join(baseDir, strings.TrimPrefix(path, source))
		}

		if info.IsDir() {
			header.Name += "/"
		} else {
			header.Method = zip.Deflate
		}

		writer, err := archive.CreateHeader(header)
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		file, err := os.Open(path)
		if err != nil {
			return err
		}
		defer file.Close()
		_, err = io.Copy(writer, file)
		return err
	})
	return err
}
func InsertexceptiondetailsintoFormAnswer(ctx *context.Context, input model.ExceptionalDetailsInput) *model.ExceptionalDetailsResponse {
	var response model.ExceptionalDetailsResponse
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	exceptionalDetailsInputEntity, err1 := mapper.ExceptionalDetailsModelToEntity(input)
	if err1 != nil {
		response.Error = true
		response.Message = err1.Error()
		return &response
	}
	err := postgres.InsertexceptiondetailsData(exceptionalDetailsInputEntity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
	} else {
		response.Error = false
		response.Message = "Successfully inserted"
	}
	return &response
}

func ChangeRequestUpdate(ctx *context.Context, input model.ChangeRequestInput) *model.ChangeRequestOutput {
	var response model.ChangeRequestOutput
	var inputEntity entity.ChangeRequestInputEntity
	var hcpIndex int
	var roleInString string
	var answersid []string
	var index []int
	var hcps []string
	var err error
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	inputEntity, err, hcps = mapper.ChangeRequestModelToEntity(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	draftStatusId := code["draft"].ID
	code1 := codeController.GetIdKeyCodes()["approvedstatus"]
	draftStatusTitle := code1[draftStatusId].Title
	draftStatusValue := code1[draftStatusId].Value
	answersid, err = postgres.GetIdOfJsonbAnswersInFormAnswer(inputEntity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if (inputEntity.StartDate != "" && inputEntity.EndDate != "") || (inputEntity.NoOfHcp != "" && inputEntity.NoOfNonHCP != "") || inputEntity.Venue != "" || inputEntity.VirtualEvent != nil {
		for i, val := range answersid {
			if inputEntity.StartDate != "" && inputEntity.EndDate != "" {
				if val == "activity-start-date" || val == "activity-end-date" || val == "duration-of-the-activity" {
					index = append(index, i)
				}
			} else if inputEntity.NoOfHcp != "" && inputEntity.NoOfNonHCP != "" {
				if val == "no-of-hcps" || val == "no-of-non-hcps" {
					index = append(index, i)
				}
			} else if inputEntity.Venue != "" {
				if val == "venue" {
					index = append(index, i)
				}
			} else {
				if val == "virtual-event" {
					index = append(index, i)
				}
			}
		}
		err = postgres.UpdateActivityStartDateEndDateHcpNonHcpInFormAnswer(inputEntity, index, draftStatusId)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		} else {
			err = postgres.SoftDeleteApproversInApprovers(inputEntity)
			if err != nil {
				log.Println("err: ", err)
				response.Error = true
				response.Message = err.Error()
				return &response
			}
			if inputEntity.StartDate != "" && inputEntity.EndDate != "" {
				err = postgres.UpdateActivityStartDateEndDateStatusInReportingdb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "activity start date end date duration successfully updated"
				return &response
			}
			if inputEntity.NoOfHcp != "" && inputEntity.NoOfNonHCP != "" {
				err = postgres.UpdateStatusInReportingDb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "no of hcp and non hcp successfully updated"
				return &response
			}
			if inputEntity.Venue != "" {
				err = postgres.UpdateStatusInReportingDb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "venue successfully updated"
				return &response
			}
			if inputEntity.VirtualEvent != nil {
				err = postgres.UpdateStatusInReportingDb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "virtual event successfully updated"
				return &response
			}
		}
	} else if inputEntity.TypeOfHCP != "" {

		if strings.ToLower(inputEntity.TypeOfHCP) == "local" {
			for i, val := range hcps {
				if inputEntity.TypeOfHCP == val {
					hcpIndex = i
				}
			}
			for i, val := range inputEntity.Roles {
				dummystring := strings.ToLower(*val)
				if i != 0 {
					roleInString += "," + dummystring
				} else {
					roleInString += dummystring
				}
			}
			err = postgres.UpdateTypeOfHcpInFormAnswer(inputEntity, hcpIndex, roleInString, draftStatusId)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			} else {
				err = postgres.SoftDeleteApproversInApprovers(inputEntity)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				err = postgres.UpdateStatusInReportingDb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "hcp roles successfully updated"
				return &response
			}
		} else if strings.ToLower(inputEntity.TypeOfHCP) == "sponsored" {
			for i, val := range hcps {
				if inputEntity.TypeOfHCP == val {
					hcpIndex = i
				}
			}
			err = postgres.UpdateTypeOfHcpForSponsoredInFormAnswer(inputEntity, hcpIndex, draftStatusId)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			} else {
				err = postgres.SoftDeleteApproversInApprovers(inputEntity)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				err = postgres.UpdateStatusInReportingDb(inputEntity, draftStatusTitle, draftStatusValue)
				if err != nil {
					response.Error = true
					response.Message = err.Error()
					return &response
				}
				response.Error = false
				response.Message = "hcp roles successfully updated"
				return &response
			}
		}

	}
	// FOR APL MAPTOOL
	userCountry := auth.GetCountry(*ctx)
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(*ctx, nil, input.FormAnswerID)
	}
	//
	response.Error = false
	response.Message = ""
	return &response
}
func ChangeRequestUpdateForAllEvent(ctx context.Context, input model.RequestorActionInput) model.FormSubmissionApprovalForAllEventResponse {
	var response model.FormSubmissionApprovalForAllEventResponse
	userId := auth.GetUserID(ctx)
	// log.Println(userId)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	entity, err := mapper.ChangeRequestUpdateModelToEntity(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	err = postgres.ChangeRequestUpdateForAllEvent(entity, *userId, input.IsApollo)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.Error = false
	response.Message = "Successfully inserted"

	return response
}
func FetchAllProductDetails(ctx context.Context, input model.FetchAllProductRequest) *model.FetchAllProductResponse {
	var response model.FetchAllProductResponse
	userId := auth.GetUserID(ctx)
	countryId := auth.GetCountry(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	ProductResponse, err := postgres.FetchAllProductDetails(input, countryId)

	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.AllProductDetails = ProductResponse
	return &response
}
func GetAllEventIDListForController(ctx context.Context, input model.GetAllEventIDListRequest) model.GetAllEventIDListResponse {
	var response model.GetAllEventIDListResponse
	userId := auth.GetUserID(ctx)
	countryId := auth.GetCountry(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	alleventResponse, err := postgres.GetAllEventIDListForDB(input, *countryId, *userId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.GetAllEventIDListDetails = alleventResponse
	return response

}

func ValidationFormAnswerDate(ctx context.Context, input model.BasicInfoDateValidationRequest) model.BasicInfoDateValidationResponse {
	var isChangeRequest bool
	if input.IsChangeRequest != nil {
		isChangeRequest = *input.IsChangeRequest
	}

	allDetails, _ := postgres.GetFormAnswerAllDetails(input.ID)
	eventStatus := allDetails.Status.String
	todayDate, _ := time.Parse("2006-01-02", allDetails.TodayDate.String)
	createdDate, _ := time.Parse("2006-01-02", allDetails.CreatedDate.String)
	startDate, _ := time.Parse("2006-01-02", input.StartDate)
	durationMaxLength, _ := strconv.Atoi(os.Getenv("DURATION_MAX_LENGTH_LIMIT"))
	maxWorkingHours, _ := strconv.ParseFloat(os.Getenv("MAXIMUM_WORKING_HOURS"), 64)
	durationTodayTime := startDate.Sub(todayDate)
	daysApartTodayTime := durationTodayTime.Hours() / 24
	durationCreatedTime := startDate.Sub(createdDate)
	daysApartCreatedTime := durationCreatedTime.Hours() / 24

	activityStartDate, _ := strconv.Atoi(input.StartUnixTime)
	activityStartDateTime := time.Unix(int64(activityStartDate), 0)
	activityEndDate, _ := strconv.Atoi(input.EndUnixTime)
	activityEndDateTime := time.Unix(int64(activityEndDate), 0)
	durationHours := activityEndDateTime.Sub(activityStartDateTime).Hours()
	duration := (durationHours * maxWorkingHours) / 24

	if input.ActivityEvent == nil || strings.TrimSpace(*input.ActivityEvent) == "" {
		return model.BasicInfoDateValidationResponse{
			Error:   true,
			Message: "Activity Type can not be blank",
		}
	}
	activityType := strings.TrimSpace(*input.ActivityEvent)

	if activityType == "ZP - Hosted Event" && (input.EventType == nil || strings.TrimSpace(*input.EventType) == "") {
		return model.BasicInfoDateValidationResponse{
			Error:   true,
			Message: "Event Type can not be blank for ZP - Hosted Event",
		}
	}
	eventType := strings.TrimSpace(*input.EventType)

	durationHoursRounded := math.Ceil(duration*100) / 100
	if len(strconv.Itoa(int(durationHoursRounded))) > durationMaxLength {
		return model.BasicInfoDateValidationResponse{
			Error:   true,
			Message: "Duration can not be more than " + strconv.Itoa(durationMaxLength) + " digits",
		}
	}

	virtualEventDetailsMaxLength, _ := strconv.Atoi(os.Getenv("VIRTUAL_EVENT_DETAILS_MAX_LIMIT"))
	if len(input.VirtualEventDetails) > virtualEventDetailsMaxLength {
		return model.BasicInfoDateValidationResponse{
			Error:   true,
			Message: "Virtual event details can not be more than " + strconv.Itoa(virtualEventDetailsMaxLength) + " characters",
		}
	}

	if strings.TrimSpace(*input.ActivityEvent) == "Sponsorship to Healthcare Organizations/Institutions" && len(input.BoardOfDirectoryValidation) == 0 {
		return model.BasicInfoDateValidationResponse{
			Error:   true,
			Message: constants.BoardOfDirectoryValidationMessage,
		}
	}

	userCountryID := func() int {
		i := auth.GetCountry(ctx)
		return *i
	}()

	// New Event
	if input.ID == nil || strings.TrimSpace(*input.ID) == "" {
		var delta int

		if activityType == "Sponsorship to individual HCPs - To Local Event" ||
			activityType == "Sponsorship to Individual HCPs – To Overseas Event" ||
			activityType == "Educational Grants to HCO" {

			delta = 7
		} else if activityType == "Sponsorship to individual HCPs - Within Local Country" ||
			activityType == "Sponsorship to Healthcare Organizations/Institutions" ||
			activityType == "Sponsorship to individual HCPs - Within ZP Location" ||
			activityType == "Sponsorship to individual HCPs - Outside ZP Location" {

			delta = 14
		} else if activityType == "ZP - Hosted Event" && (eventType == "Face-to-face detailing" || eventType == "Product Presentation") {
			delta = 4
		} else if activityType == "ZP - Hosted Event" {
			delta = 7
		} else {
			return model.BasicInfoDateValidationResponse{
				Error:   true,
				Message: `Invalid activity type - event type`,
			}
		}

		// if country = PH
		if userCountryID == 400 && delta == 14 {
			delta = 7
		}

		if int(daysApartTodayTime) < delta {
			return model.BasicInfoDateValidationResponse{
				Error:   true,
				Message: fmt.Sprintf("Please select date after %d days from today.", delta),
			}
		}
	}

	// Existed Event
	if input.ID != nil && strings.TrimSpace(*input.ID) != "" {
		if eventStatus == "return" || eventStatus == "recalled" {
			if isChangeRequest {
				if int(daysApartCreatedTime) < 0 {
					return model.BasicInfoDateValidationResponse{
						Error:   true,
						Message: "You can't select date before created date of this event.",
					}
				}
			} else {
				if int(daysApartTodayTime) < 0 {
					return model.BasicInfoDateValidationResponse{
						Error:   true,
						Message: "You can't select date before today.",
					}
				}
			}
		} else {
			var delta int
			if activityType == "Sponsorship to individual HCPs - To Local Event" || activityType == "Sponsorship to Individual HCPs – To Overseas Event" || activityType == "Educational Grants to HCO" {
				delta = 7
			} else if activityType == "Sponsorship to individual HCPs - Within Local Country" ||
				activityType == "Sponsorship to Healthcare Organizations/Institutions" ||
				activityType == "Sponsorship to individual HCPs - Within ZP Location" ||
				activityType == "Sponsorship to individual HCPs - Outside ZP Location" {
				delta = 14

			} else if activityType == "ZP - Hosted Event" {
				delta = 7
				if eventType == "Face-to-face detailing" || eventType == "Product Presentation" {
					delta = 4
				}
			}

			// if country = PH
			if userCountryID == 400 && delta == 14 {
				delta = 7
			}

			if isChangeRequest {
				if int(daysApartCreatedTime) < delta {
					return model.BasicInfoDateValidationResponse{
						Error:   true,
						Message: fmt.Sprintf("Please select date after %d days.", delta),
					}
				}

			} else {
				if int(daysApartTodayTime) < delta {
					return model.BasicInfoDateValidationResponse{
						Error:   true,
						Message: fmt.Sprintf("Please select date after %d days from today.", delta),
					}
				}
			}
		}
	}

	return model.BasicInfoDateValidationResponse{
		Error: false,
	}
}

func GetHcpLoiController(ctx context.Context, input model.HcpLoiRequest) model.HcpLoiResponse {
	var response model.HcpLoiResponse
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.LowlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.MediumlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNa {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerLocalRegions {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerNo && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerHospital {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	if strings.TrimSpace(strings.ToLower(input.Answer1)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer2)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer3)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer4)) == constants.LoiAnswerYes && strings.TrimSpace(strings.ToLower(input.Answer5)) == constants.LoiAnswerNational {
		response.LevelOfInfluence = constants.HighlevelOfInfluence
		return response
	}
	response.Error = true
	response.Message = "Something error !!"
	return response
}
func GetOwnerInfo(ctx *context.Context, input model.OwnerDataRequest) model.OwnerDataResponse {
	var response model.OwnerDataResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return response
	}
	if input.UserID == "" {
		response.Error = true
		response.Message = "Please provide user Id!!"
		return response
	}
	onwerAd, ownerName, err := postgres.GetOwnerInfo(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.Error = false
	response.Message = ""
	response.OwnerFullName = ownerName
	response.OwnerActiveDirectory = onwerAd
	return response
}
func GetFmvAuditlogsController(ctx *context.Context, input model.GetFmvAuditlogsRequest) *model.GetFmvAuditlogsResponse {
	var response model.GetFmvAuditlogsResponse
	userId := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userId == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	superadmin := postgres.GetRoleIDByValueInUserRoles(os.Getenv("SUPERADMIN"))
	admin := postgres.GetRoleIDByValueInUserRoles(os.Getenv("ADMIN"))
	clusterComplianceOfficer := postgres.GetRoleIDByValueInUserRoles(os.Getenv("CCO"))
	support := postgres.GetRoleIDByValueInUserRoles("support")

	if *approvalRole == admin || *approvalRole == clusterComplianceOfficer || *approvalRole == superadmin || *approvalRole == support {
		response.Error = false
	} else {
		response.Error = true
		response.Message = "Only admin and cluster compliance officer can take action !!"
		return &response
	}
	res, count, err := postgres.GetFmvAuditlogsDB(userCountry, input)
	if err != nil {
		return &response
	}
	result := mapper.GetFmvAuditlogsEntityToModel(res)
	response.GetFmvAuditlogsData = result
	response.DataCount = int(count)
	return &response
}
func GetlevelOfInfluenceValidationController(ctx *context.Context, input model.LevelOfInfluenceValidationRequest) *model.LevelOfInfluenceValidationResponse {
	var response model.LevelOfInfluenceValidationResponse
	if strings.TrimSpace(input.LevelOfInfluence) == "" || (strings.ToLower(strings.TrimSpace(input.LevelOfInfluence)) != "high" && strings.ToLower(strings.TrimSpace(input.LevelOfInfluence)) != "low" && strings.ToLower(strings.TrimSpace(input.LevelOfInfluence)) != "medium") {
		response.Error = true
		response.Message = constants.LevelOfInfluenceValidationMessage
		return &response
	}
	if strings.ToLower(strings.TrimSpace(input.LevelOfInfluence)) == "high" && strings.TrimSpace(input.LoiquestionAnswer) == "" {
		response.Error = true
		response.Message = constants.LoiquestionAnswerValidationMessage
		return &response
	}
	return &response
}

func FormAnswerAttachmentSyncController(ctx *context.Context, input model.FormAnswerAttachmentForEzClaim) *model.FormAnswerAttachmentResponseForEzClaim {
	var formAnswerAttachmentResponse model.FormAnswerAttachmentResponseForEzClaim
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		formAnswerAttachmentResponse.Error = true
		formAnswerAttachmentResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &formAnswerAttachmentResponse
	}
	ownerEmail, _, _ := postgres.GetOwnersEmailID(input.FormAnswerID)
	getMeetingCode, getMeetingSeq, _ := postgres.GetEventId(input.FormAnswerID)
	getMeetingId := util.ConvertToEventID(getMeetingCode, getMeetingSeq)
	formAnswerAttachmentResponse, attachmentsData := mapper.FormAnswerAttachmentModelToEntityForEzClaim(input, getMeetingId)
	emailfrom := ownerEmail
	emailBody := getMeetingId
	toMailAddress := os.Getenv("EZCLAIM_EMAIL_ADDRESS")
	if formAnswerAttachmentResponse.Error {
		return &formAnswerAttachmentResponse
	} else {
		go postgres.SendAttachmentMaileZClaim(attachmentsData, emailfrom, toMailAddress, emailfrom, emailBody, getMeetingId, input.FormAnswerID)
		return &formAnswerAttachmentResponse
	}
}
func FetchFormAnswerForApollo(ctx context.Context, inputModel model.FormAnswerInputForApollo) *model.FormAnswerResponseForApollo {
	var response model.FormAnswerResponseForApollo
	userId := auth.GetUserID(ctx)
	userCountry := auth.GetCountry(ctx)
	var loggedInId uuid.UUID
	var e *entity.ExceptionDetailsEntity
	var expectionalStatusTitle string
	var expectionalStatusValue string
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	} else {
		loggedInId, err = uuid.FromString(*userId)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
	}
	approvalRole := auth.GetApprovalRole(ctx)
	entity, err := mapper.FormAnswerFetchModelToEntityForApollo(&inputModel, approvalRole, &loggedInId, *userCountry)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	requesterCountry := postgres.FetchRequesterCountry(inputModel.FormID)
	requesterCurrency, boardOfDirectors, ConversionRate, ownerAd, requestorName := postgres.FetchRequesterCurrency(inputModel.FormID)
	e, _ = postgres.FetchExceptionalDetails(inputModel.FormID)
	if e == nil {
		return &model.FormAnswerResponseForApollo{
			Error:   true,
			Message: `formAnswerId not found`,
		}

	}
	formAnswer, errString := postgres.FetchFormAnswers(&loggedInId, entity, approvalRole)
	if errString != "" {
		response.Error = true
		response.Message = errString
		return &response
	}
	code := codeController.GetValueKeyCodes()["country"]
	rate, countryID, err := postgres.ConvertedCurrencyValueForApollo(378, code[requesterCountry].ID, "local")
	if err != nil {
		log.Println(err)
	}
	data := mapper.FormAnswerDesignEntityToModelForApollo(formAnswer, requesterCountry, countryID, rate)
	attachments, boardOfDirectorsDataRes := mapper.FormAnswerAttachmentEntityToModel(formAnswer, boardOfDirectors)
	createdDate, statusTitle, statusValue, err := postgres.GetDateCreatedByFormAnswerId(entity.FormId)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	dateCreated := util.GetTimeUnixTimeStamp(createdDate.Time)
	meetingID := util.ConvertToEventID(e.EventCode, e.EventSeq)
	response.DateCreated = &dateCreated
	response.Answers = data
	response.Attachments = attachments
	response.RequestorCountry = &requesterCountry
	response.RequestorCurrency = &requesterCurrency
	response.IsExceptionalApproval = *e.IsExceptionalApproval
	response.DetailsOfRequest = e.DetailsOfRequest
	response.IhcpOtherLocalPolicies = e.IhcpOtherLocalPolicies
	response.MoreComments = e.MoreComments
	response.ExceptionalApprovalFileUpload = e.ExceptionalApprovalFileUpload
	response.ScopeOfExceptionalRequest = e.ScopeOfExceptionalRequest
	response.IsChangeRequest = e.IsChangeRequest
	response.ChangeRequestSummary = e.ChangeRequestSummary
	response.ChangeRequestType = e.ChangeRequestType
	response.MeetingID = &meetingID
	response.IsRecall = e.IsRecall
	response.RecallSummary = e.RecallSummary
	response.BoardOfDirectory = boardOfDirectorsDataRes
	response.ConversionRate = &ConversionRate
	response.OwnerActiveDirectory = &ownerAd
	response.RequestorName = &requestorName
	if e.IsExceptionalApproval != nil && *e.IsExceptionalApproval {
		expectionalStatusTitle = "Exceptional " + statusTitle.String
		expectionalStatusValue = "exceptional " + statusValue.String
		response.EventStatusTitle = &expectionalStatusTitle
		response.EventStatusValue = &expectionalStatusValue
	} else {
		response.EventStatusTitle = &statusTitle.String
		response.EventStatusValue = &statusValue.String
	}

	return &response
}
func FetchApolloApprovalTrailDataForAnActivity(ctx context.Context, inputModel model.ApolloFormAnswerIDInput) *model.ApolloApprovalTrailFormSubmissionResponse {
	var response model.ApolloApprovalTrailFormSubmissionResponse
	userId := auth.GetUserID(ctx)
	var err error
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	entity, err := mapper.FetchApolloApprovalRoleModelToEntity(&inputModel)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	var data []*model.ApolloApprovalData
	approvalTrailDetails, err := postgres.GetApolloApprovalTrailDataForANActivity(entity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	data = mapper.FetchApolloApprovalTrailValuesEntityToModel(approvalTrailDetails, entity)
	response.Approval = data
	return &response
}
func GetCalculatedExpenseController(ctx *context.Context, input model.CalculateExpenseRequest) *model.CalculateExpenseResponse {
	var response model.CalculateExpenseResponse
	var CalculateExpenseData model.CalculateExpenseData
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		return &response
	}
	if input.NoOfUnits < 0 {
		response.Error = true
		response.Message = "No Of Units" + constants.NegativeValueValidation
		return &response
	}
	if input.CostPerUnit < 0 {
		response.Error = true
		response.Message = "Cost Per Unit" + constants.NegativeValueValidation
		return &response
	}
	val := float64(float64(input.NoOfUnits) * input.CostPerUnit)
	usdvalue, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, float64(val))
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	usdvalueCostPerUnits, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, input.CostPerUnit)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	usdvalueroundedValue := math.Round(usdvalue*100) / 100
	valRoundedValue := math.Round(val*100) / 100
	usdvalueCostPerUnitsRoundedValue := math.Round(usdvalueCostPerUnits*100) / 100
	valueinUsd := fmt.Sprintf("%0.2f", usdvalueroundedValue)
	valueinstring := fmt.Sprintf("%0.2f", valRoundedValue)
	CostPerUnitInUsdValue := fmt.Sprintf("%0.2f", usdvalueCostPerUnitsRoundedValue)
	CalculateExpenseData.TotalCostInUsd = valueinUsd
	CalculateExpenseData.TotalCostInLocalCurrency = valueinstring
	CalculateExpenseData.CostPerUnitInUsd = CostPerUnitInUsdValue
	response.CalculateExpenseData = &CalculateExpenseData
	return &response
}
func GetTotalHcpExpenseController(ctx *context.Context, input model.TotalHcpExpenseRequest) *model.TotalHcpExpenseResponse {
	var response model.TotalHcpExpenseResponse
	var CalculateHcpExpenseData model.CalculateHcpExpenseData
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	var totalcost float64
	if input.TotalHcpExpense != nil && len(input.TotalHcpExpense) != 0 {
		for _, val := range input.TotalHcpExpense {
			if val != nil {
				if val.AccomodationTotalCost < 0 {
					response.Error = true
					response.Message = "Accomodation Totalcost" + constants.NegativeValueValidation
					response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
						TotalHcpExpenseInLocalCurrency: "0",
						TotalHcpExpenseInUsd:           "0",
					}
					return &response
				}
				if val.AirfareTotalcost < 0 {
					response.Error = true
					response.Message = "Airfare Totalcost" + constants.NegativeValueValidation
					response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
						TotalHcpExpenseInLocalCurrency: "0",
						TotalHcpExpenseInUsd:           "0",
					}
					return &response
				}
				if val.GroundTransportationTotalcost < 0 {
					response.Error = true
					response.Message = "Ground Transportation Totalcost" + constants.NegativeValueValidation
					response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
						TotalHcpExpenseInLocalCurrency: "0",
						TotalHcpExpenseInUsd:           "0",
					}
					return &response
				}
				if val.PoposedHonorarium < 0 {
					response.Error = true
					response.Message = "Poposed Honorarium" + constants.NegativeValueValidation
					response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
						TotalHcpExpenseInLocalCurrency: "0",
						TotalHcpExpenseInUsd:           "0",
					}
					return &response
				}
				if val.RegistrationFeeTotalcost < 0 {
					response.Error = true
					response.Message = "Registration Fee Totalcost" + constants.NegativeValueValidation
					response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
						TotalHcpExpenseInLocalCurrency: "0",
						TotalHcpExpenseInUsd:           "0",
					}
					return &response
				}
				totalcost += val.AccomodationTotalCost + val.AirfareTotalcost + val.GroundTransportationTotalcost + val.PoposedHonorarium + val.RegistrationFeeTotalcost
				if len(val.MealTotalcost) != 0 {
					for _, Mealcost := range val.MealTotalcost {
						if Mealcost != nil && *Mealcost >= 0 {
							totalcost += *Mealcost
						} else {
							response.Error = true
							response.Message = "Meal Totalcost" + constants.NegativeValueValidation
							response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
								TotalHcpExpenseInLocalCurrency: "0",
								TotalHcpExpenseInUsd:           "0",
							}
							return &response
						}
					}
				}
				if len(val.OthersTotalCost) != 0 {
					for _, othercost := range val.OthersTotalCost {
						if othercost != nil && *othercost >= 0 {
							totalcost += *othercost
						} else {
							response.Error = true
							response.Message = "Others TotalCost" + constants.NegativeValueValidation
							response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
								TotalHcpExpenseInLocalCurrency: "0",
								TotalHcpExpenseInUsd:           "0",
							}
							return &response
						}
					}
				}
			}
		}
	}
	usdvalueTotalCost, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, totalcost)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	totalcostroundedValue := math.Round(totalcost*100) / 100
	usdvalueTotalCostRoundedValue := math.Round(usdvalueTotalCost*100) / 100
	totalCostValueinstring := fmt.Sprintf("%0.2f", totalcostroundedValue)
	HcpExpenseTotalCostValueInUsd := fmt.Sprintf("%0.2f", usdvalueTotalCostRoundedValue)
	CalculateHcpExpenseData.TotalHcpExpenseInLocalCurrency = totalCostValueinstring
	CalculateHcpExpenseData.TotalHcpExpenseInUsd = HcpExpenseTotalCostValueInUsd
	response.CalculateHcpExpenseData = &CalculateHcpExpenseData
	return &response
}
func GetTotalEventExpenseController(ctx *context.Context, input model.TotalEventExpenseRequest) *model.TotalEventExpenseResponse {
	var response model.TotalEventExpenseResponse
	var CalculateEventExpenseData model.CalculateEventExpenseData
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}
	var totalcost float64
	if input.TotalEventExpense != nil && len(input.TotalEventExpense) != 0 {
		for _, val := range input.TotalEventExpense {
			if val.TotalCost != nil && len(val.TotalCost) != 0 {
				for _, v := range val.TotalCost {
					if v != nil && *v >= 0 {
						totalcost += *v
					} else {
						response.Error = true
						response.Message = "Value" + constants.NegativeValueValidation
						response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
							TotalEventExpenseInLocalCurrency: "0",
							TotalEventExpenseInUsd:           "0",
						}
						return &response
					}
				}
			}
		}
	}
	usdvalueTotalCost, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, totalcost)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}

	totalcostroundedValue := math.Round(totalcost*100) / 100
	usdvalueTotalCostRoundedValue := math.Round(usdvalueTotalCost*100) / 100
	totalCostValueinstring := fmt.Sprintf("%0.2f", totalcostroundedValue)
	EventExpenseTotalCostValueInUsd := fmt.Sprintf("%0.2f", usdvalueTotalCostRoundedValue)
	CalculateEventExpenseData.TotalEventExpenseInLocalCurrency = totalCostValueinstring
	CalculateEventExpenseData.TotalEventExpenseInUsd = EventExpenseTotalCostValueInUsd
	response.CalculateEventExpenseData = &CalculateEventExpenseData
	return &response
}
func GetCalculateTotalEventExpenseController(ctx *context.Context, input model.CalculatetotalEventExpenseRequest) *model.TotalEventExpenseResponse {
	var response model.TotalEventExpenseResponse
	var CalculateEventExpenseData model.CalculateEventExpenseData
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}
	if input.FormID == "" {
		response.Error = true
		response.Message = "Please provide Valid formID"
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}

	var totalcost float64
	hcpexpenseDataArr, eventCountry, err := postgres.CalculateTotalEventExpenseFromEventID(input.FormID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}
	if userCountry != nil && eventCountry != *userCountry {
		response.Error = true
		response.Message = "This event does not belong to loggedin user"
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}
	for _, val := range hcpexpenseDataArr {
		value, ok := FloatChecking(val)
		if ok {
			totalcost += value
		}
	}
	usdvalueTotalCost, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, totalcost)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateEventExpenseData = &model.CalculateEventExpenseData{
			TotalEventExpenseInLocalCurrency: "0",
			TotalEventExpenseInUsd:           "0",
		}
		return &response
	}

	totalcostroundedValue := math.Round(totalcost*100) / 100
	usdvalueTotalCostRoundedValue := math.Round(usdvalueTotalCost*100) / 100
	totalCostValueinstring := fmt.Sprintf("%0.2f", totalcostroundedValue)
	EventExpenseTotalCostValueInUsd := fmt.Sprintf("%0.2f", usdvalueTotalCostRoundedValue)
	CalculateEventExpenseData.TotalEventExpenseInLocalCurrency = totalCostValueinstring
	CalculateEventExpenseData.TotalEventExpenseInUsd = EventExpenseTotalCostValueInUsd
	response.CalculateEventExpenseData = &CalculateEventExpenseData
	return &response
}
func GetCalculateTotalHcpExpenseController(ctx *context.Context, input model.CalculateTotalHcpExpenseRequest) *model.TotalHcpExpenseResponse {
	var response model.TotalHcpExpenseResponse
	var CalculateHcpExpenseData model.CalculateHcpExpenseData
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	if input.FormID == "" {
		response.Error = true
		response.Message = "Please provide Valid formID "
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	var totalcost float64
	hcpexpenseDataArr, eventCountry, err := postgres.CalculateTotalHCPExpenseFromEventID(input.FormID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	if userCountry != nil && eventCountry != *userCountry {
		response.Error = true
		response.Message = "This event does not belong to loggedin user"
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	for _, val := range hcpexpenseDataArr {
		value, ok := FloatChecking(val)
		if ok {
			totalcost += value
		}
	}

	usdvalueTotalCost, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, totalcost)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		response.CalculateHcpExpenseData = &model.CalculateHcpExpenseData{
			TotalHcpExpenseInLocalCurrency: "0",
			TotalHcpExpenseInUsd:           "0",
		}
		return &response
	}
	totalcostroundedValue := math.Round(totalcost*100) / 100
	usdvalueTotalCostRoundedValue := math.Round(usdvalueTotalCost*100) / 100
	totalCostValueinstring := fmt.Sprintf("%0.2f", totalcostroundedValue)
	HcpExpenseTotalCostValueInUsd := fmt.Sprintf("%0.2f", usdvalueTotalCostRoundedValue)
	CalculateHcpExpenseData.TotalHcpExpenseInLocalCurrency = totalCostValueinstring
	CalculateHcpExpenseData.TotalHcpExpenseInUsd = HcpExpenseTotalCostValueInUsd
	response.CalculateHcpExpenseData = &CalculateHcpExpenseData
	return &response
}
func FloatChecking(s string) (float64, bool) {
	val, err := strconv.ParseFloat(s, 64)
	return val, err == nil
}
func GetDurationFormatController(ctx *context.Context, input model.GetDurationFormatRequest) *model.GetDurationFormatResponse {
	var response model.GetDurationFormatResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		return &response
	}
	if input.StartDate > input.EndDate {
		response.Error = true
		response.Message = constants.EventEndDateMustBeAfterStartDate
		return &response
	}
	if input.StartDate == 0 || input.EndDate == 0 {
		response.Error = true
		response.Message = constants.StartEndDateIsOmitted
		return &response
	}
	// Convert Unix timestamps to time.Time objects
	start := time.Unix(int64(input.StartDate), 0)
	end := time.Unix(int64(input.EndDate), 0)

	// Calculate the duration between the two times
	duration := end.Sub(start)

	// Calculate years, months, days, hours, and minutes
	years := duration.Hours() / (24 * 365)
	months := int((years - float64(int(years))) * 12)
	days := int(duration.Hours()/24) % 365 % 30
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60
	var formatValue string
	var flag bool
	if int(years) != 0 {
		var value string
		if int(years) <= 1 {
			value = fmt.Sprintf("%d year", int(years))
		} else {
			value = fmt.Sprintf("%d years", int(years))
		}
		formatValue += value
		flag = true
	}
	if months != 0 || flag {
		var value string
		if months <= 1 {
			value = fmt.Sprintf(" %d month", int(months))
		} else {
			value = fmt.Sprintf(" %d months", int(months))
		}
		formatValue += value
		flag = true
	}
	var value string
	if days <= 1 {
		value = fmt.Sprintf(" %d day", int(days))
	} else {
		value = fmt.Sprintf(" %d days", int(days))
	}
	formatValue += value
	if hours <= 1 {
		value = fmt.Sprintf(" %d hour", int(hours))
	} else {
		value = fmt.Sprintf(" %d hours", int(hours))
	}
	formatValue += value

	if minutes <= 1 {
		value = fmt.Sprintf(" %d minute", int(minutes))
	} else {
		value = fmt.Sprintf(" %d minutes", int(minutes))
	}
	formatValue += value

	response.DurationFormat = strings.TrimSpace(formatValue)
	return &response
}
func GetconversionAmountController(ctx *context.Context, input model.ConversionAmountRequest) *model.ConversionAmountResponse {
	var response model.ConversionAmountResponse
	userID := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		return &response
	}
	if input.ProposedPayableHonorariumAmount < 0 {
		response.Error = true
		response.Message = "Value" + constants.NegativeValueValidation
		return &response
	}
	usdvalueProposedPayableHonorarium, err := postgres.ConvertedCurrencyValueForExpense(378, userCountry, input.ProposedPayableHonorariumAmount)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	ProposedPayableHonorariumAmountroundedValue := math.Round(input.ProposedPayableHonorariumAmount*100) / 100
	usdvalueProposedPayableHonorariumroundedValue := math.Round(usdvalueProposedPayableHonorarium*100) / 100
	ProposedPayableHonorariumValueinstring := fmt.Sprintf("%0.2f", usdvalueProposedPayableHonorariumroundedValue)
	ProposedPayableHonorariumAmount := fmt.Sprint(ProposedPayableHonorariumAmountroundedValue)
	response.ProposedPayableHonorariumAmountInLocalCurrency = ProposedPayableHonorariumAmount
	response.ProposedPayableHonorariumAmountInUsd = ProposedPayableHonorariumValueinstring
	return &response
}
func GetCurrencyConversionController(ctx *context.Context, input model.USDValueRequest) *model.USDValueResponse {
	var response model.USDValueResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = constants.Unauthorized
		return &response
	}
	if input.ExpenseAmount < 0 {
		response.Error = true
		response.Message = "Value" + constants.NegativeValueValidation
		return &response
	}
	if input.Currency == "" {
		response.Error = true
		response.Message = constants.Pleaseprovidecurrencyvalue
		return &response
	}
	userCountry, err := postgres.GetUserCountryFromCurrencyValue(strings.TrimSpace(strings.ToLower(input.Currency)))
	if err != nil {
		response.Error = true
		response.Message = constants.CorrectCurrency
		return &response
	}
	expenseAmountUsd, err := postgres.GetUSDvalueFromCurrencyValue(133, userCountry, input.ExpenseAmount)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	expenseAmountUsdroundedValue := math.Round(expenseAmountUsd*100) / 100
	expenseAmountUsdroundedValueinstring := fmt.Sprintf("%0.2f", expenseAmountUsdroundedValue)
	response.ExpenseAmountUsd = expenseAmountUsdroundedValueinstring
	return &response
}
func HcpEngagementValidationController(ctx *context.Context, input model.HcpEngagementValidationRequest) *model.HcpEngagementValidationResponse {
	var response model.HcpEngagementValidationResponse
	userID := auth.GetUserID(*ctx)
	country := auth.GetCountry(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	uniqueSpeakerCheck := make(map[entity.SpeakerDuplicateValidationCheck]bool)
	var speakerArrForDuplicate []entity.SpeakerDuplicateValidationCheck
	// var ErrorMessageHcpEngagement string
	if input.Hcps != nil && len(input.Hcps) > 0 {
		for _, val := range input.Hcps {
			var HcpEngagementValidationentity model.HcpEngagementValidationData
			HcpEngagementValidationentity.Valid = true
			var veevaId string
			HcpEngagementValidationentity.Name = val.Name
			HcpEngagementValidationentity.VeevaID = val.VeevaID
			HcpEngagementValidationentity.Organization = val.Organization
			HcpEngagementValidationentity.Specialty = val.Specialty
			if len(val.VeevaID) == 18 {
				veevaId = val.VeevaID[:len(val.VeevaID)-3]
			} else if len(val.VeevaID) == 15 {
				veevaId = val.VeevaID
			} else {
				response.Error = true
				HcpEngagementValidationentity.Valid = false
				ErrorMessage := "veeva_reference_id 0015h00001Gla is incorrect. Specify an veeva_reference_id that is 15 or 18 characters long"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessage
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}
			spekaerArray, err := postgres.CheckSpeakernameArrayForApollo(veevaId, val.Name, *country, val.Specialty, val.Organization)
			if err != nil {
				response.Error = true
				HcpEngagementValidationentity.Valid = false
				ErrorMessage := "Invalid Customer Details"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessage
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}
			var ExistspeakerName, ExistSpecilty, cityChecking, veevaIDChecking bool
			for _, value := range spekaerArray {
				if value.QueryValue == 1 {
					HcpEngagementValidationentity.Name = val.Name
					HcpEngagementValidationentity.ID = value.SpeakerId
					HcpEngagementValidationentity.VeevaID = val.VeevaID
					HcpEngagementValidationentity.Organization = val.Organization
					HcpEngagementValidationentity.Specialty = val.Specialty
				} else if value.QueryValue == 4 {
					ExistspeakerName = true
				} else if value.QueryValue == 3 {
					ExistSpecilty = true
				} else if value.QueryValue == 2 {
					cityChecking = true
				} else if value.QueryValue == 5 {
					veevaIDChecking = true
				}
			}
			if !ExistspeakerName {
				HcpEngagementValidationentity.Valid = false
				ErrorMessageHcpEngagement := "SpeakerName("
				ErrorMessageHcpEngagement += val.Name
				ErrorMessageHcpEngagement += ") doesn't exist in this country!!"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessageHcpEngagement
				response.Error = true
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}

			if !veevaIDChecking {
				HcpEngagementValidationentity.Valid = false
				ErrorMessageHcpEngagement := "VeevaID("
				ErrorMessageHcpEngagement += val.VeevaID
				ErrorMessageHcpEngagement += ") doesn't match with respect to speaker(" + val.Name + ")!!"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessageHcpEngagement
				response.Error = true
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}

			// ExistOrganization := postgres.CheckSpecialty(Speakername, country, Specialty)
			if !ExistSpecilty {
				HcpEngagementValidationentity.Valid = false
				ErrorMessageHcpEngagement := "Specialty("
				ErrorMessageHcpEngagement += val.Specialty
				ErrorMessageHcpEngagement += ") doesn't match with respect to speaker(" + val.Name + ")!!"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessageHcpEngagement
				response.Error = true
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}
			// cityChecking := postgres.CheckSpeakerCity(Speakername, country, Hcoinstitutename)
			if !cityChecking {
				HcpEngagementValidationentity.Valid = false
				ErrorMessageHcpEngagement := "Organization("
				ErrorMessageHcpEngagement += val.Organization
				ErrorMessageHcpEngagement += ") doesn't match with respect to speaker(" + val.Name + ")!!"
				HcpEngagementValidationentity.ErrorMessage = ErrorMessageHcpEngagement
				response.Error = true
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}
			speakerValidationRes := entity.SpeakerDuplicateValidationCheck{
				SpeakerName:      val.Name,
				SpeakerCity:      val.Organization,
				SpeakerSpecialty: val.Specialty,
			}
			if _, ok := uniqueSpeakerCheck[speakerValidationRes]; !ok {
				uniqueSpeakerCheck[speakerValidationRes] = true
			} else {
				speakerArrForDuplicate = append(speakerArrForDuplicate, speakerValidationRes)
			}
			if speakerArrForDuplicate != nil {
				response.Error = true
				ErrorMessageHcpEngagement := "Duplicate speakerName("
				for i, SpeakName := range speakerArrForDuplicate {
					ErrorMessageHcpEngagement += SpeakName.SpeakerName
					if i != (len(speakerArrForDuplicate) - 1) {
						ErrorMessageHcpEngagement += " , "
					}
				}
				ErrorMessageHcpEngagement += ") exist!!"
				response.Message = ErrorMessageHcpEngagement
				response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
				continue
			}
			response.HcpEngagementValidationData = append(response.HcpEngagementValidationData, &HcpEngagementValidationentity)
		}
	} else {
		response.Error = true
		response.Message = "Please provide hcp list"
		return &response
	}

	return &response
}

var ValidChangeApprovalTypeList []string = []string{`major`, `minor`}

func SetChangeApproval(ctx context.Context, input *model.ChangeApprovalRequest) (*model.CommonResponse, error) {
	if err := checkUserAuthorization(ctx); err != nil {
		e := err.Error()
		return &model.CommonResponse{
			Success: false,
			Message: &e,
		}, nil
	}

	userID := auth.GetUserID(ctx)
	//userRoleID := auth.GetApprovalRole(ctx)
	userCountry := auth.GetCountry(ctx)

	uuid, err := uuid.FromString(input.FormAnswerID)
	if err != nil {
		e := `invalid formAnswerId`
		return &model.CommonResponse{
			Success: false,
			Message: &e,
		}, nil
	}

	if r := slices.Contains(ValidChangeApprovalTypeList, strings.ToLower(input.ChangeApprovalType)); !r {
		e := `invalid change approval type`
		return &model.CommonResponse{
			Success: false,
			Message: &e,
		}, nil
	}

	if !postgres.IsFormIDExists(&uuid) {
		e := `formAnswerId does not exist`
		return &model.CommonResponse{
			Success: false,
			Message: &e,
		}, nil
	}

	tx, err := postgres.GetPool().Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	postgres.UpdateEventChangeRequest(ctx, tx, input.FormAnswerID, input.ChangeApprovalType, *userID)

	if err := tx.Commit(ctx); err != nil {
		panic(err)
	}

	// FOR APL MAPTOOL
	if *userCountry == 11 { // Indonesia
		SendEventToMapTool(ctx, nil, input.FormAnswerID)
	}
	//

	return &model.CommonResponse{
		Success: true,
	}, nil
}
