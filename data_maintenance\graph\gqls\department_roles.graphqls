input DepartmentRolesInput {
    id: String
    isDelete: Boolean
    department:  String
    userRole: [String]
}

input DepartmentRoleRequest{
    department: String
    userRole: String
}

type userRolesValue {
    id: String!
    value: String!
    title: String!
    description: String!
}

type DepartmentRole {
    id: String!
    department: String!
    userRole: [userRolesValue]!
}

type DepartmentRoleResponse{
    error: Boolean!
    message: String!
    data: [DepartmentRole]!
}

extend type Query {
    getDepartmentRole(input: DepartmentRoleRequest): DepartmentRoleResponse!
}

extend type Mutation {
    upsertDepartmentRoles(input: DepartmentRolesInput!): upsertResponse!
}