package controller

import (
	"context"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/azure"
	"time"

	wkhtml "github.com/Sebastiaan<PERSON><PERSON><PERSON>/go-wkhtmltopdf"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func GetDownloadSponsorshipData(ctx context.Context, input *model.GetSponsorshipAgreementPDFBlobURLRequest) (*mapper.EventSponsorshipAgreementInformation, error) {
	userID := auth.GetUserID(ctx)
	userRoleID := auth.GetApprovalRole(ctx)
	userUUID, err := uuid.FromString(*userID)
	if err != nil {
		panic(err)
	}

	formAnswerID := input.FormID
	formAnswerUUID, err := uuid.FromString(formAnswerID)
	if err != nil {
		return nil, errors.New("invalid form answer id")
	}

	formAnswer, errStr := postgres.FetchFormAnswers(&userUUID, &entity.FormAnswerFetchEntity{FormId: formAnswerUUID}, userRoleID)
	if errStr != "" {
		return nil, errors.New(errStr)
	}

	requesterCountry := postgres.FetchRequesterCountry(formAnswerID)
	data := mapper.FormAnswerDesignEntityToModel(formAnswer, requesterCountry)
	basicInfoData := data[1]
	v := mapper.GetEventInformationForSponsorshipAgreement(data)
	// mapper.GetEventInformationFor...[name of the api]
	v.BankInformation = mapper.GetBankData(basicInfoData)

	formUUID, err := uuid.FromString(input.FormID)
	if err != nil {
		return nil, err
	}

	// intentionally ignore err
	approvalTrailDetails, _ := postgres.GetApprovalTrailDataForANActivity(&entity.FetchApprovalTrailEntity{
		FormAnswerId: formUUID,
	})
	for i := range approvalTrailDetails {
		fmt.Println(approvalTrailDetails[i].DateCreated, approvalTrailDetails[i].ActionedBy)
	}

	v.ZPRepresentative = `N/A`
	if v.LedBy != "Medical" && len(approvalTrailDetails) != 0 {
		v.ZPRepresentative = approvalTrailDetails[0].ActionedBy.String
	}

	if approvalTrailDetails[len(approvalTrailDetails)-1].DateCreated.Valid {
		v.DateOfAgreement = approvalTrailDetails[len(approvalTrailDetails)-1].DateCreated.Time.Format(`January 2, 2006`)
		v.DateOfAgreementDatetime = approvalTrailDetails[len(approvalTrailDetails)-1].DateCreated.Time
	}

	v.ZPRepresentativeTitle = `Sales/Marketing Director`
	return v, nil
}

func GetPDFBlobURLController(ctx context.Context, input model.GetPDFBlobURLRequest) model.GetPDFBlobURLResponse {
	var response model.GetPDFBlobURLResponse

	userID := auth.GetUserID(ctx)
	if userID == nil {
		return model.GetPDFBlobURLResponse{
			Error:   true,
			Message: "User do not have access",
		}
	}
	userRoleID := auth.GetApprovalRole(ctx)

	formUUID, _ := uuid.FromString(input.FormID)
	userUUID, _ := uuid.FromString(*userID)
	formAnswer, errStr := postgres.FetchFormAnswers(
		&userUUID,
		&entity.FormAnswerFetchEntity{FormId: formUUID}, userRoleID)
	if errStr != "" {
		return model.GetPDFBlobURLResponse{
			Error:   true,
			Message: errStr,
			URL:     "",
		}
	}

	requesterCountry := postgres.FetchRequesterCountry(input.FormID)
	data := mapper.FormAnswerDesignEntityToModel(formAnswer, requesterCountry)
	d := mapper.GetMajorBasicInfoFromAnswer(data[1])

	blobURL, err := postgres.NewPDFGeneratorInView(ctx, d.LedBy, input.FormID, input.CountryCurrency, input.UsdCurrency, input.ConvertRate, input.StartDate, input.EndDate, input.HcpTotalExpense, input.EventTotalExpense, input.TotalCost, input.MeetingID, input.HcpTotalExpenseUsd, input.EventTotalExpenseUsd, input.Submission)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return response
	}
	response.URL = blobURL
	return response
}

func GetSponsorshipAgreementPDFBlobURL(ctx context.Context, input *model.GetSponsorshipAgreementPDFBlobURLRequest) *model.GetPDFBlobURLResponse {
	if err := checkUserAuthorization(ctx); err != nil {
		return &model.GetPDFBlobURLResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	v, err := GetDownloadSponsorshipData(ctx, input)
	if err != nil {
		return &model.GetPDFBlobURLResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	formAnswerInfo, err := postgres.FetchExceptionalDetails(input.FormID)
	if err != nil {
		return &model.GetPDFBlobURLResponse{
			Error:   true,
			Message: err.Error(),
		}
	}
	v.Currency = *input.CountryCurrency
	meetingID := util.ConvertToEventID(formAnswerInfo.EventCode, formAnswerInfo.EventSeq)

	//---
	buf := mapper.ParseSponsorshipAgreementHTML(v)
	//---
	fmt.Println("-------------------")
	fmt.Println("sponsor expense list:", len(v.Expense))
	for _, val := range v.Expense {
		fmt.Println(val.PackageTitle, val.PackageDetails, val.PackageAmount)
	}

	//fmt.Println("-----------")
	//fmt.Println("Event Name:", v.EventName)
	//fmt.Println("Event Start At:", v.EventStartAt)
	//fmt.Println("Event Location:", v.EventLocation)
	//fmt.Println("Event Country:", v.Country)
	//fmt.Println("Event Amount:", v.Amount)
	//fmt.Println("Date Of Agreement:", v.DateOfAgreement)
	//fmt.Println("HCP number:", v.HCPNumber)
	//fmt.Println("HC0 name:", v.HCOName)

	// To PDF
	page := wkhtml.NewPageReader(buf)
	mapper.SponsorshipAgrHeaderFooter(page, &meetingID)

	pdfg, err := wkhtml.NewPDFGenerator()
	if err != nil {
		panic(err)
	}
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		panic(err)
	}

	//Upload to AZ blob
	var blobURL string
	latestApprovalAt := time.Now()
	if !v.DateOfAgreementDatetime.IsZero() {
		latestApprovalAt = v.DateOfAgreementDatetime
	}
	name := fmt.Sprintf(`Sponsorship_Agreement_%s_%s_%v.pdf`, meetingID, latestApprovalAt.Format("20060102150405"), time.Now().Round(0).Format("20060102150405"))
	blobURL, err = azure.UploadPDFToBlob(pdfg.Buffer().Bytes(), name)
	if err != nil {
		panic(err)
	}

	// --------- For Testing ----------- write html file to local
	//name := fmt.Sprintf(`Sponsorship_Agreement_%s_%v.html`, meetingID, time.Now().Round(0).Unix())
	//err = os.WriteFile(name, buf.Bytes(), 0644)
	//if err != nil {
	//	panic(err)
	//}
	// For Testing

	return &model.GetPDFBlobURLResponse{
		URL: blobURL,
		//URL: "test request success",
	}
}
