package logengine

import (
	"log"
	"os"
	"time"

	"github.com/microsoft/ApplicationInsights-Go/appinsights"
)

var telemetryClient appinsights.TelemetryClient

func CreateTelemetryClient() {
	instrumentationKey := os.Getenv("APPLICATION_INSIGHTS_KEY")
	telemetryConfig := appinsights.NewTelemetryConfiguration(instrumentationKey)

	appinsights.NewDiagnosticsMessageListener(func(msg string) error {
		log.Printf("[%s] %s\n", time.Now().Format(time.UnixDate), msg)
		return nil
	})

	client := appinsights.NewTelemetryClientFromConfig(telemetryConfig)
	telemetryClient = client
	telemetryClient.Context().Tags.Cloud().SetRole("Upload")

}

func GetTelemetryClient() appinsights.TelemetryClient {
	if telemetryClient == nil {
		CreateTelemetryClient()
	}
	return telemetryClient
}
