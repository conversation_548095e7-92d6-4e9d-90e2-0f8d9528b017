package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertTeamData(ctx *context.Context, inputModel model.TeamInput) *model.UpsertTeamResponse {
	upsertResponse := &model.UpsertTeamResponse{}

	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}

	entity, validationResult := mapper.MapTeamModelToEntity(&inputModel)

	if !validationResult.Error {
		if entity.ID == nil && !entity.IsDeleted {
			postgres.InsertTeamData(entity, upsertResponse, userUUID)
		} else if entity.IsDeleted {
			postgres.DeleteTeamData(entity, upsertResponse, userUUID)
		} else {
			postgres.UpdateTeamData(entity, upsertResponse, userUUID)
		}
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = "Team validation failed!"
		upsertResponse.ValidationErrors = validationResult.ValidationErrors
	}
	return upsertResponse
}

func FetchTeamValues(ctx *context.Context) *model.TeamResponse {
	var response model.TeamResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var err error
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	countryID, err := postgres.GetCountryIDByUserID(*userID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	values, err := postgres.GetTeamValues(countryID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	fetchTeamValue := mapper.FetchTeamValuesEntityToModel(values)
	response.Data = fetchTeamValue
	return &response
}
