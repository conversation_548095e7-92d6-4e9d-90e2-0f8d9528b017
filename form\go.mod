module github.com/ihcp/form

go 1.22.4

replace github.com/ihcp/login => ../login

replace github.com/ihcp/code => ../code

replace github.com/ihcp/teams => ../teams

replace github.com/ihcp/mail_service => ../mail_service

replace github.com/ihcp/veeva_service => ../veeva_service

require (
	github.com/360EntSecGroup-Skylar/excelize v1.4.1
	github.com/360EntSecGroup-Skylar/excelize/v2 v2.3.2
	github.com/99designs/gqlgen v0.17.49
	github.com/Azure/azure-storage-blob-go v0.13.0
	github.com/SebastiaanKlippert/go-wkhtmltopdf v1.7.2
	github.com/go-chi/chi v3.3.2+incompatible
	github.com/gorilla/websocket v1.5.0
	github.com/grokify/html-strip-tags-go v0.0.0-20200923094847-079d207a09f1
	github.com/ihcp/code v0.0.0-00010101000000-000000000000
	github.com/ihcp/login v0.0.0-00010101000000-000000000000
	github.com/ihcp/mail_service v0.0.0-00010101000000-000000000000
	github.com/ihcp/teams v0.0.0-00010101000000-000000000000
	github.com/ihcp/veeva_service v0.0.0-00010101000000-000000000000
	github.com/imdario/mergo v0.3.11
	github.com/jackc/pgtype v1.14.0
	github.com/jackc/pgx/v4 v4.18.3
	github.com/jasonlvhit/gocron v0.0.1
	github.com/jmoiron/sqlx v1.2.0
	github.com/joho/godotenv v1.3.0
	github.com/lib/pq v1.10.5
	github.com/microsoft/ApplicationInsights-Go v0.4.4
	github.com/rs/cors v1.6.0
	github.com/satori/go.uuid v1.2.0
	github.com/shopspring/decimal v1.2.0
	github.com/vektah/gqlparser/v2 v2.5.16
	gopkg.in/gomail.v2 v2.0.0-20160411212932-81ebce5c23df
)

require (
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/gofrs/uuid v4.4.0+incompatible // indirect
	github.com/kr/text v0.2.0 // indirect
	github.com/sendgrid/rest v2.6.9+incompatible // indirect
)

require (
	code.cloudfoundry.org/clock v1.1.0 // indirect
	github.com/Azure/azure-pipeline-go v0.2.3 // indirect
	github.com/agnivade/levenshtein v1.1.1 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/hashicorp/golang-lru/v2 v2.0.7 // indirect
	github.com/jackc/chunkreader/v2 v2.0.1 // indirect
	github.com/jackc/pgconn v1.14.3 // indirect
	github.com/jackc/pgio v1.0.0 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgproto3/v2 v2.3.3 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/puddle v1.3.0 // indirect
	github.com/mattn/go-ieproxy v0.0.1 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/mohae/deepcopy v0.0.0-20170929034955-c48cc78d4826 // indirect
	github.com/richardlehane/mscfb v1.0.3 // indirect
	github.com/richardlehane/msoleps v1.0.1 // indirect
	github.com/sendgrid/sendgrid-go v3.16.0+incompatible
	github.com/sosodev/duration v1.3.1 // indirect
	github.com/xuri/efp v0.0.0-20201016154823-031c29024257 // indirect
	golang.org/x/crypto v0.28.0 // indirect
	golang.org/x/net v0.30.0 // indirect
	golang.org/x/sys v0.26.0 // indirect
	golang.org/x/text v0.19.0
	google.golang.org/appengine v1.6.7 // indirect
	gopkg.in/alexcesaro/quotedprintable.v3 v3.0.0-20150716171945-2caba252f4dc // indirect
)
