package controller

import (
	"bufio"
	"bytes"
	"github.com/360EntSecGroup-Skylar/excelize"
	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	"log"
)

func getBytesFromFile(f *excelize.File) []byte {
	var excelBytes bytes.Buffer
	writer := bufio.NewWriter(&excelBytes)
	f.<PERSON>rite(writer)
	writer.Flush()
	return excelBytes.Bytes()
}

func getBytesFromFileV2(f *excelizev2.File) []byte {
	buffer, err := f.WriteToBuffer()
	if err != nil {
		log.Println(err)
	}
	myresult := buffer.Bytes()
	return myresult
}
func getColumnName(i int) string {
	value := ""
	multiples := i / 26
	for count := 0; count < multiples; count++ {
		value = value + "A"
	}
	i = i % 26
	value = value + string(rune('A'-1+i))
	return value
}
