package resolvers

import (
	"github.com/ihcp/data_maintenance/graph/ad_service"
	"github.com/ihcp/data_maintenance/graph/controller"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct {
	*ControllerResolver
}

func InitResolver() *Resolver {
	return &Resolver{
		InitControllerResolver(),
	}
}

// --------------------

type Controller<PERSON><PERSON>olver struct {
	*controller.LoginController
}

func InitControllerResolver() *ControllerResolver {
	adService := ad_service.New()

	return &ControllerResolver{
		LoginController: &controller.LoginController{
			AdService: adService,
		},
	}
}
