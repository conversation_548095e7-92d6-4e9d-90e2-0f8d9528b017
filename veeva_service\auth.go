package veeva_service

import (
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"strings"
)

func (s *Controller) haveCredentials() bool {
	if len(s.configs.GrandType) == 0 ||
		len(s.configs.ClientId) == 0 ||
		len(s.configs.UsernameVeeva) == 0 ||
		len(s.configs.PasswordVeeva) == 0 ||
		len(s.configs.VeevaAuthUrl) == 0 ||
		len(s.configs.ClientSecret) == 0 {
		return false
	}

	return true
}

func (s *Controller) authenticate() error {
	if s.auth != nil {
		return nil
	}

	data := url.Values{}
	data.Set("grant_type", s.configs.GrandType)
	data.Set("client_id", s.configs.ClientId)
	data.Set("client_secret", s.configs.ClientSecret)
	data.Set("username", s.configs.UsernameVeeva)
	data.Set("password", s.configs.PasswordVeeva)
	request, err := http.NewRequest("POST", s.configs.VeevaAuthUrl, strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	request.Header.Add("Content-Length", strconv.Itoa(len(data.Encode())))
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}

	log.Println("*** GetAuthVeeva()\n with payload: ", data.Encode())
	response, err := client.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	var authResp VeevaAuthorizationRestResponse
	if response.StatusCode != http.StatusOK {
		return errors.New(
			fmt.Sprintf("Veeva Authentication request has failed with response Code: %v \n%s", response.StatusCode, string(responseBody)))
	}

	if err := json.Unmarshal(responseBody, &authResp); err != nil {
		panic(err)
	}

	log.Println("+++ Authorized Veeva Successfully")
	s.auth = &authResp
	return nil
}
