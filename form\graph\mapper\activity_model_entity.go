package mapper

import (
	"context"

	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func MapActivityModelToEntity(input model.ActivityInput, ctx *context.Context, response *model.UpsertResponse) *entity.Activity {
	userId := auth.GetUserID(*ctx)
	userCountry := auth.GetCountry(*ctx)

	var activityEntity = &entity.Activity{}
	if input.ID != nil {
		if *input.ID != "" {
			activityID, err := uuid.FromString(*input.ID)
			if err != nil {
				response.Error = true
				response.Message = "Invalid UUID Format for activity id!"
				return nil
			} else {
				activityEntity.ID = &activityID
			}
		} else {
			//Error message for blank activity
			response.Error = true
			response.Message = "Blank UUID provided for activity id!"
			return nil
		}
	}
	timenow := util.GetCurrentTime()
	activityEntity.IsDeleted = input.IsDeleted

	//Handle delete only
	if activityEntity.IsDeleted != nil {
		// Check for id
		if activityEntity.ID == nil && *activityEntity.IsDeleted {
			response.Error = true
			response.Message = "Please provide an ID to delete!"
			return nil
		}
		if *activityEntity.IsDeleted {
			var isActive bool = false
			activityEntity.IsActive = &isActive
		}
	} else {
		activityEntity.IsActive = input.IsActive
	}
	if activityEntity.ID != nil {
		activityEntity.LastModified = &timenow
		activityEntity.ModifiedBy = userId
	} else {
		activityEntity.DateCreated = &timenow
		activityEntity.CreatedBy = userId
	}
	if input.Description != nil && *input.Description != "" {
		activityEntity.Description = *input.Description
	} else if input.IsDeleted != nil && !(*input.IsDeleted) {
		response.Error = true
		response.Message = "Please provide activity description!"
		return nil
	}
	activityEntity.Country = *userCountry
	return activityEntity
}

func MapActivityInputFilterModelToEntity(input model.ActivityInput, country int, response *model.UpsertResponse) *entity.Activity {
	var activityFilterEntity = &entity.Activity{IsActive: input.IsActive}
	if input.Description != nil {
		activityFilterEntity.Description = *input.Description
	}
	return activityFilterEntity
}
