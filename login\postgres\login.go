package postgres

import (
	"context"
	"strings"

	"github.com/ihcp/login/model"

	"github.com/jackc/pgx/v4/pgxpool"
)

func GetUserDetails(pool *pgxpool.Pool, adname string, country string) (*model.LoggedInUser, error) {

	var userModel *model.LoggedInUser
	activeDirectoryName := strings.ToLower(adname)
	if pool != nil {
		queryString := `SELECT "user".id, "user".approval_role, "user".country, "user".user_role_id FROM "user" 
		WHERE LOWER("user".active_directory) = $1
		AND "user".is_active = true AND "user".is_deleted = false 
		and "user".country = (select id from code where title = $2 and category='Country' and is_active= true)
		`
		userModel = &model.LoggedInUser{}
		err := pool.QueryRow(context.Background(), queryString, activeDirectoryName, country).Scan(&userModel.ID, &userModel.UserRole, &userModel.Country, &userModel.UserRoleID)
		if err != nil {
			return nil, err
		}
	}
	return userModel, nil
}
