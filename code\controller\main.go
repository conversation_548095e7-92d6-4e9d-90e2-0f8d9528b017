package controller

import (
	"log"
	"strings"

	"github.com/ihcp/code/entity"
	"github.com/ihcp/code/postgres"
	"github.com/jackc/pgx/v4/pgxpool"
)

var codes map[string][]*entity.Code
var codeService *postgres.CodeService

func InitCodes(dbPool *pgxpool.Pool) {
	codeService = &postgres.CodeService{Pool: dbPool}
	var codeError error
	codes, codeError = codeService.GetCodes()
	if codeError != nil {
		log.Println(codeError)
	}
}

func GetCodesByCategory(category string) []*entity.Code {
	return codes[category]
}

func RefreshCodes() {
	var codeError error
	codes, codeError = codeService.GetCodes()
	if codeError != nil {
		log.Println(codeError)
	}
}

func GetIdKeyCodes() map[string]map[int]entity.Code {
	output := make(map[string]map[int]entity.Code)
	for category, codesOfCategory := range codes {
		category = strings.ToLower(category)
		output[category] = make(map[int]entity.Code)
		for _, codeEntity := range codesOfCategory {
			output[category][codeEntity.ID] = *codeEntity
		}
	}
	return output
}

func GetValueKeyCodes() map[string]map[string]entity.Code {
	output := make(map[string]map[string]entity.Code)
	for category, codesOfCategory := range codes {
		category = strings.ToLower(category)
		output[category] = make(map[string]entity.Code)
		for _, codeEntity := range codesOfCategory {
			output[category][codeEntity.Value] = *codeEntity
		}
	}
	return output
}

func GetTitleKeyCodes() map[string]map[string]entity.Code {
	output := make(map[string]map[string]entity.Code)
	for category, codesOfCategory := range codes {
		category = strings.ToLower(category)
		output[category] = make(map[string]entity.Code)
		for _, codeEntity := range codesOfCategory {
			if codeEntity.Title.String != "" {
				output[category][codeEntity.Title.String] = *codeEntity
			}
		}
	}
	return output
}
