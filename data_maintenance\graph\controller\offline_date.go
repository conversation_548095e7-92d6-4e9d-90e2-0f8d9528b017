package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
)

func UpsertforOfflineDate(ctx context.Context, input model.OfflineDateInput) *model.UpsertOfflineDateResponse {
	upsertResponse := &model.UpsertOfflineDateResponse{}
	userID := auth.GetUserID(ctx)
	if userID == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
	}

	OfflineMapping, err := mapper.OfflineDateMapping(input, userID)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return upsertResponse
	}
	if input.ID == nil {
		err := postgres.InsertOfflineDate(OfflineMapping, upsertResponse, userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
		upsertResponse.Error = false
		upsertResponse.Message = "Offline Date Successfully Inserted"
	}
	if input.IsDeleted != nil && *input.IsDeleted == true {
		err := postgres.UpdateOfflineDate(*input.ID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
		upsertResponse.Error = false
		upsertResponse.Message = "Offline Date Successfully deleted"
	}

	return upsertResponse
}

func FetchOfflineDate(ctx context.Context) *model.GetOfflineDateResponse {
	var responce model.GetOfflineDateResponse

	userId := auth.GetUserID(ctx)

	if userId == nil {
		responce.Error = true
		responce.Message = "You are not authorized to login please contact your country ezflow admin."
		return &responce
	}

	OfflineDate, err := postgres.FetchOfflineDatepostgres(userId)
	if err != nil {
		responce.Error = true
		responce.Message = err.Error()
		return nil
	}
	OfflineModel := mapper.FetchOfflineDateMapping(OfflineDate)

	responce.Error = false
	responce.Message = ""
	responce.OfflineDate = OfflineModel
	return &responce
}
