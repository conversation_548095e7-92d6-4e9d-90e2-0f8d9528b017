'use strict';

const axios = require('axios')
const { parseMultipartData, sanitizeEntity } = require('strapi-utils');
const _ = require("lodash");
const moment = require('moment');
const { AlphabetMapping } = require('./AlphabetMapping');
const crypto = require('crypto');
const jwt = require('jsonwebtoken');
const pdf = require('pdf-creator-node');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { PDFDocument: PDFLibDocument } = require('pdf-lib');
const { Readable } = require('stream');
const PDFDocument = require('pdfkit');
const notificationService = require('./../../notification/controllers/Notification');

const CIPHER_KEY = process.env.CIPHER_KEY; //Cypher key
const EZRX_API_ENDPOINT = process.env.EZRXURL // order engine url
const EZRX_LICENSE_ENGINE_ENDPOINT = process.env.EZRXLICENSEENGINEURL // license engine url

const CRONSERVICES_GQL_URL = process.env.CRONSERVICESGQLURL;

const ALGORITHM = "aes-256-cbc";
const BLOCK_SIZE = 16;

/**
 * Read the documentation (https://strapi.io/documentation/3.0.0-beta.x/concepts/controllers.html#core-controllers)
 * to customize this controller
 */

const encryptNew = async (msg) => {
	////// NEW LOGIC //////
	const iv = crypto.randomBytes(BLOCK_SIZE);
	//CIPHER_KEY to be updated as pass
	const cipher = crypto.createCipheriv(ALGORITHM, CIPHER_KEY, iv);
	let cipherText;
	try {
		cipherText = cipher.update(msg, 'utf8', 'hex');
		cipherText += cipher.final('hex');
		cipherText = iv.toString('hex') + cipherText;
	} catch (e) {
		cipherText = null;
	}
	return cipherText;
}

const signHash = async (msg) => {
	const hash = crypto.createHmac('sha256', CIPHER_KEY).update(msg).digest('hex');
	return hash;
};


// Function to generate a hash from a file buffer
const generateHash = (buffer) => {
	return crypto.createHash('sha256').update(buffer).digest('hex');
};

const fetchDetailsForRedemtionTable = async (patient_id, program_id) => {
	if (patient_id && program_id) {
		const enrolD = await strapi.services.programenrollmentregister.findOne({ user: patient_id, program: program_id });
		if (enrolD.accessBenifit) {
			return true;
		} else {
			return false;
		}
	}
};

function formatDate(startTime) {
	var localDate = new Date(startTime).toLocaleString('en-US', { timeZone: 'Asia/Manila' });
	var date = new Date(localDate);
	var hours = date.getHours();
	var minutes = date.getMinutes();
	var suffix = hours >= 12 ? 'pm' : 'am';
	hours = hours % 12;
	hours = hours ? hours : 12;
	minutes = minutes < 10 ? '0' + minutes : minutes;
	var strTime = hours + ':' + minutes + ' ' + suffix;
	return date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear() + ", " + strTime;
}

const sendPrescriptionEmail = async (to, subject, body) => {
	try {
		await axios.post(
			"https://l6088-iflmap.hcisbp.ap1.hana.ondemand.com/http/automatedemail/trigger/v1",
			{},
			{
				headers: {
					"Content-Type": "text/html",
					Authorization: "Basic UzAwMTYwOTMxNzc6RGFuaWVsMSE=",
					sender: "<EMAIL>",
					receiver: to,
					subject: subject,
					bodytext: body,
				},
			}
		);
		return true;
	} catch (err) {
		console.log(err);
		return true;
	}
};

const sendMailWithAttachment = async (req, res) => {
	try {
		const ctxBody = req.request.body;
		const { subject, from, to, body, attachments } = ctxBody;
		const access_token = req.request.header.authorization || req.request.header.Authorization;
		const mutation = `mutation ($input : sendMailInput!){
			sendMail(input: $input){
				status
			}
		}`

		const email = await axios.post(`${CRONSERVICES_GQL_URL}`,
			{ query: mutation, variables: { input: { subject: subject, from: from, to: to, body: body, attachments: attachments } } }, { headers: { "authorization": `${access_token}`, 'Content-Type': 'application/json' } });
		if (email.data.errors) {
			return { err: email.data.errors }
		} else {
			return { status: email.data.data.sendMail }
		}
	} catch (err) {
		console.log(err);
		return { err: err }
	}
}

const sendMailWithoutAttachment = async (req, res) => {
	try {
		const ctxBody = req.request.body;
		const { subject, from, to, body } = ctxBody;
		const access_token = req.request.header.authorization || req.request.header.Authorization;
		const mutation = `mutation ($input : sendMailInput!){
			sendMail(input: $input){
				status
			}
		}`

		const email = await axios.post(`${CRONSERVICES_GQL_URL}`,
			{ query: mutation, variables: { input: { subject: subject, from: from, to: to, body: body } } }, { headers: { "authorization": `${access_token}`, 'Content-Type': 'application/json' } });
		if (email.data.errors) {
			return { err: email.data.errors }
		} else {
			return { status: email.data.data.sendMail }
		}
	} catch (err) {
		console.log(err);
		return { err: err }
	}
}

const calculateSOAlphabet = (deliveryDate) => {
	try {
		const month = moment(deliveryDate).format('M');
		const year = moment(deliveryDate).format('YYYY');
		const monthLetter = AlphabetMapping[0].months.find(m => m.month == month).letter
		const yearLetter = AlphabetMapping[1].years.find(y => y.year == year).letter

		return yearLetter + monthLetter
	} catch (error) {
		return null
	}
}

//Create Search Query
const createOrderHistoryQuery = (searchText, searchType) => {
	let query = {}
	switch (searchType) {
		case 'id':
			query['id'] = searchText.trim();
			break;
		case 'soNumber':
			query['soNo_contains'] = String(searchText).trim();
			break;
		case 'patient_name':
			query['name_contains'] = String(searchText).trim();
			break;
	}
	return query
}

const searchPrescriptions = async (eligibleUsers, prescriptionStatus, start, limit, searchText, searchType) => {

	let query = {};
	let countQuery = {};
	let prescriptions = [];
	let countOfPrescriptions;
	query = {
		_limit: limit,
		_start: start,
		patient_id_in: eligibleUsers,
		_sort: "created_at:DESC",
	};
	countQuery = {
		_limit: limit,
		_start: start,
		patient_id_in: eligibleUsers,
		_sort: "created_at:DESC",
	};
	if (searchType && searchText) {
		let searchByType = createOrderHistoryQuery(searchText, searchType)
		Object.assign(query, searchByType)
		Object.assign(countQuery, searchByType)
	}

	prescriptionStatus && prescriptionStatus === "Reordered"
		? ((query["prescription_status"] = "Reordered"),
			(countQuery["prescription_status"] = "Reordered"))
		: ((query["prescription_status_ne"] = "Reordered"),
			(countQuery["prescription_status_ne"] = "Reordered"));

	if (searchType !== 'fundingNo') {
		prescriptions = await strapi.query("prescription").find(query);

		countOfPrescriptions = await strapi
			.query("prescription")
			.count(countQuery);
	} else {
		query = {
			_limit: -1,
			patient_id_in: eligibleUsers,
			_sort: "created_at:DESC",
		};

		prescriptionStatus && prescriptionStatus === "Reordered" ? query["prescription_status"] = "Reordered" : query["prescription_status_ne"] = "Reordered";

		let eligiblePrescriptions = await strapi.query("prescription").find(query);

		let allprescriptions = eligiblePrescriptions.filter(prescription => {
			if (prescription.medicinelist && prescription.medicinelist.deliveryInfo && prescription.medicinelist.deliveryInfo.fundingAccount == searchText) {
				return prescription
			}
		})

		countOfPrescriptions = allprescriptions.length
		prescriptions = allprescriptions.slice(start, (start + limit))
	}

	return {
		users: prescriptions,
		count: countOfPrescriptions,
	};
}

const getPrescriptionById = async (id) => {

	let query = {};
	let searchByType = createOrderHistoryQuery(id, "id")
	Object.assign(query, searchByType)
	let eligiblePrescription = await strapi.query("prescription").findOne(query);
	return {
		prescription: eligiblePrescription,
	};
}

const downloadFile = async (url) => {
	try {
		// Get the file buffer using the SAS URL
		const response = await axios.get(url, { responseType: 'arraybuffer' });
		return response.data; // This is the file buffer
	} catch (error) {
		console.error('Error downloading file:', error);
		throw new Error('File download failed');
	}
};

const updateTempleteNameBaxterToVantiv = async (ctx) =>{

}


const customeParseMultipartData = async (ctx) => {
	const { body = {}, files = {} } = ctx.request;



	if (!body.data) {
		throw strapi.errors.badRequest(
			`When using multipart/form-data you need to provide your data in a JSON 'data' field.`
		);
	}

	let data;
	try {
		data = JSON.parse(body.data);
	} catch (error) {
		console.log(`Invalid 'data' field. 'data' should be a valid JSON.`);
		throw strapi.errors.badRequest(
			`Invalid 'data' field. 'data' should be a valid JSON.`
		);
	}


	const filesToUpload = Object.keys(files).reduce((acc, key) => {
		const fullPath = _.toPath(key);
		if (fullPath.length <= 1 || fullPath[0] !== 'files') {
			console.log(`Bad request: Files not prefixed with 'files'`);
			// throw strapi.errors.badRequest(
			//   `When using multipart/form-data you need to provide your files by prefixing them with the 'files'.`
			// );
		}
		// const path = _.tail(fullPath);

		acc[fullPath[0]] = files[key];
		// console.log(acc)
		console.log("******************************");
		return acc;
	}, {});


	return {
		data,
		files: filesToUpload
	};
};

// Example function to generate PDF from image using pdfkit
async function generateImagePdf(fileBuffer, outputPath) {
	return new Promise((resolve, reject) => {
	  const doc = new PDFDocument({
		size: 'A4',
		margin: 0
	  });
  
	  const stream = fs.createWriteStream(outputPath);
	  doc.pipe(stream);
  
	  const tempImagePath = outputPath.replace('.pdf', '.img');
  
	  // Save image to disk temporarily
	  fs.writeFileSync(tempImagePath, fileBuffer);
  
	  doc.image(tempImagePath, {
		fit: [doc.page.width, doc.page.height],
		align: 'center',
		valign: 'center'
	  });
  
	  doc.end();
  
	  stream.on('finish', () => {
		fs.unlinkSync(tempImagePath); // clean up image
		resolve();
	  });
  
	  stream.on('error', reject);
	});
  }


const generatePrescriptionPDF = async (ctx, id) => {

	const config = await strapi
		.store({
			environment: strapi.config.environment,
			type: 'plugin',
			name: 'upload',
		})
		.get({ key: 'provider' });

	// Step 1: Get data from the user
	// const { id } = prescriptionId;
	console.log(config);
	// const images = files; // ctx.request.files will contain the uploaded files in memory

	const alpabetMapping = [
		{
			months: [{
				month: 1, letter: 'A'
			},
			{
				month: 2, letter: 'B'
			}, {
				month: 3, letter: 'C'
			}, {
				month: 4, letter: 'D'
			}, {
				month: 5, letter: 'E'
			}, {
				month: 6, letter: 'F'
			}, {
				month: 7, letter: 'G'
			}, {
				month: 8, letter: 'H'
			}, {
				month: 9, letter: 'I'
			}, {
				month: 10, letter: 'J'
			}, {
				month: 11, letter: 'K'
			}, {
				month: 12, letter: 'L'
			}]
		}, {
			years: [{
				year: 2022, letter: 'A'
			},
			{
				year: 2023, letter: 'B'
			},
			{
				year: 2024, letter: 'C'
			},
			{
				year: 2025, letter: 'D'
			},
			{
				year: 2026, letter: 'E'
			},
			{
				year: 2027, letter: 'F'
			},
			{
				year: 2028, letter: 'G'
			},
			{
				year: 2029, letter: 'H'
			},
			{
				year: 2030, letter: 'I'
			},
			{
				year: 2031, letter: 'J'
			},
			{
				year: 2032, letter: 'K'
			},
			{
				year: 2033, letter: 'L'
			},
			{
				year: 2034, letter: 'M'
			},
			]
		}
	];

	const COUNTRY_CODE = '+60-'

	const order_reason_list = await strapi.query('staticcontent').findOne({
		id: 4
	});
	// try {
	// Array to store the image objects with name, type, and Base64 data
	// let imageObjects = [];

	var prescription;
	await getPrescriptionById(id.toString()).then((prescriptionsResponse) => {
		prescription = prescriptionsResponse.prescription;
	})
		.catch((err) => {
			ctx.badRequest(err);
		});


	const medicinelist = prescription?.medicinelist;
	const deliveryInfo = medicinelist?.deliveryInfo;
	let month = moment(deliveryInfo?.DeliveryDate).format('M');
	let year = moment(deliveryInfo?.DeliveryDate).format('YYYY');
	// Accessing the letter from the first element of the filtered array
	const monthLetter = alpabetMapping[0].months.find(m => m.month == month)?.letter;
	const yearLetter = alpabetMapping[1].years.find(y => y.year == year)?.letter;

	const prescriptionId = prescription?.id;
	const SONO = `${yearLetter}${monthLetter}${(prescriptionId ?? "").toString().padStart(5, '0')}`;

	const patientName = prescription?.name;
	const templateName = medicinelist?.templateName;
	const patientContactNumber = deliveryInfo?.patientContactNumber ? COUNTRY_CODE + deliveryInfo?.patientContactNumber : "N/A";
	const fundingAccount = deliveryInfo?.fundingAccount ?? "N/A";
	const anotherContactNumber = deliveryInfo?.anotherContactNumber ?? "N/A";
	const fundingName = deliveryInfo?.fundingName ?? "N/A";
	const paymentType = deliveryInfo?.paymentType ?? "N/A";
	const totalAmountToFloat = parseFloat(medicinelist?.grandTotal ?? "00");
	const totalAmount = totalAmountToFloat?.toFixed(2);
	const entitlement = deliveryInfo?.entitlement ?? "N/A";
	const advanceOrder = deliveryInfo?.advanceOrder ?? "N/A";
	const sdoOrder = deliveryInfo?.sdoOrder ?? "N/A";
	const reminder = deliveryInfo?.reminder ?? "N/A";
	const delivery_address = medicinelist?.delivery_address;
	const customerShipTo = delivery_address?.customerShipTo ?? "N/A";
	const address = delivery_address?.address ?? "N/A";
	const hospital = deliveryInfo?.hospital ?? "N/A";
	const deliveryDate = deliveryInfo?.DeliveryDate ? moment(deliveryInfo?.DeliveryDate).format('DD/MM/YYYY') : moment().format('DD/MM/YYYY');
	const medicineList = medicinelist?.medicine_list;
	const orderNo = deliveryInfo?.orderNo ?? "N/A";
	const DATE = deliveryInfo?.DATE ? moment(deliveryInfo?.DATE).format('DD/MM/YYYY') : moment().format('DD/MM/YYYY');
	const PNPONO = deliveryInfo?.PNPONO ?? "N/A";
	const ZPINVNO = deliveryInfo?.ZPINVNO ?? "N/A";
	const REMARKS = deliveryInfo?.REMARKS ?? "N/A";
	const prescriptionStatus = prescription?.prescription_status == "ShipmentProcessing" ? "Shipped" : prescription?.prescription_status ?? "N/A";
	// const recipets = imageObjects;
	const orderReasonWithDescription = prescription?.orderReasonWithDescription ?? "N/A";
	// Find the matching order reason and description
	const orderReasonData = order_reason_list?.contentJson.find(reason =>
		reason.orderReason === deliveryInfo?.orderReason || reason.orderReason === orderReasonWithDescription
	)?.description ?? "N/A";

	// Modify the array by adding the conditional logic
	const modifiedMedicineList = medicineList.map(el => {
		const slicedCode = {
			codePart1: el?.code?.slice(0, 10) || "",
			codePart2: el?.code?.slice(10, 20) || "",
			codePart3: el?.code?.slice(20) || ""
		};

		// Add a new property `display_name` or modify the existing properties
		return {
			...el, // Spread the original properties
			...slicedCode,
			display_name: el.name || el.medicineName || el.medicine_name,
			sap_code: el.sap_code || "NA"
		};
	});

	// Get the user data from the request body
	// if (!users || users.length === 0) {
	//     return ctx.throw(400, "Users data is required");
	// }
	// Step 2: Read HTML Template
	const htmlTemplate = fs.readFileSync(path.join(__dirname, 'template.html'), 'utf8');
	const imageTemplate = fs.readFileSync(path.join(__dirname, 'img_template.html'), 'utf8');
	// Step 3: Define the PDF options

	const options = {
		format: "A4", // Define your preferred format
		orientation: "portrait", // portrait or landscape
		footer: {
			height: "28mm",
			contents: ` <div style=" width: 100%; height: 50px;  position: relative; margin: 10px;">
  <div style= "position: absolute;
  right: 50;  
  display: flex;
  align-items: center; 
  justify-content: flex-end;">

	<table style="width: auto;">
	  <tr>
		<td style="padding: 0px;">
		  <img  style="height: 38px; width: 29px; margin-right: 10px;" src="data:image/jpg;base64,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"/>
		</td>
		<td style="padding: 0px; ">
		  
		  <div class="name" style="color: #064E59; font-family:Arial, sans-serif; font-style: normal; font-weight: normal; text-decoration: none; font-size: 18pt;">eZConsult</div>
		</td>
	  </tr>
	</table>
  </div>
</div>
`
		}
	};

	// Generate a unique file name using the current timestamp and a random number as a fallback
	const timestamp = Date.now();
	const randomSuffix = Math.floor(Math.random() * 100);  // Random number between 0 and 99
	const uniqueFileName = `${SONO}${randomSuffix}${timestamp}.pdf`;  // Combine timestamp and random number
	const tempFilePath = path.join(os.tmpdir(), uniqueFileName);

	// Step 4: Create the PDF document object
	const document = {
		html: htmlTemplate,
		data: {
			SONO,
			templateName,
			patientName,
			patientContactNumber,
			fundingAccount,
			anotherContactNumber,
			fundingName,
			paymentType,
			totalAmount,
			entitlement,
			advanceOrder,
			sdoOrder,
			reminder,
			customerShipTo,
			address,
			hospital,
			deliveryDate,
			modifiedMedicineList,
			orderNo,
			DATE,
			PNPONO,
			ZPINVNO,
			REMARKS,
			prescriptionStatus,
			orderReasonData,

		}, // Pass users data to the HTML template  
		path: tempFilePath
	};
	// Step 5: Create the PDF using the pdf-creator-node package
	// Generate the PDF
	const result = await pdf.create(document, options);
	// Extract the directory from the full path
	const directory = path.dirname(result.filename);
	const newFullPath = path.join(directory, uniqueFileName);
	// Rename the file
	fs.rename(result.filename, newFullPath, (err) => {
		if (err) {
			console.error('Error renaming the file:', err);
			return;
		}
		console.log('File renamed successfully!');
	});

	const mergedPdf = await PDFLibDocument.create();

	const pdfBytes = fs.readFileSync(newFullPath);
	const pdfd = await PDFLibDocument.load(pdfBytes);
	const copiedPages = await mergedPdf.copyPages(pdfd, pdfd.getPageIndices());
	copiedPages.forEach(page => mergedPdf.addPage(page));

	try {
		// Check if 'recipets' is an array or a single file
		if (prescription && prescription.transaction_receipt) {
			// If it's an array (multiple files), process each file
			const recipetsArray = prescription.transaction_receipt; // Use the array of files
			// Use a for...of loop instead of forEach to properly handle async/await
			for (const file of recipetsArray) {
				const fileType = file.mime;
				if (fileType && fileType !== 'application/pdf') {
					const parsedUrl = new URL(file.url);
					const urlPath = parsedUrl.pathname;
					const fileName = path.basename(urlPath);
					// Wait for token URL to be generated
					var tokenURL = await notificationService.getsasUrlToken(fileName);
					console.log(tokenURL.sasurl);
					// Step 1: Download the file from the SAS URL
					const fileBuffer = await downloadFile(tokenURL.sasurl); // Use the SAS URL here
				
					const timestamp = Date.now();
					const randomSuffix = Math.floor(Math.random() * 100);  // Random number between 0 and 99
					const uniqueFileName = `${SONO}${randomSuffix}${timestamp}.pdf`;  // Combine timestamp and random number
					const tempPdfPath = path.join(os.tmpdir(), uniqueFileName);

					// Generate image PDF with pdfkit
					await generateImagePdf(fileBuffer, tempPdfPath);


				 // Load and merge into existing mergedPdf
				 const ipdfBytes = fs.readFileSync(tempPdfPath);
				 const ipdfd = await PDFLibDocument.load(ipdfBytes);
				 const icopiedPages = await mergedPdf.copyPages(ipdfd, ipdfd.getPageIndices());
				 icopiedPages.forEach(page => mergedPdf.addPage(page));
			 
				 // Clean up the temporary image
				 fs.unlinkSync(tempPdfPath);
				}
			}
		}
	} catch (error) {
		console.error('Error converting files to Base64:', error);
	}




	try {
		// Check if 'prescription' and 'transaction_receipt' exist
		if (prescription && prescription.transaction_receipt) {
			// If it's an array (multiple files), process each file
			const recipetsArray = prescription.transaction_receipt;

			// Use a for...of loop instead of forEach to properly handle async/await
			for (const file of recipetsArray) {
				const fileType = file.mime;


				if (fileType && fileType === 'application/pdf') {
					// Parse the URL to extract the file name
					const parsedUrl = new URL(file.url);
					const urlPath = parsedUrl.pathname;
					const fileName = path.basename(urlPath);

					// Wait for the SAS URL token to be generated
					var tokenURL = await notificationService.getsasUrlToken(fileName);
					console.log(tokenURL.sasurl);

					// Step 1: Download the file from the SAS URL
					const fileBuffer = await downloadFile(tokenURL.sasurl);

					// Step 2: Load the PDF and merge pages
					const pdf = await PDFLibDocument.load(fileBuffer, { ignoreEncryption: true });
					const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());

					// Add the copied pages to the merged PDF
					copiedPages.forEach(page => mergedPdf.addPage(page));
				}
			}
		}
	} catch (error) {
		console.error('Error processing files:', error);
	}


	const mergedPdfBytes = await mergedPdf.save();
	fs.writeFileSync(newFullPath, mergedPdfBytes);

	const buffer = fs.readFileSync(newFullPath);
	// Convert the Buffer to a stream
	// const bufferStream = new Readable();
	// bufferStream.push(buffer);
	// bufferStream.push(null);

	// Stream the file content
	const fileStream = fs.createReadStream(newFullPath);
	// Get file MIME type (you can use a library to get mime based on file extension, e.g. mime-types)
	// const mimeType = 'application/pdf'; // Default for binary files, change if known (e.g., 'image/jpeg')

	// Set response headers
	// ctx.set('Content-Type', mimeType);
	// ctx.set('Content-Disposition', `attachment; filename="${result.filename}"`);

	// Ensure the file exists
	if (!fileStream) {
		return ctx.badRequest('File stream could not be created.');
	}

	// Get the size of the buffer in bytes
	const sizeInBytes = buffer.length;

	// Convert to megabytes (MB)
	const sizeInMB = sizeInBytes / (1024);

	// Pipe the file stream to the response
	// ctx.body = fileStream;

	// File object to be passed to upload
	const file = {
		hash: generateHash(buffer), // Example hash
		ext: '.pdf', // File extension
		buffer: buffer, // The file buffer
		mime: 'application/pdf', // MIME type of the file
		// path:  ?? , // Optional path if applicable
		name: uniqueFileName,
		size: sizeInMB
	};
	try {
		const createdFiles = await strapi.plugins['upload'].services.upload.upload([file], config);
		if (createdFiles && createdFiles.length >= 1) {
			const entity = await strapi.services.prescription.update({ id }, { "prescription": createdFiles[0].id });
			return entity;
		}
	} catch (error) {
		console.error(error);
		return ctx.internalServerError('Something went wrong while uploading the file');
	}
	setTimeout(() => {
		fs.unlink(result.filename, (err) => {
			if (err) {
				console.error('Error deleting file after timeout:', err);
			} else {
				console.log('Temporary file deleted after 10 second.');
			}
		});
	}, 11000); // 5 minutes

	// } catch (error) {
	// 	console.error(error);
	// 	ctx.throw(500, "Failed to generate PDF");
	// }

}


module.exports = {
	async find(ctx) {
		try {
			let entities;
			if (ctx.query._q) {
				entities = await strapi.services.prescription.search(ctx.query);
			} else {
				if (ctx.query.fundingAccount) {
					let query = `select p.*, to_json(d) as deliveryhub from prescriptions p left join deliveryhubs d on
					p.deliveryhub = d.id where medicinelist->'deliveryInfo'->>'fundingAccount' = '${ctx.query.fundingAccount}'`

					if (ctx.query.prescription_status_in && ctx.query.prescription_status_in.length) {
						query += " and prescription_status in ("
						ctx.query.prescription_status_in.forEach((el, index) => {
							query += `'${el}'`
							if (index != ctx.query.prescription_status_in.length - 1) query += `,`
						})
						query += ")"
					}

					if (ctx.query.patient_id) {
						query += ` and patient_id = '${ctx.query.patient_id}'`
					}

					if (ctx.query._sort) {
						const orders = ctx.query._sort.split(":")
						if (orders.length != 2) {
							return ctx.badRequest("Something went wrong, invalid sort method.")
						}
						query += ` order by ${orders[0]} ${orders[1]}`
					}

					if (ctx.query._limit) {
						query += ` limit ${ctx.query._limit}`
					}

					if (ctx.query._start) {
						query += ` offset ${ctx.query._start}`
					}

					const result = await strapi.connections.default.raw(query)
					return result.rows
				}
				entities = await strapi.services.prescription.find(ctx.query);
			}
			return entities;
		} catch (error) {
			console.log(error)
			return ctx.badRequest("Something went wrong")
		}
	},

	async create(ctx) {

		const config = await strapi
			.store({
				environment: strapi.config.environment,
				type: 'plugin',
				name: 'upload',
			})
			.get({ key: 'provider' });

		// Step 1: Get data from the user
		// const { id } = prescriptionId;
		console.log(config);
		let entity
		let creatorRoleDetails = {};
		const receivedJWT = String(ctx.request.header.authorization).split(' ')[1];
		/**
		 * Extracting JWT payload from received request header
		 * @returns { payload }
		 */
		const jwtDetails = jwt.verify(
			receivedJWT,
			process.env.JWT_SECRET,
			(err, decoded) => {
				if (err) {
					return err;
				}
				return decoded;
			});

		if (!jwtDetails.role.length) return err

		/**
		 * Fetching role Id from user-permissions_role table
		 * by role name
		 * @returns [ role ] 
		 */

		const role = await strapi.query('role', 'users-permissions').find({ name: jwtDetails.role });

		if (role.length) {
			creatorRoleDetails = {
				createdByRole:  role[0].id
			}
		}

		const isMultipart = ctx.is('multipart');
		let data;

		if (isMultipart) {
			const parsed = await customeParseMultipartData(ctx);
			data = parsed.data;
		} else {
			data = ctx.request.body;
		}

		// Check and update templateName in root
		if (data.templateName) {
			data.templateName = data.templateName.replace(/BAXTER/g, 'VANTIVE').replace(/Baxter/g, 'Vantive');
		}

		// Check and update templateName inside medicinelist
		if (data.medicinelist && data.medicinelist.templateName) {
			data.medicinelist.templateName = data.medicinelist.templateName.replace(/BAXTER/g, 'VANTIVE').replace(/Baxter/g, 'Vantive');
		}

		// Proceed with creation (assuming this is a wrapper around creation logic)
		if (isMultipart) {
			entity = await strapi.services.prescription.create(
				{ ...data, ...creatorRoleDetails },
				{ files: parsed.files }
			);
		} else {
			entity = await strapi.services.prescription.create({ ...data, ...creatorRoleDetails });
		}

		const deliveryDate = entity.medicinelist.deliveryInfo.DeliveryDate
		if (deliveryDate) {
			const SOAlphabet = calculateSOAlphabet(deliveryDate)
			const id = entity.id
			const modifiedId = id.toString().padStart(5, '0')

			entity = await strapi.services.prescription.update({ id }, { soNo: SOAlphabet + modifiedId });
		}

		return await generatePrescriptionPDF(ctx, entity.id);
	},

	async update(ctx) {
		const { id } = ctx.params;

		let entity;
		if (ctx.is('multipart')) {
			const { data, files } = parseMultipartData(ctx);
			entity = await strapi.services.prescription.update({ id }, data, {
				files,
			});
		} else {
			entity = await strapi.services.prescription.update({ id }, ctx.request.body);
		}

		/* Insert redemption data after checking compliance rate */
		try {
			if (entity.adherence_program && entity.medicinelist.compliance_rate) {
				if (entity.medicinelist.compliance_rate >= 100) {
					const configDetails = await strapi.services.programconfig.findOne({ id: entity.partnercompany.programconfig });
					const benifitEligibility = await fetchDetailsForRedemtionTable(entity.patient_id, configDetails.program.id);
					const programData = await strapi.services.programs.findOne({ id: configDetails.program.id });
					const programDetails = programData.programdetails;
					let benf_type = "";
					let benf_details = "";
					programDetails.forEach(dataElement => {
						if (dataElement.key === 'program_details_page') {
							benf_type = dataElement.value.benifit_type;
							benf_details = dataElement.value.benifit_details;
						}
					});
					if (benifitEligibility) {
						/* Add redemption data */
						let redDoctId = entity.doctor_id ? String(entity.doctor_id) : null;
						const redemptionData = {
							status: "New",
							prescription: String(entity.id),
							patient: String(entity.patient_id),
							doctor: redDoctId,
							program: String(configDetails.program.id),
							prescriptionsorder: String(entity.prescriptionsorders[0].id),
							redemptionDetails: {
								benifit_type: benf_type,
								benifit_details: benf_details,
							},
						};

						await strapi.services.redemptions.create(redemptionData);

						/* Sending Email to Admin regarding Free Goods */
						let patientDet = await strapi.plugins['users-permissions'].services.user.fetchAll({ id: entity.patient_id });
						let adminDet = await strapi.services.staticcontent.findOne({ label: "ADHERENCE_ADMIN_EMAIL" });
						let adhAdminEmail = adminDet.content;
						let emailsendAdmin = await sendPrescriptionEmail(
							adhAdminEmail,
							`Actions Needed ${patientDet[0].firstName} ${patientDet[0].lastName} is eligable for a ${benf_type}`,
							`<html><head><link href="https://fonts.googleapis.com/css2?family=Roboto:wght@900&display=swap" rel="stylesheet"></head><body><div>Hello, Admin,<br>${patientDet[0].firstName} ${patientDet[0].lastName} is eligable for a ${benf_type} on his/her next order.<br><br>Benifits are: <br><br><strong>Benifit Type: ${benf_type}</strong><br><strong>Benifit Details: ${benf_details}</strong><br><br>Thank You<br><br></div><div style="display: flex;align-items: center;margin-top: 20px"> <img src="https://ezpharmacy-dev3.azurewebsites.net/static/ezConsult_logo_white.png" width="30" height="30"> <p style="color: rgb(195, 213, 0);display: inline-block;margin: 0;font-family: Roboto, sans-serif;font-weight: 900;font-size: 24px;letter-spacing: 1px;margin-left: 5px;">eZConsult </p></div></body></html>`
						);

					}
				}
			}
		}
		catch (error) {
			console.log(error);
		}

		const patientDet = await strapi.plugins['users-permissions'].services.user.fetchAll({ id: entity.patient_id });

		const patientEmail = patientDet[0].email;

		/* Send mail and insert notification */
		if (entity.adherence_program) {
			if (entity.prescription_status === "DoctorAccepted" || entity.prescription_status === "DoctorRejected") {
				let presStatus = entity.prescription_status === "DoctorAccepted" ? "Accepted" : "Rejected";
				let subject = entity.prescription_status === "DoctorAccepted" ? "Prescription accepted by doctor" : "Prescription rejected by doctor";
				let emailsend = await sendPrescriptionEmail(
					patientEmail,
					subject,
					`<html><head><link href="https://fonts.googleapis.com/css2?family=Roboto:wght@900&display=swap" rel="stylesheet"></head><body><div>Hi, ${patientDet[0].firstName},<br>Your prescription which you uploaded at ${formatDate(entity.created_at)} under ${entity.partnercompany.company_name} program has been <strong>${presStatus}</strong> by Doctor.<br></div><div style="display: flex;align-items: center;margin-top: 20px"> <img src="https://ezpharmacy-dev3.azurewebsites.net/static/ezConsult_logo_white.png" width="30" height="30"> <p style="color: rgb(195, 213, 0);display: inline-block;margin: 0;font-family: Roboto, sans-serif;font-weight: 900;font-size: 24px;letter-spacing: 1px;margin-left: 5px;">eZConsult </p></div></body></html>`
				);

				let prescriptionNoti = {
					title: `Prescription ${presStatus}`,
					message: subject,
					userId: String(entity.patient_id),
					type: "Adherence Prescription",
					isread: false,
					notificationFor: "user",
					prescription_id: String(entity.id)
				}

				await strapi.services.notification.create(prescriptionNoti);
			}

			/* Send mail and insert notification when admin accepted or rejected */

			if (entity.prescription_status === "AdminAccepted" || entity.prescription_status === "AdminRejected") {
				let presStatus = entity.prescription_status === "AdminAccepted" ? "Accepted" : "Rejected";
				let subject = entity.prescription_status === "AdminAccepted" ? "Prescription accepted by admin" : "Prescription rejected by admin";
				let emailsend = await sendPrescriptionEmail(
					patientEmail,
					subject,
					`<html><head><link href="https://fonts.googleapis.com/css2?family=Roboto:wght@900&display=swap" rel="stylesheet"></head><body><div>Hi, ${patientDet[0].firstName},<br>Your prescription which you uploaded at ${formatDate(entity.created_at)} under ${entity.partnercompany.company_name} program has been <strong>${presStatus}</strong> by Admin.<br></div><div style="display: flex;align-items: center;margin-top: 20px"> <img src="https://ezpharmacy-dev3.azurewebsites.net/static/ezConsult_logo_white.png" width="30" height="30"> <p style="color: rgb(195, 213, 0);display: inline-block;margin: 0;font-family: Roboto, sans-serif;font-weight: 900;font-size: 24px;letter-spacing: 1px;margin-left: 5px;">eZConsult </p></div></body></html>`
				);

				let prescriptionNoti = {
					title: `Prescription ${presStatus}`,
					message: subject,
					userId: String(entity.patient_id),
					type: "Adherence Prescription",
					isread: false,
					notificationFor: "user",
					prescription_id: String(entity.id)
				}

				await strapi.services.notification.create(prescriptionNoti);

			}
		}

		entity = await generatePrescriptionPDF(ctx, id)
		return sanitizeEntity(entity, { model: strapi.models.prescription });
	},

	// updatereceipt: async ctx => {
	// 	const { id } = ctx.params;
	// 	const entity = await strapi.services.prescription.update({ id }, ctx.request.body);
	// const en = await	generatePrescriptionPDF(ctx, id);
	// 	return en // sanitizeEntity(en, { model: strapi.models.prescription });
	// },

	prescriptionSearch: async ctx => {
		const { searchVal, start, limit } = ctx.request.body

		let users_name = [];

		if (searchVal) {
			users_name = await strapi.plugins['users-permissions'].services.user.fetchAll({ firstName_contains: searchVal, lastName_contains: searchVal, email_contains: searchVal }, ["patientprofile"]);
		}

		const count = users_name.length
		return { users: users_name.slice((start || 0), (limit || 10) + 1), count: count }

	},
	shipEzrxOrder: async ctx => {
		try {
			const data = ctx.request.body;
			const token = ctx.request.body.ezrxToken;
			console.log("======= Shipment Start =======", moment().format('DD-MM-YYYY h:mm:ss a'))
			console.log('FE Request', data)
			const customerName = await axios.post(
				EZRX_LICENSE_ENGINE_ENDPOINT,
				{
					query: `
							query($customer: String!, $salesOrganisation: String!) {
								customerInformation(customer: $customer, salesOrganisation: $salesOrganisation ) {
									name1
									name2
									name3
									name4
							}
							}
							`,
					variables: {
						customer: `${data.customer.customerNumber}`,
						salesOrganisation: `${data.customer.salesOrganisation}`
					}
				},
				{
					headers: {
						Authorization: `Bearer V2 ${token}`,
					},
				}
			);

			let customerInfo;
			if (customerName.status === 200 && customerName.data.data) {
				customerInfo = customerName.data.data.customerInformation || false
			}

			const NewOrderTwoInputData = {
				NewOrderInput: {
					subscribeStatusChange: true,
					skipCartValidation: true,
					username: data.username,
					customer: { ...data.customer },
					specialInstructions: data.specialInstructions,
					trackingLevel: data.trackingLevel,
					notificationFor: data.notificationFor,
					materials: data.materials,
					POReference: data.POReference,
					RequestedDeliveryDate: data.requestedDeliveryDate,
					...(data.orderReason && { orderReason: data.orderReason }),
					...(customerInfo && {
						companyName:
							customerInfo.name1 +
							customerInfo.name2 +
							customerInfo.name3 +
							customerInfo.name4,
					}),
				},
			};
			const encryptedData = await encryptNew(JSON.stringify(NewOrderTwoInputData));
			const signedHash = await signHash(encryptedData);

			const response = await axios.post(
				EZRX_API_ENDPOINT,
				{
					query: `mutation submitOrderMicroservicesMutation($NewOrderInput: NewOrderInputSecure!) {
						submitOrderMicroservices(order: $NewOrderInput) {
							SalesDocument
							Messages {
								Type
								Message
							}
						}
					}`,
					variables: {
						NewOrderInput: {
							data: encryptedData,
							hash: signedHash
						}
					}
				},
				{
					headers: {
						"Authorization": `Bearer V2 ${token}`
					}
				}
			);
			ctx.send(response.data);
		} catch (e) {
			console.log("ERROR EZRX ORDER PLACE:", e.toJSON ? e.toJSON() : e);
			ctx.badRequest({ error: e.toJSON ? e.toJSON() : e });
		}
	},
	getEzrxOrderDetails: async ctx => {
		try {
			const { salesDocument } = ctx.params;
			const token = ctx.request.body.token;

			if (!salesDocument) return ctx.badRequest({ error: "salesDocument is required" });

			const response = await axios.post(
				EZRX_API_ENDPOINT,
				{
					query: `
					query($salesDocument: String!) {
						orderDetails(salesDocument: $salesDocument) {
							OrderHeader {
								Type
								OrderNumber
								EZRXNumber
								SoldTo
								ShipTo
								CompanyName
								OrderValue
								ProcessingStatus
								CreatedDate
								CreatedTime
								RequestedDeliveryDate
								OrderBy
							}
							ShippingInformation {
								InvoiceNumber
								InvoiceDate
								POReference
								Address
								PostalCode
								Country
								Phone
								Fax
							}
							SalesRepresentativeInformation {
								Name
								Email
								Contact
								ZPUserType
							}
							OrderItems {
								Type
								MaterialCode
								MaterialDescription
								SDSCode
								Qty
								UnitPrice
								TotalPrice
								LineReferenceNotes
								Batch
								ExpiryDate
								SAPStatus
							}
							SpecialInstructions
							Messages {
								Type
								Message
							}
						}
					}
					`,
					variables: {
						salesDocument
					}
				},
				{
					headers: {
						"Authorization": `Bearer ${token}`

					}
				}
			);

			ctx.send(response.data);
		} catch (e) {
			console.log("ERROR GETTING EZRX ORDER DETAILS", e.toJSON ? e.toJSON() : e);
			ctx.badRequest({ err: e.toJSON ? e.toJSON() : e });
		}
	},
	getEzrxOrderHistory: async ctx => {
		try {
			const query = ctx.request.body;
			const token = ctx.request.body.token;

			const response = await axios.post(
				EZRX_API_ENDPOINT,
				{
					query: `
					query(
						$soldTo: String!,
						$fromDate: String,
						$toDate: String,
						$shipTo: [String],
						$first: Int,
						$after: Int,
						$orderBy: String,
						$sort: String,
						$orderedBySalesRep: String,
						$language: String,
						$poReference: String,
						$soldTos: [String],
						$searchKey: String,
						$salesOrg: [String]
					) {
						orderHistoryV3(request: {
							soldTo: $soldTo,
							fromDate: $fromDate,
							toDate: $toDate,
							shipTo: $shipTo,
							first: $first,
							after: $after,
							orderBy: $orderBy,
							sort: $sort,
							orderedBySalesRep: $orderedBySalesRep,
							language: $language,
							poReference: $poReference,
							soldTos: $soldTos,
							searchKey: $searchKey,
							salesOrg: $salesOrg
						}) {
							orderCount
							orderHeaders {
								Type
								OrderNumber
								EZRXNumber
								SoldTo
								ShipTo
								CompanyName
								OrderValue
								ProcessingStatus
								CreatedDate
								CreatedTime
								RequestedDeliveryDate
								OrderBy
								TotalTax
								POReference
								PurchaseOrderType
								TelephoneNumber
								ShippingConditions
								OrderReason
								ReferenceNotes
								CollectiveNumber
								SubscribeStatusChange
							}
						}
					}
					`,
					variables: {
						...query
					}
				},
				{
					headers: {
						"Authorization": `Bearer ${token}`
					}
				}
			);

			ctx.send(response.data);
		} catch (e) {
			console.log('e', e)
			console.log("ERROR GETTING EZRX ORDER HISTORY", e.toJSON ? e.toJSON() : e);
			ctx.badRequest({ err: e.toJSON ? e.toJSON() : e });
		}
	},
	sendMail: async (req, res) => {
		return await sendMailWithAttachment(req, res);
	},
	sendMailWithoutAttachment: async (req, res) => {
		return await sendMailWithoutAttachment(req, res);
	},
	pharmacyPatients: async ctx => {
		const { deliveryHubId, start, limit, searchVal, sort, startDate, endDate } = ctx.request.body

		let pharmacyPrescriptions = []
		let prescriptions = []
		let patients = []
		let prescriptions_name = []
		let patients_name = []
		let pharmacyPatients = []
		let patients_email = []
		let patients_mobile = []

		if (parseInt(deliveryHubId)) {
			prescriptions = await strapi.query('prescription').find({ _sort: sort, _limit: -1, deliveryhub: parseInt(deliveryHubId) }, ["deliveryhub"])
		} else {
			prescriptions = await strapi.query('prescription').find({ _sort: sort, _limit: -1 }, ["deliveryhub"])

		}
		pharmacyPrescriptions = [...new Map(prescriptions.map(item =>
			[item['patient_id'], item])).values()];

		if (searchVal) {
			prescriptions_name = await strapi.query("prescription").find({ _limit: -1, _sort: sort, name_contains: searchVal }, [])
			await Promise.all(prescriptions_name.map(async (p) => {
				let users = await strapi.plugins['users-permissions'].services.user.fetchAll({ id: p.patient_id }, [])
				patients_name.push(...users)
			}))
			patients_email = await strapi.plugins['users-permissions'].services.user.fetchAll({ _sort: sort, email_contains: searchVal }, ["deliveryhub", "patientprofile"]);
			patients_mobile = await strapi.plugins['users-permissions'].services.user.fetchAll({ _sort: sort, mobileNumber_contains: searchVal }, ["deliveryhub", "patientprofile"]);
			const allPatients = [...patients_name, ...patients_email, ...patients_mobile]
			patients = [...new Map(allPatients.map(item =>
				[item['id'], item])).values()];
		} else {
			patients = await strapi.query('user', 'users-permissions').find({ _limit: -1 }, ['deliveryhub']);
		}

		pharmacyPrescriptions.map(u => {
			patients.map(p => {
				if (u.patient_id == p.id) {
					pharmacyPatients.push(p)
				}
			})
		})
		const _data = pharmacyPatients.map(async (u) => {
			try {
				const prescription_condition = {
					// partnercompany: partnerId,
					patient_id: u.id,
				};

				const prescription = await strapi
					.query('prescription')
					.findOne(prescription_condition, ['address']);
				const nurseUser = await strapi
					.query('nurseusers')
					.findOne({ user: u.id }, []);
				let nurse;
				if (nurseUser) {
					nurse = await strapi.plugins['users-permissions'].services.user.fetch(
						{ id: nurseUser.nurseId }
					);
				}

				let patientProfile = await strapi
					.query('patientprofile')
					.findOne({ user: u.id }, []);

				let address = await strapi
					.query('address')
					.findOne({ user_id: u.id, default: true }, []);

				let currentUser = await strapi.plugins[
					'users-permissions'
				].services.user.fetch({ id: u.id }, ['deliveryhubs']);

				const funding = await strapi.query('patientfundingamount').find(
					{
						patient: u.id,
					},
					[]
				);

				const object = {
					id: u.id,
					patientName: u.patient_full_name || u.firstName + " " + u.lastName,
					created_at: u.created_at,
					mobileNumber: u.mobileNumber,
					email: u.email,
					fundingAccountNumber: funding ? funding.fundingAccountNumber : 'N/A',
					funding: funding,
					patientProfile: patientProfile
						?
						patientProfile
						: 'N/A',
					lastPrescription: prescription ? prescription : 'N/A',
					// prescriptionId :  prescription ? prescription.id :  "N/A",
				};
				return object
			} catch (e) {
				console.log(e);
				return null;
			}
		});


		const data = await Promise.all(_data);
		pharmacyPatients = data.filter((d) => !!d);

		const count = pharmacyPatients.length
		return { users: pharmacyPatients.slice((start || 0), (limit || 10)), count: count }
	},

	//nurse order history
	nursesOrderHistory: async ctx => {
		try {
			const { prescriptionStatus, nurseId, start, limit, searchVal, searchType } = ctx.request.body;

			let eligibleUsers = [];
			let users = [];

			users = await strapi
				.query("nurseusers")
				.find({ _limit: -1, nurseId: nurseId, _sort: "created_at:DESC" }, ["user"]);

			users.map((user) => {
				if (user.user && Object.keys(user.user).length) {
					const { id } = user.user;
					eligibleUsers.push(id);
				}
			});

			if (searchVal && searchType) {
				if (searchType === "fundingNo") {
					eligibleUsers = []
					const fundingUser = await strapi.query("patientfundingamount").find({
						fundingAccountNumber: String(searchVal).trim()
					})

					if (fundingUser.length) {
						fundingUser.map(account => {
							if (account.patient) {
								const { patient } = account;
								if (patient.id) {
									eligibleUsers.push(patient.id)
								}
							}
						})
					}
				}
			}

			await searchPrescriptions(eligibleUsers, prescriptionStatus, start, limit, searchVal, searchType)
				.then((prescriptionsResponse) => {
					ctx.send(prescriptionsResponse);
				})
				.catch((err) => {
					ctx.badRequest(err);
				});

		} catch (err) {
			console.error(err)
		}

	},

	//Admin Order History
	adminOrderHistory: async ctx => {

		try {
			const { prescriptionStatus, start, limit, searchVal, searchType } = ctx.request.body;
			let eligibleUsers = [];
			let users = [];

			users = await strapi
				.query("nurseusers")
				.find({ _limit: -1, _sort: "created_at:DESC" }, ["user"]);

			users.map((user) => {
				if (user.user && Object.keys(user.user).length) {
					const { id } = user.user;
					eligibleUsers.push(id);
				}
			});

			if (searchVal && searchType) {
				if (searchType === "fundingNo") {
					eligibleUsers = [];
					const fundingUser = await strapi.query("patientfundingamount").find({
						fundingAccountNumber: searchVal,
					});

					if (fundingUser.length) {
						fundingUser.map((account) => {
							if (account.patient) {
								const { patient } = account;
								if (patient.id) {
									eligibleUsers.push(patient.id)
								}
							}
						});
					}
				}
			}
			await searchPrescriptions(eligibleUsers, prescriptionStatus, start, limit, searchVal, searchType)
				.then((prescriptionsResponse) => {
					ctx.send(prescriptionsResponse);
				})
				.catch((err) => {
					ctx.badRequest(err);
				});
		} catch (err) {
			console.error(err);
		}
	},

	orderHistory: async ctx => {
		const { nurseId, prescriptionStatus, searchVal, start, limit, role } = ctx.request.body;

		let users = [];
		let eligibleUsers = [];
		let prescriptions = [];
		let searchValues = [];
		let prescriptions_name = [];
		let prescriptions_id = [];
		let prescriptions_fundingNo = [];
		let prescriptions_SONo = [];

		if (role === "partner_company_nurse") {
			users = await strapi
				.query("nurseusers")
				.find({ _limit: -1, nurseId: nurseId }, ["user"]);
			users.map((user) => {
				if (user.user && Object.keys(user.user).length) {
					const { id } = user.user;
					// listOfUserIds[Number(id)] = true;
					eligibleUsers.push(id);
				}
			});
		} else if (role === "partner_company_admin") {
			users = await strapi
				.query("nurseusers")
				.find({ _limit: -1, _sort: "created_at:DESC" }, ["user"]);
			users.map((user) => {
				if (user.user && Object.keys(user.user).length) {
					const { id } = user.user;
					eligibleUsers.push(id);
					// listOfUserIds[Number(id)] = true;
				}
			});
		}

		if (searchVal) {
			const splitValues = searchVal.split(/[\s, ,\t]+/);
			splitValues.map((sp) => {
				if (sp) {
					searchValues.push(sp);
				}
			});
		}

		if (searchVal) {
			if (prescriptionStatus == "Reordered") {
				let allPrescriptions = await strapi.query("prescription").find({
					patient_id_in: eligibleUsers,
					prescription_status: prescriptionStatus,
					_sort: "created_at:DESC",
					_limit: -1,
				});
				await Promise.all(
					searchValues.map(async (searchVal) => {
						prescriptions_name = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							name_contains: searchVal,
							prescription_status: prescriptionStatus,
							_sort: "created_at:DESC",
							_limit: -1,
						});
						prescriptions_id = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							id: !isNaN(parseInt(searchVal)) ? searchVal : 0,
							prescription_status: prescriptionStatus,
							_sort: "created_at:DESC",
							_limit: -1,
						});
						prescriptions_SONo = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							soNo_contains: searchVal,
							prescription_status: prescriptionStatus,
							_sort: "created_at:DESC",
							_limit: -1,
						});

						await allPrescriptions.map((p) => {
							if (
								p.medicinelist &&
								p.medicinelist.deliveryInfo &&
								p.medicinelist.deliveryInfo.fundingAccount
							) {
								const toStringNumber =
									p.medicinelist.deliveryInfo.fundingAccount.toString();
								toStringNumber.includes(searchValues)
									? prescriptions_fundingNo.push(p)
									: "";
							}
						});

						prescriptions.push(
							...prescriptions_name,
							...prescriptions_id,
							...prescriptions_SONo,
							...prescriptions_fundingNo
						);
					})
				);
				prescriptions = [
					...new Map(prescriptions.map((item) => [item["id"], item])).values(),
				];
			} else {
				let allPrescriptions = await strapi.query("prescription").find({
					patient_id_in: eligibleUsers,
					prescription_status_ne: "Reordered",
					_sort: "created_at:DESC",
					_limit: -1,
				});

				await Promise.all(
					searchValues.map(async (searchVal) => {
						prescriptions_name = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							name_contains: searchVal,
							prescription_status_ne: "Reordered",
							_sort: "created_at:DESC",
							_limit: -1,
						});
						prescriptions_id = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							id: !isNaN(parseInt(searchVal)) ? searchVal : 0,
							prescription_status_ne: "Reordered",
							_sort: "created_at:DESC",
							_limit: -1,
						});
						prescriptions_SONo = await strapi.query("prescription").find({
							patient_id_in: eligibleUsers,
							soNo_contains: searchVal,
							prescription_status_ne: "Reordered",
							_sort: "created_at:DESC",
							_limit: -1,
						});

						await allPrescriptions.map((p) => {
							if (
								p.medicinelist &&
								p.medicinelist.deliveryInfo &&
								p.medicinelist.deliveryInfo.fundingAccount
							) {
								const toStringNumber =
									p.medicinelist.deliveryInfo.fundingAccount.toString();
								toStringNumber.includes(searchValues)
									? prescriptions_fundingNo.push(p)
									: "";
							}
						});

						prescriptions.push(
							...prescriptions_name,
							...prescriptions_id,
							...prescriptions_SONo,
							...prescriptions_fundingNo
						);
					})
				);
				prescriptions = [
					...new Map(prescriptions.map((item) => [item["id"], item])).values(),
				];
			}
		} else {
			prescriptionStatus == "Reordered"
				? (prescriptions = await strapi.query("prescription").find({
					_limit: -1,
					patient_id_in: eligibleUsers,
					prescription_status: prescriptionStatus,
					_sort: "created_at:DESC",
				}))
				: (prescriptions = await strapi.query("prescription").find({
					_limit: -1,
					patient_id_in: eligibleUsers,
					prescription_status_ne: "Reordered",
					_sort: "created_at:DESC",
				}));
		}

		const count = prescriptions.length;
		return {
			users: prescriptions.slice(start || 0, limit || 10),
			count: count,
		};

	},
	pharmacyPrescriptions: async ctx => {

		const { prescriptionStatus, sort, start, limit, searchVal, startDate, endDate, hubId } = ctx.request.body

		let prescriptions = []
		let users = []
		let prescriptions_name = []
		let users_name = []
		let users_email = []
		let allUsers = []
		let prescriptionUsers = []
		let prescriptionsData = []
		let prescriptions_id = []
		let prescriptions_search = []
		let prescriptions_email = []
		let prescriptions_all = []
		let prescriptions_sap = []

		if (startDate && endDate) {
			users = await strapi.plugins['users-permissions'].services.user.fetchAll({ _limit: -1 }, ["deliveryhub"]);
			prescriptions = await strapi.query("prescription").find({ _limit: -1, _sort: sort, prescription_status_in: prescriptionStatus, created_at_gte: startDate, created_at_lt: endDate }, ["deliveryhub", "prescription"])
		}
		else if (searchVal) {
			users_email = await strapi.plugins['users-permissions'].services.user.fetchAll({ email_contains: searchVal, _limit: -1 }, ["deliveryhub"]);
			users_email.map(async user => {
				let prescriptionQuery = await strapi.query("prescription").find({ patient_id: user.id, _limit: -1, _sort: sort, prescription_status_in: prescriptionStatus }, ["deliveryhub", "prescription"])
				prescriptions_email.push(...prescriptionQuery)
			})

			prescriptions_all = await strapi.query("prescription").find({ _limit: -1, prescription_status_in: prescriptionStatus });
			prescriptions_sap = prescriptions_all.filter(prescription => prescription.medicinelist && prescription.medicinelist.deliveryInfo && prescription.medicinelist.deliveryInfo.fundingAccount == searchVal)

			prescriptions_name = await strapi.query("prescription").find({ _limit: -1, name_contains: searchVal, prescription_status_in: prescriptionStatus }, ["deliveryhub", "prescription"], []);
			prescriptions_id = Number.isInteger(parseInt(searchVal)) ? await strapi.query("prescription").find({ _limit: 1, id: parseInt(searchVal), prescription_status_in: prescriptionStatus }, ["deliveryhub", "prescription"]) : [];
			prescriptions_search = [...prescriptions_name, ...prescriptions_id, ...prescriptions_email, ...prescriptions_sap];

			await Promise.all(prescriptions_search.map(async (p) => {
				let users = await strapi.plugins['users-permissions'].services.user.fetchAll({ id: p.patient_id }, [])
				users_name.push(...users)
			}))

			allUsers = [...users_name];
			users = [...new Map(allUsers.map(item =>
				[item['id'], item])).values()];

			prescriptions = [...new Map(prescriptions_search.map(item =>
				[item['id'], item])).values()];

		} else {
			users = await strapi.plugins['users-permissions'].services.user.fetchAll({ _limit: -1 }, ["deliveryhub"]);
			prescriptions = await strapi.query("prescription").find({ _sort: sort, prescription_status_in: prescriptionStatus, _limit: -1 }, ["deliveryhub", "prescription"])
		}

		parseInt(hubId) ? prescriptions = prescriptions.map(p => {
			if (p.deliveryhub && p.deliveryhub.id && p.deliveryhub.id == parseInt(hubId)) {
				return p
			}
		}) : '';

		prescriptions.map(p => {
			users.map(u => {
				if (p && p.patient_id == u.id) {
					prescriptionUsers.push(u)
					prescriptionsData.push(p)
				}
			}
			)
		})

		const count = prescriptionsData.length;
		return {
			users: prescriptionUsers.slice((start || 0), (limit || 10)),
			prescriptions: prescriptionsData.slice((start || 0), (limit || 10)),
			count: count
		};
	},
	exportPrescriptions: async ctx => {

		const { from, to, created_by, prescriptionStatus } = ctx.request.body;

		let prescriptions = [];

		if ((from && to) || created_by) {

			if (prescriptionStatus == 'All') {

				prescriptions = await strapi.query("prescription").find({ prescription_status_ne: 'Reordered', created_at_gte: from, created_at_lt: to, _limit: -1, _sort: 'created_at:DESC' }, [])

			} else {

				prescriptions = await strapi.query("prescription").find({ prescription_status: prescriptionStatus, created_at_gte: from, created_at_lt: to, _limit: -1, _sort: 'created_at:DESC' }, [])

			}
			if (created_by !== "All") {

				prescriptions = prescriptions.filter(prescription => {

					if (prescription.medicinelist && prescription.medicinelist.orderBy) {

						if (
							prescription.medicinelist.orderBy.createrEmail !== undefined &&
							prescription.medicinelist.orderBy.createrEmail.includes(created_by)
						) {
							return prescription
						}
					}
				})
			}

		}
		return prescriptions
	},
	envdetails: async ctx => {
		console.log(ctx.request.body.url);
		ctx.send(await downloadFile(ctx.request.body.url));
		// let data = {
		// 	EZRXTOKEN: process.env.EZRXTOKEN || "abc",
		// 	TWILIO_ACCOUNT_SID: process.env.TWILIO_ACCOUNT_SID || "bca",
		// 	TWILIO_AUTH_TOKEN: process.env.TWILIO_AUTH_TOKEN || "cda",
		// 	JWT_SECRET: process.env.JWT_SECRET || "mno",
		// 	CIPHER_KEY: process.env.CIPHER_KEY || "pqr"
		// }

		// ctx.send(data);
	}
};
