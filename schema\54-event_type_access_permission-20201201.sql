CREATE TABLE event_type_access_permission (
	id uuid NOT NULL DEFAULT public.uuid_generate_v4(),
	user_role _int8 NOT NULL,
	event_type _int8 NOT NULL,
	country int4 null,
	is_active bool NOT NULL DEFAULT true,
	is_deleted bool NOT NULL DEFAULT false,
	created_by uuid not NULL,
	date_created timestamptz NOT NULL DEFAULT now(),
	last_modified timestamptz NULL,
	modified_by uuid NULL
);

ALTER TABLE event_type_access_permission ADD CONSTRAINT fk_country FOREIGN KEY (country) REFERENCES code(id);

insert into event_type_access_permission (user_role,event_type,created_by) values 
(array [68,69,46,71,72],array [98,94,86],'52dad980-4b26-411f-96a3-83483ad143a9');

insert into event_type_access_permission (user_role,event_type,created_by) values 
(array [68,69,46,71,72],array [92],'52dad980-4b26-411f-96a3-83483ad143a9');

insert into event_type_access_permission (user_role,event_type,created_by) values 
(array [69,46,71,72],array [93],'52dad980-4b26-411f-96a3-83483ad143a9');

insert into event_type_access_permission (user_role,event_type,created_by) values 
(array [47],array [96],'52dad980-4b26-411f-96a3-83483ad143a9');

insert into event_type_access_permission (user_role,event_type,created_by) values 
(array [47,46,69],array [100],'52dad980-4b26-411f-96a3-83483ad143a9');