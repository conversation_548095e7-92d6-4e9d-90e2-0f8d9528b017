package controller

import (
	"context"
	"errors"
	"log"
	"strings"
	"sync"

	"github.com/gofrs/uuid"
	"github.com/ihcp/login/auth"
	"github.com/ihcp/user_activity/graph/entity"
	"github.com/ihcp/user_activity/graph/mapper"
	"github.com/ihcp/user_activity/graph/model"
	"github.com/ihcp/user_activity/graph/postgres"
)

func FetchUserTrackAPIController(ctx *context.Context, input model.FetchUserTrackingRequest) *model.FetchUserTrackingResponse {
	var response model.FetchUserTrackingResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	var action string
	var formAnswerId uuid.UUID
	if input.Action != nil && strings.TrimSpace(*input.Action) != "" {
		action = *input.Action
	}
	if input.FormID != nil && strings.TrimSpace(*input.FormID) != "" {
		uuid, err := uuid.FromString(*input.FormID)
		if err != nil {
			response.Error = true
			response.Message = "Invalid Form answer Id !!"
			return &response
		}
		formAnswerId = uuid
	}
	res, err := postgres.FetchUserTrackAPIDb(action, formAnswerId, country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.Data = res
	return &response
}
func UserTrackingActionController(ctx *context.Context, input model.UserTrackingRequest) *model.UserTrackingResponse {
	var response model.UserTrackingResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	res, err := mapper.UserTrackingActionModelToEntity(input, userID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	wgMain := &sync.WaitGroup{}
	errorChannelMain := make(chan error, 2)
	wgMain.Add(1)
	go func(input entity.UserTrackingEntity, wgMain *sync.WaitGroup) {
		defer wgMain.Done()
		var err error
		err = postgres.InsertUserTrackingDb(input)
		if err != nil {
			errorChannelMain <- err
		}
	}(res, wgMain)
	go func() {
		wgMain.Wait()
		close(errorChannelMain)
	}()
	var lastErrMain error
	for eachError := range errorChannelMain {
		if eachError != nil {
			if lastErrMain == nil {
				lastErrMain = errors.New(eachError.Error())
			} else {
				lastErrMain = errors.New(lastErrMain.Error() + "\n" + eachError.Error())
			}
		}
	}
	if lastErrMain != nil {
		response.Error = true
		response.Message = lastErrMain.Error()
		return &response
	}
	return &response

}
func UserActivitiesDeleteCron() error {
	log.Println("UserActivitiesDeleteCron")
	//var wg sync.WaitGroup
	allUserActivityPreviousData, err := postgres.GetAllUserActivitiesExceptToday()
	if err != nil {
		return err
	}
	uploadUrl, filename, err := mapper.CreateExportPreviousUserActivityExcelGenerate(allUserActivityPreviousData)
	if err != nil {
		return err
	}
	err = postgres.InsertUploadLogs(uploadUrl, filename, "useractivitytracking", "00000000-0000-0000-0000-000000000000")
	if err != nil {
		return err
	}
	err = postgres.DeletePreviousData()
	if err != nil {
		return err
	}
	return err
}
