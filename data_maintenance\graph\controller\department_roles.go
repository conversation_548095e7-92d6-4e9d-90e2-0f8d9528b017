package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertDepartmentRolesData(ctx *context.Context, inputModel model.DepartmentRolesInput) *model.UpsertResponse {
	var upsertResponse model.UpsertResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
	entity, err := mapper.MapdepartmentRolesModelToEntity(&inputModel, userUUID)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	if entity.ID == nil && !entity.IsDeleted {
		err := postgres.InsertDepartmentRoles(entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Department roles successfully inserted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else if entity.IsDeleted {
		err := postgres.DeleteDepartmentRoles(entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Department roles successfully deleted"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	} else {
		err := postgres.UpdateDepartmentRoles(&inputModel, entity)
		if err == nil {
			upsertResponse.Error = false
			upsertResponse.Message = "Department roles successfully updated"
			return &upsertResponse
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
}

func FetchDepartmentRoles(ctx *context.Context, input *model.DepartmentRoleRequest) *model.DepartmentRoleResponse {
	var response model.DepartmentRoleResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var err error
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	if input != nil {
		if input.Department != nil {
			if *input.Department == "" {
				response.Error = true
				response.Message = "Department cannot be blank"
				return &response
			}
		}
		if input.UserRole != nil {
			if *input.UserRole == "" {
				response.Error = true
				response.Message = "User role cannot be blank"
				return &response
			}
		}
	}
	values, err := postgres.GetDepartmentRolesInfo(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if values == nil {
		response.Error = true
		response.Message = "No data exists"
		return &response
	}
	fetchDepartmentRolesValue := mapper.FetchDepartmentRolesEntityToModel(values)
	response.Error = false
	response.Data = fetchDepartmentRolesValue
	return &response
}
