DELETE FROM approval_role_management
where activity_id = 419;



INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 8, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 8, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 8, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 8, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));

INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 6, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 6, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 6, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 6, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));

INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 9, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 9, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 9, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 9, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));

INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 10, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 10, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 10, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 10, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));


INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 12, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 12, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 12, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 12, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));


INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 185, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 185, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 185, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 185, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));



INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, date_created, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values
( 0, 56, true, false, 4, '2021-05-04 11:12:09.449', NULL, 10001, NULL, 419, false, NULL, NULL, 400, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 3, '2021-05-04 11:11:45.246', NULL, 10001, NULL, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, true, false, 2, '2021-05-04 11:11:18.543', NULL, 10001, NULL, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-05-04 11:10:56.059', NULL, 10001, NULL, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, false, true, 4, '2021-04-30 14:03:24.394', NULL, 10001, NULL, 419, false, NULL, NULL, 400, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, false, true, 3, '2021-04-30 14:02:34.926', NULL, 10001, 50000, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
( 0, 55, false, true, 2, '2021-04-30 14:01:50.755', NULL, 10001, 50000, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, false, true, 1, '2021-04-30 14:01:11.052', NULL, 10001, 50000, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true)),
( 0, 56, true, false, 3, '2021-04-30 13:57:50.132', NULL, 0, 10000, 419, false, NULL, NULL, 400, true, NULL, (select id from departments where department = 'Regional Medical' and is_active = true)),
( 0, 55, true, false, 2, '2021-04-30 13:57:02.133', NULL, 0, 10000, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Compliance' and is_active = true)),
( 0, 55, true, false, 1, '2021-04-30 13:56:35.774', NULL, 0, 10000, 419, false, NULL, NULL, 400, false, NULL, (select id from departments where department = 'Country Medical' and is_active = true));


