@echo off

ECHO "Prune existing docker images"

docker image prune -f

ECHO "Remove all image dependencies"
docker rmi -f $(docker images --filter reference="*:latest" -q)

ECHO "Building ezFlow backend services ***DEVLOPMENT*** Environment"
:: build-dep-prod.sh


ECHO "Building Form Template"

docker-compose -f docker-compose-form.yml build

ECHO "Building Data Maintenance"

docker-compose -f docker-compose-data-maintenance.yml build

ECHO "Building Upload"

docker-compose -f docker-compose-upload.yml build

ECHO "Building User Activity"

docker-compose -f docker-compose-user-activity.yml build

ECHO "Deploying eZFlow backend services ***DEVLOPMENT*** Environment"

echo "Deploying Form Template"

docker tag ezflow-backend-form:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_form:v1.0.0_DEV
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_form:v1.0.0_DEV


echo "Deploying Data Maintenance"

docker tag ezflow-backend-data_maintenance:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_data_maintenance:v1.0.0_DEV
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_data_maintenance:v1.0.0_DEV

echo "Deploying Upload"

docker tag ezflow-backend-upload:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_upload:v1.0.0_DEV
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_upload:v1.0.0_DEV

echo "Deploying User Activity"

docker tag ezflow-backend-user_activity:latest ezflowlatestcr.azurecr.io/ihcp_backend_dev_user_activity:v1.0.0_DEV
docker push ezflowlatestcr.azurecr.io/ihcp_backend_dev_user_activity:v1.0.0_DEV