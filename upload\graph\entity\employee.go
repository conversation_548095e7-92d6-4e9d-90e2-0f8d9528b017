package entity

import (
	"github.com/ihcp/upload/graph/model"
	uuid "github.com/satori/go.uuid"
)

type Employee struct {
	ID              *uuid.UUID
	EmployeeCode    string
	FirstName       string
	LastName        string
	Team            string
	Country         int
	Position        string
	ActiveDirectory string
	CreatedBy       uuid.UUID
	ModifiedBy      *uuid.UUID
	IsActive        bool
	IsDeleted       bool
}

type EmployeeExcelInput struct {
	ID               *uuid.UUID
	EmployeeCode     string
	ActiveDirectory  string
	FirstName        string
	LastName         string
	Country          int
	TeamName         string
	Position         string
	IsActive         bool
	IsDeleted        bool
	ApprovalRole     string
	VeevaReferenceId string
	Email            string
	TeamID           *uuid.UUID
}

type ExportEmployeeExcel struct {
	IsExcel      bool       `json:"isExcel"`
	ID           *uuid.UUID `json:"id"`
	Country      int        `json:"country"`
	Team         string     `json:"team"`
	Position     string     `json:"position"`
	EmployeeCode string     `json:"employeeCode"`
}

type UserLogin struct {
	ActiveDirectoryName string
	Password            string
}

type UserAuth struct {
	ID              string
	ActiveDirectory string
	JWTToken        string
}

func (o *EmployeeExcelInput) ValidateEmployeeExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(o.ID) != uuid.Nil && o.Country == 0 && o.TeamName == "" && o.Position == "" && o.FirstName == "" && o.LastName == "" && o.EmployeeCode == "" {
		o.IsDeleted = false
		o.IsActive = false
	} else {
		if o.FirstName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Employee Name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.Position == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Position is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.TeamName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " TeamName is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}

	}
}
