package postgres

import (
	"context"
	"errors"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/jmoiron/sqlx"
)

func HasUserRole(inputModel *model.UserRolesInput) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	var hasValue int
	if inputModel.ID != nil {
		queryString := `select 1 from user_roles where title = $1 
	and is_active = true and is_deleted = false and id <> $2`
		err := pool.QueryRow(context.Background(), queryString, *inputModel.UserRole, *inputModel.ID).Scan(&hasValue)
		if err == nil {
			result = true
		} else {
			result = false
		}
	} else {
		queryString := `select 1 from user_roles where title = $1 
	and is_active = true and is_deleted = false`
		err := pool.QueryRow(context.Background(), queryString, *inputModel.UserRole).Scan(&hasValue)

		if err == nil {
			result = true
		} else {
			result = false
		}
	}
	return result
}

func InsertUserRoles(item *entity.UserRolesEntity) error {
	functionName := "InsertDepartmentRoles()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertUserRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New(err.Error())
	}
	defer tx.Rollback(context.Background())
	query := `INSERT INTO user_roles (value ,created_by, title, description) 
	VALUES ($1,$2,$3,$4)`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.Value, item.CreatedBy, item.UserRole, item.Description)
	_, queryErr := tx.Exec(context.Background(), query, inputArgs...)

	if queryErr != nil {
		log.Printf("%s - error: {%s}", functionName, queryErr.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New(txErr.Error())
	}
	return err
}

func UpdateUserRoles(inputModel *model.UserRolesInput, entity *entity.UserRolesEntity) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("UpdateUserRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())

	querystring := `update user_roles set last_modified = now(),`
	var inputArgs []interface{}
	if inputModel.UserRole != nil {
		querystring += `value = ? ,`
		inputArgs = append(inputArgs, entity.Value)
		querystring += `title = ? ,`
		inputArgs = append(inputArgs, entity.UserRole)
	}
	if inputModel.Description != nil {
		querystring += `description = ? ,`
		inputArgs = append(inputArgs, entity.Description)
	}

	querystring += `modified_by= ? `
	inputArgs = append(inputArgs, entity.CreatedBy)

	querystring += `WHERE id= ? and is_active = true and is_deleted = false`
	inputArgs = append(inputArgs, entity.ID)
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	_, err = tx.Exec(context.Background(), querystring, inputArgs...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", err.Error())
		return errors.New("Failed to update data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit User roles data")
	}
	return nil
}

func DeleteUserRoles(entity *entity.UserRolesEntity, userID string) error {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("DeleteUserRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New(err.Error())
	}
	defer tx.Rollback(context.Background())
	querystring := `update user_roles set modified_by = $2, last_modified = now(), is_active = false , is_deleted = true 
	where id = $1`
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return errors.New(err.Error())
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New(txErr.Error())
	}
	return err
}

func GetUserRolesInfo(input *model.UserRoleRequest) ([]entity.UserRolesFetchEntity, error) {
	functionName := "GetDepartmentRolesInfo()"
	if pool == nil {
		pool = GetPool()
	}

	queryString := `select id, value, title, description from user_roles where is_active = true and is_deleted = false`
	log.Printf("%s", functionName)
	var inputArgs []interface{}
	if input.ID != nil {
		queryString += ` AND id = ? `
		inputArgs = append(inputArgs, input.ID)
	}
	if input.UserRole != nil {
		queryString += ` AND value ILIKE ? `
		inputArgs = append(inputArgs, "%"+*input.UserRole+"%")
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)

	var userRoles []entity.UserRolesFetchEntity
	logengine.GetTelemetryClient().TrackEvent("GetUserRolesInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	} else {
		for rows.Next() {
			var userRole entity.UserRolesFetchEntity
			err := rows.Scan(&userRole.ID, &userRole.UserRole, &userRole.Title, &userRole.Description)
			if err != nil {
				log.Printf("%s - Error: %s", functionName, err.Error())
				return userRoles, err
			}
			userRoles = append(userRoles, userRole)
		}
	}
	return userRoles, nil
}
