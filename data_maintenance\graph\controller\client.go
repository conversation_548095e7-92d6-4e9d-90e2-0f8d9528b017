package controller

import (
	"context"
	"errors"
	"log"
	"reflect"
	"strings"
	"time"

	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/ihcp/data_maintenance/graph/azure"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func UpsertClientData(ctx *context.Context, inputModel model.ClientInput) *model.UpsertClientResponse {
	upsertResponse := &model.UpsertClientResponse{}
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, validationResult := mapper.MapClientModelToEntity(inputModel, userUUID)

	if !validationResult.Error {
		if postgres.ClientExistingCheckInDB(entity.ClientName, entity.CountryCode) && entity.ID == nil {
			postgres.InsertClientData(entity, userUUID, upsertResponse)
		} else if entity.ID != nil && (postgres.ClientExistingCheckInDB(entity.ClientName, entity.CountryCode) || !entity.IsActive) {
			postgres.UpdateClientData(entity, upsertResponse, userUUID)
		} else {
			upsertResponse.Error = true
			upsertResponse.Message = "This Client(" + entity.ClientName + ") Already Exist!"
			upsertResponse.ValidationErrors = validationResult.ValidationErrors
		}
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = "Client validation failed!"
		upsertResponse.ValidationErrors = validationResult.ValidationErrors
	}
	return upsertResponse

}
func ExportClientExcel(ctx *context.Context, input *model.ClientRequest) *model.ClientResponse {
	var response model.ClientResponse
	var userUUID uuid.UUID
	var err error
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	if !postgres.UserIsAdmin(*userID) {
		response.Error = true
		response.Message = "User is not authorized to access"
		return &response
	}
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		}
	}
	exportClient, err := mapper.ExportClientInputModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	ClientCount, countErr := postgres.ClientCount(exportClient.Country)
	if countErr != nil {
		response.Error = true
		response.Message = countErr.Error()
		return &response
	}
	ClientInfo, err := postgres.GetClientExcelInfo(input, exportClient)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputModels, outputModelsExcel := mapper.ExportClientInfoEntityToModel(ClientInfo)
	if input.IsExcel {
		url, err := createExportClientExcel(outputModelsExcel)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.URL = url
	} else {
		response.Data = outputModels
	}
	response.TotalCount = ClientCount
	return &response
}
func createExportClientExcel(input []*entity.ClientExcelData) (string, error) {
	if len(input) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "client"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)

	// Populate excel 1 row header columns
	typeInfo := reflect.TypeOf(entity.ClientExcelResponse{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
		headerRow = append(headerRow, strings.ToLower(typeInfo.Field(fieldIndex).Name))
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	for index, item := range input {
		rowNumber := index + 2

		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "clientExcel" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }
	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}
