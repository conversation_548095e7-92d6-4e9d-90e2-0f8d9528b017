package postgres

import (
	"context"
	"log"
	"strings"

	uuid "github.com/satori/go.uuid"
)

func UpsertExcelEmployeeAttendance(entities string, entitiesEmail string, FormAnswerID string, multiCheck bool, isApollo bool) {
	functionName := "UpsertExcelEmployeeAttendance()"
	log.Println(functionName)

	var formAnswerId uuid.UUID
	if strings.TrimSpace(FormAnswerID) != "" {
		var err error
		formAnswerId, err = uuid.FromString(FormAnswerID)
		if err != nil {
			panic(err)
		}

	}
	if CheckFormAnswerIdAndFormAttendanceId(formAnswerId) && !multiCheck {
		UpdateFormAttendance(formAnswerId)
	}

	CreateFormAttendance(entities, entitiesEmail, formAnswerId, isApollo)
}

func UpdateFormAttendance(formanswerId uuid.UUID) {
	functionName := "UpdateFormAttendance()"
	log.Println(functionName)

	pool = GetPool()
	query := `Update  form_attendances set is_active=false where form_answers_id=$1 and is_veeva=false`
	_, err := pool.Exec(context.Background(), query, formanswerId)
	if err != nil {
		panic(err)
	}
}
func CreateFormAttendance(entities string, entitiesEmail string, formanswerId uuid.UUID, isApollo bool) {
	functionName := "CreateFormAttendance()"
	log.Println(functionName)

	pool = GetPool()

	query := `insert into form_attendances(form_answers_id,event_attendances,is_active,date_created,event_attendances_internal_jsonb,is_apollo)
	values($1,$2,true,now(),$3,$4) `
	_, err := pool.Exec(context.Background(), query, formanswerId, entities, entitiesEmail, isApollo)
	if err != nil {
		panic(err)
	}
}
func CheckFormAnswerIdAndFormAttendanceId(formanswerId uuid.UUID) bool {
	functionName := "CheckFormAnswerIdAndFormAttendanceId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var check int
	var result bool
	querystring := `select 1  from form_attendances fatt where fatt.form_answers_id=$1 and is_active=true and is_veeva=false`
	err := pool.QueryRow(context.Background(), querystring, formanswerId).Scan(&check)
	if err == nil {
		result = true
	}
	return result
}
