package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/model"
)

// FormTemplateSubmit is the resolver for the formTemplateSubmit field.
func (r *mutationResolver) FormTemplateSubmit(ctx context.Context, input model.FormTemplateSubmitRequest) (*model.FormTemplateSubmitResponse, error) {
	response := controller.SaveFormTemplate(ctx, input)
	return response, nil
}

// FormAnswerSubmission is the resolver for the formAnswerSubmission field.
func (r *mutationResolver) FormAnswerSubmission(ctx context.Context, input model.FormAnswerSubmissionRequest) (*model.FormAnswerSubmissionResponse, error) {
	response := controller.SubmitFormAnswer(ctx, input)
	return response, nil
}

// RecallFormSubmission is the resolver for the recallFormSubmission field.
func (r *mutationResolver) RecallFormSubmission(ctx context.Context, input model.RecallFormApprovalInput) (*model.RecallFormApprovalResponse, error) {
	response := controller.RecallFormSubmissionForApproval(&ctx, input)
	return response, nil
}

// FormSubmissionApproval is the resolver for the formSubmissionApproval field.
func (r *mutationResolver) FormSubmissionApproval(ctx context.Context, input model.FormApprovalInput) (*model.FormApprovalResponse, error) {
	response := controller.FormSubmissionForApproval(ctx, &input)
	return response, nil
}

// ApprovalRoleSelection is the resolver for the approvalRoleSelection field.
func (r *mutationResolver) ApprovalRoleSelection(ctx context.Context, input model.RoleSelection) (*model.FormApprovalResponse, error) {
	response := controller.SubmitApprovalRole(ctx, input)
	return response, nil
}

// ApprovalRoleSelectionForExceptional is the resolver for the approvalRoleSelectionForExceptional field.
func (r *mutationResolver) ApprovalRoleSelectionForExceptional(ctx context.Context, input model.ApprovalRoleSelectionForExceptionalInput) (*model.FormApprovalResponse, error) {
	response := controller.ApprovalRoleSelectionForExceptionalLogic(ctx, input)
	return response, nil
}

// RequestorAction is the resolver for the requestorAction field.
func (r *mutationResolver) RequestorAction(ctx context.Context, input model.RequestorActionInput) (*model.FormApprovalResponse, error) {
	response := controller.RequestorActionSubmission(&ctx, input)
	return response, nil
}

// FormAnswerAttachments is the resolver for the formAnswerAttachments field.
func (r *mutationResolver) FormAnswerAttachments(ctx context.Context, input model.FormAnswerAttachment) (*model.FormAnswerAttachmentResponse, error) {
	response := controller.UploadFormAnswerAttachments(&ctx, input)
	return response, nil
}

// FormAnswerAttachmentseZClaimSyncAPI is the resolver for the formAnswerAttachmentseZClaimSyncApi field.
func (r *mutationResolver) FormAnswerAttachmentseZClaimSyncAPI(ctx context.Context, input model.FormAnswerAttachmentForEzClaim) (*model.FormAnswerAttachmentResponseForEzClaim, error) {
	response := controller.FormAnswerAttachmentSyncController(&ctx, input)
	return response, nil
}

// SaveFormAnswerAttachments is the resolver for the saveFormAnswerAttachments field.
func (r *mutationResolver) SaveFormAnswerAttachments(ctx context.Context, input model.FormAnswerAttachment) (*model.FormAnswerAttachmentResponse, error) {
	response := controller.SaveFormAnswerAttachments(&ctx, input)
	return response, nil
}

// CancelFormAnswer is the resolver for the cancelFormAnswer field.
func (r *mutationResolver) CancelFormAnswer(ctx context.Context, input model.UserformAnsID) (*model.FormAnswerforcancel, error) {
	response := controller.CancelFormAnsID(ctx, input)
	return response, nil
}

// UpdateFormAnswerActivityDate is the resolver for the updateFormAnswerActivityDate field.
func (r *mutationResolver) UpdateFormAnswerActivityDate(ctx context.Context, input model.FormSubmissionActivity) (*model.FormSubmissionResponse, error) {
	response := controller.UpdateFormAnswerActivityDate(ctx, input)
	return response, nil
}

// UpdateStatusChangeNotification is the resolver for the updateStatusChangeNotification field.
func (r *mutationResolver) UpdateStatusChangeNotification(ctx context.Context, input *model.StatusChangeNotificationInput) (*model.ResponseStatusChangeNotification, error) {
	response := controller.UpdateStatusChangeNotification(ctx, input)
	return response, nil
}

// UpdateCompletedEvents is the resolver for the updateCompletedEvents field.
func (r *mutationResolver) UpdateCompletedEvents(ctx context.Context, input model.UpdateCompletedEventsInput) (*model.UpdateCompletedEventsResponse, error) {
	response := controller.UpdateCompletedEvents(ctx, input)
	return response, nil
}

// InsertExceptionDetails is the resolver for the insertExceptionDetails field.
func (r *mutationResolver) InsertExceptionDetails(ctx context.Context, input model.ExceptionalDetailsInput) (*model.ExceptionalDetailsResponse, error) {
	response := controller.InsertexceptiondetailsintoFormAnswer(&ctx, input)
	return response, nil
}

// ChangeRequest is the resolver for the changeRequest field.
func (r *mutationResolver) ChangeRequest(ctx context.Context, input model.ChangeRequestInput) (*model.ChangeRequestOutput, error) {
	response := controller.ChangeRequestUpdate(&ctx, input)
	return response, nil
}

// FormSubmissionExceptionalApprovalForAllEvent is the resolver for the formSubmissionExceptionalApprovalForAllEvent field.
func (r *mutationResolver) FormSubmissionExceptionalApprovalForAllEvent(ctx context.Context, input model.RequestorActionInput) (*model.FormSubmissionApprovalForAllEventResponse, error) {
	response := controller.ChangeRequestUpdateForAllEvent(ctx, input)
	return &response, nil
}

// SetChangeApproval SetChangeApprovalType is the resolver for the setChangeApprovalType field.
func (r *mutationResolver) SetChangeApproval(ctx context.Context, input *model.ChangeApprovalRequest) (*model.CommonResponse, error) {
	return controller.SetChangeApproval(ctx, input)
}

// GetPDFBlobURL is the resolver for the getPdfBlobUrl field.
func (r *queryResolver) GetPDFBlobURL(ctx context.Context, input model.GetPDFBlobURLRequest) (*model.GetPDFBlobURLResponse, error) {
	response := controller.GetPDFBlobURLController(ctx, input)
	return &response, nil
}

// GetSponsorshipAgreementPDFBlobURL is the resolver for the getSponsorshipAgreementPdfBlobUrl field.
func (r *queryResolver) GetSponsorshipAgreementPDFBlobURL(ctx context.Context, input model.GetSponsorshipAgreementPDFBlobURLRequest) (*model.GetPDFBlobURLResponse, error) {
	return controller.GetSponsorshipAgreementPDFBlobURL(ctx, &input), nil
}

// GetOwnerInfo is the resolver for the getOwnerInfo field.
func (r *queryResolver) GetOwnerInfo(ctx context.Context, input model.OwnerDataRequest) (*model.OwnerDataResponse, error) {
	response := controller.GetOwnerInfo(&ctx, input)
	return &response, nil
}

// GetHcpLoi is the resolver for the getHcpLoi field.
func (r *queryResolver) GetHcpLoi(ctx context.Context, input model.HcpLoiRequest) (*model.HcpLoiResponse, error) {
	response := controller.GetHcpLoiController(ctx, input)
	return &response, nil
}

// GetAllEventIDList is the resolver for the getAllEventIdList field.
func (r *queryResolver) GetAllEventIDList(ctx context.Context, input model.GetAllEventIDListRequest) (*model.GetAllEventIDListResponse, error) {
	response := controller.GetAllEventIDListForController(ctx, input)
	return &response, nil
}

// FetchAllProductDetails is the resolver for the fetchAllProductDetails field.
func (r *queryResolver) FetchAllProductDetails(ctx context.Context, input model.FetchAllProductRequest) (*model.FetchAllProductResponse, error) {
	response := controller.FetchAllProductDetails(ctx, input)
	return response, nil
}

// GetApprovalLog is the resolver for the getApprovalLog field.
func (r *queryResolver) GetApprovalLog(ctx context.Context, input model.FormAnswerInput) (*model.ApprovalLogResponse, error) {
	response := controller.GetApprovalLogData(ctx, input)
	return response, nil
}

// FormTemplate is the resolver for the formTemplate field.
func (r *queryResolver) FormTemplate(ctx context.Context) (*model.FormTemplateResponse, error) {
	response := controller.FetchFormTemplate(ctx)
	return response, nil
}

// GetPendingApproval is the resolver for the getPendingApproval field.
func (r *queryResolver) GetPendingApproval(ctx context.Context, input *model.Limit) (*model.PendingApprovalResponse, error) {
	response := controller.FetchPendingApprovals(&ctx, input)
	return response, nil
}

// BasicInfoValidationDate is the resolver for the basicInfoValidationDate field.
func (r *queryResolver) BasicInfoValidationDate(ctx context.Context, input model.BasicInfoDateValidationRequest) (*model.BasicInfoDateValidationResponse, error) {
	response := controller.ValidationFormAnswerDate(ctx, input)
	return &response, nil
}

// GetRequesterSubmittedFormAnswerList is the resolver for the getRequesterSubmittedFormAnswerList field.
func (r *queryResolver) GetRequesterSubmittedFormAnswerList(ctx context.Context, input *model.RequesterInput) (*model.RequesterSubmittedFormAnswerListResponse, error) {
	response := controller.FetchRequesterSubmittedFormAnswer(&ctx, input)
	return response, nil
}

// GetFormAnswer is the resolver for the getFormAnswer field.
func (r *queryResolver) GetFormAnswer(ctx context.Context, input model.FormAnswerInput) (*model.FormAnswerResponse, error) {
	response := controller.FetchFormAnswer(ctx, input)
	return response, nil
}

// GetFormAnswerForApollo is the resolver for the getFormAnswerForApollo field.
func (r *queryResolver) GetFormAnswerForApollo(ctx context.Context, input model.FormAnswerInputForApollo) (*model.FormAnswerResponseForApollo, error) {
	response := controller.FetchFormAnswerForApollo(ctx, input)
	return response, nil
}

// GetApprovalRolesByActivities is the resolver for the getApprovalRolesByActivities field.
func (r *queryResolver) GetApprovalRolesByActivities(ctx context.Context, input model.ActivityInputs) (*model.ApprovalRolesUserSelectionResponse, error) {
	response := controller.FetchApprovalRolesByActivities(ctx, input)
	return response, nil
}

// GetTotalEvents is the resolver for the getTotalEvents field.
func (r *queryResolver) GetTotalEvents(ctx context.Context) (*model.TotalEventsResponse, error) {
	response := controller.FetchTotalEvents(&ctx)
	return response, nil
}

// GetApprovalTrailData is the resolver for the getApprovalTrailData field.
func (r *queryResolver) GetApprovalTrailData(ctx context.Context, input model.FormAnswerIDInput) (*model.ApprovalTrailFormSubmissionResponse, error) {
	response := controller.FetchApprovalTrailDataForAnActivity(ctx, input)
	return response, nil
}

// GetApolloApprovalTrailData is the resolver for the getApolloApprovalTrailData field.
func (r *queryResolver) GetApolloApprovalTrailData(ctx context.Context, input model.ApolloFormAnswerIDInput) (*model.ApolloApprovalTrailFormSubmissionResponse, error) {
	response := controller.FetchApolloApprovalTrailDataForAnActivity(ctx, input)
	return response, nil
}

// GetFormAnswerIDByEventID is the resolver for the getFormAnswerIdByEventId field.
func (r *queryResolver) GetFormAnswerIDByEventID(ctx context.Context, input model.FormAnswerByEventIDInput) (*model.FormAnswerByEventIDResponse, error) {
	response := controller.FormAnswerIDByEventID(ctx, input)
	return response, nil
}

// GetApproverDataByFormAnsID is the resolver for the getApproverDataByFormAnsId field.
func (r *queryResolver) GetApproverDataByFormAnsID(ctx context.Context, input model.ApproverformAnsID) (*model.FetchapproverDataByFormAnsID, error) {
	response := controller.FetchApproverDataByFormAnsId(ctx, input)
	return response, nil
}

// GetNotificationStatusForUser is the resolver for the getNotificationStatusForUser field.
func (r *queryResolver) GetNotificationStatusForUser(ctx context.Context, input *model.NotificationKeyLimit) (*model.GetNotificationStatusForUserResponse, error) {
	response := controller.GetNotificationStatus(ctx, input)
	return response, nil
}

// GetActivitiesAndEventsSelection is the resolver for the getActivitiesAndEventsSelection field.
func (r *queryResolver) GetActivitiesAndEventsSelection(ctx context.Context) (*model.GetActivitiesAndEventsSelectionResponse, error) {
	response := controller.FetchActivitiesAndEventsSelection(ctx)
	return response, nil
}

// GetActivitiesAndEventSelectionAllDropDownValues is the resolver for the getActivitiesAndEventSelectionAllDropDownValues field.
func (r *queryResolver) GetActivitiesAndEventSelectionAllDropDownValues(ctx context.Context) (*model.GetActivitiesAndEventSelectionAllDropDownValuesResponse, error) {
	response := controller.FetchActivitiesAndEventsSelectionForAllDropdownValue(ctx)
	return response, nil
}

// GetEmployeeManagementFilter is the resolver for the getEmployeeManagementFilter field.
func (r *queryResolver) GetEmployeeManagementFilter(ctx context.Context) (*model.GetEmployeeManagementFilterResponse, error) {
	response := controller.FetchEmployeeManagementFilter(&ctx)
	return response, nil
}

// GetRequestorForCountry is the resolver for the getRequestorForCountry field.
func (r *queryResolver) GetRequestorForCountry(ctx context.Context, input *model.GetRequestorForCountryInput) (*model.GetRequestorForCountryResponse, error) {
	response := controller.FetchRequestorDataForCountry(&ctx, input)
	return response, nil
}

// GetAttachmentZip is the resolver for the getAttachmentZip field.
func (r *queryResolver) GetAttachmentZip(ctx context.Context, input *model.GetAttachmentZipInput) (*model.GetAttachmentZipResponse, error) {
	response := controller.FetchAttachmentZip(&ctx, input)
	return response, nil
}

// GetTypeApprovalList is the resolver for the getTypeApprovalList field.
func (r *queryResolver) GetTypeApprovalList(ctx context.Context, input model.GetTypeApprovalListInput) (*model.GetTypeApprovalListResponse, error) {
	response := controller.FetchTypeApprovalList(&ctx, input)
	return response, nil
}

// ExcelExtractTrailData is the resolver for the excelExtractTrailData field.
func (r *queryResolver) ExcelExtractTrailData(ctx context.Context, input model.ExcelExtractTrailDataRequest) (*model.ExcelExtractTrailDataResponse, error) {
	response := controller.ExcelExtractTrailDataController(&ctx, input)
	return response, nil
}

// GetFormAnswerDetailsForLinkedEvent is the resolver for the getFormAnswerDetailsForLinkedEvent field.
func (r *queryResolver) GetFormAnswerDetailsForLinkedEvent(ctx context.Context, input model.FormAnswerInputForLinkedEvent) (*model.FormAnswerResponseForLinkedEvent, error) {
	response := controller.FetchFormAnswerDetailsForLinkedEvent(ctx, input)
	return response, nil
}

// GetFmvAuditlogs is the resolver for the getFmvAuditlogs field.
func (r *queryResolver) GetFmvAuditlogs(ctx context.Context, input model.GetFmvAuditlogsRequest) (*model.GetFmvAuditlogsResponse, error) {
	response := controller.GetFmvAuditlogsController(&ctx, input)
	return response, nil
}

// GetlevelOfInfluenceValidation is the resolver for the getlevelOfInfluenceValidation field.
func (r *queryResolver) GetlevelOfInfluenceValidation(ctx context.Context, input model.LevelOfInfluenceValidationRequest) (*model.LevelOfInfluenceValidationResponse, error) {
	response := controller.GetlevelOfInfluenceValidationController(&ctx, input)
	return response, nil
}

// GetCalculatedExpense is the resolver for the getCalculatedExpense field.
func (r *queryResolver) GetCalculatedExpense(ctx context.Context, input model.CalculateExpenseRequest) (*model.CalculateExpenseResponse, error) {
	response := controller.GetCalculatedExpenseController(&ctx, input)
	return response, nil
}

// GetTotalHcpExpense is the resolver for the getTotalHcpExpense field.
func (r *queryResolver) GetTotalHcpExpense(ctx context.Context, input model.TotalHcpExpenseRequest) (*model.TotalHcpExpenseResponse, error) {
	response := controller.GetTotalHcpExpenseController(&ctx, input)
	return response, nil
}

// GetTotalEventExpense is the resolver for the getTotalEventExpense field.
func (r *queryResolver) GetTotalEventExpense(ctx context.Context, input model.TotalEventExpenseRequest) (*model.TotalEventExpenseResponse, error) {
	response := controller.GetTotalEventExpenseController(&ctx, input)
	return response, nil
}

// GetCalculateTotalEventExpense is the resolver for the getCalculateTotalEventExpense field.
func (r *queryResolver) GetCalculateTotalEventExpense(ctx context.Context, input model.CalculatetotalEventExpenseRequest) (*model.TotalEventExpenseResponse, error) {
	response := controller.GetCalculateTotalEventExpenseController(&ctx, input)
	return response, nil
}

// GetCalculateTotalHcpExpense is the resolver for the getCalculateTotalHcpExpense field.
func (r *queryResolver) GetCalculateTotalHcpExpense(ctx context.Context, input model.CalculateTotalHcpExpenseRequest) (*model.TotalHcpExpenseResponse, error) {
	response := controller.GetCalculateTotalHcpExpenseController(&ctx, input)
	return response, nil
}

// GetDurationFormat is the resolver for the getDurationFormat field.
func (r *queryResolver) GetDurationFormat(ctx context.Context, input model.GetDurationFormatRequest) (*model.GetDurationFormatResponse, error) {
	response := controller.GetDurationFormatController(&ctx, input)
	return response, nil
}

// GetconversionAmount is the resolver for the getconversionAmount field.
func (r *queryResolver) GetconversionAmount(ctx context.Context, input model.ConversionAmountRequest) (*model.ConversionAmountResponse, error) {
	response := controller.GetconversionAmountController(&ctx, input)
	return response, nil
}

// GetUSDValue is the resolver for the getUSDValue field.
func (r *queryResolver) GetUSDValue(ctx context.Context, input model.USDValueRequest) (*model.USDValueResponse, error) {
	response := controller.GetCurrencyConversionController(&ctx, input)
	return response, nil
}

// HcpEngagementValidation is the resolver for the hcpEngagementValidation field.
func (r *queryResolver) HcpEngagementValidation(ctx context.Context, input model.HcpEngagementValidationRequest) (*model.HcpEngagementValidationResponse, error) {
	response := controller.HcpEngagementValidationController(&ctx, input)
	return response, nil
}
