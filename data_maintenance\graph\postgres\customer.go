package postgres

import (
	"context"
	"log"
	"strings"

	codeController "github.com/ihcp/code/controller"
	codeEntity "github.com/ihcp/code/entity"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func createCustomer(tx pgx.Tx, item *entity.CustomerExcelInput, genders map[string]codeEntity.Code) error {
	functionName := "createCustomer()"
	log.Println(functionName)
	gender := genders[item.Gender].ID
	queryString := `INSERT INTO customer (name,country,city,customer_number,gender,v_sp_desc,is_active,created_by) VALUES($1,$2,$3,$4,$5,$6,$7,$8) RETURNING(id)`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.Name, item.CountryNo, item.City, item.CustomerNo, gender, item.SpDesc, true, uuid.Nil)
	err := tx.QueryRow(context.Background(), queryString, inputArgs...).Scan(&item.ID)
	logengine.GetTelemetryClient().TrackEvent("createCustomer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
	}
	return err
}

func GetCustomerExcelInfo(customerInput *model.CustomerExcelRequest, input *entity.ExportCustomerExcel) ([]entity.CustomerExcel, error) {
	functionName := "GetCustomerExcelInfo()"
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["country"]
	phCountryCodeID := code["ph"].ID
	phCountryCodeIDZP := code["phzpc"].ID
	queryString := `	SELECT customer.id as id, customer.customer_number as customerNumber, customer.name as customerName, customer.city as city, customer.v_sp_desc as vSpDesc, customer.gender as gender,
	customer.country as country, team.name as team, customer.speaker_weight as SpeakerWeight,
	tm.v_position as vPosition, 
	emp.first_name as empFirstName,
	emp.last_name as empLastName,
	emp.active_directory,
	customer.cmsl_class as cmslClass,
	customer.is_active,
	customer.veeva_reference_id ,customer.organization,customer.account_type
    FROM customer  
	LEFT JOIN team_member_customer tmc on customer.id = tmc.customer 
	AND ( (tmc.is_active = true or tmc.is_active is null ) AND ( tmc.is_deleted = false or tmc.is_deleted is null ) )
	LEFT JOIN team_member tm on tm.id = tmc.team_member 
	AND ( (tm.is_active = true or tm.is_active  is null )  AND ( tm.is_deleted = false or tm.is_deleted is null ) )
	LEFT JOIN team on team.id = tm.team
    LEFT JOIN "user" emp on emp.id = tm.employee
	WHERE customer.is_deleted = false`
	log.Printf("%s", functionName)
	var inputArgs []interface{}

	if input.ID != nil && *(input).ID != uuid.Nil {
		queryString += ` AND customer.id = ? `
		inputArgs = append(inputArgs, input.ID)
	}
	if input.IsForm == true {
		queryString += ` AND ((customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country <> ? AND customer.country <> ?)) AND customer.is_active = true `
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)

	} else {
		if input.Country > 0 {
			queryString += ` AND customer.country = ? `
			inputArgs = append(inputArgs, input.Country)
		}
	}
	if input.SearchItem != "" {
		queryString += ` AND (team.name ilike ? or tm.v_position ILIKE ? 
		or emp.first_name ilike ? or emp.last_name ilike ?
		or  customer.customer_number ilike ? or customer.name ilike ? 
		or customer.v_sp_desc ilike ? or customer.cmsl_class ilike ? or customer.veeva_reference_id ilike ?
		or customer.organization ilike ? or customer.account_type ilike ?
		or customer.city ILIKE ? or customer.gender in (select id from code where value ILIKE ?)) `
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.IsActive != nil {
			queryString += ` AND customer.is_active = ? `
			inputArgs = append(inputArgs, input.IsActive)
		}
		if input.Team != "" {
			queryString += ` AND team.name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Team+"%")
		}
		if input.VPosition != "" {
			queryString += ` AND tm.v_position ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VPosition+"%")
		}
		if input.EmpFirstName != "" {
			queryString += ` AND emp.first_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.EmpFirstName+"%")
		}
		if input.EmpLastName != "" {
			queryString += ` AND emp.last_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.EmpLastName+"%")
		}
		if input.CustomerNumber != "" {
			queryString += ` AND customer.customer_number = ? `
			inputArgs = append(inputArgs, input.CustomerNumber)
		}
		if input.CustomerName != "" {
			queryString += ` AND customer.name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.CustomerName+"%")
		}
		if input.VSpDesc != "" {
			queryString += ` AND customer.v_sp_desc ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VSpDesc+"%")
		}
		if input.CmslClass != "" {
			queryString += ` AND customer.cmsl_class ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.CmslClass+"%")
		}
		if input.City != "" {
			queryString += ` AND customer.city ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.City+"%")
		}
		if input.VeevaReferenceId != "" {
			queryString += ` AND customer.veeva_reference_id ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VeevaReferenceId+"%")
		}
		if input.Organization != "" {
			queryString += ` AND customer.organization ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Organization+"%")
		}
		if input.AccountType != "" {
			queryString += ` AND customer.account_type ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.AccountType+"%")
		}
		if input.Gender > 0 {
			queryString += ` AND customer.gender = ? `
			inputArgs = append(inputArgs, input.Gender)
		}
	}
	if customerInput.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY customer.date_created desc, id asc`
	}
	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ? `
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	var customers []entity.CustomerExcel
	logengine.GetTelemetryClient().TrackEvent("GetCustomerExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	} else {
		for rows.Next() {
			var customer entity.CustomerExcel
			err := rows.Scan(&customer.ID, &customer.CustomerNumber, &customer.CustomerName, &customer.City, &customer.VSpDesc, &customer.Gender, &customer.Country, &customer.Team, &customer.SpeakerWeight, &customer.VPosition, &customer.EmpFirstName, &customer.EmpLastName, &customer.EmpActiveDirectoryName, &customer.CmslClass, &customer.IsActive, &customer.VeevaReferenceID, &customer.Organization, &customer.AccountType)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - Error: %s", functionName, err.Error())
				return customers, err
			}
			customers = append(customers, customer)
		}
	}
	return customers, nil
}

func mapTeamMemberCustomerFromCustomer(teamMemberId, customerId uuid.UUID, cmslClass int) *entity.TeamMemberCustomer {

	return &entity.TeamMemberCustomer{
		ID:         nil,
		TeamMember: &teamMemberId,
		Customer:   &customerId,
		CMSLClass:  cmslClass,
		IsActive:   false,
		IsDeleted:  false,
	}
}

func CheckSpeakernamevalid(Speakername string, country int, Specialty string, city string, organization string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	query := `select 1 from customer c where lower(c."name") =$1 and country =$2 and is_active =true and is_deleted =false and REPLACE(lower(c.city),' ','') = $3 and REPLACE(lower(c.v_sp_desc),' ','') = $4 and REPLACE(lower(c.organization),' ','') = $5`
	err := pool.QueryRow(context.Background(), query, strings.ToLower(Speakername), country, strings.ToLower(strings.ReplaceAll(city, " ", "")), strings.ToLower(strings.ReplaceAll(Specialty, " ", "")), strings.ToLower(strings.ReplaceAll(organization, " ", ""))).Scan(&hasValue)
	if err == nil {
		return false
	}
	return true
}

func InsertCustomerData(entity *entity.CustomerUpserInput, userUUID uuid.UUID, response *model.UpsertCustomerResponse) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertCustomerData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	var isActive bool
	if entity.IsDeleted {
		isActive = false
	} else {
		isActive = true
	}
	if entity.TeamName != "" {
		var customerID string
		customerQuerystring := `INSERT INTO customer (name, country, customer_number, gender, created_by, v_sp_desc, city,speaker_weight,cmsl_class,is_active,is_deleted,organization,account_type)VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,false,$11,$12) RETURNING(id)`
		err = tx.QueryRow(context.Background(), customerQuerystring, entity.Name, entity.Country, entity.CustomerNo, entity.GenderID, userUUID, entity.SpDesc, entity.City, entity.SpeakerWeight, entity.CMSLClass, isActive, entity.Organization, entity.AccountType).Scan(&customerID)

		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to roll back Customer data"
			response.Error = true
			return
		}
		QueryString := `INSERT INTO team_member_customer (customer, team_member, created_by) VALUES($1, $2, $3)`
		_, err = tx.Exec(context.Background(), QueryString, customerID, entity.TeamMemberID, userUUID)

		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = err.Error()
			response.Error = true
			return
		}
	} else {
		customerQuerystring := `INSERT INTO customer (name, country, customer_number, gender, created_by, v_sp_desc, city,speaker_weight,cmsl_class,is_active,is_deleted,organization,account_type)VALUES($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,false,$11,$12) `

		_, err = tx.Exec(context.Background(), customerQuerystring, entity.Name, entity.Country, entity.CustomerNo, entity.GenderID, userUUID, entity.SpDesc, entity.City, entity.SpeakerWeight, entity.CMSLClass, isActive, entity.Organization, entity.AccountType)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = err.Error()
			response.Error = true
			return
		}
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit customer and team member customer data"
		response.Error = true
		return
	}
	response.Message = "Customer successfully inserted"
	response.Error = false
}

func DeleteCustomerData(entity *entity.CustomerUpserInput, userUUID uuid.UUID, response *model.UpsertCustomerResponse) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("DeleteCustomerData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	defer tx.Rollback(context.Background())
	timenow := util.GetCurrentTime()
	querystring := "UPDATE customer SET is_active = false, modified_by = $2, last_modified = $3 WHERE id = $1"
	_, err = tx.Exec(context.Background(), querystring, entity.ID, userUUID, timenow)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to deactivate in Customer"
		response.Error = true
		return
	}
	if IsTeamMembersCustomerExists(entity.ID) {
		classQueryString := "update team_member_customer set is_active=false, is_deleted = true, modified_by = $2, last_modified = $3 where customer = $1 "
		_, err = tx.Exec(context.Background(), classQueryString, entity.ID, userUUID, timenow)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			response.Message = "Failed to delete in team member customer"
			response.Error = true
			return
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to commit customer and team member customer data"
		response.Error = true
		return
	}

	response.Message = "Customer successfully deactivated"
	response.Error = false
}

func UpdateCustomerData(entity *entity.CustomerUpserInput, userUUID uuid.UUID, response *model.UpsertCustomerResponse) {
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("UpdateCustomerData query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to begin transaction"
		response.Error = true
	}
	var isActive bool
	if entity.IsDeleted {
		isActive = false
	} else {
		isActive = true
	}

	defer tx.Rollback(context.Background())
	queryString := `UPDATE customer SET last_modified = now(), `
	var inputArgs []interface{}
	if entity.Name != "" {
		queryString += `name = ?, `
		inputArgs = append(inputArgs, entity.Name)
	}
	if entity.CustomerNo != "" {
		queryString += `customer_number = ?, `
		inputArgs = append(inputArgs, entity.CustomerNo)
	}
	if entity.GenderID > 0 {
		queryString += `gender = ?, `
		inputArgs = append(inputArgs, entity.GenderID)
	}
	if entity.City != "" {
		queryString += `city = ?, `
		inputArgs = append(inputArgs, entity.City)
	}
	if entity.SpDesc != "" {
		queryString += `v_sp_desc = ?, `
		inputArgs = append(inputArgs, entity.SpDesc)
	}
	if entity.SpeakerWeight > 0 {
		queryString += `speaker_weight = ?, `
		inputArgs = append(inputArgs, entity.SpeakerWeight)
	}
	if entity.CMSLClass != "" {
		queryString += `cmsl_class = ?, `
		inputArgs = append(inputArgs, entity.CMSLClass)
	}
	if entity.Organization != "" {
		queryString += `organization =?, `
		inputArgs = append(inputArgs, entity.Organization)

	}
	if entity.AccountType != "" {
		queryString += `account_type =?, `
		inputArgs = append(inputArgs, entity.AccountType)

	}
	queryString += `modified_by= ? ,`
	inputArgs = append(inputArgs, userUUID)
	queryString += `is_active= ? ,`
	inputArgs = append(inputArgs, isActive)
	queryString += `country= ? `
	inputArgs = append(inputArgs, entity.Country)
	queryString += `WHERE id= ? AND is_deleted = false`
	inputArgs = append(inputArgs, entity.ID)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)

	_, err = tx.Exec(context.Background(), queryString, inputArgs...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		response.Message = "Failed to update in Customer"
		response.Error = true
		return
	}

	if entity.TeamName != "" {
		previousTeamMember := GetTeamMemberByCustomerId(entity.ID)
		if previousTeamMember == nil {
			err = UpsertTeamMemberCustomerForExcel(tx, mapTeamMemberCustomerFromCustomer(*entity.TeamMemberID, *entity.ID, entity.CMSLClassID), userUUID)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				response.Message = err.Error()
				response.Error = true
				return
			}
		} else {
			if previousTeamMember.TeamMember != entity.TeamMemberID {
				classQueryString := "update team_member_customer set is_active=false, is_deleted = true, modified_by = $3, last_modified = now() where customer = $1 and team_member = $2"
				_, err = tx.Exec(context.Background(), classQueryString, entity.ID, entity.TeamMemberID, userUUID)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					response.Message = "Failed to delete in team member customer"
					response.Error = true
					return
				}
				UpsertTeamMemberCustomerForExcel(tx, mapTeamMemberCustomerFromCustomer(*entity.TeamMemberID, *entity.ID, entity.CMSLClassID), userUUID)
			}
			if previousTeamMember.CMSLClass != entity.CMSLClassID {
				classQueryString := "update team_member_customer set modified_by = $2, last_modified = now() where id = $1"
				_, err = tx.Exec(context.Background(), classQueryString, entity.ID, userUUID)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					response.Message = "Failed to update in team member customer"
					response.Error = true
					return
				}
			}

		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		response.Message = "Failed to customer and team member customer data"
		response.Error = true
		return
	}
	response.Message = "Customer successfully updated"
	response.Error = false
}

func HasCustomerID(customerID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from customer where id = $1 AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, customerID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

// func CheckCustomerNumberUnique(customerNumber string, customerID *string) bool {
// 	functionName := "CheckCustomerNumberUnique()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var result bool
// 	if customerID != nil {
// 		queryString := `select 1 from customer where id <> $2 AND customer_number = $1 AND is_deleted = false`
// 		var hasValue int
// 		err := pool.QueryRow(context.Background(), queryString, customerNumber, customerID).Scan(&hasValue)
// 		if err == nil {
// 			result = true
// 		}
// 	} else {
// 		queryString := `select 1 from customer where customer_number = $1 AND is_deleted = false`
// 		var hasValue int
// 		err := pool.QueryRow(context.Background(), queryString, customerNumber).Scan(&hasValue)
// 		if err == nil {
// 			result = true
// 		}
// 	}
// 	return result
// }

func GetCustomerNumberByID(ID string) (string, error) {
	functionName := "GetCustomerNumberByID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var customerNumber string
	query := `select customer_number from customer 
	where id = $1 and is_deleted = false`
	err := pool.QueryRow(context.Background(), query, ID).Scan(&customerNumber)
	logengine.GetTelemetryClient().TrackEvent("GetCustomerNumberByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}
	return customerNumber, nil
}

func CustomerCount(country int, isForm bool) (int, error) {
	functionName := "CustomerCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["country"]
	phCountryCodeID := code["ph"].ID
	phCountryCodeIDZP := code["phzpc"].ID
	var inputArgs []interface{}
	query := `SELECT count(customer.id) as id FROM customer WHERE customer.is_deleted = false`
	if isForm == true {
		query += ` AND ((customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country <> ? AND customer.country <> ?)) AND customer.is_active = true `
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)

	} else {
		if country > 0 {
			query += ` AND customer.country = ? `
			inputArgs = append(inputArgs, country)
		}
	}
	var result int
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	err := pool.QueryRow(context.Background(), query, inputArgs...).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("CustomerCount query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return result, nil
}
func GetCustomerInfo(customerInput *model.CustomerInfoRequest, input *entity.ExportCustomerExcel) ([]entity.CustomerExcel, error) {
	functionName := "GetCustomerExcelInfo()"
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["country"]
	phCountryCodeID := code["ph"].ID
	phCountryCodeIDZP := code["phzpc"].ID
	queryString := `	SELECT customer.id as id, customer.customer_number as customerNumber, customer.name as customerName, customer.city as city, customer.v_sp_desc as vSpDesc, customer.gender as gender,
	customer.country as country, team.name as team, customer.speaker_weight as SpeakerWeight,
	tm.v_position as vPosition, 
	emp.first_name as empFirstName,
	emp.last_name as empLastName,
	emp.active_directory,
	customer.cmsl_class as cmslClass,
	customer.is_active,
	customer.veeva_reference_id ,customer.organization,customer.account_type
    FROM customer  
	LEFT JOIN team_member_customer tmc on customer.id = tmc.customer 
	AND ( (tmc.is_active = true or tmc.is_active is null ) AND ( tmc.is_deleted = false or tmc.is_deleted is null ) )
	LEFT JOIN team_member tm on tm.id = tmc.team_member 
	AND ( (tm.is_active = true or tm.is_active  is null )  AND ( tm.is_deleted = false or tm.is_deleted is null ) )
	LEFT JOIN team on team.id = tm.team
    LEFT JOIN "user" emp on emp.id = tm.employee
	WHERE customer.is_deleted = false and customer.is_active=true  and customer.account_type in ('Professional','Non-Professional')`
	log.Printf("%s", functionName)
	var inputArgs []interface{}

	if input.CustomerIDs != nil && len((input).CustomerIDs) > 0 {
		queryString += ` AND customer.id IN ( `
		// inputArgs = append(inputArgs, input.ID)
		placeholders := make([]string, len(input.CustomerIDs))
		for i, id := range input.CustomerIDs {
			if id != nil {
				placeholders[i] = "?"
				inputArgs = append(inputArgs, *id)
			}
		}
		queryString += strings.Join(placeholders, ", ") + ")"
	}
	if input.ID != nil && *(input).ID != uuid.Nil {
		queryString += ` AND customer.id = ? `
		inputArgs = append(inputArgs, input.ID)
	}
	if input.IsForm == true {
		queryString += ` AND ((customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country = ? AND customer.speaker_weight IS NOT NULL) OR 
		(customer.country <> ? AND customer.country <> ?)) AND customer.is_active = true `
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)
		inputArgs = append(inputArgs, phCountryCodeID)
		inputArgs = append(inputArgs, phCountryCodeIDZP)

	} else {
		if input.Country > 0 {
			queryString += ` AND customer.country = ? `
			inputArgs = append(inputArgs, input.Country)
		}
	}
	if input.SearchItem != "" {
		queryString += ` AND (team.name ilike ? or tm.v_position ILIKE ? 
		or emp.first_name ilike ? or emp.last_name ilike ?
		or  customer.customer_number ilike ? or customer.name ilike ? 
		or customer.v_sp_desc ilike ? or customer.cmsl_class ilike ? or customer.veeva_reference_id ilike ?
		or customer.organization ilike ? or customer.account_type ilike ?
		or customer.city ILIKE ? or customer.gender in (select id from code where value ILIKE ?)) `
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
		inputArgs = append(inputArgs, "%"+input.SearchItem+"%")
	} else {
		if input.IsActive != nil {
			queryString += ` AND customer.is_active = ? `
			inputArgs = append(inputArgs, input.IsActive)
		}
		if input.Team != "" {
			queryString += ` AND team.name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Team+"%")
		}
		if input.VPosition != "" {
			queryString += ` AND tm.v_position ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VPosition+"%")
		}
		if input.EmpFirstName != "" {
			queryString += ` AND emp.first_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.EmpFirstName+"%")
		}
		if input.EmpLastName != "" {
			queryString += ` AND emp.last_name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.EmpLastName+"%")
		}
		if input.CustomerNumber != "" {
			queryString += ` AND customer.customer_number = ? `
			inputArgs = append(inputArgs, input.CustomerNumber)
		}
		if input.CustomerName != "" {
			queryString += ` AND customer.name ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.CustomerName+"%")
		}
		if input.VSpDesc != "" {
			queryString += ` AND customer.v_sp_desc ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.VSpDesc+"%")
		}
		if input.CmslClass != "" {
			queryString += ` AND customer.cmsl_class ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.CmslClass+"%")
		}
		if input.City != "" {
			queryString += ` AND customer.city ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.City+"%")
		}
		if input.VeevaReferenceId != "" {
			queryString += ` AND customer.veeva_reference_id = ? `
			inputArgs = append(inputArgs, input.VeevaReferenceId)
		}
		if input.Organization != "" {
			queryString += ` AND customer.organization ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.Organization+"%")
		}
		if input.AccountType != "" {
			queryString += ` AND customer.account_type ILIKE ? `
			inputArgs = append(inputArgs, "%"+input.AccountType+"%")
		}
		if input.Gender > 0 {
			queryString += ` AND customer.gender = ? `
			inputArgs = append(inputArgs, input.Gender)
		}
	}
	if customerInput.Sort != nil && len(input.Sort) > 0 {
		queryString += `ORDER BY `
		for i, val := range input.Sort {
			queryString += val.Column + " " + val.Sort
			if i < len(input.Sort)-1 {
				queryString += `, `
			}
		}

	} else {
		queryString += ` ORDER BY customer.date_created desc, id asc`
	}
	if input.Limit > 0 {
		queryString += ` LIMIT ?
		OFFSET ? `
		inputArgs = append(inputArgs, input.Limit)
		inputArgs = append(inputArgs, input.Offset)
	}

	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	var customers []entity.CustomerExcel
	logengine.GetTelemetryClient().TrackEvent("GetCustomerExcelInfo query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	} else {
		for rows.Next() {
			var customer entity.CustomerExcel
			err := rows.Scan(&customer.ID, &customer.CustomerNumber, &customer.CustomerName, &customer.City, &customer.VSpDesc, &customer.Gender, &customer.Country, &customer.Team, &customer.SpeakerWeight, &customer.VPosition, &customer.EmpFirstName, &customer.EmpLastName, &customer.EmpActiveDirectoryName, &customer.CmslClass, &customer.IsActive, &customer.VeevaReferenceID, &customer.Organization, &customer.AccountType)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - Error: %s", functionName, err.Error())
				return customers, err
			}
			customers = append(customers, customer)
		}
	}
	return customers, nil
}
