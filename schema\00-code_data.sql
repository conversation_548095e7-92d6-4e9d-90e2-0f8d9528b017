INSERT INTO code (id,is_active,is_deleted,category,sequence_no,title,value,description,last_modified) VALUES
(50,true,false,'ApprovedStatus',NULL,'Pending','pending','Pending',NULL)
,(51,true,false,'ApprovedStatus',NULL,'Approved',' approved','Approved',NULL)
,(52,true,false,'ApprovedStatus',NULL,'Rejected','rejected','Rejected',NULL)
;


INSERT INTO code (id,is_active,is_deleted,category,sequence_no,title,value,description,last_modified) VALUES
(53,true,false,'Group',NULL,'Local','local','Local',NULL)
,(54,true,false,'Group',NULL,'Regional',' regional','Regional',NULL)
;


	update code set title ='Commercial / Education', value ='commercial/education' , 
    description ='Commercial / Education' where id = 89
    
INSERT INTO test.code (category,sequence_no,title,value,description,last_modified,id) VALUES
 ('ServerIP',NULL,'ServerIP','************:25','************:25',null,129);


