package mapper

import (
	"errors"
	"log"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapEventTypeAccessPermissionModelToEntity(inputModel *model.EventTypeAccessPermissionUpsertInput, userUUID uuid.UUID) (*entity.EventTypeAccessPermissionUpsert, error) {

	log.Println("MapEventTypeAccessPermissionModelToEntity()")
	var entity entity.EventTypeAccessPermissionUpsert

	if inputModel.ID != nil && strings.TrimSpace(*inputModel.ID) != "" {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err == nil {
			entity.ID = &uuid
			if !postgres.HasEventTypeAccessPermissionID(entity.ID) {
				return nil, errors.New("EventTypeAccessPermissionID does not exist!")
			}
		} else {
			return nil, errors.New("EventTypeAccessPermissionID format is invalid!")
		}
	} else {
		if inputModel.UserRole == nil {
			return nil, errors.New("Please provide User Roles!")
		} else if inputModel.EventType == nil {
			return nil, errors.New("Please provide Event Type!")
		}
	}
	countryID, err := postgres.GetUserCountryByID(userUUID)
	if err != nil {
		return nil, errors.New("Country does not exists!")
	}
	entity.Country = countryID
	if inputModel.ID != nil && !postgres.IsEventTypeAccessPermissionCreatedBy(countryID, inputModel.ID) {
		return nil, errors.New("User not allowed to take action!")
	}

	entity.CreatedBy = &userUUID
	if inputModel.EventType != nil {
		eventID, err := postgres.FetchEventTypeIDByValue(*inputModel.EventType)
		if err != nil {
			return nil, errors.New("Unable to fetch event type id!")
		}
		entity.EventType = eventID
	}
	var userRoles []string
	if inputModel.UserRole != nil {
		for _, role := range inputModel.UserRole {
			// userRoleID := codes[*role].ID
			userRoleID, err := postgres.GetRoleIDByValueInUserRoles(*role)
			if err != nil {
				return nil, errors.New("Invalid User Roles!")
			} else {
				userRoles = append(userRoles, userRoleID)
			}
		}
		entity.UserRole = userRoles
	}
	codes := codeController.GetValueKeyCodes()["userrole"]
	var userRolesForApprover []int
	if inputModel.UserRole != nil {
		for _, role := range inputModel.UserRole {
			userRoleIDApprover := codes[*role].ID
			// userRoleID := postgres.GetRoleIDByValueInUserRoles(*role)
			userRolesForApprover = append(userRolesForApprover, userRoleIDApprover)
		}
		entity.UserRoleForApprover = userRolesForApprover
	}

	if inputModel.IsDelete != nil {
		entity.IsDelete = *inputModel.IsDelete
	}
	return &entity, nil

}

func FetchEventTypeAccessPermissionEntityToModel(input []entity.EventAccessTypeEntity) []*model.EventTypeAccessPermission {

	var outEntity []*model.EventTypeAccessPermission
	for _, eventTypeData := range input {
		var eventData model.EventTypeAccessPermission
		if eventTypeData.CountryValue.String != "" {
			countryVal := eventTypeData.CountryValue.String
			eventData.CountryValue = &countryVal
		}
		if eventTypeData.CountryTitle.String != "" {
			countryTitle := eventTypeData.CountryTitle.String
			eventData.Country = &countryTitle
		}
		eventData.EventTypeName = eventTypeData.EventTypeName
		eventData.EventTypeValue = eventTypeData.EventTypeValue
		eventData.ID = eventTypeData.ID
		var userValues []*model.UserRole
		for _, val := range eventTypeData.UserRoleID {
			userRolesTitleValue, _ := postgres.GetUserRoleValueTitle(val)
			var userData model.UserRole
			userData.UserRoleTitle = userRolesTitleValue.UserRoleTitle
			userData.UserRoleValue = userRolesTitleValue.UserRoleValue
			userValues = append(userValues, &userData)
		}
		eventData.UserRoles = userValues
		outEntity = append(outEntity, &eventData)
	}
	return outEntity
}
