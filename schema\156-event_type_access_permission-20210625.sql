delete from event_type_access_permission where country = 185


INSERT INTO event_type_access_permission 
(user_role, country, is_active, is_deleted, created_by, event_type, user_role_id) values
( '{68,69,46,71,72}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 94,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' and is_active = true and is_deleted = false))),
('{69,46,71,72}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 93,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'clustercomplianceofficer' or value = 'salesmanager' and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid,  86,(select array(select id from user_roles where value = 'productmanager' or value = 'salesrepresentative' or value = 'admin' and is_active = true and is_deleted = false))),
( '{72,69,71,48}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid,  116,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'countrymedical' and is_active = true and is_deleted = false))),
('{47}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid,  96,(select array(select id from user_roles where value = 'bumanager' or  value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 92,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
( '{47,46,69}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 100,(select array(select id from user_roles where value = 'bumanager' or  value = 'clustercomplianceofficer' or value = 'productmanager' or value = 'medicalmanager' or value = 'salesmanager' or value = 'countrymedical' and is_active = true and is_deleted = false))),
('{72,48,47,49}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 420,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'countrymedical' or value = 'bumanager' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
( '{72,48,49}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 419,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'countrymedical' and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 98,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'clustercomplianceofficer' or value = 'salesmanager' and is_active = true and is_deleted = false))),
( '{48,72,69,49}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid,  418,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
( '{72,69,71,46,49,48,47}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 101,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'bumanager' or value = 'salesmanager' or value = 'clustercomplianceofficer' or value = 'countrymedical' and is_active = true and is_deleted = false))),
('{69,72,71,46}', 185, true, false, '00000000-0000-0000-0000-000000000000'::uuid, 89,(select array(select id from user_roles where value = 'medicalmanager' or  value = 'marketaccessmanager' or value = 'productmanager' or value = 'clustercomplianceofficer' or value = 'salesmanager' and is_active = true and is_deleted = false)));


