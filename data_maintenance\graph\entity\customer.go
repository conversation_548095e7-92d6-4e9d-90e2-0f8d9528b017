package entity

import (
	"database/sql"

	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

type CustomerExcelInput struct {
	ID           *uuid.UUID
	Name         string
	City         string
	CustomerNo   int
	Gender       string
	CountryName  string
	Country      int
	CountryNo    int
	TeamName     string
	EmployeeName string
	Position     string
	SpDesc       string
	CMSLClass    string
	IsActive     bool
	IsDeleted    bool
	TeamMemberID *uuid.UUID
	CMSLClassID  int
	GenderID     int
}

type CustomerUpserInput struct {
	ID            *uuid.UUID
	Name          string
	City          string
	CustomerNo    string
	Gender        string
	CountryName   string
	Country       int
	CountryNo     int
	TeamName      string
	ActiveDirName string
	Position      string
	SpDesc        string
	CMSLClass     string
	IsActive      bool
	IsDeleted     bool
	TeamMemberID  *uuid.UUID
	CMSLClassID   int
	GenderID      int
	SpeakerWeight float64
	Organization  string
	AccountType   string
}

type CustomerExcel struct {
	ID                     *uuid.UUID
	Country                sql.NullInt32
	SpeakerWeight          sql.NullFloat64
	Team                   sql.NullString
	VPosition              sql.NullString
	EmpFirstName           sql.NullString
	EmpLastName            sql.NullString
	EmpActiveDirectoryName sql.NullString
	CustomerNumber         sql.NullString
	CustomerName           sql.NullString
	VSpDesc                sql.NullString
	CmslClass              sql.NullString
	City                   sql.NullString
	Gender                 sql.NullInt32
	VeevaReferenceID       sql.NullString
	Organization           sql.NullString
	AccountType            sql.NullString
	IsActive               bool
}

type CustomerExcelResponse struct {
	ID               string
	CountryName      string
	Team             string
	VPositionName    string
	EmpName          string
	ActiveDirectory  string
	CustomerNumber   string
	CustomerName     string
	VSpDesc          string
	CmslClass        string
	City             string
	Gender           string
	SpeakerWeight    float64
	Active           bool
	VeevaReferenceID string
	Organization     string
	AccountType      string
}

type CustomerExcelData struct {
	RowNo int
	Data  []interface{}
}

type ExportCustomerExcel struct {
	IsExcel          bool
	ID               *uuid.UUID
	SearchItem       string
	Country          int
	Team             string
	VPosition        string
	EmpFirstName     string
	EmpLastName      string
	CustomerNumber   string
	CustomerName     string
	VSpDesc          string
	CmslClass        string
	City             string
	Gender           int
	HcpType          string
	Limit            int
	Offset           int
	IsForm           bool
	IsActive         *bool
	VeevaReferenceId string
	Organization     string
	AccountType      string
	CustomerIDs      []*string
	Sort             []SortingElements
}

func (o *CustomerUpserInput) ValidateCustomerUpsertData(result *model.UpsertCustomerResponse) {

	validationMessages := []*model.ValidationMessage{}
	if !o.IsDeleted {
		if o.Name == "" {
			errorMessage := &model.ValidationMessage{Message: "Customer name cannot be blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.TeamName != "" {
			if o.ActiveDirName == "" {
				errorMessage := &model.ValidationMessage{Message: "Active directory name cannot be blank"}
				validationMessages = append(validationMessages, errorMessage)
			}
			if o.Position == "" {
				errorMessage := &model.ValidationMessage{Message: "Position cannot be blank"}
				validationMessages = append(validationMessages, errorMessage)
			}
		}

		if o.Gender == "" {
			errorMessage := &model.ValidationMessage{Message: "Gender cannot be blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		// if o.CMSLClass == "" {
		// 	errorMessage := &model.ValidationMessage{Message: "CMSL class cannot be blank"}
		// 	validationMessages = append(validationMessages, errorMessage)
		// }

	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
		}
		result.ValidationErrors = append(result.ValidationErrors, validationMessages...)
	}

}
