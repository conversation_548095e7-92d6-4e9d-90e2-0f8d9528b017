package entity

import (
	"time"

	"github.com/ihcp/data_maintenance/graph/model"
	uuid "github.com/satori/go.uuid"
)

type Team struct {
	ID           *uuid.UUID
	Name         string
	Country      int64
	IsActive     bool
	IsDeleted    bool
	CreatedBy    *uuid.UUID
	ModifiedBy   *uuid.UUID
	DateCreated  time.Time
	LastModified time.Time
}

type UpsertTeam struct {
	ID           *uuid.UUID
	Name         string
	Country      int
	CountryName  string
	IsActive     bool
	IsDeleted    bool
	CreatedBy    *uuid.UUID
	ModifiedBy   *uuid.UUID
	DateCreated  time.Time
	LastModified time.Time
}

func (o *UpsertTeam) ValidateTeamUpsertData(result *model.UpsertTeamResponse) {

	validationMessages := []*model.ValidationMessage{}
	if !o.IsDeleted {
		if o.CountryName == "" {
			errorMessage := &model.ValidationMessage{Message: "Country name cannot be blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.Name == "" {
			errorMessage := &model.ValidationMessage{Message: "Team name cannot be blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
	}
	if len(validationMessages) > 0 {
		if !result.Error {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
		}
		result.ValidationErrors = append(result.ValidationErrors, validationMessages...)
	}

}
