package mapper

import (
	"errors"
	"strconv"
	"time"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/data_maintenance/graph/postgres/util"
)

func OfflineDateMapping(input model.OfflineDateInput, userID *string) (*entity.UpsertOfflineDate, error) {
	var entity entity.UpsertOfflineDate
	var isDeleted bool
	if input.IsDeleted != nil {
		isDeleted = *input.IsDeleted
	}

	if !isDeleted {
		if &input.StartDate != nil {
			check := time.Unix(int64(input.StartDate), 0).Format("2006-01-02")
			if postgres.CheckDateOverlapping(check, userID) {
				return nil, errors.New("StartDate Overlap")
			} else {
				entity.StartDate = check
			}
		}

		if &input.EndDate != nil {
			check := time.Unix(int64(input.EndDate), 0).Format("2006-01-02")
			if postgres.CheckDateOverlapping(check, userID) {
				return nil, errors.New("EndDate Overlap")
			} else {
				entity.EndDate = check

			}

		}
	} else {
		if input.ID != nil {
			if postgres.CheckUserInDB(userID) {
				entity.ID = *input.ID
			} else {
				return nil, errors.New("You are not allowed to take action")
			}
		} else {
			return nil, errors.New("Please provide ID")
		}
	}
	return &entity, nil
}

func FetchOfflineDateMapping(input []entity.FetchOfflineDate) []*model.OfflineDate {
	var responce []*model.OfflineDate

	for _, entity := range input {
		var date model.OfflineDate

		date.ID = entity.ID.String()
		if entity.StartDate.Time.String() != "" {
			startDate := util.GetTimeUnixTimeStamp(entity.StartDate.Time)
			start, _ := strconv.Atoi(startDate)

			date.StartDate = &start
		} else {
			date.StartDate = nil
		}

		if entity.EndDate.Time.String() != "" {
			endDate := util.GetTimeUnixTimeStamp(entity.EndDate.Time)
			end, _ := strconv.Atoi(endDate)
			date.EndDate = &end
		} else {
			date.EndDate = nil
		}

		responce = append(responce, &date)

	}

	return responce
}
