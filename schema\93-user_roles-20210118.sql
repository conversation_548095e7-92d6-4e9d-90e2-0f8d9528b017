alter table user_roles rename  column "user_role" to  "value"  ;
alter table user_roles add column "title" text ;
alter table user_roles add column "description" text ;



	INSERT INTO test.user_roles (value,is_active,is_deleted,created_by,last_modified,modified_by,title,description) VALUES 
('sfeadmin',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'SFE Admin','SFE Admin')
,('linesecretary',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Line Secretary','Line Secretary')
,('manager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Manager','Manager')
,('medicalmanager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Medical Manager','MDL')
,('marketaccessmanager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Market Access Manager','MAC')
,('productmanager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Product Manager','MKT')
,('salesrepresentative',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Sales Representative','SLS')
,('admin',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Admin','MKT')
,('regionalfranchisehead',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Regional Franchise Head','MKT')
,('regionalmedical',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Regional Medical','MDL')
,('regionalcompliance',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Regional Compliance','MKT')
,('regionalmarketing',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Regional Marketing','MKT')
,('countrycommercialsolutionhead',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Country Commercial Solution Head','CCSH')
,('clustercomplianceofficer',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Cluster Compliance Officer','CCO')
,('countrymedical',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Country Medical','MDL')
,('bumanager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'BU Manager','MKT')
,('salesmanager',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Sales Manager','MKT')
,('finance',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Finance','Finance')
,('datastewardadmin',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Data Steward Admin','Data Steward Admin')
,('regionalcommercialsolutions',true,false,'00000000-0000-0000-0000-000000000000',NULL,NULL,'Regional Commercial Solutions','RCS')
;
