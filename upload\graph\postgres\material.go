package postgres

import (
	"context"
	"log"
	"strings"

	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/postgres/util"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

var existingDivisions map[int]map[string]uuid.UUID

func UpsertExcelMaterial(entities []*entity.MaterialExcelInput, uploadId *uuid.UUID, userUUID uuid.UUID) {
	functionName := "UpsertExcelMaterial()"
	if pool == nil {
		pool = GetPool()
	}
	existingDivisions = map[int]map[string]uuid.UUID{}
	for index, item := range entities {
		tx, err := pool.Begin(context.Background())
		if err != nil {
			log.Printf("%s - Error: Failed to begin transaction", functionName)
			continue
		}
		defer tx.Rollback(context.Background())
		if *(item.ID) == uuid.Nil && CheckMaterialDB(item) {
			err := createMaterial(tx, item, userUUID)
			if err != nil {
				tx.Rollback(context.Background())
				log.Printf("%s - error: {%s}", functionName, err.Error())
			}
		} else if *(item.ID) != uuid.Nil && (CheckMaterialDB(item) || !item.IsActive) {
			if *(item.ID) != uuid.Nil && item.CountryName == "" && item.GroupCode == "" && item.GroupName == "" && item.ClientName == "" {
				err := deleteMaterial(tx, item, userUUID)
				if err != nil {
					tx.Rollback(context.Background())
					log.Printf("%s - error: {%s}", functionName, err.Error())
				}
			} else {
				err := updateMaterial(tx, item, index, uploadId, userUUID)
				if err != nil {
					tx.Rollback(context.Background())
					log.Printf("%s - error: {%s}", functionName, err.Error())
				}
			}
		}

		txErr := tx.Commit(context.Background())
		if txErr != nil {
			log.Printf("%s - Error: Failed to commit material data", functionName)
		}
	}
}

func createMaterial(tx pgx.Tx, entity *entity.MaterialExcelInput, userUUID uuid.UUID) error {
	functionName := "createMaterial()"
	log.Printf("%s", functionName)
	var err error
	if entity.DivisionName != "" {
		var divisionID string
		query := `INSERT INTO division (name, country, created_by, is_active)VALUES($1,$2,$3,$4) RETURNING(id)`
		var inputArgs []interface{}
		inputArgs = append(inputArgs, entity.DivisionName, entity.CountryNo, userUUID, entity.IsActive)
		err = tx.QueryRow(context.Background(), query, inputArgs...).Scan(&divisionID)
		query1 := `INSERT INTO material (division,country,group_code,group_name,is_active,created_by,veeva_reference_id,product_owner_uid) VALUES ($1,$2,$3,$4,$5,$6,$7,(select id from product_owner where owner_name =$8 and country=$2  limit 1))`
		var inputArgs1 []interface{}
		inputArgs1 = append(inputArgs1, divisionID, entity.CountryNo, entity.GroupCode, entity.GroupName, entity.IsActive, userUUID, entity.VeevareferenceId, entity.ClientName)
		_, err = tx.Exec(context.Background(), query1, inputArgs1...)
	} else {
		query := `INSERT INTO material (country,group_code,group_name,is_active,created_by,veeva_reference_id,product_owner_uid) VALUES ($1,$2,$3,$4,$5,$6,(select id from product_owner where owner_name =$7 and country=$1  limit 1))`
		var inputArgs []interface{}
		inputArgs = append(inputArgs, entity.CountryNo, entity.GroupCode, entity.GroupName, entity.IsActive, userUUID, entity.VeevareferenceId, entity.ClientName)
		_, err = tx.Exec(context.Background(), query, inputArgs...)
	}
	return err
}

func updateMaterial(tx pgx.Tx, entity *entity.MaterialExcelInput, index int, uploadId *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "updateMaterial()"
	log.Printf("%s", functionName)
	var err error
	timenow := util.GetCurrentTime()
	materialQueryString := `UPDATE material SET group_name = $1, group_code = $2, is_active = $3, modified_by = $4, country = $5, last_modified = $7,veeva_reference_id = $8,product_owner_uid=(select id from product_owner where owner_name =$9 and country=$5  limit 1) WHERE id = $6`
	_, err = tx.Exec(context.Background(), materialQueryString, entity.GroupName, entity.GroupCode, entity.IsActive, userUUID, entity.CountryNo, entity.ID, timenow, entity.VeevareferenceId, entity.ClientName)
	if err != nil {
		return err
	}

	return err
}

func deleteMaterial(tx pgx.Tx, entity *entity.MaterialExcelInput, userUUID uuid.UUID) error {
	functionName := "deleteMaterial()"
	log.Printf("%s", functionName)
	var err error
	query := `UPDATE material set is_active=$2,is_deleted=$3,modified_by=$4,last_modified=now() where id=$1`
	_, err = tx.Exec(context.Background(), query, entity.ID, entity.IsActive, entity.IsDeleted, userUUID)

	return err
}

func CheckMaterialDB(entity *entity.MaterialExcelInput) bool {
	functionName := "CheckMaterialDB()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var val int
	query := `select 1 from material m 
	inner join product_owner po on po.id =m.product_owner_uid 
	where lower(m.group_code) =$1 and lower(m.group_name) =$2 and m.country =$3 and po.owner_name  =$4  and m.is_active =true `
	err := pool.QueryRow(context.Background(), query, strings.ToLower(entity.GroupCode), strings.ToLower(entity.GroupName), entity.CountryNo, entity.ClientName).Scan(&val)
	if err == nil {
		return false
	}
	return true
}

func materialCountForDivision(divisionID uuid.UUID) (int, error) {
	functionName := "materialCountForDivision"
	if pool == nil {
		pool = GetPool()
	}
	count := 0
	query := `select count(id) from material where division=$1 and is_deleted=false`
	err := pool.QueryRow(context.Background(), query, divisionID).Scan(&count)
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	return count, err
}

func mapDivisionFromMaterial(divisionName string, countryId int) *entity.Division {
	return &entity.Division{
		Name:    divisionName,
		Country: int64(countryId),
	}
}
