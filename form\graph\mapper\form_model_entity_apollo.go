package mapper

import (
	"fmt"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"log"
	"reflect"
	"strconv"
)

// #1

func FormAnswerDesignEntityToModelForApollo(input *entity.FormAnswerEntity, country string, countryID int, rate float64) []*model.FormTabsAnswer {
	log.Println("FormAnswerDesignEntityToModelForApollo")
	var response []*model.FormTabsAnswer
	formSection := input.Design.Get()
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormTabsAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				idValue := ""

				// Access the "id" key directly
				if idField := v.MapIndex(reflect.ValueOf("id")); idField.IsValid() {
					strct := idField.Interface()
					if strct != nil {
						id := strct.(string)
						formTab.ID = &id
					}
				}

				// Access the "title" key directly
				if titleField := v.MapIndex(reflect.ValueOf("title")); titleField.IsValid() {
					strct := titleField.Interface()
					if strct != nil {
						title := strct.(string)
						formTab.Title = &title
						if formTab.ID == nil {
							idValue = ConvertToKebabCase(title)
							formTab.ID = &idValue
						}
					}
				}

				// Access the "sequenceNo" key directly
				if sequenceNoField := v.MapIndex(reflect.ValueOf("sequenceNo")); sequenceNoField.IsValid() {
					strct := sequenceNoField.Interface()
					if strct != nil {
						seqValue := int(strct.(float64))
						formTab.SequenceNo = &seqValue
					}
				}

				// Access the "sectionAnswer" key directly
				if sectionAnswerField := v.MapIndex(reflect.ValueOf("sectionAnswer")); sectionAnswerField.IsValid() {
					strct := sectionAnswerField.Interface()
					if strct != nil {
						// Ensure idValue is set if title was processed
						if formTab.Title != nil && idValue == "" {
							idValue = ConvertToKebabCase(*formTab.Title)
							formTab.ID = &idValue
						}
						formTab.SectionAnswer = getFormAnswerSectionValueFromMapForApollo(strct, country, countryID, rate, idValue)
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #2

func getFormAnswerSectionValueFromMapForApollo(formSection interface{}, country string, countryID int, rate float64, idValue string) []*model.FormSectionAnswer {
	log.Println("getFormAnswerSectionValueFromMapForApollo")
	var response []*model.FormSectionAnswer
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormSectionAnswer
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					sectionId := ""
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &id
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "title":
						if strct != nil {
							title := strct.(string)
							formTab.Title = &title
						}
					case "form":
						if strct != nil {
							if sectionId == "" {
								sectionId = idValue + "-sections-" + strconv.Itoa(1)
							}
							formTab.Form = getFormQuestionAnswerFromMapForApollo(strct, country, countryID, rate, sectionId, "-form-")
						}
					case "childForm":
						if strct != nil {
							if sectionId == "" {
								sectionId = idValue + "-sections-" + strconv.Itoa(1)
							}
							formTab.ChildForm = getFormQuestionAnswerFromMapForApollo(strct, country, countryID, rate, sectionId, "-child-form-")
						}
					}
					if formTab.ID == nil {
						sectionId = idValue + "-sections-" + strconv.Itoa(1)
						formTab.ID = &sectionId
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #3

func getFormQuestionAnswerFromMapForApollo(questions interface{}, country string, countryID int, rate float64, sectionId string, formIdValue string) *model.FormSectionContentAnswer {
	log.Println("getFormQuestionAnswerFromMapForApollo")
	var response model.FormSectionContentAnswer
	v := reflect.ValueOf(questions)
	if v.Kind() == reflect.Map {
		for _, key := range v.MapKeys() {
			formId := ""
			strct := v.MapIndex(key).Interface()
			switch key.Interface() {
			case "id":
				if strct != nil {
					id := strct.(string)
					response.ID = &id
				}
			case "sequenceNo":
				if strct != nil {
					seqValue := int(strct.(float64))
					response.SequenceNo = &seqValue
				}
			case "title":
				if strct != nil {
					title := strct.(string)
					response.Title = &title
				}
			case "groupAnswer":
				if strct != nil {
					if formId == "" {
						formId = sectionId + formIdValue + strconv.Itoa(1)
					}
					response.GroupAnswer = getQuestionGroupFromAnswerInterfaceMapForApollo(strct, country, countryID, rate, formId, formIdValue)
				}
			}
			if response.ID == nil {
				formId = sectionId + formIdValue + strconv.Itoa(1)
				response.ID = &formId
			}
		}
	}
	return &response
}

// #4

func getQuestionGroupFromAnswerInterfaceMapForApollo(groups interface{}, country string, countryID int, rate float64, formId string, formIdValue string) []*model.QuestionGroupAnswers {
	log.Println("getQuestionGroupFromAnswerInterfaceMapForApollo")
	var response []*model.QuestionGroupAnswers
	v := reflect.ValueOf(groups)
	if v.Kind() == reflect.Slice {
		for _, item := range groups.([]interface{}) {
			var formTab model.QuestionGroupAnswers
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &id
						}
					case "title":
						if strct != nil {
							title := strct.(string)
							formTab.Title = &title
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "questionAnswers":
						if strct != nil {
							formTab.QuestionAnswers = getGroupFromAnswerInterfaceMapForApollo(strct, country, countryID, rate)
						}
					}
					if formTab.ID == nil && formIdValue != "-child-form-" {
						formIdVal := formId + "-group" + strconv.Itoa(1)
						formTab.ID = &formIdVal
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #5

func getGroupFromAnswerInterfaceMapForApollo(inputGroup interface{}, country string, countryID int, rate float64) []*model.GroupAnswers {
	log.Println("getGroupFromAnswerInterfaceMapForApollo")
	var response []*model.GroupAnswers
	v := reflect.ValueOf(inputGroup)
	if v.Kind() == reflect.Slice {
		for _, item := range inputGroup.([]interface{}) {
			var formTab model.GroupAnswers
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							formTab.ID = &(id)
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							formTab.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = &seqValue
						}
					case "source":
						if strct != nil {
							source := strct.(string)
							formTab.Source = &source
						}
					case "answers":
						if strct != nil {
							formTab.Answers = getInputsAnswersFromInterfaceMapForApollo(strct, country, countryID, rate)
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

//#6

func getInputsAnswersFromInterfaceMapForApollo(inputs interface{}, country string, countryID int, rate float64) []*model.InputAnswers {
	log.Println("getInputsAnswersFromInterfaceMapForApollo")
	var response []*model.InputAnswers

	v := reflect.ValueOf(inputs)
	if v.Kind() == reflect.Slice {
		for _, item := range inputs.([]interface{}) {
			var formTab model.InputAnswers

			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					answer := v.MapIndex(key).Interface()
					if answer == nil {
						continue
					}

					switch key.Interface() {
					case "id":
						id := answer.(string)
						formTab.ID = &(id)
					case "title":
						seqValue := answer.(string)
						formTab.Title = &seqValue
					case "sequenceNo":
						seqValue := int(answer.(float64))
						formTab.SequenceNo = &seqValue
					case "type":
						seqValue := answer.(string)
						formTab.Type = &seqValue
					case "source":
						seqValue := answer.(string)
						formTab.Source = &seqValue
					case "values":
						var title string
						if titleField := v.MapIndex(reflect.ValueOf("title")); titleField.IsValid() {
							if s := titleField.Interface(); s != nil {
								title = s.(string)
							}
						}

						var isAccommodation bool
						formTab.Values, isAccommodation = getValuesAnswersFromInterfaceMapForApollo(answer, country, countryID, rate, title)
						if isAccommodation {
							Accommodation := "Accomodation" // correct this may break all the old events
							formTab.Title = &Accommodation
						}
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

// #7

func getValuesAnswersFromInterfaceMapForApollo(question interface{}, country string, countryID int, rate float64, titleField string) ([]*model.ValuesAnswer, bool) {
	log.Println("getValuesAnswersFromInterfaceMapForApollo")
	var response []*model.ValuesAnswer
	var isAccommodation bool

	v := reflect.ValueOf(question)
	if v.Kind() == reflect.Slice {
		for _, item := range question.([]interface{}) {
			var formTab model.ValuesAnswer

			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					s := v.MapIndex(key).Interface()
					if s == nil {
						continue
					}

					f := s.(string)
					switch key.Interface() {
					case "id":
						formTab.ID = &f
					case "description":
						formTab.Description = &f
						if titleField == "Client" {
							formTab.Description = &titleField
						}

						if f == "No of Days" {
							isAccommodation = true
						}
					case "value":
						formTab.Value = &f
					}
				}
			}
			var valueInUsd string
			if formTab.Description != nil && formTab.Value != nil {
				d := *formTab.Description
				if d == "Total Cost" || d == "Proposed Payable Honorarium" || d == "Ground Transportation" || d == "Airfare" ||
					d == "Cost per Meal" || d == "Cost per Day" || d == "Registration Fee" || d == "Cost" {

					if *formTab.Value != "" {
						if val, err := strconv.ParseFloat(*formTab.Value, 10); err == nil {
							if countryID == 378 {
								valueInUsd = fmt.Sprintf("%0.3f", val/rate)
							} else {
								valueInUsd = fmt.Sprintf("%0.3f", val*rate)
							}

						}
					}
				}
			}
			formTab.ValueInUsd = &valueInUsd

			response = append(response, &formTab)
		}
	}
	return response, isAccommodation
}
