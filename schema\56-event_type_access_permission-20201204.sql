DELETE FROM event_type_access_permission ; 
alter table event_type_access_permission drop column event_type ;
alter table event_type_access_permission  add column event_type int8 not null;
INSERT INTO event_type_access_permission (user_role,country,is_active,is_deleted,created_by,last_modified,modified_by,event_type) VALUES
	 ('{68,69,46,71,72}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,92),
	 ('{69,46,71,72}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,93),
	 ('{47}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,96),
	 ('{68,69,46,71,72}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,98),
	 ('{68,69,46,71,72}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,94),
	 ('{68,69,46,71,72}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,86),
	 ('{47,46,69}',NULL,true,false,'52dad980-4b26-411f-96a3-83483ad143a9',NULL,NULL,100);