pool:
  vmImage: 'ubuntu-latest'
trigger:
 branches:
   include:
     - development
     - staging
 paths:
   exclude:
     - "*.yaml"

resources:
  repositories: 
  - repository: azure-pipeline-template
    type: git
    ref: refs/heads/webapp
    name: Digital and Data Program Management/azure-pipeline-template
    endpoint: build-service

variables:
  - name: app-name
    value: data-maintenance
  - group: ezflow-cicd
  - name: context
    value: '$(Build.Repository.LocalPath)'
  - name: imageRepository
    value: stg/ezflow/$(app-name)
  - name: ado-project
    value: 'Commercialization Technologies'
  - name: ado-repo
    value: 'eZFlow-Backend'
  - name: ado-branch
    value: staging

extends:
  template: stages/ci.yml@azure-pipeline-template
  parameters:
    sonarqubeProjectKey: ezflow-data-maintenance
    skipUnitTest: true
    skipSonarqubeAnalysis: true
    exclude: '**/user_activity/**,**/upload/**,**/form/**,**/schema/'