package postgres

import (
	"context"
	"log"

	"github.com/ihcp/code/entity"
	"github.com/jackc/pgx/v4/pgxpool"
)

type CodeService struct {
	Pool *pgxpool.Pool
}

func (c *CodeService) GetCodes() (map[string][]*entity.Code, error) {
	codeEntities := make(map[string][]*entity.Code)
	pool := c.Pool
	if pool != nil {
		queryString := `SELECT id, category, title, value, description, is_active , sequence_no FROM code WHERE is_deleted = false and is_active= true`
		rows, err := pool.Query(context.Background(), queryString)
		if err == nil {
			for rows.Next() {
				codeEntity := &entity.Code{}
				err := rows.Scan(&codeEntity.ID, &codeEntity.Category, &codeEntity.Title, &codeEntity.Value, &codeEntity.Description, &codeEntity.IsActive, &codeEntity.SequenceNo)
				if err != nil {
					log.Println(err)
				} else {
					codeEntities[codeEntity.Category] = append(codeEntities[codeEntity.Category], codeEntity)
				}
			}
		}
	}
	return codeEntities, nil
}
