package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/logengine"
)

func GetRoleIDByValueInUserRoles(roleValue string) (string, error) {
	functionName := "GetRoleIDByValueInUserRoles()"
	log.Println(functionName)
	var codeValue string
	if pool == nil {
		pool = GetPool()
	}
	var value string
	queryString := `select id from user_roles where value = $1 and is_active = true and is_deleted = false `
	err := pool.QueryRow(context.Background(), queryString, roleValue).Scan(&value)
	// var codeValue string
	logengine.GetTelemetryClient().TrackEvent("GetRoleIDByValueInUserRoles query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return "", err
	} else {
		codeValue = value
	}
	return codeValue, nil
}
