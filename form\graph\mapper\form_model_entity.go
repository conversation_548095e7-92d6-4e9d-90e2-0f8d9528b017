package mapper

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
	"time"

	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/constants"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/imdario/mergo"
	"github.com/jackc/pgtype"
	uuid "github.com/satori/go.uuid"
)

func boolData(data bool) *bool {
	b := data
	return &b
}

func newFalse() *bool {
	b := false
	return &b
}

func FetchActivityEntityToModel(input []entity.FetchActivities) []*model.ActivitySelection {
	var outEntity []*model.ActivitySelection
	for _, item := range input {
		activity := new(model.ActivitySelection)
		activity.ID = item.ID.String()
		activity.Description = item.Description
		outEntity = append(outEntity, activity)
	}
	return outEntity
}

func FormDesignModelToEntity(input model.FormTemplateSubmitRequest) (*entity.FormEntity, error) {
	functionName := "FormDesignModelToEntity()"
	e, err := json.Marshal(input.Design)
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err)
	}
	design := pgtype.JSONB{}
	_ = design.Set(string(e))
	return &entity.FormEntity{
		Design: design,
	}, nil
}

func FormAnswerAttachmentEntityToModel(input *entity.FormAnswerEntity, boardOfDirectors pgtype.JSONB) ([]*model.AttachmentResponse, []*model.BoardOfDirectoryData) {
	log.Println("FormAnswerAttachmentEntityToModel")
	var responses []*model.AttachmentResponse
	boardOfDirectorsResponse := []*model.BoardOfDirectoryData{}
	var boardOfDirectorsDataRes []entity.BoardOfDirectorsData
	json.Unmarshal(boardOfDirectors.Bytes, &boardOfDirectorsDataRes)
	for _, data := range boardOfDirectorsDataRes {
		var bODResponse model.BoardOfDirectoryData
		Name := strings.TrimSpace(data.Name)
		Position := strings.TrimSpace(data.Position)
		Question := strings.TrimSpace(data.Question)
		Remarks := strings.TrimSpace(data.Remarks)
		bODResponse.Name = &Name
		bODResponse.Position = &Position
		bODResponse.Question = &Question
		bODResponse.Remarks = &Remarks
		boardOfDirectorsResponse = append(boardOfDirectorsResponse, &bODResponse)
	}
	for _, item := range input.Attachments {
		var response model.AttachmentResponse
		var response2 []*model.AttachmentCategoryResponse
		if item.Value != "" {
			types := item.Value
			response.Type = &types
		}
		for _, categories := range item.Categories {
			var response1 model.AttachmentCategoryResponse
			var urlss []*string
			if categories.Category != "" {
				desc := categories.Category
				response1.Description = &desc
			}
			for _, urls := range categories.URL {
				var url string
				url = urls
				urlss = append(urlss, &url)
			}
			response1.URL = urlss
			response2 = append(response2, &response1)
		}
		response.Categories = response2
		responses = append(responses, &response)
	}
	return responses, boardOfDirectorsResponse
}

func UpdateFormAnswerActivityDateModelToEntity(input model.FormSubmissionActivity, country int) (*entity.FormSubmissionRequest, error) {
	log.Println("UpdateFormAnswerActivityDateModelToEntity()")
	var response entity.FormSubmissionRequest

	formAnsCountry, err := postgres.FetchCountryIdFromFormAnswer(input.FormAnsID)
	if err != nil {
		return nil, errors.New("Form answer country can not be fetched")
	}
	if formAnsCountry != country {
		return nil, errors.New("User not allowed from other country!!")
	}

	if input.FormAnsID != "" {
		response.FormAnsId = input.FormAnsID
	} else {
		return nil, errors.New("Form AnswerID can not be null")
	}
	status, err := postgres.FetchFormAnswersForCancel(input.FormAnsID)
	if err != nil {
		return nil, errors.New("Unable to fetch form answer status")
	}
	draftaction := "draft"
	codesfordraft := codeController.GetValueKeyCodes()["approvedstatus"]
	draft := codesfordraft[draftaction].ID

	actionforcomplete := "completed"
	codesforcomplte := codeController.GetValueKeyCodes()["formanswertype"]
	completed := codesforcomplte[actionforcomplete].ID

	actionforrejected := "rejected"
	codesforrejected := codeController.GetValueKeyCodes()["approvedstatus"]
	rejected := codesforrejected[actionforrejected].ID

	actionforcancelled := "cancelled"
	codesforcancelled := codeController.GetValueKeyCodes()["approvedstatus"]
	cancelled := codesforcancelled[actionforcancelled].ID

	if status == cancelled {
		return nil, errors.New("Event is cancelled, can not updated")
	}
	if status == completed {
		return nil, errors.New("Event is completed, can not updated")
	}
	if status == draft {
		return nil, errors.New("Event is draft, can not updated")
	}
	if status == rejected {
		return nil, errors.New("Event is rejected, can not updated")
	}
	if input.StartDate != "" {
		response.StartDate = input.StartDate
	} else {
		return nil, errors.New("Form StartDate can not be null")
	}
	if input.EndDate != "" {
		response.EndDate = input.EndDate
	} else {
		return nil, errors.New("Form EndDate not be null")
	}
	response.IsExceptionalApprover = input.IsExceptionalApprover
	if input.Duration != "" {
		response.Duration = input.Duration
	} else {
		return nil, errors.New("Form Duration not be null")
	}
	return &response, nil
}

func FormDesignEntityToModel(input *entity.FormEntity, userID string, approvalRole string) []*model.FormTabs {
	countryID, _ := postgres.GetCountryIDByUserID(userID)
	log.Println("countryID ==> ", countryID)
	log.Println("FormDesignEntityToModel")
	var response []*model.FormTabs
	formSection := input.Design.Get()
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormTabs
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = seqValue
						}
					case "id":
						formTab.ID = strct.(string)
					case "title":
						formTab.Title = strct.(string)
					case "sections":
						formTab.Sections = getFormSectionValueFromMap(strct, userID, countryID, approvalRole)
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

func getFormSectionValueFromMap(formSection interface{}, userId string, countryID int, approvalRole string) []*model.FormSections {
	log.Println("getFormSectionValueFromMap")
	var response []*model.FormSections
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			formsec := new(model.FormSections)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							idValue := strct.(string)
							formsec.ID = &idValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formsec.SequenceNo = &seqValue
						}
					case "form":
						if strct != nil {
							formsec.Form = getFormQuestionFromMap(strct, userId, countryID, approvalRole)
						}
					case "childForm":
						if strct != nil {
							formsec.ChildForm = getFormQuestionFromMap(strct, userId, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, formsec)
		}
	}
	return response
}

func getFormQuestionFromMap(questions interface{}, userId string, countryID int, approvalRole string) *model.FormSectionContent {
	log.Println("getFormQuestionFromMap")
	var response model.FormSectionContent
	v := reflect.ValueOf(questions)
	if v.Kind() == reflect.Map {
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			switch key.Interface() {
			case "sequenceNo":
				if strct != nil {
					seqValue := int(strct.(float64))
					response.SequenceNo = &seqValue
				}
			case "title":
				if strct != nil {
					title := strct.(string)
					response.Title = &title
				}
			case "id":
				if strct != nil {
					idValue := strct.(string)
					response.ID = &idValue
				}
			case "group":
				if strct != nil {
					response.Group = getGroupFromInterfaceMap(strct, userId, countryID, approvalRole)
				}
			}
		}
	}
	return &response
}

func getGroupFromInterfaceMap(groups interface{}, userID string, countryID int, approvalRole string) []*model.QuestionGroup {
	log.Println("getGroupFromInterfaceMap")
	var response []*model.QuestionGroup
	v := reflect.ValueOf(groups)
	if v.Kind() == reflect.Slice {
		for _, item := range groups.([]interface{}) {
			group := new(model.QuestionGroup)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							idValue := strct.(string)
							group.ID = &idValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							group.SequenceNo = &seqValue
						}
					case "questions":
						if strct != nil {
							group.Inputs = getQuestionInputGroupFromInterfaceMap(strct, userID, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, group)
		}
	}
	return response
}

func getQuestionInputGroupFromInterfaceMap(inputGroup interface{}, userID string, countryID int, approvalRole string) []*model.InputGroup {
	log.Println("getQuestionInputGroupFromInterfaceMap")
	var response []*model.InputGroup
	v := reflect.ValueOf(inputGroup)
	if v.Kind() == reflect.Slice {
		for _, item := range inputGroup.([]interface{}) {
			input := new(model.InputGroup)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "groupId":
						if strct != nil {
							id := strct.(string)
							input.GroupID = &(id)
						}
					case "groupRules":
						if strct != nil {
							input.GroupRules = getGroupRulesFromInterface(strct, countryID)
						}
					case "hidden":
						if strct != nil {
							hidden := strct.(bool)
							input.Hidden = &hidden
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							input.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							input.SequenceNo = &seqValue
						}
					case "inputs":
						if strct != nil {
							input.Inputs = getQuestionInputsFromInterfaceMap(strct, userID, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, input)
		}
	}

	return response
}

func getQuestionInputsFromInterfaceMap(inputs interface{}, userID string, countryID int, approvalRole string) []*model.Inputs {
	log.Println("getQuestionInputsFromInterfaceMap")
	var response []*model.Inputs
	v := reflect.ValueOf(inputs)
	if v.Kind() == reflect.Slice {
		for _, item := range inputs.([]interface{}) {
			input := new(model.Inputs)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					//log.Printf("%#v =======> ", key.Interface())
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							input.ID = &(id)
						}
					case "hidden":
						if strct != nil {
							hidden := strct.(bool)
							input.Hidden = &hidden
						}
					case "rules":
						if strct != nil {
							input.Rules = getRulesFromInterface(strct, countryID)
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							input.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							input.SequenceNo = &seqValue
						}
					case "type":
						if strct != nil {
							seqValue := strct.(string)
							input.Type = &seqValue
						}

					case "source":
						if strct != nil {
							seqValue := strct.(string)
							input.Source = &seqValue
							category := strings.Split(*(input).Source, "|")
							if category[0] == "code" {
								code := category[0]
								if len(category) > 1 {
									code = category[1]
									if code == "Currency" {
										input.Values = getCurrencyValuesFromCodeTable(code)
										continue
									}
								}
								input.Values = getValuesFromCodeTable(code, approvalRole, countryID)
							} else {
								input.Values = postgres.GetValuesFromOtherTable(*(input).Source, countryID)
							}
						}
					case "validatesource":
						if strct != nil {
							seqValue := strct.(string)
							input.Validatesource = &seqValue
							category := strings.Split(*(input).Validatesource, "|")
							if category[0] == "controls" {
								log.Println("validation control for type :" + category[1])
								input.Validations = postgres.GetControlValidationByType(category[1], &userID, countryID)
							}
						}

					case "readOnly":
						if strct != nil {
							read := strct.(bool)
							input.ReadOnly = &read
						}
					case "operation":
						if strct != nil {
							seqValue := strct.(string)
							input.Operation = &seqValue
						}
					}
				}
			}
			response = append(response, input)
		}
	}
	return response
}

func getValuesFromInterfaceMap(question interface{}) []*model.Values {
	log.Println("getValuesFromInterfaceMap")
	var response []*model.Values
	v := reflect.ValueOf(question)
	if v.Kind() == reflect.Slice {
		for _, item := range question.([]interface{}) {
			question := new(model.Values)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "description":
						if strct != nil {
							question.Description = strct.(string)
						}
					case "value":
						if strct != nil {
							question.Value = strct.(string)
						}
					}
				}
			}
			response = append(response, question)
		}
	}
	return response
}

func getGroupRulesFromInterface(rules interface{}, countryID int) []*model.GroupRules {
	log.Println("getGroupRulesFromInterface")
	var response []*model.GroupRules
	v := reflect.ValueOf(rules)

	if v.Kind() == reflect.Slice {
		for _, item := range rules.([]interface{}) {
			v = reflect.ValueOf(item)
			rule := new(model.GroupRules)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "value":
						if strct != nil {
							value := strct.(string)
							rule.Value = &value
						}
					case "actions":
						if strct != nil {
							rule.Actions = getGroupActionValuesFromInterface(strct)
						}
					}
				}
			}
			response = append(response, rule)
		}
	}

	return response
}

func getRulesFromInterface(rules interface{}, countryID int) []*model.Rules {
	log.Println("getRulesFromInterface")
	var response []*model.Rules
	v := reflect.ValueOf(rules)

	if v.Kind() == reflect.Slice {
		for _, item := range rules.([]interface{}) {
			v = reflect.ValueOf(item)
			rule := new(model.Rules)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "value":
						if strct != nil {
							value := strct.(string)
							rule.Value = &value
						}
					case "actions":
						if strct != nil {
							rule.Actions = getActionValuesFromInterface(strct, countryID)
						}
					}
				}
			}
			response = append(response, rule)
		}
	}

	return response
}

func getGroupActionValuesFromInterface(actions interface{}) []*model.GroupActions {
	log.Println("getGroupActionValuesFromInterface")
	var response []*model.GroupActions
	v := reflect.ValueOf(actions)

	if v.Kind() == reflect.Slice {
		for _, item := range actions.([]interface{}) {
			v = reflect.ValueOf(item)
			action := new(model.GroupActions)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "groupId":
						if strct != nil {
							id := strct.(string)
							action.GroupID = &(id)
						}
					case "action":
						if strct != nil {
							act := strct.(string)
							action.Action = &(act)
						}
					}
				}
			}
			response = append(response, action)
		}
	}
	return response
}

func getActionValuesFromInterface(actions interface{}, countryID int) []*model.Actions {
	log.Println("getActionValuesFromInterface")
	var response []*model.Actions
	v := reflect.ValueOf(actions)

	if v.Kind() == reflect.Slice {
		for _, item := range actions.([]interface{}) {
			v = reflect.ValueOf(item)
			action := new(model.Actions)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							action.ID = &(id)
						}
					case "action":
						if strct != nil {
							act := strct.(string)
							action.Action = &(act)
						}
					}
				}
			}
			response = append(response, action)
		}
	}
	return response
}

func FormAnswerModelToEntity(ctx context.Context, createdBy *uuid.UUID, input []*model.FormTabsAnswerInput, isApollo bool) (*entity.AnswerEntity, error) {
	countryCurrency := postgres.GetCountryCurrencyId(createdBy)
	if countryCurrency == nil {
		panic("Something went wrong. User must be assigned to a valid country")
	}

	var response entity.AnswerEntity
	e, err := json.Marshal(input)
	if err != nil {
		panic(err)
	}

	answer := pgtype.JSONB{}
	_ = answer.Set(string(e))

	var f entity.FormAnswerEntity
	f.Design = answer

	if err := f.Validate(countryCurrency.Country, isApollo); err != nil {
		return nil, err
	}

	form := postgres.FetchFormTemplate(ctx)
	if form == nil {
		panic("Something went wrong")
	}

	response.Country = countryCurrency.Country
	response.Currency = countryCurrency.Currency
	response.FormId = form.ID
	response.CreatedBy = createdBy
	response.Answer = answer
	return &response, nil
}

func SubmitApprovalRoleModelToEntity(createdBy *uuid.UUID, approvalRoles []*model.ApprovalRole, formAnswerID string, country int, changeRequest *bool) (*entity.ApproversEntity, error) {
	var response entity.ApproversEntity
	code := codeController.GetValueKeyCodes()["group"]
	regionalGroupID := code["regional"].ID
	localGroupID := code["local"].ID
	countryCode := codeController.GetIdKeyCodes()["country"]
	approvers := []entity.Approvers{}

	response.CreatedBy = createdBy
	localApproverCount := 0
	regionalApproverCount := 0
	for _, val := range approvalRoles {
		code := codeController.GetValueKeyCodes()["approvedstatus"]
		approver := entity.Approvers{}
		if val.RoleID == "" {
			return nil, errors.New("Approver Role ID cannot be blank")
		} else {
			approver.RoleID = val.RoleID
		}

		if val.UserID == "" {
			return nil, errors.New("Approver user ID cannot be blank")
		} else {
			approver.UserID = val.UserID
		}

		if val.DepartmentID == "" {
			return nil, errors.New("Approver Department ID cannot be blank")
		} else {
			approver.DepartmentID = val.DepartmentID
		}

		if countryCode[country].Value == "rg" {
			approver.GroupID = regionalGroupID
		} else {
			approver.GroupID = postgres.GetGroupIDByApprovalRoleID(approver.RoleID)
		}
		if approver.GroupID == localGroupID {
			localApproverCount++
		}
		if approver.GroupID == regionalGroupID {
			regionalApproverCount++
		}

		approver.SequenceNo = val.SequenceNo
		approver.StatusID = code["pending"].ID
		setNumber, _ := postgres.GetSetNumberFromFormAnswer(formAnswerID)
		if setNumber <= 0 {
			approver.SetNumber = 1
		} else {
			approver.SetNumber = setNumber + 1
		}
		approvers = append(approvers, approver)

	}
	if localApproverCount > 0 {
		response.LocalApproverPresent = true
	}
	if regionalApproverCount > 0 {
		response.RegionalApproverPresent = true
	}
	if changeRequest != nil {
		response.IsChangeRequest = *changeRequest
	} else {
		response.IsChangeRequest = false
	}
	response.ApproverRoles = approvers
	return &response, nil
}

func ApprovalRoleSelectionForExceptionalModelToEntity(approvalRoles []*model.ApprovalRole, formAnswerID string, country int) (*entity.ApproversEntity, error) {
	var response entity.ApproversEntity
	code := codeController.GetValueKeyCodes()["group"]
	regionalGroupID := code["regional"].ID
	localGroupID := code["local"].ID
	countryCode := codeController.GetIdKeyCodes()["country"]
	approvers := []entity.Approvers{}
	createdBy, err := postgres.CreatedbyfromFormId(formAnswerID)
	if err != nil {
		return nil, err
	}
	create, err := uuid.FromString(createdBy)
	if err != nil {
		return nil, err
	}

	response.CreatedBy = &create
	localApproverCount := 0
	regionalApproverCount := 0
	for _, val := range approvalRoles {
		code := codeController.GetValueKeyCodes()["approvedstatus"]
		approver := entity.Approvers{}
		if val.RoleID == "" {
			return nil, errors.New("Approver Role ID cannot be blank")
		} else {
			approver.RoleID = val.RoleID
		}

		if val.UserID == "" {
			return nil, errors.New("Approver user ID cannot be blank")
		} else {
			approver.UserID = val.UserID
		}

		if val.DepartmentID == "" {
			return nil, errors.New("Approver Department ID cannot be blank")
		} else {
			approver.DepartmentID = val.DepartmentID
		}

		if countryCode[country].Value == "rg" {
			approver.GroupID = regionalGroupID
		} else {
			approver.GroupID = postgres.GetGroupIDByApprovalRoleID(approver.RoleID)
		}
		if approver.GroupID == localGroupID {
			localApproverCount++
		}
		if approver.GroupID == regionalGroupID {
			regionalApproverCount++
		}

		approver.SequenceNo = val.SequenceNo
		approver.StatusID = code["pending"].ID
		setNumber, _ := postgres.GetSetNumberFromFormAnswer(formAnswerID)
		approver.SetNumber = setNumber
		approvers = append(approvers, approver)
	}
	if localApproverCount > 0 {
		response.LocalApproverPresent = true
	}
	if regionalApproverCount > 0 {
		response.RegionalApproverPresent = true
	}
	response.ApproverRoles = approvers
	return &response, nil
}

func getCurrencyValuesFromCodeTable(category string) []*model.Values {
	var response []*model.Values
	codes := codeController.GetCodesByCategory(category)
	for _, item := range codes {
		var value model.Values
		value.Value = strconv.Itoa(item.ID)
		value.Description = item.Title.String
		response = append(response, &value)
	}

	return response

}

func fetchCountryOptions() []*model.Values {
	return []*model.Values{
		{
			Value:       "vn",
			Description: "Vietnam",
		}, {
			Value:       "th",
			Description: "Thailand",
		}, {
			Value:       "my",
			Description: "Malaysia",
		}, {
			Value:       "sg",
			Description: "Singapore",
		}, {
			Value:       "ph",
			Description: "Philippines",
		}, {
			Value:       "id",
			Description: "Indonesia",
		}, {
			Value:       "mm",
			Description: "Myanmar",
		}, {
			Value:       "tw",
			Description: "Taiwan",
		}, {
			Value:       "hk",
			Description: "Hong Kong",
		}, {
			Value:       "mo",
			Description: "Macau",
		}, {
			Value:       "kr",
			Description: "Korea",
		}, {
			Value:       "kh",
			Description: "Cambodia",
		}, {
			Value:       "bn",
			Description: "Brunei",
		}, {
			Value:       "au",
			Description: "Australia",
		}, {
			Value:       "nz",
			Description: "New Zealand",
		}, {
			Value:       "jp",
			Description: "Japan",
		}, {
			Value:       "cn",
			Description: "China",
		}, {
			Value:       "in",
			Description: "India",
		}, {
			Value:       "us",
			Description: "USA",
		}, {
			Value:       "ca",
			Description: "Canada",
		}, {
			Value:       "fr",
			Description: "French",
		}, {
			Value:       "it",
			Description: "Italy",
		}, {
			Value:       "uk",
			Description: "United Kingdom",
		}, {
			Value:       "sz",
			Description: "Switzerland",
		},
	}
}

func getValuesFromCodeTable(category string, approvalRoleID string, countryID int) []*model.Values {
	var response []*model.Values
	approvalRole := postgres.GetCodeValueByID(approvalRoleID)
	codes := codeController.GetCodesByCategory(category)

	if category == "Country" {
		return fetchCountryOptions()
	}

	if category == "HcpType" && *approvalRole == "regionalihcp" {
		return postgres.GetCodeValuesForRegionalIHCP(category)
	}

	if category == "RoleMaxPaidTime" && countryID != 11 {
		return postgres.GetCodeValuesForIndonesia(category)
	}

	for _, item := range codes {
		if category == "ActivityType" || category == "ActivityEventType" {
			response = append(response, &model.Values{
				Description: item.Title.String,
				Value:       strconv.Itoa(item.ID),
			})
		} else {
			var value model.Values
			value.Value = item.Value
			value.Description = item.Title.String
			response = append(response, &value)
		}

	}

	return response

}

func ExportRequestorInfoForAdminEntityToExcelModel(input []entity.FormAnswerForAdmins) []*entity.RequestorFormAnswerListExcel {
	var responseExcel []*entity.RequestorFormAnswerListExcel
	for index, item := range input {
		var responseExcelForBOD []*entity.RequestorFormAnswerListExcel
		var responseLevelOfInfluence []entity.LevelOfInfluenceEntity
		var data []interface{}
		var singleDataArray []string
		var eventTypes []string
		var govTypes []string
		var typeOfEngagementsArr []string
		loc := ConvertLocalTimeZone(item.CountryValue.String)
		json.Unmarshal(item.LevelOfInfluence.Bytes, &responseLevelOfInfluence)
		if item.SingleDataArray != nil {
			singleDataArray = *item.SingleDataArray
		}
		if item.EventTypes != nil {
			eventTypes = *item.EventTypes
		}
		if item.GovtNonGovtHcp != nil && len(*item.GovtNonGovtHcp) > 0 && strings.Trim(strings.Trim(fmt.Sprint(*item.GovtNonGovtHcp), "[]"), " ") != "" {
			govTypes = *item.GovtNonGovtHcp
		}
		resExcel := entity.RequestorFormAnswerListExcel{}
		var rowData []interface{}
		resExcel.RowNo = index + 1

		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, item.EventSeq)
			rowData = append(rowData, formAnsSeq)
			data = append(data, formAnsSeq)
		}

		if len(singleDataArray) > 1 {
			rowData = append(rowData, singleDataArray[1])
			data = append(data, singleDataArray[1])
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(eventTypes) > 0 {
			rowData = append(rowData, eventTypes[0])
			data = append(data, eventTypes[0])
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(eventTypes) > 1 {
			eventTypes := eventTypes[1:]
			rowData = append(rowData, strings.Join(eventTypes, ","))
			data = append(data, strings.Join(eventTypes, ","))
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if item.RequestorName.String != "" {
			rowData = append(rowData, item.RequestorName.String)
			data = append(data, item.RequestorName.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(singleDataArray) > 0 {
			rowData = append(rowData, singleDataArray[0])
			data = append(data, singleDataArray[0])
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if item.CreatedDate.Time.String() != "" {
			rowData = append(rowData, item.CreatedDate.Time.In(loc).Format("02-01-2006"))
			data = append(data, item.CreatedDate.Time.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(singleDataArray) > 2 {
			EventStartDate, _ := strconv.Atoi(singleDataArray[2])
			t := time.Unix(int64(EventStartDate), 0)
			rowData = append(rowData, t.In(loc).Format("02-01-2006"))
			data = append(data, t.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(singleDataArray) > 3 {
			EventEndDate, _ := strconv.Atoi(singleDataArray[3])
			t := time.Unix(int64(EventEndDate), 0)
			rowData = append(rowData, t.In(loc).Format("02-01-2006"))
			data = append(data, t.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		// Product
		if item.Product.Valid {
			rowData = append(rowData, item.Product.String)
			data = append(data, item.Product.String)
		} else if len(singleDataArray) > 4 {
			rowData = append(rowData, singleDataArray[4])
			data = append(data, singleDataArray[4])
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(singleDataArray) > 6 {
			rowData = append(rowData, singleDataArray[6])
			data = append(data, singleDataArray[6])
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}

		if len(singleDataArray) > 5 {
			if singleDataArray[5] != "" {
				rowData = append(rowData, singleDataArray[5])
				data = append(data, singleDataArray[5])
			} else {
				rowData = append(rowData, " -")
				data = append(data, " -")
			}
		} else {
			rowData = append(rowData, " -")
			data = append(data, " -")
		}
		var governTypesarr []string
		var speakerNamearr []string
		var specialtyarr []string
		var instituteNamearr []string
		var hcpCostarr []string
		if len(govTypes) > 0 {
			if math.Mod(float64(len(govTypes)), 5) == 0 {
				total := len(govTypes) / 5
				if total == 1 {
					governTypesarr = append(governTypesarr, govTypes[0])
					speakerNamearr = append(speakerNamearr, govTypes[1])
					specialtyarr = append(specialtyarr, govTypes[2])
					instituteNamearr = append(instituteNamearr, govTypes[3])
					hcpCostarr = append(hcpCostarr, govTypes[4])
				} else {
					k := 0
					for i := 0; i < total; i++ {
						governTypesarr = append(governTypesarr, govTypes[0+k])
						speakerNamearr = append(speakerNamearr, govTypes[1+k])
						specialtyarr = append(specialtyarr, govTypes[2+k])
						instituteNamearr = append(instituteNamearr, govTypes[3+k])
						hcpCostarr = append(hcpCostarr, govTypes[4+k])
						k = k + 5
					}
				}
			}
		}
		if item.TypeOfEngagements != nil {
			typeOfEngagementsArr = *item.TypeOfEngagements
		}
		if governTypesarr != nil && len(governTypesarr) > 0 {
			allData := data
			checkArr := 0
			for countHcp, hcpValue := range governTypesarr {
				resExcel2 := entity.RequestorFormAnswerListExcel{}
				hcpData := allData
				allpreviousData := data
				if hcpValue != "" && hcpValue != "false" {
					allpreviousData = append(allpreviousData, hcpValue)
					hcpData = append(hcpData, hcpValue)
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if speakerNamearr[countHcp] != "" {
					allpreviousData = append(allpreviousData, speakerNamearr[countHcp])
					hcpData = append(hcpData, speakerNamearr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if specialtyarr[countHcp] != "" {
					allpreviousData = append(allpreviousData, specialtyarr[countHcp])
					hcpData = append(hcpData, specialtyarr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if instituteNamearr[countHcp] != "" {
					allpreviousData = append(allpreviousData, instituteNamearr[countHcp])
					hcpData = append(hcpData, instituteNamearr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if hcpCostarr[countHcp] != "" {
					hcpData = append(hcpData, "HCP")
				} else {
					hcpData = append(hcpData, " -")
				}
				if hcpCostarr[countHcp] != "" {
					hcpData = append(hcpData, hcpCostarr[countHcp])
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					hcpData = append(hcpData, decimalString)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.Status.String != "" {
					if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						hcpData = append(hcpData, "Exceptional "+item.Status.String)
					} else {
						hcpData = append(hcpData, item.Status.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.CompletionTime.Valid {
					hcpData = append(hcpData, item.CompletionTime.Time.Format("02-01-2006"))
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.ProductOwner.Valid {
					hcpData = append(hcpData, item.ProductOwner.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.RequestingHco.String != "" {
					hcpData = append(hcpData, item.RequestingHco.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.Recipient.String != "" {
					hcpData = append(hcpData, item.Recipient.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						hcpData = append(hcpData, "Yes")
					} else {
						hcpData = append(hcpData, item.MeetingMode.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						hcpData = append(hcpData, "Yes")
					} else {
						hcpData = append(hcpData, item.VirtualEventDetails.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if len(speakerNamearr) == len(typeOfEngagementsArr) {
					if len(typeOfEngagementsArr) > 0 {
						hcpData = append(hcpData, typeOfEngagementsArr[countHcp])
					} else {
						hcpData = append(hcpData, "-")
					}
				} else {
					hcpData = append(hcpData, "-")
				}
				if len(responseLevelOfInfluence) > 0 {
					hcpData = append(hcpData, responseLevelOfInfluence[countHcp].LevelOfInfluence)
				} else {
					hcpData = append(hcpData, "-")
				}
				resExcel2.Data = hcpData
				responseExcelForBOD = append(responseExcelForBOD, &resExcel2)
				// responseExcel = append(responseExcel, &resExcel2)
				if item.HcpTypeOfExpense != nil && len(*item.HcpTypeOfExpense) > 0 {
					for {
						resExcel1 := entity.RequestorFormAnswerListExcel{}
						eventData := allpreviousData
						if item.HcpTypeOfExpense != nil {
							value := *item.HcpTypeOfExpense
							if len(value) > checkArr {
								if value[checkArr] != "" {
									eventData = append(eventData, value[checkArr])
								} else {
									break
								}
							} else {
								break
							}
						}
						if item.HcpCostList != nil {
							expenseCost := *item.HcpCostList
							if len(expenseCost) > checkArr {
								if len(expenseCost) != 0 {
									eventData = append(eventData, expenseCost[checkArr])
								} else {
									eventData = append(eventData, " -")
								}
							} else {
								eventData = append(eventData, " -")
							}
						}
						if item.TotalCost.String != "" {
							totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
							decimalString := fmt.Sprintf("%.0f", totalCost)
							eventData = append(eventData, decimalString)
						} else {
							eventData = append(eventData, " -")
						}
						if item.Status.String != "" {
							if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
								eventData = append(eventData, "Exceptional "+item.Status.String)
							} else {
								eventData = append(eventData, item.Status.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if item.CompletionTime.Valid {
							eventData = append(eventData, item.CompletionTime.Time.Format("02-01-2006"))
						} else {
							eventData = append(eventData, " -")
						}

						if item.ProductOwner.String != "" {
							eventData = append(eventData, item.ProductOwner.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.RequestingHco.String != "" {
							eventData = append(eventData, item.RequestingHco.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.Recipient.String != "" {
							eventData = append(eventData, item.Recipient.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
							if item.MeetingMode.String == "true" {
								eventData = append(eventData, "Yes")
							} else {
								eventData = append(eventData, item.MeetingMode.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
							if item.VirtualEventDetails.String == "true" {
								eventData = append(eventData, "Yes")
							} else {
								eventData = append(eventData, item.VirtualEventDetails.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if len(speakerNamearr) == len(typeOfEngagementsArr) {
							if len(typeOfEngagementsArr) > 0 {
								eventData = append(eventData, typeOfEngagementsArr[countHcp])
							} else {
								eventData = append(eventData, "-")
							}
						} else {
							eventData = append(eventData, "-")
						}
						if len(responseLevelOfInfluence) > 0 {
							eventData = append(eventData, responseLevelOfInfluence[countHcp].LevelOfInfluence)
						} else {
							eventData = append(eventData, "-")
						}
						resExcel1.Data = eventData
						responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
						// responseExcel = append(responseExcel, &resExcel1)
						checkArr++
					}
					checkArr++
				}
			}
		}
		if item.EventExpenses != nil && len(*item.EventExpenses) > 0 {
			allData := data
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, "Event Expense")
			allData = append(allData, " -")
			if item.TotalCost.String != "" {
				totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
				decimalString := fmt.Sprintf("%.0f", totalCost)
				allData = append(allData, decimalString)
			} else {
				allData = append(allData, " -")
			}
			if item.Status.String != "" {
				if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
					allData = append(allData, "Exceptional "+item.Status.String)
				} else {
					allData = append(allData, item.Status.String)
				}
			} else {
				allData = append(allData, " -")
			}
			if item.CompletionTime.Valid {
				allData = append(allData, item.CompletionTime.Time.Format("02-01-2006"))
			} else {
				allData = append(allData, " -")
			}
			if item.ProductOwner.String != "" {
				allData = append(allData, item.ProductOwner.String)
			} else {
				allData = append(allData, " -")
			}
			if item.RequestingHco.String != "" {
				allData = append(allData, item.RequestingHco.String)
			} else {
				allData = append(allData, " -")
			}
			if item.Recipient.String != "" {
				allData = append(allData, item.Recipient.String)
			} else {
				allData = append(allData, " -")
			}
			if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
				if item.MeetingMode.String == "true" {
					allData = append(allData, "Yes")
				} else {
					allData = append(allData, item.MeetingMode.String)
				}
			} else {
				allData = append(allData, " -")
			}
			if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
				if item.VirtualEventDetails.String == "true" {
					allData = append(allData, "Yes")
				} else {
					allData = append(allData, item.VirtualEventDetails.String)
				}
			} else {
				allData = append(allData, " -")
			}
			allData = append(allData, " -")
			allData = append(allData, "-")
			resExcel1 := entity.RequestorFormAnswerListExcel{}
			resExcel1.Data = allData
			responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
			// responseExcel = append(responseExcel, &resExcel1)
			for count, value := range *item.EventExpenses {
				resExcel1 := entity.RequestorFormAnswerListExcel{}
				eventData := data
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				if value != "" {
					eventData = append(eventData, value)
				} else {
					eventData = append(eventData, " -")
				}

				if item.ExpenseCost != nil {
					expenseCost := *item.ExpenseCost
					if len(expenseCost) >= count {
						if len(expenseCost) != 0 {
							eventData = append(eventData, expenseCost[count])
						} else {
							eventData = append(eventData, " -")
						}
					} else {
						eventData = append(eventData, " -")
					}
				}
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					eventData = append(eventData, decimalString)
				} else {
					eventData = append(eventData, " -")
				}
				if item.Status.String != "" {
					if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						eventData = append(eventData, "Exceptional "+item.Status.String)
					} else {
						eventData = append(eventData, item.Status.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				if item.CompletionTime.Valid {
					eventData = append(eventData, item.CompletionTime.Time.Format("02-01-2006"))
				} else {
					eventData = append(eventData, " -")
				}
				if item.ProductOwner.String != "" {
					eventData = append(eventData, item.ProductOwner.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.RequestingHco.String != "" {
					eventData = append(eventData, item.RequestingHco.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.Recipient.String != "" {
					eventData = append(eventData, item.Recipient.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						eventData = append(eventData, "Yes")
					} else {
						eventData = append(eventData, item.MeetingMode.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						eventData = append(eventData, "Yes")
					} else {
						eventData = append(eventData, item.VirtualEventDetails.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				eventData = append(eventData, "-")
				eventData = append(eventData, "-")
				resExcel1.Data = eventData
				responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
				// responseExcel = append(responseExcel, &resExcel1)
			}
		} else {
			resExcel1 := entity.RequestorFormAnswerListExcel{}
			eventData := data
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, "Event Expense")
			eventData = append(eventData, " -")
			if item.TotalCost.String != "" {
				totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
				decimalString := fmt.Sprintf("%.0f", totalCost)
				eventData = append(eventData, decimalString)
			} else {
				eventData = append(eventData, " -")
			}
			if item.Status.String != "" {
				if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
					eventData = append(eventData, "Exceptional "+item.Status.String)
				} else {
					eventData = append(eventData, item.Status.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			if item.CompletionTime.Valid {
				eventData = append(eventData, item.CompletionTime.Time.Format("02-01-2006"))
			} else {
				eventData = append(eventData, " -")
			}
			if item.ProductOwner.String != "" {
				eventData = append(eventData, item.ProductOwner.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.RequestingHco.String != "" {
				eventData = append(eventData, item.RequestingHco.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.Recipient.String != "" {
				eventData = append(eventData, item.Recipient.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
				if item.MeetingMode.String == "true" {
					eventData = append(eventData, "Yes")
				} else {
					eventData = append(eventData, item.MeetingMode.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
				if item.VirtualEventDetails.String == "true" {
					eventData = append(eventData, "Yes")
				} else {
					eventData = append(eventData, item.VirtualEventDetails.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			eventData = append(eventData, "-")
			eventData = append(eventData, "-")
			resExcel1.Data = eventData
			responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
			// responseExcel = append(responseExcel, &resExcel1)
		}
		var bodData []entity.BoardOfDirectorsData
		json.Unmarshal(item.BoardOfDirectory.Bytes, &bodData)
		for i, val := range bodData {
			if len(responseExcelForBOD) >= i+1 {
				responseExcelForBOD[i].Data = append(responseExcelForBOD[i].Data, val.Name, val.Position, val.Question, val.Remarks)
			} else {
				resExcel12 := entity.RequestorFormAnswerListExcel{}
				eventData1 := data
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					eventData1 = append(eventData1, decimalString)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.Status.String != "" {
					if item.IsException != nil && *item.IsException && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						eventData1 = append(eventData1, "Exceptional "+item.Status.String)
					} else {
						eventData1 = append(eventData1, item.Status.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.CompletionTime.Valid {
					eventData1 = append(eventData1, item.CompletionTime.Time.Format("02-01-2006"))
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.ProductOwner.String != "" {
					eventData1 = append(eventData1, item.ProductOwner.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.RequestingHco.String != "" {
					eventData1 = append(eventData1, item.RequestingHco.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.Recipient.String != "" {
					eventData1 = append(eventData1, item.Recipient.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						eventData1 = append(eventData1, "Yes")
					} else {
						eventData1 = append(eventData1, item.MeetingMode.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						eventData1 = append(eventData1, "Yes")
					} else {
						eventData1 = append(eventData1, item.VirtualEventDetails.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, val.Name, val.Position, val.Question, val.Remarks)
				resExcel12.Data = eventData1
				responseExcelForBOD = append(responseExcelForBOD, &resExcel12)
				// responseExcel = append(responseExcel, &resExcel12)
			}
		}
		responseExcel = append(responseExcel, responseExcelForBOD...)
	}

	return responseExcel
}

func ExportRequestorInfoEntityToExcelModel(input []entity.FormAnswer) []*entity.RequestorFormAnswerListExcel {
	var responseExcel []*entity.RequestorFormAnswerListExcel
	for index, item := range input {
		var responseExcelForBOD []*entity.RequestorFormAnswerListExcel
		var responseLevelOfInfluence []entity.LevelOfInfluenceEntity
		loc := ConvertLocalTimeZone(item.CountryValue.String)
		var govTypes []string
		resExcel := entity.RequestorFormAnswerListExcel{}
		var rowData []interface{}
		var data []interface{}
		var typeOfEngagementsArr []string
		json.Unmarshal(item.LevelOfInfluence.Bytes, &responseLevelOfInfluence)
		resExcel.RowNo = index + 1
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, item.EventSeq)
			rowData = append(rowData, formAnsSeq)
			data = append(data, formAnsSeq)
		}

		if item.ActivityName.String != "" {
			rowData = append(rowData, item.ActivityName.String)
			data = append(data, item.ActivityName.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.ActivityTypes.String != "" {
			rowData = append(rowData, strings.ReplaceAll(item.ActivityTypes.String, "|", ","))
			data = append(data, item.ActivityTypes.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.EventTypes.String != "" {
			rowData = append(rowData, strings.ReplaceAll(item.EventTypes.String, "|", ","))
			data = append(data, item.EventTypes.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.RequestorName.String != "" {
			rowData = append(rowData, item.RequestorName.String)
			data = append(data, item.RequestorName.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.EventOwner.String != "" {
			rowData = append(rowData, item.EventOwner.String)
			data = append(data, item.EventOwner.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.CreatedDate.Time.String() != "" {
			rowData = append(rowData, item.CreatedDate.Time.In(loc).Format("02-01-2006"))
			data = append(data, item.CreatedDate.Time.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.StartDate.String != "" {
			EventStartDate, _ := strconv.Atoi(item.StartDate.String)
			t := time.Unix(int64(EventStartDate), 0)
			rowData = append(rowData, t.In(loc).Format("02-01-2006"))
			data = append(data, t.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.EndDate.String != "" {
			EventEndDate, _ := strconv.Atoi(item.EndDate.String)
			t := time.Unix(int64(EventEndDate), 0)
			rowData = append(rowData, t.In(loc).Format("02-01-2006"))
			data = append(data, t.In(loc).Format("02-01-2006"))
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.ProductName.String != "" {
			rowData = append(rowData, item.ProductName.String)
			data = append(data, item.ProductName.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if item.Venue.String != "" {
			rowData = append(rowData, item.Venue.String)
			data = append(data, item.Venue.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}
		if item.GovtNonGovtHcp != nil && len(*item.GovtNonGovtHcp) > 0 && strings.Trim(strings.Trim(fmt.Sprint(*item.GovtNonGovtHcp), "[]"), " ") != "" {
			govTypes = *item.GovtNonGovtHcp
		}
		var governTypesarr []string
		var speakerNamearr []string
		var specialtyarr []string
		var instituteNamearr []string
		var hcpCostarr []string
		if len(govTypes) > 0 {
			if math.Mod(float64(len(govTypes)), 5) == 0 {
				total := len(govTypes) / 5
				if total == 1 {
					governTypesarr = append(governTypesarr, govTypes[0])
					speakerNamearr = append(speakerNamearr, govTypes[1])
					specialtyarr = append(specialtyarr, govTypes[2])
					instituteNamearr = append(instituteNamearr, govTypes[3])
					hcpCostarr = append(hcpCostarr, govTypes[4])
				} else {
					k := 0
					for i := 0; i < total; i++ {
						governTypesarr = append(governTypesarr, govTypes[0+k])
						speakerNamearr = append(speakerNamearr, govTypes[1+k])
						specialtyarr = append(specialtyarr, govTypes[2+k])
						instituteNamearr = append(instituteNamearr, govTypes[3+k])
						hcpCostarr = append(hcpCostarr, govTypes[4+k])
						k = k + 5
					}
				}
			}
		}
		if item.TypeOfEngagements != nil {
			typeOfEngagementsArr = *item.TypeOfEngagements
		}
		if item.EventOrganizer.String != "" {
			rowData = append(rowData, item.EventOrganizer.String)
			data = append(data, item.EventOrganizer.String)
		} else {
			rowData = append(rowData, " -")
			data = append(data, "-")
		}

		if governTypesarr != nil && len(governTypesarr) > 0 {
			allData := data
			checkArr := 0
			for countHcp, hcpValue := range governTypesarr {
				resExcel2 := entity.RequestorFormAnswerListExcel{}
				hcpData := allData
				allpreviousData := data
				if hcpValue != "" && hcpValue != "false" {
					allpreviousData = append(allpreviousData, hcpValue)
					hcpData = append(hcpData, hcpValue)
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if speakerNamearr[countHcp] != "" {
					allpreviousData = append(allpreviousData, speakerNamearr[countHcp])
					hcpData = append(hcpData, speakerNamearr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if specialtyarr[countHcp] != "" {
					allpreviousData = append(allpreviousData, specialtyarr[countHcp])
					hcpData = append(hcpData, specialtyarr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if instituteNamearr[countHcp] != "" {
					allpreviousData = append(allpreviousData, instituteNamearr[countHcp])
					hcpData = append(hcpData, instituteNamearr[countHcp])
				} else {
					allpreviousData = append(allpreviousData, "-")
					hcpData = append(hcpData, " -")
				}
				if hcpCostarr[countHcp] != "" {
					hcpData = append(hcpData, "HCP")
				} else {
					hcpData = append(hcpData, " -")
				}
				if hcpCostarr[countHcp] != "" {
					hcpData = append(hcpData, hcpCostarr[countHcp])
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					hcpData = append(hcpData, decimalString)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.Status.String != "" {
					if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						hcpData = append(hcpData, "Exceptional "+item.Status.String)
					} else {
						hcpData = append(hcpData, item.Status.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.CompletionDate.Valid {
					hcpData = append(hcpData, item.CompletionDate.Time.Format("02-01-2006"))
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.ProductOwner.String != "" {
					hcpData = append(hcpData, item.ProductOwner.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.RequestingHco.String != "" {
					hcpData = append(hcpData, item.RequestingHco.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.Recipient.String != "" {
					hcpData = append(hcpData, item.Recipient.String)
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						hcpData = append(hcpData, "Yes")
					} else {
						hcpData = append(hcpData, item.MeetingMode.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						hcpData = append(hcpData, "Yes")
					} else {
						hcpData = append(hcpData, item.VirtualEventDetails.String)
					}
				} else {
					hcpData = append(hcpData, " -")
				}
				if len(speakerNamearr) == len(typeOfEngagementsArr) {
					if len(typeOfEngagementsArr) > 0 {
						hcpData = append(hcpData, typeOfEngagementsArr[countHcp])
					} else {
						hcpData = append(hcpData, "-")
					}
				} else {
					hcpData = append(hcpData, "-")
				}
				if len(responseLevelOfInfluence) > 0 {
					hcpData = append(hcpData, responseLevelOfInfluence[countHcp].LevelOfInfluence)
				} else {
					hcpData = append(hcpData, "-")
				}
				resExcel2.Data = hcpData
				responseExcelForBOD = append(responseExcelForBOD, &resExcel2)
				// responseExcel = append(responseExcel, &resExcel2)
				if item.HcpTypeOfExpense != nil && len(*item.HcpTypeOfExpense) > 0 {
					for {
						resExcel1 := entity.RequestorFormAnswerListExcel{}
						eventData := allpreviousData
						if item.HcpTypeOfExpense != nil {
							value := *item.HcpTypeOfExpense
							if len(value) > checkArr {
								if value[checkArr] != "" {
									eventData = append(eventData, value[checkArr])
								} else {
									break
								}
							} else {
								break
							}
						}
						if item.HcpCostList != nil {
							expenseCost := *item.HcpCostList
							if len(expenseCost) > checkArr {
								if len(expenseCost) != 0 {
									eventData = append(eventData, expenseCost[checkArr])
								} else {
									eventData = append(eventData, " -")
								}
							} else {
								eventData = append(eventData, " -")
							}
						}
						if item.TotalCost.String != "" {
							totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
							decimalString := fmt.Sprintf("%.0f", totalCost)
							eventData = append(eventData, decimalString)
						} else {
							eventData = append(eventData, " -")
						}
						if item.Status.String != "" {
							if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
								eventData = append(eventData, "Exceptional "+item.Status.String)
							} else {
								eventData = append(eventData, item.Status.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if item.CompletionDate.Valid {
							eventData = append(eventData, item.CompletionDate.Time.Format("02-01-2006"))
						} else {
							eventData = append(eventData, " -")
						}
						if item.ProductOwner.String != "" {
							eventData = append(eventData, item.ProductOwner.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.RequestingHco.String != "" {
							eventData = append(eventData, item.RequestingHco.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.Recipient.String != "" {
							eventData = append(eventData, item.Recipient.String)
						} else {
							eventData = append(eventData, " -")
						}
						if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
							if item.MeetingMode.String == "true" {
								eventData = append(eventData, "Yes")
							} else {
								eventData = append(eventData, item.MeetingMode.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
							if item.VirtualEventDetails.String == "true" {
								eventData = append(eventData, "Yes")
							} else {
								eventData = append(eventData, item.VirtualEventDetails.String)
							}
						} else {
							eventData = append(eventData, " -")
						}
						if len(speakerNamearr) == len(typeOfEngagementsArr) {
							if len(typeOfEngagementsArr) > 0 {
								eventData = append(eventData, typeOfEngagementsArr[countHcp])
							} else {
								eventData = append(eventData, "-")
							}
						} else {
							eventData = append(eventData, "-")
						}
						if len(responseLevelOfInfluence) > 0 {
							eventData = append(eventData, responseLevelOfInfluence[countHcp].LevelOfInfluence)
						} else {
							eventData = append(eventData, "-")
						}
						resExcel1.Data = eventData
						responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
						// responseExcel = append(responseExcel, &resExcel1)
						checkArr++
					}
					checkArr++
				}
			}
		}
		if item.EventExpenses != nil && len(*item.EventExpenses) > 0 {
			allData := data
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, " -")
			allData = append(allData, "Event Expense")
			allData = append(allData, " -")
			if item.TotalCost.String != "" {
				totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
				decimalString := fmt.Sprintf("%.0f", totalCost)
				allData = append(allData, decimalString)
			} else {
				allData = append(allData, " -")
			}
			if item.Status.String != "" {
				if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
					allData = append(allData, "Exceptional "+item.Status.String)
				} else {
					allData = append(allData, item.Status.String)
				}
			} else {
				allData = append(allData, " -")
			}
			if item.CompletionDate.Valid {
				allData = append(allData, item.CompletionDate.Time.Format("02-01-2006"))
			} else {
				allData = append(allData, " -")
			}
			if item.ProductOwner.String != "" {
				allData = append(allData, item.ProductOwner.String)
			} else {
				allData = append(allData, " -")
			}
			if item.RequestingHco.String != "" {
				allData = append(allData, item.RequestingHco.String)
			} else {
				allData = append(allData, " -")
			}
			if item.Recipient.String != "" {
				allData = append(allData, item.Recipient.String)
			} else {
				allData = append(allData, " -")
			}
			if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
				if item.MeetingMode.String == "true" {
					allData = append(allData, "Yes")
				} else {
					allData = append(allData, item.MeetingMode.String)
				}
			} else {
				allData = append(allData, " -")
			}
			if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
				if item.VirtualEventDetails.String == "true" {
					allData = append(allData, "Yes")
				} else {
					allData = append(allData, item.VirtualEventDetails.String)
				}
			} else {
				allData = append(allData, " -")
			}
			allData = append(allData, "-")
			allData = append(allData, "-")
			resExcel1 := entity.RequestorFormAnswerListExcel{}
			resExcel1.Data = allData
			responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
			// responseExcel = append(responseExcel, &resExcel1)
			for count, value := range *item.EventExpenses {
				resExcel1 := entity.RequestorFormAnswerListExcel{}
				eventData := data
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				eventData = append(eventData, " -")
				if value != "" {
					eventData = append(eventData, value)
				} else {
					eventData = append(eventData, " -")
				}

				if item.ExpenseCost != nil {
					expenseCost := *item.ExpenseCost
					if len(expenseCost) >= count {
						if len(expenseCost) != 0 {
							eventData = append(eventData, expenseCost[count])
						} else {
							eventData = append(eventData, " -")
						}
					} else {
						eventData = append(eventData, " -")
					}
				}
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					eventData = append(eventData, decimalString)
				} else {
					eventData = append(eventData, " -")
				}
				if item.Status.String != "" {
					if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						eventData = append(eventData, "Exceptional "+item.Status.String)
					} else {
						eventData = append(eventData, item.Status.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				if item.CompletionDate.Valid {
					eventData = append(eventData, item.CompletionDate.Time.Format("02-01-2006"))
				} else {
					eventData = append(eventData, " -")
				}
				if item.ProductOwner.String != "" {
					eventData = append(eventData, item.ProductOwner.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.RequestingHco.String != "" {
					eventData = append(eventData, item.RequestingHco.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.Recipient.String != "" {
					eventData = append(eventData, item.Recipient.String)
				} else {
					eventData = append(eventData, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						eventData = append(eventData, "Yes")
					} else {
						eventData = append(eventData, item.MeetingMode.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						eventData = append(eventData, "Yes")
					} else {
						eventData = append(eventData, item.VirtualEventDetails.String)
					}
				} else {
					eventData = append(eventData, " -")
				}
				eventData = append(eventData, "-")
				eventData = append(eventData, "-")
				resExcel1.Data = eventData
				responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
				// responseExcel = append(responseExcel, &resExcel1)
			}
		} else {
			resExcel1 := entity.RequestorFormAnswerListExcel{}
			eventData := data
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, " -")
			eventData = append(eventData, "Event Expense")
			eventData = append(eventData, " -")
			if item.TotalCost.String != "" {
				totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
				decimalString := fmt.Sprintf("%.0f", totalCost)
				eventData = append(eventData, decimalString)
			} else {
				eventData = append(eventData, " -")
			}
			if item.Status.String != "" {
				if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
					eventData = append(eventData, "Exceptional "+item.Status.String)
				} else {
					eventData = append(eventData, item.Status.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			if item.CompletionDate.Valid {
				eventData = append(eventData, item.CompletionDate.Time.Format("02-01-2006"))
			} else {
				eventData = append(eventData, " -")
			}
			if item.ProductOwner.String != "" {
				eventData = append(eventData, item.ProductOwner.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.RequestingHco.String != "" {
				eventData = append(eventData, item.RequestingHco.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.Recipient.String != "" {
				eventData = append(eventData, item.Recipient.String)
			} else {
				eventData = append(eventData, " -")
			}
			if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
				if item.MeetingMode.String == "true" {
					eventData = append(eventData, "Yes")
				} else {
					eventData = append(eventData, item.MeetingMode.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
				if item.VirtualEventDetails.String == "true" {
					eventData = append(eventData, "Yes")
				} else {
					eventData = append(eventData, item.VirtualEventDetails.String)
				}
			} else {
				eventData = append(eventData, " -")
			}
			eventData = append(eventData, "-")
			eventData = append(eventData, "-")
			resExcel1.Data = eventData
			responseExcelForBOD = append(responseExcelForBOD, &resExcel1)
			// responseExcel = append(responseExcel, &resExcel1)
		}
		var bodData []entity.BoardOfDirectorsData
		json.Unmarshal(item.BoardOfDirectory.Bytes, &bodData)
		for i, val := range bodData {
			if len(responseExcelForBOD) >= i+1 {
				responseExcelForBOD[i].Data = append(responseExcelForBOD[i].Data, val.Name, val.Position, val.Question, val.Remarks)
			} else {
				resExcel12 := entity.RequestorFormAnswerListExcel{}
				eventData1 := data
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, " -")
				if item.TotalCost.String != "" {
					totalCost, _ := strconv.ParseFloat(item.TotalCost.String, 64)
					decimalString := fmt.Sprintf("%.0f", totalCost)
					eventData1 = append(eventData1, decimalString)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.Status.String != "" {
					if item.IsExceptionalApprover != nil && *item.IsExceptionalApprover && (item.Status.String == "Approved" || item.Status.String == "Pending" || item.Status.String == "Return" || item.Status.String == "Completed") {
						eventData1 = append(eventData1, "Exceptional "+item.Status.String)
					} else {
						eventData1 = append(eventData1, item.Status.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.CompletionDate.Valid {
					eventData1 = append(eventData1, item.CompletionDate.Time.Format("02-01-2006"))
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.ProductOwner.String != "" {
					eventData1 = append(eventData1, item.ProductOwner.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.RequestingHco.String != "" {
					eventData1 = append(eventData1, item.RequestingHco.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.Recipient.String != "" {
					eventData1 = append(eventData1, item.Recipient.String)
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.MeetingMode.String != "" && item.MeetingMode.String != "false" {
					if item.MeetingMode.String == "true" {
						eventData1 = append(eventData1, "Yes")
					} else {
						eventData1 = append(eventData1, item.MeetingMode.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				if item.VirtualEventDetails.String != "" && item.VirtualEventDetails.String != "false" {
					if item.VirtualEventDetails.String == "true" {
						eventData1 = append(eventData1, "Yes")
					} else {
						eventData1 = append(eventData1, item.VirtualEventDetails.String)
					}
				} else {
					eventData1 = append(eventData1, " -")
				}
				eventData1 = append(eventData1, "-")
				eventData1 = append(eventData1, " -")
				eventData1 = append(eventData1, val.Name, val.Position, val.Question, val.Remarks)
				resExcel12.Data = eventData1
				responseExcelForBOD = append(responseExcelForBOD, &resExcel12)
				// responseExcel = append(responseExcel, &resExcel12)
			}
		}
		responseExcel = append(responseExcel, responseExcelForBOD...)
	}
	return responseExcel
}

func delete_str(selector string, s []string) []string {
	var r []string
	for _, str := range s {
		if str != selector {
			r = append(r, str)
		}
	}
	return r
}

func ListRequestorResponseEntityToModel(input []entity.FormAnswer, commentInput map[string][]entity.RequestorResponse, userID string) []*model.RequesterSubmittedFormAnswerList {
	var response []*model.RequesterSubmittedFormAnswerList
	for _, item := range input {
		var res model.RequesterSubmittedFormAnswerList
		var isRecallAccess bool
		res.ID = item.ID.String
		if item.CreatedDate.Time.String() != "" {
			res.SubmitedDate = util.GetTimeUnixTimeStamp(item.CreatedDate.Time)
		}
		res.ApprovalStatus = item.Status.String
		res.TotalCost, _ = strconv.ParseFloat(item.TotalCost.String, 64)
		res.TypeOfActivity = strings.Split(item.ActivityTypes.String, "|")
		res.TypeOfEvent = strings.Split(item.EventTypes.String, "|")

		if item.StartDate.String != "" {
			res.EventStartDate, _ = strconv.Atoi(item.StartDate.String)
		} else {
			res.EventStartDate = 0
		}

		if item.EndDate.String != "" {
			res.EventEndDate, _ = strconv.Atoi(item.EndDate.String)
		} else {
			res.EventEndDate = 0
		}

		if item.ApproveAccessTime.Int64 != 0 && item.ApproveCancelTime.Int64 < item.ApproveAccessTime.Int64 {
			loc, _ := time.LoadLocation("UTC")
			todayDate := time.Now()
			todayLoc := todayDate.In(loc).Format("2006-01-02T15:04:05Z07:00")
			TodayLocTime, _ := time.Parse("2006-01-02T15:04:05Z07:00", todayLoc)
			t := time.Unix(int64(item.ApproveAccessTime.Int64), 0)
			recallAccessTime := t.In(loc).Format("2006-01-02T15:04:05Z07:00")
			startDate, _ := time.Parse("2006-01-02T15:04:05Z07:00", recallAccessTime)
			durationTodayTime := TodayLocTime.Sub(startDate)
			minutes := int(durationTodayTime.Minutes())
			log.Println(minutes, durationTodayTime, startDate, TodayLocTime)
			if minutes < 5 {
				isRecallAccess = false
				res.IsRecallAccess = &isRecallAccess
			} else {
				isRecallAccess = true
				res.IsRecallAccess = &isRecallAccess
			}
		} else {
			isRecallAccess = true
			res.IsRecallAccess = &isRecallAccess
		}

		res.ActivityName = item.ActivityName.String
		res.RequestorName = item.RequestorName.String
		res.ProductName = item.ProductName.String
		res.DepartmentName = item.Team.String
		res.ApprovalStatusValue = item.StatusValue.String
		res.CountryTitle = item.CountryTitle.String
		res.CountryValue = item.CountryValue.String
		res.CurrencyTitle = item.CurrencyTitle.String
		res.CurrencyValue = item.CurrencyValue.String
		res.IsOwner = item.IsOwner
		res.IsExceptionalApprover = item.IsExceptionalApprover
		res.RequestorActiveDirectory = item.RequestorActiveDirectory.String
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, item.EventSeq)
			res.EventID = formAnsSeq
		}

		//Add commments
		var commentModels []*model.RequestorResponse
		for _, comment := range commentInput[res.ID] {
			commentModel := &model.RequestorResponse{}
			commentModel.ApproverName = comment.Name.String
			commentModel.ApproverComment = comment.Comment.String
			commentModel.ApproverCommentDate = util.GetTimeUnixTimeStamp(comment.Date.Time)
			commentModels = append(commentModels, commentModel)
		}

		res.Response = commentModels

		response = append(response, &res)

	}

	return response
}

func ListRequestorResponseForAdminEntityToModel(input []entity.FormAnswerForAdmins, commentInput map[string][]entity.RequestorResponse) []*model.RequesterSubmittedFormAnswerList {
	var response []*model.RequesterSubmittedFormAnswerList
	for _, item := range input {

		var singleDataArray []string
		var eventTypes []string
		if item.SingleDataArray != nil {
			singleDataArray = *item.SingleDataArray
		}
		if item.EventTypes != nil {
			eventTypes = *item.EventTypes
		}
		var res model.RequesterSubmittedFormAnswerList
		res.ID = item.ID.String
		if item.CreatedDate.Time.String() != "" {
			res.SubmitedDate = util.GetTimeUnixTimeStamp(item.CreatedDate.Time)
		}
		res.ApprovalStatus = item.Status.String
		res.TotalCost, _ = strconv.ParseFloat(item.TotalCost.String, 64)
		if len(eventTypes) > 0 {
			res.TypeOfActivity = strings.Split(eventTypes[0], "|")
		}

		if len(eventTypes) > 1 {
			eventTypes := eventTypes[1:]
			res.TypeOfEvent = eventTypes
		}

		if len(singleDataArray) > 2 {
			res.EventStartDate, _ = strconv.Atoi(singleDataArray[2])
		} else {
			res.EventStartDate = 0
		}

		if len(singleDataArray) > 3 {
			res.EventEndDate, _ = strconv.Atoi(singleDataArray[3])
		} else {
			res.EventEndDate = 0
		}

		if len(singleDataArray) > 1 {
			res.ActivityName = singleDataArray[1]
		}

		if len(singleDataArray) > 4 {
			res.ProductName = singleDataArray[4]
		}

		// Product
		if item.Product.Valid {
			res.ProductName = item.Product.String
		}

		res.ApprovalStatusValue = item.StatusValue.String
		res.DepartmentName = item.Team.String
		res.CountryTitle = item.CountryTitle.String
		res.CountryValue = item.CountryValue.String
		res.CurrencyTitle = item.CurrencyTitle.String
		res.CurrencyValue = item.CurrencyValue.String
		res.RequestorName = item.RequestorName.String
		res.IsExceptionalApprover = item.IsException
		res.RequestorActiveDirectory = item.RequestorActiveDirectory.String
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, item.EventSeq)
			res.EventID = formAnsSeq
		}
		var commentModels []*model.RequestorResponse
		for _, comment := range commentInput[res.ID] {
			commentModel := &model.RequestorResponse{}
			commentModel.ApproverName = comment.Name.String
			commentModel.ApproverComment = comment.Comment.String
			commentModel.ApproverCommentDate = util.GetTimeUnixTimeStamp(comment.Date.Time)
			commentModels = append(commentModels, commentModel)
		}

		res.Response = commentModels
		response = append(response, &res)
	}
	return response
}

func ListNotificationStatusEntityToModel(input []entity.ListNotificationStatusEntity, userID string) ([]*model.GetNotificationStatus, error) {
	var response []*model.GetNotificationStatus
	for _, item := range input {
		var res model.GetNotificationStatus
		res.ID = item.ID.String()
		res.FormAnswerID = item.FormAnswerID.String()
		res.IsRead = item.HasRead
		if item.DateCreated.Time.String() != "" {
			res.DateCreated = util.GetTimeUnixTimeStamp(item.DateCreated.Time)
		}
		codes := codeController.GetIdKeyCodes()["approvedstatus"]
		status := codes[item.Status].Value
		userName, err := postgres.FetchUserNameByID(item.ActionedBy.String())
		if err != nil {
			return nil, err
		}
		var message string
		if status == "pending" {
			message = "Event : " + item.EventID.String + " requires your approval. "
		} else if status == "approved" {
			message = "Event :" + item.EventID.String + " has been approved by " + userName
		} else if status == "requestorresponse" {
			message = "Event :" + item.EventID.String + " requires your response. "
		} else if status == "pendingresponse" {
			message = "Event :" + item.EventID.String + " has been resubmitted by " + userName
		} else if status == "rejected" {
			message = "Event :" + item.EventID.String + " has been rejected by " + userName
		} else if status == "return" {
			message = "Event :" + item.EventID.String + " has been returned by " + userName
		} else if status == "finalapproved" {
			message = "Event :" + item.EventID.String + " has completed approvals."
		}
		code := codeController.GetIdKeyCodes()["formanswertype"]
		status1 := code[item.Status].Value
		if status1 == "completed" {
			message = "Event :" + item.EventID.String + " created by " + userName + " has been completed."
		}
		res.Message = message
		response = append(response, &res)

	}
	return response, nil
}

func ListPendingApprovalEntityToModel(input []entity.ListPendingApprovalEntity, userID string, userRole string) []*model.PendingApproval {
	var response []*model.PendingApproval
	tempStatus := "Pending Approval"
	for _, item := range input {
		var res model.PendingApproval
		var isRecallaccess bool
		res.ID = item.FormAnswerID.String

		code := codeController.GetValueKeyCodes()["approvedstatus"]
		StatusID := code["rejected"].ID
		cancelledStatusID := code["cancelled"].ID
		if item.FormStatus == StatusID {
			res.ApprovalStatus = "Rejected"
			res.ApprovalStatusValue = "rejected"
		} else if item.FormStatus == cancelledStatusID {
			res.ApprovalStatus = "Cancelled"
			res.ApprovalStatusValue = "cancelled"
		} else if item.StatusValue.String == "pending" {
			if item.SequenceNo == 1 {
				res.ApprovalStatus = tempStatus
				res.ApprovalStatusValue = "pendingapproval"
			} else {
				res.ApprovalStatus = item.Status.String
				res.ApprovalStatusValue = item.StatusValue.String
			}
		} else {
			res.ApprovalStatus = item.Status.String
			res.ApprovalStatusValue = item.StatusValue.String
		}

		res.RequestorName = item.Name.String
		if item.SubmissionDate.Time.String() != "" {
			res.SubmitedDate = util.GetTimeUnixTimeStamp(item.SubmissionDate.Time)
		}

		res.TotalCost, _ = strconv.ParseFloat(item.TotalCost.String, 64)

		if item.StartDate.String != "" {
			res.EventStartDate, _ = strconv.Atoi(item.StartDate.String)
		} else {
			res.EventStartDate = 0
		}

		if item.EndDate.String != "" {
			res.EventEndDate, _ = strconv.Atoi(item.EndDate.String)
		} else {
			res.EventEndDate = 0
		}
		if item.RecallAccessTime.Int64 != 0 && item.RecallCancelTime.Int64 < item.RecallAccessTime.Int64 {
			loc, _ := time.LoadLocation("UTC")
			todayDate := time.Now()
			todayLoc := todayDate.In(loc).Format("2006-01-02T15:04:05Z07:00")
			TodayLocTime, _ := time.Parse("2006-01-02T15:04:05Z07:00", todayLoc)
			t := time.Unix(int64(item.RecallAccessTime.Int64), 0)
			recallAccessTime := t.In(loc).Format("2006-01-02T15:04:05Z07:00")
			startDate, _ := time.Parse("2006-01-02T15:04:05Z07:00", recallAccessTime)
			durationTodayTime := TodayLocTime.Sub(startDate)
			minutes := int(durationTodayTime.Minutes())
			log.Println(minutes, durationTodayTime, startDate, TodayLocTime)
			if minutes < 5 {
				isRecallaccess = false
				res.IsApproverAccess = &isRecallaccess
			} else {
				isRecallaccess = true
				res.IsApproverAccess = &isRecallaccess
			}
		} else {
			isRecallaccess = true
			res.IsApproverAccess = &isRecallaccess
		}
		isRecall := item.IsRecall.Bool
		res.IsRecall = &isRecall
		res.DepartmentName = item.Team.String
		res.ActivityName = item.ActivityName.String
		res.ProductName = item.ProductName.String
		res.CountryTitle = item.CountryTitle
		res.CountryValue = item.CountryValue
		res.CurrencyTitle = item.CurrencyTitle
		res.CurrencyValue = item.CurrencyValue
		res.TypeOfActivity = strings.Split(item.ActivityType.String, "|")
		res.TypeOfEvent = strings.Split(item.EventType.String, "|")
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, item.EventSeq)
			res.EventID = formAnsSeq
		}
		if item.ChangeRequestSummary.String != "" {
			changeReqestSummary := item.ChangeRequestSummary.String
			res.ChangeRequestSummary = &changeReqestSummary
		}
		res.IsExceptionalApprover = item.IsExceptionalApprover
		response = append(response, &res)
	}
	// sort.Slice(response, func(i, j int) bool {
	// 	return response[i].SubmitedDate > response[j].SubmitedDate
	// })
	return response
}

func MapSubmissionFormApprovalModelToEntity(inputModel *model.FormApprovalInput, userUUID uuid.UUID, approvalRoleID *string, country int) (*entity.SubmissionFormApprovalEntity, error) {
	uuid, err := uuid.FromString(inputModel.ID)
	if err != nil {
		return nil, errors.New("invalid form answer ID")
	}

	if !postgres.IsFormAnswerExists(&uuid) {
		return nil, errors.New("form answer does not exist")
	}

	action := strings.TrimSpace(inputModel.Action)
	if action == "" {
		return nil, errors.New("invalid action")
	}

	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	_, valid := codes[action]
	if !valid {
		return nil, errors.New("invalid action")

	}
	var result entity.SubmissionFormApprovalEntity
	result.ApprovedBy = userUUID
	result.FormAnswerID = uuid
	result.Action = codes[action].ID
	result.ActionValue = codes[action].Value
	result.ActionTitle = codes[action].Title.String
	result.ApprovalRole = *approvalRoleID

	if inputModel.Comment != nil {
		result.Comment = *inputModel.Comment
	}

	if inputModel.IsExceptionalApprover != nil {
		result.IsExceptionalApprover = *inputModel.IsExceptionalApprover
	}

	if inputModel.IsCompliance != nil {
		result.IsCompliance = *inputModel.IsCompliance
	}

	userGroupID, err := postgres.GetGroupIDByUserID(result.ApprovedBy.String(), result.FormAnswerID.String())
	if err != nil {
		log.Printf("%s - Error: %s", err.Error())
	}
	groupCode := codeController.GetIdKeyCodes()
	code := groupCode["group"]
	userGroupCode := code[userGroupID].Value
	localRegionalGroupID := code[userGroupID].ID
	requestorID, err := postgres.GetRequesterIDByFormAnswer(inputModel.ID)
	if err != nil {
		return nil, err
	}
	receiverID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(result.FormAnswerID)
	result.RequestorID = *requestorID
	if result.Action == 60 {

		notificationErr := postgres.InsertStatusNotification(result.FormAnswerID.String(), result.ApprovedBy.String(), result.Action, receiverID)
		if notificationErr != nil {
			return nil, errors.New(notificationErr.Error())
		}
		emailData, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
		if err != nil {
			return nil, errors.New(err.Error())
		}
		data := SetEmailData(emailData)
		email, _ := postgres.GetEmailIDFromFormAnswerID(result.FormAnswerID)
		emailContent := util.SendEmailScenarioTwo(email, data)
		emailCode := codeController.GetValueKeyCodes()["typeofemail"]
		requestorStatus := emailCode["requireresponse"].ID
		newEmailContent := strip.StripTags(emailContent)
		_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newEmailContent, result.ApprovedBy.String(), receiverID)

		emailOwner, ownerId, err2 := postgres.GetOwnersEmailIDFromFormAnswerID(result.FormAnswerID)
		if err2 != nil {
			log.Printf("Error: %s ", err2.Error())
		} else {
			if emailOwner != "" {
				if emailOwner != email {
					emailOwnerContent := util.SendEmailScenarioTwo(emailOwner, data)
					newOwnerEmailContent := strip.StripTags(emailOwnerContent)
					_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newOwnerEmailContent, result.ApprovedBy.String(), ownerId)
				}
			}
		}
	}

	if userGroupCode == "local" {
		if !postgres.CheckPreviousApproverAction(result.FormAnswerID.String(), result.ApprovedBy.String(), userGroupCode) {
			return nil, errors.New("request not approved by previous approver")
		}

		if postgres.AlreadyApprovedByApprover(result.ApprovedBy.String(), result.FormAnswerID.String()) {
			return nil, errors.New("you have already taken action on this form")
		}

		var action string = "pending"
		if result.Action == 58 {
			remainingApprovalCount := postgres.CountRemainingPendingApproval(context.Background(), result.FormAnswerID.String(), result.ApprovedBy.String(), localRegionalGroupID)

			if !result.IsCompliance && remainingApprovalCount == 0 {
				action = "approved"
				emailData, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
				if err != nil {
					return nil, errors.New(err.Error())
				}
				data := SetEmailData(emailData)
				email, _ := postgres.GetEmailIDFromFormAnswerID(result.FormAnswerID)
				emailContent := util.SendEmailScenarioFive(email, data)
				emailCode := codeController.GetValueKeyCodes()["typeofemail"]
				requestorStatus := emailCode["approverapproved"].ID
				newEmailContent := strip.StripTags(emailContent)
				_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newEmailContent, result.ApprovedBy.String(), receiverID)

				emailOwner, ownerId, err := postgres.GetOwnersEmailIDFromFormAnswerID(result.FormAnswerID)
				if err != nil {
					panic(err)
				}
				if emailOwner != "" && emailOwner != email {
					emailOwnerContent := util.SendEmailScenarioFive(emailOwner, data)
					_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, strip.StripTags(emailOwnerContent), result.ApprovedBy.String(), ownerId)

				}
				emailData1, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
				if err != nil {
					return nil, errors.New(err.Error())
				}

				data1 := SetEmailData(emailData1)
				emails, _ := postgres.GeEmailIDByFormID(result.FormAnswerID.String())
				approverStatus := emailCode["requireapproval"].ID
				var newApproverEmailContent string
				for _, val := range emails {
					approverEmailContent := util.SendEmailScenarioOne(val, data1)
					newApproverEmailContent = strip.StripTags(approverEmailContent)
				}
				code := codeController.GetValueKeyCodes()["approvedstatus"]
				status := code["pending"].ID

				userIDs, _ := postgres.GeRegionalUserIDByFormID(result.FormAnswerID.String())
				for _, userid := range userIDs {
					_ = postgres.InsertStatusNotification(result.FormAnswerID.String(), userUUID.String(), status, userid)
					_ = postgres.InsertEmailLog(result.FormAnswerID.String(), approverStatus, newApproverEmailContent, userUUID.String(), userid)
				}
			}

		} else if result.Action == 59 { //rejected
			action = "rejected"
			emailData, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
			if err != nil {
				return nil, errors.New(err.Error())
			}
			data := SetEmailData(emailData)
			email, _ := postgres.GetEmailIDFromFormAnswerID(result.FormAnswerID)
			allEmail, _ := postgres.GetEmailIDOfApproversFromFormAnswerID(result.FormAnswerID)
			allEmail = append(allEmail, email)
			emailOwner, ownerId, err2 := postgres.GetOwnersEmailIDFromFormAnswerID(result.FormAnswerID)
			if err2 != nil {
				log.Printf("Error: %s ", err2.Error())
			} else {
				if emailOwner != "" {
					if emailOwner != email {
						allEmail = append(allEmail, emailOwner)
					}
				}
			}
			var emailContent string
			for _, value := range allEmail {
				emailContent = util.SendEmailScenarioFourA(value, data)
			}
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			requestorStatus := emailCode["approverrejected"].ID
			newEmailContent := strip.StripTags(emailContent)
			requestorID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(result.FormAnswerID)
			approverID, _ := postgres.GetIDOfApproversFromFormAnswerID(result.FormAnswerID)
			receiverIDs := append(approverID, requestorID)
			if ownerId != "" {
				if ownerId != requestorID {
					receiverIDs = append(receiverIDs, ownerId)
				}
			}
			for _, receiver := range receiverIDs {
				_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newEmailContent, result.ApprovedBy.String(), receiver)
			}

		} else if result.Action == 60 {
			action = "requestorresponse"
		}

		result.ApprovedStatus = codeController.GetValueKeyCodes()["approvedstatus"][action].ID
		result.GroupStatus = codeController.GetValueKeyCodes()["group"][userGroupCode].ID

	} else {
		if !postgres.AlreadyApprovedByLocalApprovers(result.FormAnswerID.String()) {
			return nil, errors.New("request is pending at local stage")
		}

		if country == 900001 && userGroupCode == "regional" {
			if !postgres.CheckPreviousApproverAction(result.FormAnswerID.String(), result.ApprovedBy.String(), userGroupCode) {
				return nil, errors.New("request not approved by previous approver")
			}
		}

		if postgres.AlreadyApprovedByApprover(result.ApprovedBy.String(), result.FormAnswerID.String()) {
			return nil, errors.New("you have already taken action on this form")
		}

		var action string = `pending`
		if result.Action == 58 {
			remainingApprovalCount := postgres.CountRemainingPendingApproval(context.Background(), result.FormAnswerID.String(), result.ApprovedBy.String(), localRegionalGroupID)
			if remainingApprovalCount == 0 {
				action = "approved"
				emailData, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
				if err != nil {
					return nil, errors.New(err.Error())
				}
				data := SetEmailData(emailData)
				email, _ := postgres.GetEmailIDFromFormAnswerID(result.FormAnswerID)
				emailContent := util.SendEmailScenarioFive(email, data)
				emailCode := codeController.GetValueKeyCodes()["typeofemail"]
				requestorStatus := emailCode["approverapproved"].ID
				newEmailContent := strip.StripTags(emailContent)
				_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newEmailContent, result.ApprovedBy.String(), receiverID)

				emailOwner, ownerId, err := postgres.GetOwnersEmailIDFromFormAnswerID(result.FormAnswerID)
				if err != nil {
					panic(err)
				}
				if emailOwner != "" && emailOwner != email {
					emailOwnerContent := util.SendEmailScenarioFive(emailOwner, data)
					_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, strip.StripTags(emailOwnerContent), result.ApprovedBy.String(), ownerId)
				}
			}
		} else if result.Action == 59 {
			action = "rejected"
			emailData, err := postgres.GetFormAnswerDetails(result.FormAnswerID.String())
			if err != nil {
				return nil, errors.New(err.Error())
			}
			data := SetEmailData(emailData)
			email, _ := postgres.GetEmailIDFromFormAnswerID(result.FormAnswerID)
			allEmail, _ := postgres.GetEmailIDOfApproversFromFormAnswerID(result.FormAnswerID)
			allEmail = append(allEmail, email)
			emailOwner, ownerId, err2 := postgres.GetOwnersEmailIDFromFormAnswerID(result.FormAnswerID)
			if err2 != nil {
				log.Printf("Error: %s ", err2.Error())
			} else {
				if emailOwner != "" {
					if emailOwner != email {
						allEmail = append(allEmail, emailOwner)
					}
				}
			}
			var emailContent string
			for _, value := range allEmail {
				emailContent = util.SendEmailScenarioFourA(value, data)
			}
			emailCode := codeController.GetValueKeyCodes()["typeofemail"]
			requestorStatus := emailCode["approverrejected"].ID
			newEmailContent := strip.StripTags(emailContent)
			requestorID, _, _ := postgres.GetCreatedByIDFromFormAnswerID(result.FormAnswerID)
			approverID, _ := postgres.GetIDOfApproversFromFormAnswerID(result.FormAnswerID)
			receiverIDs := append(approverID, requestorID)
			if ownerId != "" {
				if ownerId != requestorID {
					receiverIDs = append(receiverIDs, ownerId)
				}
			}
			for _, receiver := range receiverIDs {
				_ = postgres.InsertEmailLog(result.FormAnswerID.String(), requestorStatus, newEmailContent, result.ApprovedBy.String(), receiver)
			}

		}

		result.ApprovedStatus = codeController.GetValueKeyCodes()["approvedstatus"][action].ID
		result.GroupStatus = codeController.GetValueKeyCodes()["group"][userGroupCode].ID
	}

	return &result, nil
}
func FormAnswerDetailsForLinkedModelToEntity(inputModel *model.FormAnswerInputForLinkedEvent, approvalRoleID *string) (*entity.FormAnswerFetchEntity, error) {
	var entity entity.FormAnswerFetchEntity
	if strings.TrimSpace(inputModel.FormID) != "" {
		uuid, err := uuid.FromString(inputModel.FormID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormId = uuid

	} else {
		if inputModel.EventID != nil && strings.TrimSpace(*inputModel.EventID) != "" {
			formId, err := postgres.FormIDCheckWithRespectToEventId(*inputModel.EventID)
			if err != nil {
				return nil, errors.New("Form answer ID does not exists !")
			}
			checkUUID, err := uuid.FromString(formId)
			if err != nil {
				return nil, errors.New("Form answer ID format is invalid!")
			}
			entity.FormId = checkUUID
			inputModel.FormID = formId

		}
	}
	return &entity, nil
}
func MapRequestorActionModelToEntity(inputModel *model.RequestorActionInput, loggedInUser *string, userRole *string) (*entity.SubmissionFormApprovalEntity, error) {
	var entity entity.SubmissionFormApprovalEntity
	if inputModel.ID != "" {
		formUUID, err := uuid.FromString(inputModel.ID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		loggedInUserUUID, err := uuid.FromString(*loggedInUser)
		if err != nil {
			return nil, errors.New("Logged in User ID format is invalid!")
		}
		if !postgres.IsFormAnswerExists(&formUUID) {
			return nil, errors.New("Form answer ID not exists")
		}
		entity.FormAnswerID = formUUID
		entity.RequestorID = loggedInUserUUID
		entity.ApprovalRole = *userRole

		if inputModel.Comment != nil {
			entity.Comment = *inputModel.Comment
		}
	}
	return &entity, nil
}

func FormAnswerAttachmentModelToEntity(inputModel *model.FormAnswerAttachment, approvalName string) (*entity.FormAttachment, error) {
	var entities entity.FormAttachment
	var entity entity.FormAttachmentInput
	var unixTodayDate int
	var unixEndDate int
	if strings.TrimSpace(inputModel.FormAnswerID) != "" {
		userId, err := uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if approvalName != "clustercomplianceofficer" && approvalName != "admin" {
			if !postgres.IsFormIDApproved(&userId) {
				return nil, errors.New("Form answer is not Approved")
			}
		}
		entities.FormAnswerID = userId
		entity.FormAnswerId = userId
	} else {
		return nil, errors.New("Form answer ID cannot be blank!")
	}

	currentTime := time.Now()
	currentDate := currentTime.Format("01/02/2006")
	currentDateTime := util.ConvertStringToTime(currentDate)
	unixTodayDate = util.GetTimeUnixTimeStamp(currentDateTime)

	activityEndDate, activityTitle, activityEventTitle, _ := postgres.GetActivityEndDateByFormId(inputModel.FormAnswerID)
	lenHcp := postgres.GetHcpLength(inputModel.FormAnswerID)
	dateString := strings.Trim(activityEndDate, "[]\"")
	if dateString != "" {
		dateTime := util.ConvertStringToTime(dateString)
		unixEndDate = util.GetTimeUnixTimeStamp(dateTime)
	}
	if unixEndDate > unixTodayDate {
		return nil, errors.New("Event is still ongoing!")
	}

	if strings.TrimSpace(activityTitle) == "Sponsorship to individual HCPs - To Local Event" || strings.TrimSpace(activityTitle) == "Sponsorship to individual HCPs - Within Local Country" || strings.TrimSpace(activityTitle) == "Sponsorship to Healthcare Organizations/Institutions" || strings.TrimSpace(activityTitle) == "Sponsorship to Individual HCPs – To Overseas Event" || strings.TrimSpace(activityTitle) == "Sponsorship to individual HCPs - Within ZP Location" || strings.TrimSpace(activityTitle) == "Sponsorship to individual HCPs - Outside ZP Location" {
		if inputModel.Attachments == nil || len(inputModel.Attachments) == 0 {
			return nil, errors.New("Please fill up all mandatory fields")
		} else {
			mandatory := false
			for _, attachment := range inputModel.Attachments {
				for _, description := range attachment.Categories {
					actiondescription := *description.Description
					if actiondescription == "Invitation Letter" {
						mandatory = true
						if len(description.URL) == 0 || description.URL == nil {
							return nil, errors.New("Please fill up all mandatory fields")
						}
						for _, url := range description.URL {
							if strings.TrimSpace(url) == "" {
								return nil, errors.New("Please fill up all mandatory fields")
							}
						}
					}
				}
			}
			if !mandatory {
				return nil, errors.New("Please fill up all mandatory fields")
			}
		}
	}
	if strings.TrimSpace(activityTitle) == "ZP - Hosted Event" {
		if inputModel.Attachments == nil || len(inputModel.Attachments) == 0 {
			return nil, errors.New("Please fill up all mandatory fields")
		} else {
			mandatory := false
			count := 0
			for _, attachment := range inputModel.Attachments {
				for _, description := range attachment.Categories {
					actiondescription := *description.Description
					if actiondescription == "Signed Attendance list" {
						count++
						if len(description.URL) == 0 || description.URL == nil {
							return nil, errors.New("Please fill up all mandatory fields")
						}
						for _, url := range description.URL {
							if strings.TrimSpace(url) == "" {
								return nil, errors.New("Please fill up all mandatory fields")
							}
						}
					} else if actiondescription == "Material Approval Code" {
						count++
						if len(description.URL) == 0 || description.URL == nil {
							return nil, errors.New("Please fill up all mandatory fields")
						}
						for _, url := range description.URL {
							if strings.TrimSpace(url) == "" {
								return nil, errors.New("Please fill up all mandatory fields")
							}
						}
					}
				}
			}
			if count == 2 {
				mandatory = true
			}
			if !mandatory {
				return nil, errors.New("Please fill up all mandatory fields")
			}
		}
		if (strings.TrimSpace(activityEventTitle) != "Face-to-face detailing" && strings.TrimSpace(activityEventTitle) != "Product Presentation") && lenHcp > 0 {
			if inputModel.Attachments == nil || len(inputModel.Attachments) == 0 {
				return nil, errors.New("Please fill up all mandatory fields")
			} else {
				mandatory := false
				for _, attachment := range inputModel.Attachments {
					for _, description := range attachment.Categories {
						actiondescription := *description.Description
						if actiondescription == "Service Agreement" {
							mandatory = true
							if len(description.URL) == 0 || description.URL == nil {
								return nil, errors.New("Please fill up all mandatory fields")
							}
							for _, url := range description.URL {
								if strings.TrimSpace(url) == "" {
									return nil, errors.New("Please fill up all mandatory fields")
								}
							}
						}
					}
				}
				if !mandatory {
					return nil, errors.New("Please fill up all mandatory fields")
				}
			}
		}
	}
	if postgres.IsFormAnswerExistsInFormAnswerAttachment(entity.FormAnswerId) {
		err := postgres.DeleteFormAnswerAttachments(entity.FormAnswerId)
		if err != nil {
			return nil, errors.New("failed to delete the data in formAnswerAttachment table")
		}
	}

	var formAnswerAttachmentIds []string
	for _, attachment := range inputModel.Attachments {
		action1 := *attachment.Type
		entity.Type = action1
		for _, description := range attachment.Categories {
			action2 := *description.Description
			entity.Description = action2
			for _, url := range description.URL {
				entity.Url = url
				formAnswerAttachmentId, err := postgres.InsertFormAnswerAttachment(entity, inputModel.IsApollo)
				if err != nil {
					return nil, errors.New("failed to add data in formAnswerAttachment table")
				}
				formAnswerAttachmentIds = append(formAnswerAttachmentIds, formAnswerAttachmentId)
			}
		}
	}
	entities.Attachments = formAnswerAttachmentIds
	return &entities, nil
}
func FormAnswerFetchModelToEntity(inputModel *model.FormAnswerInput, approvalRoleID *string, loggedInId *uuid.UUID, userCountry int) (*entity.FormAnswerFetchEntity, error) {
	var entity entity.FormAnswerFetchEntity
	if strings.TrimSpace(inputModel.FormID) != "" {
		uuid, err := uuid.FromString(inputModel.FormID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormId = uuid

	} else {
		if inputModel.EventID != nil && strings.TrimSpace(*inputModel.EventID) != "" {
			formId, err := postgres.FormIDCheckWithRespectToEventId(*inputModel.EventID)
			if err != nil {
				return nil, errors.New("Form answer ID does not exists !")
			}
			checkUUID, err := uuid.FromString(formId)
			if err != nil {
				return nil, errors.New("Form answer ID format is invalid!")
			}
			entity.FormId = checkUUID
			inputModel.FormID = formId

		}
	}
	approvalRole := postgres.GetCodeValueByID(*approvalRoleID)
	if *approvalRole == "admin" || *approvalRole == "finance" || *approvalRole == "clustercomplianceofficer" {
		if !postgres.CheckFormAnswerCountry(entity.FormId.String(), userCountry) {
			return nil, errors.New("User is from different country")
		}
	}

	return &entity, nil
}

// func FetchRetriveApprovalRoleEntityToModelForFace(input map[string][]entity.TempUserSelection) *model.ApprovalRolesUserSelectionResponse {

// 	var outEntity model.ApprovalRolesUserSelectionResponse
// 	var usersByApprovalRole []*model.UserByApprovalRole
// 	for _, usersSelection := range input {
// 		var userByApprovalRole model.UserByApprovalRole
// 		for _, userSelection := range usersSelection {

// 			userByApprovalRole.RoleName = userSelection.RoleName
// 			userByApprovalRole.SequenceNo = int(userSelection.SequenceNo.Int32)
// 			userByApprovalRole.RoleType = userSelection.RoleType
// 			userByApprovalRole.RoleID = userSelection.RoleID

// 			tempUserSelection := new(model.UserSelection)
// 			tempUserSelection.Description = userSelection.Description.String
// 			tempUserSelection.Value = userSelection.Value.String
// 			tempUserSelection.Hint = userSelection.Hint.String
// 			userByApprovalRole.UserSelection = append(userByApprovalRole.UserSelection, tempUserSelection)
// 		}

// 		usersByApprovalRole = append(usersByApprovalRole, &userByApprovalRole)

// 	}
// 	outEntity.UsersByapprovalRole = usersByApprovalRole
// 	return &outEntity
// }

// func FetchRetriveApprovalRoleEntityToModel(input []entity.TempUserSelection, countryID int) *model.ApprovalRolesUserSelectionResponse {

// 	var outEntity model.ApprovalRolesUserSelectionResponse
// 	var usersByApprovalRole []*model.UserByApprovalRole
// 	for _, usersSelection := range input {
// 		var userByApprovalRole model.UserByApprovalRole
// 		userByApprovalRole.RoleName = usersSelection.RoleName
// 		userByApprovalRole.SequenceNo = int(usersSelection.SequenceNo.Int32)
// 		userByApprovalRole.RoleType = usersSelection.RoleType
// 		userByApprovalRole.RoleID = usersSelection.RoleID
// 		temp, _ := postgres.GetApproversBasedOnActivities(usersSelection.RoleID, countryID)
// 		var userSection []*model.UserSelection
// 		for _, users := range temp {
// 			tempUserSelection := &model.UserSelection{}
// 			tempUserSelection.Description = users.Description.String
// 			tempUserSelection.Value = users.Value.String
// 			tempUserSelection.Hint = users.Hint.String
// 			userSection = append(userSection, tempUserSelection)
// 		}
// 		userByApprovalRole.UserSelection = userSection
// 		usersByApprovalRole = append(usersByApprovalRole, &userByApprovalRole)

// 	}
// 	outEntity.UsersByapprovalRole = usersByApprovalRole
// 	return &outEntity
// }

func SetEmailData(input []entity.FormAnswer) []*entity.EmailStruct {
	var outEntity []*entity.EmailStruct
	siteUrl, _ := postgres.FetchSiteUrl()
	apolloUrl := postgres.FetchApolloUrl()

	for _, item := range input {
		country, _ := postgres.GetCountryDescriptionByUserID(item.RequestorID.String)
		userRole, _ := postgres.GetUserRoleDescriptionByUserID(item.RequestorID.String)
		emailData := new(entity.EmailStruct)
		emailData.ActivityName = item.ActivityTypes.String
		emailData.EventSubmittedDate = item.CreatedDate.Time.Format("2006-01-02")
		startDate := item.StartDate.String
		endDate := item.EndDate.String
		var dateRange string
		var startDateTime time.Time
		var endDateTime time.Time
		if startDate != "" {
			start, err := strconv.ParseInt(startDate, 10, 64)
			if err == nil {
				startDateTime = time.Unix(start, 0)

			}
		}
		if endDate != "" {
			end, err := strconv.ParseInt(endDate, 10, 64)
			if err == nil {
				endDateTime = time.Unix(end, 0)

			}
		}

		emailData.ApolloUrl = ""
		if item.IsApollo == true {
			emailData.ApolloUrl = apolloUrl
		}

		dateRange = startDateTime.Format("2006-01-02") + " - " + endDateTime.Format("2006-01-02")
		emailData.EventDate = dateRange
		emailData.Team = item.Team.String
		emailData.EventName = item.ActivityName.String
		emailData.RequesterName = item.RequestorName.String
		emailData.TotalCost, _ = strconv.ParseFloat(item.TotalCost.String, 64)
		emailData.EventCode = country + "-" + userRole + " " + item.EventSequence.String
		emailData.SiteURL = siteUrl

		outEntity = append(outEntity, emailData)
	}
	return outEntity
}

func TotalEventForRequestorEntityToModel(entity *entity.GetTotalEventEntity) *model.TotalEvents {
	var response model.TotalEvents
	var requestorTotalPending = int(entity.RequestorTotalPending.Int32)
	var requestorTotalApproved = int(entity.RequestorTotalApproved.Int32)
	var requestorTotalRejected = int(entity.RequestorTotalRejected.Int32)

	response.RequestorTotalPending = &requestorTotalPending
	response.RequestorTotalApproved = &requestorTotalApproved
	response.RequestorTotalRejected = &requestorTotalRejected
	response.ApproverTotalPending = nil
	response.ApproverTotalApproved = nil
	response.ApproverTotalRejected = nil
	return &response
}

func TotalEventForApproverEntityToModel(entity *entity.GetTotalEventEntity) *model.TotalEvents {
	var response model.TotalEvents
	var approverTotalPending = int(entity.ApproverTotalPending.Int32)
	var approverTotalApproved = int(entity.ApproverTotalApproved.Int32)
	var approverTotalRejected = int(entity.ApproverTotalRejected.Int32)

	response.RequestorTotalPending = nil
	response.RequestorTotalApproved = nil
	response.RequestorTotalRejected = nil
	response.ApproverTotalPending = &approverTotalPending
	response.ApproverTotalApproved = &approverTotalApproved
	response.ApproverTotalRejected = &approverTotalRejected
	return &response
}

func TotalEventEntityToModel(entityApprover *entity.GetTotalEventEntity, entityRequestor *entity.GetTotalEventEntity) *model.TotalEvents {
	var response model.TotalEvents

	var requestorTotalPending = int(entityRequestor.RequestorTotalPending.Int32)
	var requestorTotalApproved = int(entityRequestor.RequestorTotalApproved.Int32)
	var requestorTotalRejected = int(entityRequestor.RequestorTotalRejected.Int32)
	var approverTotalPending = int(entityApprover.ApproverTotalPending.Int32)
	var approverTotalApproved = int(entityApprover.ApproverTotalApproved.Int32)
	var approverTotalRejected = int(entityApprover.ApproverTotalRejected.Int32)

	response.RequestorTotalPending = &requestorTotalPending
	response.RequestorTotalApproved = &requestorTotalApproved
	response.RequestorTotalRejected = &requestorTotalRejected
	response.ApproverTotalPending = &approverTotalPending
	response.ApproverTotalApproved = &approverTotalApproved
	response.ApproverTotalRejected = &approverTotalRejected
	return &response
}

func FetchApprovalRoleModelToEntity(inputModel *model.FormAnswerIDInput) (*entity.FetchApprovalTrailEntity, error) {
	var entity entity.FetchApprovalTrailEntity
	if strings.TrimSpace(inputModel.FormAnswerID) != "" {
		uuid, err := uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormAnswerId = uuid

	} else {
		if inputModel.EventID != nil && strings.TrimSpace(*inputModel.EventID) != "" {
			formId, err := postgres.FormIDCheckWithRespectToEventId(*inputModel.EventID)
			if err != nil {
				return nil, errors.New("Form answer ID does not exists !")
			}
			checkUUID, err := uuid.FromString(formId)
			if err != nil {
				return nil, errors.New("Form answer ID format is invalid!")
			}
			entity.FormAnswerId = checkUUID
			inputModel.FormAnswerID = formId

		}
	}
	return &entity, nil
}

func FetchApprovalLogModelToEntity(inputModel *model.FormAnswerInput) (*entity.FetchApprovalTrailEntity, error) {
	var entity entity.FetchApprovalTrailEntity
	if strings.TrimSpace(inputModel.FormID) != "" {
		uuid, err := uuid.FromString(inputModel.FormID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormAnswerId = uuid

	} else {
		return nil, errors.New("Form answer ID cannot be blank!")
	}
	return &entity, nil
}
func FetchTypeApprovalListModelToEntity(inputModel model.GetTypeApprovalListInput) (*entity.FetchTypeApprovalListEntity, error) {
	var entity entity.FetchTypeApprovalListEntity
	if strings.TrimSpace(inputModel.FormAnswerID) != "" {
		uuid, err := uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormAnswerId = uuid

	} else {
		return nil, errors.New("Form answer ID cannot be blank!")
	}
	typeMap := TypeValidation()
	if inputModel.Type != "" {
		if _, exists := typeMap[inputModel.Type]; !exists {
			return nil, errors.New("Type is invalid!")
		}
	}
	if strings.TrimSpace(inputModel.Type) != "" {
		entity.Type = inputModel.Type
	} else {
		return nil, errors.New("Type cannot be blank!")
	}

	return &entity, nil
}

func TypeValidation() map[string]bool {
	indegenousMap := map[string]bool{
		"ActivityStartDateEndDate": true,
		"NoOfHcpNonHcp":            true,
		"TypeOfHcpLocal":           true,
		"TypeOfHcpSponsored":       true,
		"Venue":                    true,
		"VirtualEvent":             true,
	}
	return indegenousMap
}

func FormAnswerIDByEventIDModelToEntity(inputModel *model.FormAnswerByEventIDInput) (*entity.FormAnswerIdByEventIdEntity, error) {
	var entity entity.FormAnswerIdByEventIdEntity
	var err error
	if strings.TrimSpace(inputModel.EventID) != "" {
		res1 := strings.LastIndex(inputModel.EventID, "-")
		eventcode := inputModel.EventID[:res1]
		eventseq := inputModel.EventID[res1+1:]
		entity.EventCode = eventcode
		entity.EventSeq, err = strconv.Atoi(eventseq)
		if err != nil {
			return nil, errors.New("Unable to convert to integer!")
		}
	} else {
		return nil, errors.New("Event ID cannot be blank!")
	}
	return &entity, nil
}

func FormAnswerIDByEventIDEntityToModel(input entity.FormAnswerIdByEventID) *model.EventIDData {
	var outEntity model.EventIDData
	if input.EventCode != "" {
		formAnsSeq := util.ConvertToEventID(input.EventCode, input.EventSeq)
		outEntity.Description = formAnsSeq
	}
	outEntity.Value = input.FormAnswerID
	return &outEntity
}

func FetchApprovalLogValuesEntityToModel(input []entity.FetchApprovalLogData) []*model.ApprovalLog {
	var outEntity []*model.ApprovalLog
	for _, item := range input {
		approvalLogData := new(model.ApprovalLog)
		approvalLogData.ID = item.Id.String
		approvalLogData.ActionedBy = item.ActionedBy.String
		approvalLogData.ActionedByName = item.ActionedByName.String
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, int(item.EventSeq.Int32))
			approvalLogData.EventID = formAnsSeq
		}
		approvalLogData.StatusValue = item.StatusValue.String
		approvalLogData.StatusTitle = item.StatusTitle.String
		approvalLogData.UserRoleTitle = item.UserRoleTitle.String
		approvalLogData.UserRoleValue = item.UserRoleValue.String
		approvalLogData.FormAnsID = item.FormAnswerId.String
		if item.Comment.Valid {
			approvalLogData.Comment = item.Comment.String
		}
		if item.DateCreated.Valid {
			dateCreated := util.GetTimeUnixTimeStamp(item.DateCreated.Time)
			// date := time.Unix(int64(dateCreated), 0).Format("2006-01-02")
			approvalLogData.DateCreated = dateCreated
		}
		outEntity = append(outEntity, approvalLogData)
	}
	return outEntity
}

func FetchApprovalTrailValuesEntityToModel(input []entity.FetchApprovalTrailData, inputEntity *entity.FetchApprovalTrailEntity) []*model.ApprovalData {
	var outEntity []*model.ApprovalData
	var previousStatus string
	actionforcancelled := "cancelled"
	codesforcancelled := codeController.GetValueKeyCodes()["approvedstatus"]
	cancelled := codesforcancelled[actionforcancelled].ID
	status, _ := postgres.FetchFormAnswersForCancel(inputEntity.FormAnswerId.String())
	var localApproverStatus bool
	localApproverStatus = false
	for key, item := range input {
		approvalTrailData := new(model.ApprovalData)
		approvalTrailData.ApprovalRole = item.ApprovalRole
		approvalTrailData.ActionedBy = item.ActionedBy.String
		approvalTrailData.ActionedByRole = item.ActionedByRole.String
		if *item.GroupID == 55 && item.IValue.String == "pending" {
			localApproverStatus = true
		}
		if status == cancelled {
			approvalTrailData.Status = "Cancelled"
		} else {
			if previousStatus == "approved" && item.IValue.String == "pending" {
				approvalTrailData.Status = "Pending Approval"
			} else if previousStatus == "pending" && item.IValue.String == "pending" && *item.GroupID == 56 && localApproverStatus == false {
				approvalTrailData.Status = "Pending Approval"
			} else {
				if key == 0 && item.IValue.String == "pending" {
					approvalTrailData.Status = "Pending Approval"
				} else {
					approvalTrailData.Status = item.Status.String
				}
			}
			previousStatus = item.IValue.String
		}

		if item.DateCreated.Valid {
			dateCreated := util.GetTimeUnixTimeStamp(item.DateCreated.Time)
			// date := time.Unix(int64(dateCreated), 0).Format("2006-01-02")
			approvalTrailData.DateCreated = dateCreated
		}

		if item.Comment.Valid {
			approvalTrailData.Comment = strings.ReplaceAll(item.Comment.String, "\n", " ")
		}
		outEntity = append(outEntity, approvalTrailData)
	}
	return outEntity
}

func FetchApprovalTrailValuesEntityToModelForRequestor1(input []entity.FetchApprovalLogDataForRequester) []*model.ApprovalLog {
	var outEntity []*model.ApprovalLog
	for _, item := range input {
		approvalLogData := new(model.ApprovalLog)
		approvalLogData.ID = item.Id.String
		approvalLogData.ActionedBy = item.ActionedBy.String
		approvalLogData.ActionedByName = item.ActionedByName.String
		if item.EventCode.String != "" {
			formAnsSeq := util.ConvertToEventID(item.EventCode.String, int(item.EventSeq.Int32))
			approvalLogData.EventID = formAnsSeq
		}
		approvalLogData.StatusValue = item.StatusValue.String
		approvalLogData.StatusTitle = item.StatusTitle.String
		approvalLogData.UserRoleTitle = item.UserRoleTitle.String
		approvalLogData.UserRoleValue = item.UserRoleValue.String
		approvalLogData.FormAnsID = item.FormAnswerId.String
		if item.DateCreated.Valid {
			dateCreated := util.GetTimeUnixTimeStamp(item.DateCreated.Time)
			approvalLogData.DateCreated = dateCreated
		}
		outEntity = append(outEntity, approvalLogData)
	}
	return outEntity

}

func GetApprovalRoleByFormAnsID(entity []entity.FetchApprovalRoleByFormAnsIDModelToEntity) []*model.ApproverformAnsResponse {
	var outEntity []*model.ApproverformAnsResponse
	for _, item := range entity {
		approvalData := new(model.ApproverformAnsResponse)
		approvalData.ApprovalID = item.ApprovalID
		approvalData.Name = item.Name
		approvalData.ApprovalValue = item.ApprovalValue
		approvalData.ApprovalTitle = item.ApprovalTitle
		approvalData.GroupValue = item.GroupValue
		approvalData.GroupTitle = item.GroupTitle
		approvalData.SequenceNo = item.SequenceNo
		approvalData.DepartmentName = item.DepartmentName
		approvalData.DepartmentID = item.DepartmentID
		outEntity = append(outEntity, approvalData)
	}
	return outEntity
}

func GetApprovalRolesInputModelToEntity(ctx context.Context,
	inputModel *model.ActivityInputs, userRoleID string, countryID int, localCurrency float64, ledBy string) (*entity.ApprovalRoleInputEntity, error) {

	activityTypes := codeController.GetIdKeyCodes()["activitytype"]
	activityEventTypes := codeController.GetIdKeyCodes()["activityeventtype"]

	err := mergo.Merge(&activityTypes, activityEventTypes)
	if err != nil {
		return nil, errors.New("Error merging maps!")
	}

	activityValueTypes := codeController.GetValueKeyCodes()["activityeventtype"]
	approvalRoleEntity := &entity.ApprovalRoleInputEntity{
		AuthorApprovalRoleID: userRoleID,
		AuthorCountryID:      countryID,
		LedBy:                ledBy,
	}

	convertedAmount, err := util.ConvertLocalCurrencyToUSD(inputModel.TotalExpense, localCurrency)
	if err != nil {
		return nil, err
	}

	approvalRoleEntity.AuthorApprovalRoleValue = postgres.GetRoleValueByIDInUserRoles(userRoleID)
	approvalRoleEntity.TotalExpense = inputModel.TotalExpense
	approvalRoleEntity.ConvertedTotalExpense = convertedAmount

	if inputModel.HasHCPEngagement != nil {
		approvalRoleEntity.HasHCPEngagement = *inputModel.HasHCPEngagement
	} else {
		approvalRoleEntity.HasHCPEngagement = false
	}

	if inputModel.HasInternational != nil {
		approvalRoleEntity.HasInternational = *inputModel.HasInternational
	}

	if inputModel.HasLevelOfInfluence != nil {
		approvalRoleEntity.HasLevelOfInfluence = *inputModel.HasLevelOfInfluence
	}

	var hasActivityEventType bool
	var hasZPHostedEventType bool
	var activityTypesValueStore []string
	for _, activityValue := range inputModel.Activities {
		activityIdInt, err := strconv.Atoi(activityValue)
		if err != nil {
			return nil, errors.New("Activities provided can only be digits!")
		}

		if activityTypes[activityIdInt].Value == "facetofacedetailing" ||
			activityTypes[activityIdInt].Value == "productpresentation" ||
			activityTypes[activityIdInt].Value == "commercial/education" ||
			activityTypes[activityIdInt].Value == "trademarkettingactivity" ||
			activityTypes[activityIdInt].Value == "zpinternaltraining" ||
			activityTypes[activityIdInt].Value == "education" ||
			activityTypes[activityIdInt].Value == "advisoryboardmedical" ||
			activityTypes[activityIdInt].Value == "advisoryboardcommercial" {
			activityTypesValueStore = append(activityTypesValueStore, activityTypes[activityIdInt].Value)
			hasActivityEventType = true
		}

		if activityTypes[activityIdInt].Value == "zphostedevent" {
			hasZPHostedEventType = true
		}

		if activityTypes[activityIdInt].Value != "facetofacedetailing" &&
			activityTypes[activityIdInt].Value != "productpresentation" &&
			activityTypes[activityIdInt].Value != "commercial/education" &&
			activityTypes[activityIdInt].Value != "trademarkettingactivity" &&
			activityTypes[activityIdInt].Value != "zpinternaltraining" &&
			activityTypes[activityIdInt].Value != "education" &&
			activityTypes[activityIdInt].Value != "advisoryboardmedical" &&
			activityTypes[activityIdInt].Value != "advisoryboardcommercial" &&
			activityTypes[activityIdInt].Value != "zphostedevent" {
			approvalRoleEntity.Activities = append(approvalRoleEntity.Activities, activityIdInt)
		}

	}

	if hasActivityEventType && !hasZPHostedEventType {
		return nil, errors.New("Missing ZP Hosted Event selection for the provided type of event!")
	}

	if hasActivityEventType && hasZPHostedEventType {
		for _, ele := range activityTypesValueStore {
			approvalRoleEntity.Activities = append(approvalRoleEntity.Activities, activityValueTypes[ele].ID)
		}
	}

	var activities []int
	if len(approvalRoleEntity.Activities) == 2 {
		for _, ele := range activityTypesValueStore {
			if strings.Contains(strings.Trim(strings.Replace(fmt.Sprint(approvalRoleEntity.Activities), " ", " ", -1), "[]"), "98") &&
				strings.Contains(strings.Trim(strings.Replace(fmt.Sprint(approvalRoleEntity.Activities), " ", " ", -1), "[]"), strconv.Itoa(activityValueTypes[ele].ID)) {

				activities = append(activities, activityValueTypes[ele].ID)
				approvalRoleEntity.Activities = activities
			}
		}

	}
	if !postgres.CheckRequestorByActivities(approvalRoleEntity.Activities, approvalRoleEntity.AuthorApprovalRoleID, countryID) {
		return nil, errors.New("User does not have access to request")
	}

	return approvalRoleEntity, nil
}

func CheckForStatusChangeNotification(input string, recieverId string, userID string) (*entity.StatusChangeNotificationEntity, error) {
	log.Println("CheckForStatusChangeNotification()")
	var response entity.StatusChangeNotificationEntity

	if recieverId == "" {
		return nil, errors.New("Receiver ID not present in status table")
	}

	if userID != recieverId {
		return nil, errors.New("User ID Not match with Notification ID ")
	}

	response.NotificationID = input
	return &response, nil
}

func SortItemModelToEntity(sortElements []*model.SortItems) []string {
	var sortItems []string
	if sortElements != nil {
		for _, value := range sortElements {
			var sortItem string
			if *value.Columns == "eventId" {
				sortItem = "(select CONCAT (eventcode,'-', replace((to_char(eventseq, '99990999')), ' ', ''))) "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS LAST"
				}
			} else if *value.Columns == "approvalStatus" {
				sortItem = "(CASE WHEN statuss = 398 THEN 'Cancelled' WHEN statuss = 397 THEN 'Return' WHEN statuss = 59 THEN 'Rejected' when statuss = 67 then 'Completed' when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local' AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'Pending Approval'ELSE value END) "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS LAST"
				}
			} else if *value.Columns == "typeOfEvent" {
				sortItem = "event_type "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS LAST"
				}
			} else if *value.Columns == "typeOfActivity" {
				sortItem = "type_of_activity "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS LAST"
				}
			} else if *value.Columns == "requestorName" {
				sortItem = "name "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS LAST"
				}
			} else if *value.Columns == "activityName" {
				sortItem = "activity_name "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "eventStartDate" {
				sortItem = "activity_start_date "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "eventEndDate" {
				sortItem = "activity_end_date "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "productName" {
				sortItem = "product "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "departmentName" {
				sortItem = "team "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "totalCost" {
				sortItem = "total_cost "
				if *value.Order == "asc" {
					sortItem += "asc NULLS first"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "submitedDate" {
				sortItem = "date_created "
				if *value.Order == "asc" {
					sortItem += "asc NULLS first"
				} else {
					sortItem += "desc NULLS last"
				}
			}
			sortItems = append(sortItems, sortItem)
		}
	}
	return sortItems
}

func SortItemModelToEntityForRequestor(sortElements []*model.SortItems) []string {
	var sortItems []string
	if sortElements != nil {
		for _, value := range sortElements {
			var sortItem string
			if *value.Columns == "eventId" {
				sortItem = "(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "approvalStatus" {
				sortItem = " rd.status_title "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "typeOfEvent" {
				sortItem = " rd.event_type[2] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "typeOfActivity" {
				sortItem = " rd.event_type[1] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "requestorName" {
				sortItem = " rd.requestor_name "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "activityName" {
				sortItem = " rd.activity_start_date[2] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "eventStartDate" {
				sortItem = " rd.activity_start_date[3] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "eventEndDate" {
				sortItem = " rd.activity_start_date[4] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "productName" {
				sortItem = " rd.activity_start_date[5] "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "submitedDate" {
				sortItem = " rd.created_by "
				if *value.Order == "asc" {
					sortItem += "asc"
				} else {
					sortItem += "desc NULLS last"
				}
			} else if *value.Columns == "totalCost" {
				sortItem = " rd.total_cost "
				if *value.Order == "asc" {
					sortItem += "asc NULLS first"
				} else {
					sortItem += "desc NULLS last"
				}
			}
			sortItems = append(sortItems, sortItem)
		}
	}
	return sortItems
}
func ExceptionalDetailsModelToEntity(inputModel model.ExceptionalDetailsInput) (entity.ExceptionalDetailsInput, error) {
	var response entity.ExceptionalDetailsInput
	if strings.TrimSpace(inputModel.ID) != "" {
		uuid, err := uuid.FromString(inputModel.ID)
		if err != nil {
			return response, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return response, errors.New("Form answer ID does not exists !")
		}
		response.FormID = uuid
	} else {
		return response, errors.New("Form answer ID cannot be blank!")
	}
	if !postgres.CheckIsExceptionalApproval(inputModel.ID) {
		return response, errors.New("Form answer is not exceptional approval!")
	}
	response.DetailsOfRequest = inputModel.DetailsOfRequest
	response.IhcpOtherLocalPolicies = inputModel.IhcpOtherLocalPolicies
	response.MoreComments = inputModel.MoreComments
	response.ExceptionalApprovalFileUpload = inputModel.ExceptionalApprovalFileUpload
	response.ScopeOfExceptionalRequest = inputModel.ScopeOfExceptionalRequest
	return response, nil

}

func ChangeRequestModelToEntity(inputModel model.ChangeRequestInput) (entity.ChangeRequestInputEntity, error, []string) {
	var response entity.ChangeRequestInputEntity
	var flag int
	var hcps []string
	var err error
	var formuuid uuid.UUID
	if strings.TrimSpace(inputModel.FormAnswerID) != "" {
		formuuid, err = uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return response, errors.New("form answer id format is invalid!"), hcps
		}
		if !postgres.IsFormIDExists(&formuuid) {
			return response, errors.New("form answer id does not exist !"), hcps
		}
		log.Println(formuuid, "uuid")
		response.FormAnswerID = formuuid
	} else {
		return response, errors.New("form answer id cannot be blank!"), hcps
	}
	if inputModel.ActivityStartDate != nil {
		response.StartDate = *inputModel.ActivityStartDate
	}
	if inputModel.ActivityEndDate != nil {
		response.EndDate = *inputModel.ActivityEndDate
	}
	if inputModel.Duration != nil {
		response.Duration = *inputModel.Duration
	}
	if inputModel.NoOfHcp != nil {
		response.NoOfHcp = *inputModel.NoOfHcp
	}
	if inputModel.NoOfNonHcp != nil {
		response.NoOfNonHCP = *inputModel.NoOfNonHcp
	}
	if inputModel.TypeOfHcp != nil {
		if strings.ToLower(*inputModel.TypeOfHcp) == "local" || strings.ToLower(*inputModel.TypeOfHcp) == "sponsored" {
			hcps, err = postgres.CheckTypeOfHcp(response.FormAnswerID)
			if err != nil {
				return response, err, hcps
			}
			if strings.ToLower(*inputModel.TypeOfHcp) == "local" {
				for _, val := range hcps {
					if val == "Local" {
						flag = 1
						break
					}
				}
				if flag == 0 {
					return response, errors.New("local hcp does not exist for this formAnswer"), hcps
				}
				for _, val := range inputModel.Roles {
					if (strings.ToLower(*val) != "speaker") && (strings.ToLower(*val) != "moderator") && (strings.ToLower(*val) != "advisory board member") {
						return response, errors.New("roles can only be speaker, moderator, advisory board member"), hcps
					}
				}
			}
			if strings.ToLower(*inputModel.TypeOfHcp) == "sponsored" {
				for _, val := range hcps {
					if val == "Sponsored" {
						flag = 1
						break
					}
				}
				if flag == 0 {
					return response, errors.New("sponsored hcp does not exist for this formAnswer"), hcps
				}
				if inputModel.GovtNonGovtHcp != nil {
					response.GovtNonGovtHcp = *inputModel.GovtNonGovtHcp
				}
				if inputModel.Remarks != nil {
					response.Remarks = *inputModel.Remarks
				}
				if inputModel.SponsoredHcp != nil {
					response.SponsoredHcp = *inputModel.SponsoredHcp
				}
				if inputModel.Speciality != nil {
					response.Speciality = *inputModel.Speciality
				}
				if inputModel.Hco != nil {
					response.Hco = *inputModel.Hco
				}
			}
			response.TypeOfHCP = *inputModel.TypeOfHcp
			response.Roles = append(response.Roles, inputModel.Roles...)
		} else {
			return response, errors.New("type of hcp can only be local or sponsored"), hcps
		}
	}
	if inputModel.Venue != nil {
		response.Venue = "\"" + *inputModel.Venue + "\""
	}
	if inputModel.VirtualEvent != nil {
		response.VirtualEvent = inputModel.VirtualEvent
	}
	return response, nil, hcps
}
func ChangeRequestUpdateModelToEntity(inputModel model.RequestorActionInput) (entity.ChangeRequestForAllEvent, error) {
	var responseEntity entity.ChangeRequestForAllEvent
	if strings.TrimSpace(inputModel.ID) != "" {
		FormAnswerUUID, err := uuid.FromString(inputModel.ID)
		if err != nil {
			return responseEntity, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormAnswerExists(&FormAnswerUUID) {
			return responseEntity, errors.New("Form answer not exists")
		}
		responseEntity.FormAnswerID = FormAnswerUUID
	}
	if inputModel.Comment != nil && strings.TrimSpace(*inputModel.Comment) != "" {
		responseEntity.Comment = *inputModel.Comment
	}

	return responseEntity, nil
}
func ConvertLocalTimeZone(CountryValue string) *time.Location {
	var loc *time.Location
	if CountryValue == "ph" || CountryValue == "my" {
		loc, _ = time.LoadLocation("Asia/Singapore")
	} else {
		loc, _ = time.LoadLocation("Asia/Bangkok")
	}
	return loc
}
func RecallMapSubmissionFormApprovalModelToEntity(inputModel *model.RecallFormApprovalInput, userUUID uuid.UUID, approvalRoleID *string) (*entity.RecallSubmissionFormApprovalEntity, error) {
	var entity entity.RecallSubmissionFormApprovalEntity
	entity.ApprovedBy = userUUID
	if inputModel.FormAnsID != "" {
		uuid, err := uuid.FromString(inputModel.FormAnsID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormAnswerExists(&uuid) {
			return nil, errors.New("Form answer not exists")
		}
		entity.FormAnswerID = uuid
		if inputModel.Action != "" {
			action := strings.TrimSpace(inputModel.Action)
			codes := codeController.GetValueKeyCodes()["approvedstatus"]
			_, actionExists := codes[action]
			if actionExists {
				entity.Action = codes[action].ID
				entity.ActionValue = codes[action].Value
				entity.ActionTitle = codes[action].Title.String
			} else {
				return nil, errors.New("invalid approved status")
			}
		} else {
			return nil, errors.New("Approved status cannot be blank")
		}
		entity.ApprovalRole = *approvalRoleID

		requestorID, err := postgres.GetRequesterIDByFormAnswer(inputModel.FormAnsID)
		if err != nil {
			return nil, err
		}
		entity.RequestorID = *requestorID
		if inputModel.Comment != nil && strings.TrimSpace(*inputModel.Comment) != "" {
			entity.RecallSummary = strings.TrimSpace(*inputModel.Comment)
		}
		entity.IsRecall = true
	}
	return &entity, nil
}
func GetFileExtensionFromBlobURL(blobURL string) string {
	parts := strings.Split(blobURL, "/")
	fileName := parts[len(parts)-1]
	ext := filepath.Ext(fileName)
	return ext
}
func FormAnswerAttachmentModelToEntityForEzClaim(inputModel model.FormAnswerAttachmentForEzClaim, getMeetingId string) (model.FormAnswerAttachmentResponseForEzClaim, []string) {
	response := model.FormAnswerAttachmentResponseForEzClaim{}
	var attachments []string
	response.FormAnswerID = inputModel.FormAnswerID
	var contentLengthTotal int
	response.Attachments = make([]*model.AttachmentForEzClaimData, len(inputModel.Attachments))
	if len(inputModel.Attachments) != 0 && inputModel.Attachments != nil {
		for i, attachment := range inputModel.Attachments {
			var TypeAttachment string
			if attachment.Type != nil && *attachment.Type != "" {
				TypeAttachment = *attachment.Type
				response.Attachments[i] = &model.AttachmentForEzClaimData{
					Type:       &TypeAttachment,
					Categories: make([]*model.AttachmentCategoryForEzClaimData, len(attachment.Categories)),
				}
			} else {
				response.Error = true
				response.Message = "Attchment Type Can't be blank"
				response.Attachments = []*model.AttachmentForEzClaimData{}
				return response, attachments
			}

			if len(attachment.Categories) == 0 || attachment.Categories == nil {
				response.Error = true
				response.Message = "Categories Can't be blank"
				response.Attachments = []*model.AttachmentForEzClaimData{}
				return response, attachments
			}
			for j, description := range attachment.Categories {
				var b bool
				var checkUrlmessage string
				b = true
				var actiondescription string
				if description.Description != nil && *description.Description != "" {
					actiondescription = *description.Description
					if len(description.URL) == 0 || description.URL == nil {
						response.Error = true
						response.Message = "Please provide the " + actiondescription + " Attachment"
						response.Attachments = []*model.AttachmentForEzClaimData{}
						return response, attachments
					} else {
						response.Attachments[i].Categories[j] = &model.AttachmentCategoryForEzClaimData{
							Description: &actiondescription,
							URL:         make([]string, 0),
						}
					}
				} else {
					if actiondescription == "" {
						response.Error = true
						response.Message = "Description Can't be blank"
						response.Attachments = []*model.AttachmentForEzClaimData{}
						return response, attachments
					}
				}

				for _, url := range description.URL {
					if strings.TrimSpace(url) == "" {
						response.Error = true
						response.Message = "Please provide the " + actiondescription + " Attachment Url"
						response.Attachments = []*model.AttachmentForEzClaimData{}
						return response, attachments
					} else {
						response.Attachments[i].Categories[j].URL = append(response.Attachments[i].Categories[j].URL, url)
					}
					extension := GetFileExtensionFromBlobURL(url)
					if strings.ToUpper(extension) == ".PDF" || strings.ToUpper(extension) == ".JPG" || strings.ToUpper(extension) == ".JPEG" || strings.ToUpper(extension) == ".OFD" || strings.ToUpper(extension) == ".PNG" {
						checkUrl, Size := CheckBlobURLSize(url)
						contentLengthTotal += Size
						if checkUrlmessage == "" {
							checkUrlmessage = checkUrl
						}
						if checkUrl != "" || !b {
							response.Error = true
							b = false
							response.Attachments[i].Categories[j].IsValid = b
							response.Attachments[i].Categories[j].ErrorMessage = &checkUrlmessage
						} else {
							attachments = append(attachments, url)
							b = true
							response.Attachments[i].Categories[j].IsValid = b
						}
					} else if strings.ToUpper(extension) == ".XLSX" && (actiondescription == "Signed Attendance list" || actiondescription == "Certification/proof of Attendance") {
						convertpdfUrl, _ := postgres.GetStartDateAndEndDateFromMeetingID(inputModel.FormAnswerID, getMeetingId, url)
						checkUrl, size := CheckBlobURLSize(convertpdfUrl)
						contentLengthTotal += size
						if checkUrlmessage == "" {
							checkUrlmessage = checkUrl
						}
						if checkUrl != "" {
							response.Error = true
							b := false
							response.Attachments[i].Categories[j].IsValid = b
							response.Attachments[i].Categories[j].ErrorMessage = &checkUrlmessage
						} else {
							attachments = append(attachments, convertpdfUrl)
							b := true
							response.Attachments[i].Categories[j].IsValid = b
						}
					} else {
						response.Error = true
						b := false
						ExtentionValidation := constants.ExtentionValidation
						response.Attachments[i].Categories[j].IsValid = b
						response.Attachments[i].Categories[j].ErrorMessage = &ExtentionValidation
					}

				}
			}
		}
	} else {
		response.Error = true
		response.Message = "Please provide the attachments"
		response.Attachments = []*model.AttachmentForEzClaimData{}
		return response, attachments
	}
	const maxTotalSize = 100 * 1024 * 1024 // 100 MB in bytes
	if contentLengthTotal > maxTotalSize {
		response.Error = true
		response.Message = "All files total size must not exceed 100 MB."
		response.Attachments = []*model.AttachmentForEzClaimData{}
		return response, attachments
	}
	return response, attachments
}

func CheckBlobURLSize(url string) (string, int) {
	// Make a HEAD request to the Blob URL
	resp, err := http.Head(url)
	if err != nil {
		return fmt.Sprintf("error making HEAD request: %v", err), 0
	}
	defer resp.Body.Close()

	// Get the Content-Length header
	contentLengthStr := resp.Header.Get("Content-Length")
	if contentLengthStr == "" {
		return fmt.Sprintf("file not found"), 0
	}

	// Convert Content-Length to an integer
	contentLength, err := strconv.Atoi(contentLengthStr)
	if err != nil {
		return fmt.Sprintf("error converting Content-Length to integer: %v", err), 0
	}

	// Define size limits
	const maxFileSize = 10 * 1024 * 1024 // 10 MB in bytes

	// Check if the size is within the allowed range
	if contentLength > maxFileSize {
		return "File size must be less than 10 MB.", contentLength
	}
	return "", contentLength
}
func FormAnswerFetchModelToEntityForApollo(inputModel *model.FormAnswerInputForApollo, approvalRoleID *string, loggedInId *uuid.UUID, userCountry int) (*entity.FormAnswerFetchEntity, error) {
	var entity entity.FormAnswerFetchEntity
	if strings.TrimSpace(inputModel.FormID) != "" {
		uuid, err := uuid.FromString(inputModel.FormID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormId = uuid

	} else {
		if inputModel.EventID != nil && strings.TrimSpace(*inputModel.EventID) != "" {
			formId, err := postgres.FormIDCheckWithRespectToEventId(*inputModel.EventID)
			if err != nil {
				return nil, errors.New("Form answer ID does not exists !")
			}
			checkUUID, err := uuid.FromString(formId)
			if err != nil {
				return nil, errors.New("Form answer ID format is invalid!")
			}
			entity.FormId = checkUUID
			inputModel.FormID = formId

		}
	}
	approvalRole := postgres.GetCodeValueByID(*approvalRoleID)
	if *approvalRole == os.Getenv("ADMIN") || *approvalRole == os.Getenv("FINANCE") || *approvalRole == os.Getenv("CCO") {
		if !postgres.CheckFormAnswerCountry(entity.FormId.String(), userCountry) {
			return nil, errors.New("User is from different country")
		}
	}

	return &entity, nil
}

func FormDesignEntityToModelForApollo(input *entity.FormEntity, userID string, approvalRole string) []*model.FormTabs {
	countryID, _ := postgres.GetCountryIDByUserID(userID)
	var response []*model.FormTabs
	formSection := input.Design.Get()
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			var formTab model.FormTabs
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formTab.SequenceNo = seqValue
						}
					case "id":
						formTab.ID = strct.(string)
					case "title":
						formTab.Title = strct.(string)
					case "sections":
						formTab.Sections = getFormSectionValueFromMapForApollo(strct, userID, countryID, approvalRole)
					}
				}
			}
			response = append(response, &formTab)
		}
	}
	return response
}

func getFormSectionValueFromMapForApollo(formSection interface{}, userId string, countryID int, approvalRole string) []*model.FormSections {
	log.Println("getFormSectionValueFromMapForApollo")
	var response []*model.FormSections
	v := reflect.ValueOf(formSection)
	if v.Kind() == reflect.Slice {
		for _, item := range formSection.([]interface{}) {
			formsec := new(model.FormSections)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							idValue := strct.(string)
							formsec.ID = &idValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							formsec.SequenceNo = &seqValue
						}
					case "form":
						if strct != nil {
							formsec.Form = getFormQuestionFromMapForApollo(strct, userId, countryID, approvalRole)
						}
					case "childForm":
						if strct != nil {
							formsec.ChildForm = getFormQuestionFromMapForApollo(strct, userId, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, formsec)
		}
	}
	return response
}

func getFormQuestionFromMapForApollo(questions interface{}, userId string, countryID int, approvalRole string) *model.FormSectionContent {
	log.Println("getFormQuestionFromMapForApollo")
	var response model.FormSectionContent
	v := reflect.ValueOf(questions)
	if v.Kind() == reflect.Map {
		for _, key := range v.MapKeys() {
			strct := v.MapIndex(key).Interface()
			switch key.Interface() {
			case "sequenceNo":
				if strct != nil {
					seqValue := int(strct.(float64))
					response.SequenceNo = &seqValue
				}
			case "title":
				if strct != nil {
					title := strct.(string)
					response.Title = &title
				}
			case "id":
				if strct != nil {
					idValue := strct.(string)
					response.ID = &idValue
				}
			case "group":
				if strct != nil {
					response.Group = getGroupFromInterfaceMapForApollo(strct, userId, countryID, approvalRole)
				}
			}
		}
	}
	return &response
}

func getGroupFromInterfaceMapForApollo(groups interface{}, userID string, countryID int, approvalRole string) []*model.QuestionGroup {
	log.Println("getGroupFromInterfaceMapForApollo")
	var response []*model.QuestionGroup
	v := reflect.ValueOf(groups)
	if v.Kind() == reflect.Slice {
		for _, item := range groups.([]interface{}) {
			group := new(model.QuestionGroup)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							idValue := strct.(string)
							group.ID = &idValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							group.SequenceNo = &seqValue
						}
					case "questions":
						if strct != nil {
							group.Inputs = getQuestionInputGroupFromInterfaceMapForApollo(strct, userID, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, group)
		}
	}
	return response
}

func getQuestionInputGroupFromInterfaceMapForApollo(inputGroup interface{}, userID string, countryID int, approvalRole string) []*model.InputGroup {
	log.Println("getQuestionInputGroupFromInterfaceMapForApollo")
	var response []*model.InputGroup
	v := reflect.ValueOf(inputGroup)
	if v.Kind() == reflect.Slice {
		for _, item := range inputGroup.([]interface{}) {
			input := new(model.InputGroup)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "groupId":
						if strct != nil {
							id := strct.(string)
							input.GroupID = &(id)
						}
					case "groupRules":
						if strct != nil {
							input.GroupRules = getGroupRulesFromInterfaceForApollo(strct, countryID)
						}
					case "hidden":
						if strct != nil {
							hidden := strct.(bool)
							input.Hidden = &hidden
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							input.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							input.SequenceNo = &seqValue
						}
					case "inputs":
						if strct != nil {
							input.Inputs = getQuestionInputsFromInterfaceMapForApollo(strct, userID, countryID, approvalRole)
						}
					}
				}
			}
			response = append(response, input)
		}
	}

	return response
}

func getQuestionInputsFromInterfaceMapForApollo(inputs interface{}, userID string, countryID int, approvalRole string) []*model.Inputs {
	log.Println("getQuestionInputsFromInterfaceMapForApollo")
	var response []*model.Inputs
	v := reflect.ValueOf(inputs)
	if v.Kind() == reflect.Slice {
		for _, item := range inputs.([]interface{}) {
			input := new(model.Inputs)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					//log.Printf("%#v =======> ", key.Interface())
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							input.ID = &(id)
						}
					case "hidden":
						if strct != nil {
							hidden := strct.(bool)
							input.Hidden = &hidden
						}
					case "rules":
						if strct != nil {
							input.Rules = getRulesFromInterfaceForApollo(strct, countryID)
						}
					case "title":
						if strct != nil {
							seqValue := strct.(string)
							input.Title = &seqValue
						}
					case "sequenceNo":
						if strct != nil {
							seqValue := int(strct.(float64))
							input.SequenceNo = &seqValue
						}
					case "type":
						if strct != nil {
							seqValue := strct.(string)
							input.Type = &seqValue
						}

					case "source":
						if strct != nil {
							seqValue := strct.(string)
							input.Source = &seqValue
							category := strings.Split(*(input).Source, "|")
							if category[0] == "code" {
								if len(category) > 1 && category[1] == "Currency" {
									input.Values = getCurrencyValuesFromCodeTable(category[1])
								} else if len(category) > 1 {
									input.Values = getValuesFromCodeTable(category[1], approvalRole, countryID)
								} else {
									input.Values = getValuesFromCodeTable(category[0], approvalRole, countryID)
								}
							} else {
								input.Values = postgres.GetValuesFromOtherTable(*(input).Source, countryID)
							}
						}
					case "validatesource":
						if strct != nil {
							seqValue := strct.(string)
							input.Validatesource = &seqValue
							category := strings.Split(*(input).Validatesource, "|")
							if category[0] == "controls" {
								input.Validations = postgres.GetControlValidationByType(category[1], &userID, countryID)
							}
						}

					case "readOnly":
						if strct != nil {
							read := strct.(bool)
							input.ReadOnly = &read
						}
					case "operation":
						if strct != nil {
							seqValue := strct.(string)
							input.Operation = &seqValue
						}
					}
				}
			}
			response = append(response, input)
		}
	}
	return response
}

func getValuesFromInterfaceMapForApollo(question interface{}) []*model.Values {
	log.Println("getValuesFromInterfaceMapForApollo")
	var response []*model.Values
	v := reflect.ValueOf(question)
	if v.Kind() == reflect.Slice {
		for _, item := range question.([]interface{}) {
			question := new(model.Values)
			v = reflect.ValueOf(item)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "description":
						if strct != nil {
							question.Description = strct.(string)
						}
					case "value":
						if strct != nil {
							question.Value = strct.(string)
						}
					}
				}
			}
			response = append(response, question)
		}
	}
	return response
}

func getGroupRulesFromInterfaceForApollo(rules interface{}, countryID int) []*model.GroupRules {
	log.Println("getGroupRulesFromInterfaceForApollo")
	var response []*model.GroupRules
	v := reflect.ValueOf(rules)

	if v.Kind() == reflect.Slice {
		for _, item := range rules.([]interface{}) {
			v = reflect.ValueOf(item)
			rule := new(model.GroupRules)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "value":
						if strct != nil {
							value := strct.(string)
							rule.Value = &value
						}
					case "actions":
						if strct != nil {
							rule.Actions = getGroupActionValuesFromInterfaceForApollo(strct)
						}
					}
				}
			}
			response = append(response, rule)
		}
	}

	return response
}

func getRulesFromInterfaceForApollo(rules interface{}, countryID int) []*model.Rules {
	log.Println("getRulesFromInterfaceForApollo")
	var response []*model.Rules
	v := reflect.ValueOf(rules)

	if v.Kind() == reflect.Slice {
		for _, item := range rules.([]interface{}) {
			v = reflect.ValueOf(item)
			rule := new(model.Rules)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "value":
						if strct != nil {
							value := strct.(string)
							rule.Value = &value
						}
					case "actions":
						if strct != nil {
							rule.Actions = getActionValuesFromInterfaceForApollo(strct, countryID)
						}
					}
				}
			}
			response = append(response, rule)
		}
	}

	return response
}

func getGroupActionValuesFromInterfaceForApollo(actions interface{}) []*model.GroupActions {
	log.Println("getGroupActionValuesFromInterfaceForApollo")
	var response []*model.GroupActions
	v := reflect.ValueOf(actions)

	if v.Kind() == reflect.Slice {
		for _, item := range actions.([]interface{}) {
			v = reflect.ValueOf(item)
			action := new(model.GroupActions)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "groupId":
						if strct != nil {
							id := strct.(string)
							action.GroupID = &(id)
						}
					case "action":
						if strct != nil {
							act := strct.(string)
							action.Action = &(act)
						}
					}
				}
			}
			response = append(response, action)
		}
	}
	return response
}

func getActionValuesFromInterfaceForApollo(actions interface{}, countryID int) []*model.Actions {
	log.Println("getActionValuesFromInterfaceForApollo")
	var response []*model.Actions
	v := reflect.ValueOf(actions)

	if v.Kind() == reflect.Slice {
		for _, item := range actions.([]interface{}) {
			v = reflect.ValueOf(item)
			action := new(model.Actions)
			if v.Kind() == reflect.Map {
				for _, key := range v.MapKeys() {
					strct := v.MapIndex(key).Interface()
					switch key.Interface() {
					case "id":
						if strct != nil {
							id := strct.(string)
							action.ID = &(id)
						}
					case "action":
						if strct != nil {
							act := strct.(string)
							action.Action = &(act)
						}
					}
				}
			}
			response = append(response, action)
		}
	}
	return response
}
func ConvertToKebabCase(s string) string {
	// Convert the string to lower case
	s = strings.TrimSpace(strings.ToLower(s))
	// Replace spaces with hyphens
	s = strings.ReplaceAll(s, " ", "-")
	return s
}
func FetchApolloApprovalRoleModelToEntity(inputModel *model.ApolloFormAnswerIDInput) (*entity.FetchApprovalTrailEntity, error) {
	var entity entity.FetchApprovalTrailEntity
	if strings.TrimSpace(inputModel.FormAnswerID) != "" {
		uuid, err := uuid.FromString(inputModel.FormAnswerID)
		if err != nil {
			return nil, errors.New("Form answer ID format is invalid!")
		}
		if !postgres.IsFormIDExists(&uuid) {
			return nil, errors.New("Form answer ID does not exists !")
		}
		entity.FormAnswerId = uuid

	} else {
		if inputModel.EventID != nil && strings.TrimSpace(*inputModel.EventID) != "" {
			formId, err := postgres.FormIDCheckWithRespectToEventId(*inputModel.EventID)
			if err != nil {
				return nil, errors.New("Form answer ID does not exists !")
			}
			checkUUID, err := uuid.FromString(formId)
			if err != nil {
				return nil, errors.New("Form answer ID format is invalid!")
			}
			entity.FormAnswerId = checkUUID
			inputModel.FormAnswerID = formId

		}
	}
	return &entity, nil
}
func FetchApolloApprovalTrailValuesEntityToModel(input []entity.FetchApolloApprovalTrailData, inputEntity *entity.FetchApprovalTrailEntity) []*model.ApolloApprovalData {
	var outEntity []*model.ApolloApprovalData
	var previousStatus string
	actionforcancelled := "cancelled"
	codesforcancelled := codeController.GetValueKeyCodes()["approvedstatus"]
	cancelled := codesforcancelled[actionforcancelled].ID
	status, _ := postgres.FetchFormAnswersForCancel(inputEntity.FormAnswerId.String())
	var localApproverStatus bool
	localApproverStatus = false
	for key, item := range input {
		approvalTrailData := new(model.ApolloApprovalData)
		approvalTrailData.ApprovalRole = item.ApprovalRole
		approvalTrailData.ActionedBy = item.ActionedBy.String
		approvalTrailData.ActionedByRole = item.ActionedByRole.String
		approvalTrailData.ApproverActiveDirectory = item.ApproverActiveDirectory.String
		approvalTrailData.ApproverSeq = int(item.ISequenceNum.Int32)
		if *item.GroupID == 55 && item.IValue.String == "pending" {
			localApproverStatus = true
		}
		if status == cancelled {
			approvalTrailData.Status = "Cancelled"
		} else {
			if previousStatus == "approved" && item.IValue.String == "pending" {
				approvalTrailData.Status = "Pending Approval"
			} else if previousStatus == "pending" && item.IValue.String == "pending" && *item.GroupID == 56 && localApproverStatus == false {
				approvalTrailData.Status = "Pending Approval"
			} else {
				if key == 0 && item.IValue.String == "pending" {
					approvalTrailData.Status = "Pending Approval"
				} else {
					approvalTrailData.Status = item.Status.String
				}
			}
			previousStatus = item.IValue.String
		}

		if item.DateCreated.Valid {
			dateCreated := util.GetTimeUnixTimeStamp(item.DateCreated.Time)
			// date := time.Unix(int64(dateCreated), 0).Format("2006-01-02")
			approvalTrailData.DateCreated = dateCreated
		}
		if item.Comment.Valid {
			approvalTrailData.Comment = strings.ReplaceAll(item.Comment.String, "\n", " ")
		}
		outEntity = append(outEntity, approvalTrailData)
	}
	return outEntity
}
