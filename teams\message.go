package teams

type Message struct {
	title         string
	summary       string
	activityTitle string
	body          string
}

// CreateMessageCard creates the json body for sending a message card
func (m *Message) CreateMessageCard() map[string]interface{} {
	return map[string]interface{}{
		"@type":      "MessageCard",
		"@context":   "http://schema.org/extensions",
		"themeColor": "0076D7",
		"summary":    m.summary,
		"title":      m.title,
		"sections": []map[string]interface{}{
			{
				"activityTitle":    m.activityTitle,
				"activitySubtitle": m.body,
			},
		},
	}
}
