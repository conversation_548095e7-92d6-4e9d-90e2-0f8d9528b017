package entity

import (
	"database/sql"
	"time"

	uuid "github.com/satori/go.uuid"
)

type VeevaAuthorizationRestResponse struct {
	Id           string `json:"id"`
	Issued_at    string `json:"issued_at"`
	Instance_url string `json:"instance_url"`
	Signature    string `json:"signature"`
	Access_token string `json:"access_token"`
	TokenType    string `json:"token_type"`
}
type VeevaRestResponse struct {
	Data veevaCorrectResponse
}
type veevaCorrectResponse struct {
	Id           string
	Issued_at    string
	Instance_url string
	Signature    string
	Access_token string
}

type VeevaErrorResponse struct {
	Error            string `json:"error"`
	ErrorDescription string `json:"error_description"`
}

type ClientProduct struct {
	Client  string
	Product string
}

type EventExpense struct {
	Category    string   `json:"category"`
	TotalCost   string   `json:"total_cost"`
	Attachment  []string `json:"attachment"`
	Remark      string   `json:"remark"`
	Description string   `json:"description"`
}

type EventDetails struct {
	ActivityName        sql.NullString
	ActivityType        sql.NullString
	ActivityStartDate   string
	ActivityEndDate     string
	EventId             sql.NullString
	EventType           sql.NullString
	Venue               sql.NullString
	Status              sql.NullString
	RequestorVeevaId    sql.NullString
	RequestorName       sql.NullString
	RequestorEmail      sql.NullString
	OwnerId             sql.NullString // veeva ID
	OwnerName           sql.NullString
	OwnerEmail          sql.NullString
	ProductList         []string
	Country             sql.NullString
	MeetingMode         sql.NullString
	VirtualEventDetails sql.NullString
	LedBy               sql.NullString
	AgendaAttachment    []string
	ClientProductList   []*ClientProduct
	ChangeApprovalType  sql.NullString

	TherapeuticArea    sql.NullString
	EventOrganizer     sql.NullString
	NoOfHCP            sql.NullString
	NoOfNonHCP         sql.NullString
	TargetAudience     sql.NullString
	MaterialCodeNumber sql.NullString
	MeetingObjective   sql.NullString
	NoOfEmployee       sql.NullString
	GeneralRemark      sql.NullString
	AttendeeAttachment []string
	ProposalAttachment []string

	HCP          []*EventHCP
	EventExpense []*EventExpense

	CreatedAt time.Time
	UpdatedAt time.Time
}
type InputAuth struct {
	GrantType    string `json:"grant_type"`
	ClientId     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Username     string `json:"username"`
	Password     string `json:"password"`
}
type EventDetailsForVeeva struct {
	ActivityName      string
	ActivityType      string
	ActivityStartDate string
	ActivityEndDate   string
	Product           string
	EventId           string
	EventOwner        string
	EventType         string
	Duration          string
	Venue             string
	NoOfAttendees     string
	Status            int
	RequestorId       string
	OwnerId           string
	PrimaryProductId  string
	SecondaryProduct  string
	TertiaryProduct   string
}
type InputForInsertion struct {
	ActivityName      string `json:"Name"`
	ActivityType      string `json:"ZLG_eZFlow_Activity_Type__c"`
	CountryCodeQsC    string `json:"Country_Code_QS__c"`
	ActivityStartDate string `json:"Start_Time_vod__c"`
	ActivityEndDate   string `json:"End_Time_vod__c"`
	Duration          int    `json:"Duration_hrs_QS__c"`
	EventId           string `json:"ZLG_eZFlow_ID__c"`
	Venue             string `json:"Location__c"`
	Status            string `json:"ZLG_eZFlow_Status__c"`

	RequestorId string `json:"ZLG_eZFlow_Event_Requestor__c"`
	OwnerId     string `json:"OwnerId"`

	PrimaryProductId string `json:"Product_QS__c"`
	SecondaryProduct string `json:"ZLG_Secondary_Product__c"`
	TertiaryProduct  string `json:"ZLG_Tertiary_Product__c"`

	EventName           string `json:"ZLG_eZFlow_Event_Type__c"`
	FullVenueLocation   string `json:"Venue_Location__c"`
	FullActivityName    string `json:"ZLG_eZFlow_Event_Name__c"`
	MeetingMode         string `json:"ZLG_eZFlow_Meeting_Mode__c"`
	VirtualEvent        string `json:"ZLG_eZFlow_Virtual_Event__c"`
	VirtualEventDetails string `json:"ZLG_eZFlow_Details_Event__c"`
	//EZFlowLedBy         string `json:"ZLG_eZFlow_Led_By__c"`
	EZFlowLedBy string `json:"-"`
}
type VeevaIdResponse struct {
	TotalSize      int            `json:"totalSize"`
	Done           bool           `json:"done"`
	NextRecordsURL string         `json:"nextRecordsUrl"`
	Records        []VeevaRecords `json:"records"`
}
type VeevaAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type VeevaRecords struct {
	Attributes   Attributes `json:"attributes"`
	ID           string     `json:"Id"`
	ZLGEZFlowIDC string     `json:"ZLG_eZFlow_ID__c"`
}
type CustomerInputForVeevaAccount struct {
	ID               string
	RecordTypeName   string
	Name             string
	City             string
	CustomerNo       string
	Country          int
	SpDesc           string
	IsActive         bool
	IsDeleted        bool
	CMSLClass        string
	GenderID         int
	SpeakerWeight    float64
	VeevareferenceId string
	ErrorMessage     string
	OrganizationName string
}
type VeevaGetAllDataForCustomer struct {
	TotalSize      int       `json:"totalSize"`
	Done           bool      `json:"done"`
	NextRecordsURL string    `json:"nextRecordsUrl"`
	Records        []Records `json:"records"`
}
type Attributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}

type Records struct {
	Attributes              Attributes        `json:"attributes"`
	ID                      string            `json:"Id"`
	RecordTypeId            string            `json:"RecordTypeId"`
	PrimaryCountryNWKC      string            `json:"Primary_Country_NWK__c"`
	Name                    string            `json:"Name"`
	ZLGSalesSegmentC        string            `json:"ZLG_Sales_Segment__c"`
	PrimaryCityQSC          string            `json:"Primary_City_QS__c"`
	KOLVodC                 bool              `json:"KOL_vod__c"`
	CreatedDate             string            `json:"CreatedDate"`
	LastModifiedDate        string            `json:"LastModifiedDate"`
	GenderVodC              string            `json:"Gender_vod__c"`
	StatusC                 string            `json:"Status__c"`
	PrimaryAddressQSC       string            `json:"Primary_Address_QS__c"`
	PrimaryZipPostalCodeQSC string            `json:"Primary_Zip_Postal_code_QS__c"`
	PrimaryStateQSC         string            `json:"Primary_State_QS__c"`
	FirstName               string            `json:"FirstName"`
	PrimaryParentVodr       PrimaryParentVodr `json:"Primary_Parent_vod__r"`
	Specialty2QSC           string            `json:"Specialty_2_QS__c"`
	LastName                string            `json:"LastName"`
	Specialty1QSr           Specialty1QSr     `json:"Specialty_1_QS__r"`
	PersonEmail             string            `json:"PersonEmail"`
	ExternalIDVodC          string            `json:"External_ID_vod__c"`
	SpeakerWeight           float64           `json:"ZLG_HCP_Weight__c"`
}
type EventDetailsExcelEmailAlert struct {
	ActivityName        sql.NullString
	ActivityType        sql.NullString
	ActivityStartDate   sql.NullString
	ActivityEndDate     sql.NullString
	EventId             sql.NullString
	EventType           sql.NullString
	Venue               sql.NullString
	Status              sql.NullString
	RequestorVeevaId    sql.NullString
	RequestorId         sql.NullString
	OwnerId             sql.NullString
	ProductId           sql.NullString
	Country             sql.NullString
	MeetingMode         sql.NullString
	VirtualEventDetails sql.NullString
	VeevaStatus         sql.NullString
	VeevaDateCreated    sql.NullString
	ErrorMessage        sql.NullString
}
type OtherVeevaDetailsExcelEmailAlert struct {
	Id              sql.NullString
	Status          sql.NullString
	ErrorMessage    sql.NullString
	IntegrationType sql.NullString
	EventDetails    sql.NullString
	Payload         sql.NullString
	DateCreated     sql.NullString
}
type ExportEventDetailsExcelEmailAlert struct {
	ActivityName        string
	ActivityType        string
	ActivityStartDate   string
	ActivityEndDate     string
	EventId             string
	EventType           string
	Venue               string
	Status              string
	RequestorVeevaId    string
	RequestorId         string
	OwnerId             string
	ProductId           string
	Country             string
	MeetingMode         string
	VirtualEventDetails string
	VeevaStatus         string
	VeevaDateCreated    string
	ErrorMessage        string
}
type PrimaryParentVodr struct {
	Attributes Attributes `json:"attributes"`
	Name       string     `json:"Name"`
}
type Specialty1QSr struct {
	Attributes Attributes `json:"attributes"`
	Name       string     `json:"Name"`
}
type FetchCityNameFromVeevaAccount struct {
	TotalSize int                    `json:"totalSize"`
	Done      bool                   `json:"done"`
	Records   []FetchCityNameRecords `json:"records"`
}
type FetchCityNameAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type FetchCityNameRecords struct {
	Attributes FetchCityNameAttributes `json:"attributes"`
	CityVodC   string                  `json:"City_vod__c"`
}
type SpecialtyNameFromVeeva struct {
	TotalSize int                    `json:"totalSize"`
	Done      bool                   `json:"done"`
	Records   []SpecialtyNameRecords `json:"records"`
}
type SpecialtyNameAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type SpecialtyNameRecords struct {
	Attributes SpecialtyNameAttributes `json:"attributes"`
	Name       string                  `json:"Name"`
}
type InputForComplete struct {
	Status string `json:"Status_QS__c"`
}
type InsertionResponse struct {
	Id      string   `json:"id"`
	Success bool     `json:"success"`
	Errors  []string `json:"errors"`
}
type VeevaErrResponse struct {
	Message   string   `json:"message"`
	ErrorCode string   `json:"errorCode"`
	Fields    []string `json:"fields"`
}

type VeevaGetAllUsersData struct {
	TotalSize      int            `json:"totalSize"`
	Done           bool           `json:"done"`
	NextRecordsURL string         `json:"nextRecordsUrl"`
	Records        []UsersRecords `json:"records"`
}
type UsersAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type UsersRecords struct {
	Attributes              UsersAttributes `json:"attributes"`
	ID                      string          `json:"Id"`
	IsActive                bool            `json:"IsActive"`
	Username                string          `json:"Username"`
	ZLGActiveDirectoryNameC string          `json:"ZLG_Active_Directory_Name__c"`
	Country                 string          `json:"Country"`
}

type UsersInputForVeevaAccount struct {
	UserId              string
	VeevareferenceId    string
	Email               string
	IsActive            bool
	ActiveDirectoryName string
	Country             int
}
type VeevaGetAllProductData struct {
	TotalSize      int              `json:"totalSize"`
	Done           bool             `json:"done"`
	NextRecordsURL string           `json:"nextRecordsUrl"`
	Records        []ProductRecords `json:"records"`
}
type ProductAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type ProductRecords struct {
	Attributes       ProductAttributes `json:"attributes"`
	ID               string            `json:"Id"`
	IsDeleted        string            `json:"ZLG_Status__c"`
	ProductName      string            `json:"Name"`
	GroupCode        string            `json:"ZLG_Group_Code__c"`
	Country          string            `json:"Country_Code__c"`
	CreatedDate      string            `json:"CreatedDate"`
	LastModifiedDate string            `json:"LastModifiedDate"`
	ProductOwner     string            `json:"ZLG_Product_Owners_Principals__c"`
}

type ProductInputForVeevaAccount struct {
	Productid        string
	VeevareferenceId string
	GroupCode        string
	IsDeleted        bool
	ProductName      string
	Country          int
	ProductOwner     string
}

type FetchOrganizationNameFromVeevaAccount struct {
	TotalSize int                            `json:"totalSize"`
	Done      bool                           `json:"done"`
	Records   []FetchOrganizationNameRecords `json:"records"`
}
type FetchOrganizationNameAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type FetchOrganizationNameRecords struct {
	Attributes FetchOrganizationNameAttributes `json:"attributes"`
	Name       string                          `json:"Name"`
}
type FetchRecordTypeNameFromVeevaAccount struct {
	TotalSize int                      `json:"totalSize"`
	Done      bool                     `json:"done"`
	Records   []FetchRecordNameRecords `json:"records"`
}

type FetchRecordNameAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type FetchRecordNameRecords struct {
	Attributes FetchRecordNameAttributes `json:"attributes"`
	Name       string                    `json:"Name"`
}

type VeevaGetAllAttendanceData struct {
	TotalSize      int                 `json:"totalSize"`
	Done           bool                `json:"done"`
	NextRecordsURL string              `json:"nextRecordsUrl"`
	Records        []AttendanceRecords `json:"records"`
}
type AttendanceAttributes struct {
	Type string `json:"type"`
	URL  string `json:"url"`
}
type AttendanceRecords struct {
	Attributes   AttendanceAttributes `json:"attributes"`
	ID           string               `json:"Id"`
	AccountTable AccountTable         `json:"Account_vod__r"`
	LastName     string               `json:"Last_Name_vod__c"`
	FirstName    string               `json:"First_Name_vod__c"`
	Specialty    string               `json:"Specialty_input__c"`
	AccountId    string               `json:"Account_vod__c"`
	Organization string               `json:"Organization_vod__c"`
	Status       string               `json:"Status_vod__c"`
}

type AccountTable struct {
	Attributes         AttendanceAttributes `json:"attributes"`
	ZLGLocalSpecialtyr ZLGLocalSpecialtyr   `json:"ZLG_Local_Specialty__r"`
	Specialty1QSr      Specialty1QSr        `json:"Specialty_1_QS__r"`
	Specialty2QSr      Specialty2QSr        `json:"Specialty_2_QS__r"`
	PrimaryParentvodr  PrimaryParentVodr    `json:"Primary_Parent_vod__r"`
}

type ZLGLocalSpecialtyr struct {
	Attributes Attributes `json:"attributes"`
	Name       string     `json:"Name"`
}
type Specialty2QSr struct {
	Attributes Attributes `json:"attributes"`
	Name       string     `json:"Name"`
}
type AllAttendanceVeevaAccountDetailsEntityData struct {
	ID                sql.NullString
	DateCreated       sql.NullString
	Specialty         sql.NullString
	FirstName         sql.NullString
	LastName          sql.NullString
	Name              sql.NullString
	EventID           sql.NullString
	ContactType       sql.NullString
	AccountName       sql.NullString
	AccountveevaID    sql.NullString
	AttendanceveevaID sql.NullString
	Systemcode        sql.NullString
	Organization      sql.NullString
	IsHcp             bool
	IsSpeaker         bool
}

type AllAttendanceVeevaInputEntityData struct {
	Limit           int
	Page            int
	FormAnswerID    uuid.UUID
	Name            string
	DateCreatedFrom string
	DateCreatedTo   string
	EventID         string
}

type AllAttendanceDataValidation struct {
	Specialty    string
	Name         string
	Organization string
}

type MeetingIDWithEventID struct {
	Id        string
	Status    int
	MeetingID string
}
