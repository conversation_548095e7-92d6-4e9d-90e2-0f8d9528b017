package controller

import (
	"context"

	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func FetchDepartment(ctx *context.Context, inputModel *model.GetDepartmentInput) *model.GetDepartmentResponse {
	var response model.GetDepartmentResponse
	userId := auth.GetUserID(*ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	departmentData, err := postgres.GetDepartmentData(inputModel)
	if err != nil {
		response.Error = false
		response.Message = err.Error()
		return &response
	}
	responseModel := mapper.FetchDepartmentEntityToModel(departmentData)
	if responseModel == nil {
		response.Error = false
		response.Message = "No data found"
		return &response
	}
	response.Error = false
	response.Message = ""
	response.Departments = responseModel
	return &response
}

func UpsertDepartmentData(ctx context.Context, input model.DepartmentInput) *model.UpsertDepartmentResponse {
	upsertResponse := &model.UpsertDepartmentResponse{}
	userID := auth.GetUserID(ctx)
	approvalRole := auth.GetApprovalRole(ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return upsertResponse
		}
	}
	entity, validationResult := mapper.MapDepartmentModelToEntity(input, userUUID)
	if !validationResult.Error {
		if !entity.IsDelete {
			if input.ID != nil {
				postgres.UpdateDepartmentData(entity, upsertResponse)
			} else {
				postgres.InsertDepartmentData(entity, upsertResponse)
			}
		} else {
			postgres.DeleteDepartmentData(entity, upsertResponse)
		}
	} else {
		upsertResponse.Error = true
		upsertResponse.Message = "Department validation failed!"
		upsertResponse.ValidationErrors = validationResult.ValidationErrors
	}

	return upsertResponse
}
