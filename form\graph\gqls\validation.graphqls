type maxHonorariumResponse{
    error: Boolean!
    message: String!
    maxHonorarium: Float!
}

type hcpSpeakerResponse{
   error: Boolean!
   message: String! 
}

input RoleTypes {
    RoleType: String
}

input honorariumDetails {
    expertLevel: String
    preparationTime: Int
    serviceTime: Int
    roles: [RoleTypes]
    hcpType: String
    noOfAttendees: Int
    baseType: String
    customerID: String
    hourlyRate: Int
    expertLevelInternational: String
    moreThanTwoMeeting: Boolean
}

input hcpSpeakerRequest{
    speakerName:String!
}

extend type Query {
    validateHonorarium(input: honorariumDetails!): maxHonorariumResponse!
    validateHcpSpeaker(input: hcpSpeakerRequest!): hcpSpeakerResponse!
}