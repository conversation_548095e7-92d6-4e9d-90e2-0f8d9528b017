package postgres

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/model"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"strings"
)

func GetAuditEvent(ctx context.Context, db *pgxpool.Pool, meetingID string) *model.EventAudit {
	s := strings.Split(meetingID, `-`)
	eventCode, eventSeq := fmt.Sprintf(`%v-%v-%v`, s[0], s[1], s[2]), s[3]
	query := `
SELECT 
	f.answers::TEXT,
	r.event_type::TEXT as "event type",
	r.activity_name,
	concat(f.event_code, '-', f.event_seq) as "meeting ID",
	(SELECT title from code WHERE id = f.status) as "event status",
	r.status_value as "reportingDB status",
	f.is_exceptional_approval,
	r.requestor_name,
	r.active_directory,
	r.completion_date,
	l.status as "veeva sync status",
	l.date_retry,
	l.error_message,
	l.veeva_reference_id
FROM form_answers f
LEFT JOIN reporting_db r ON r.form_answer_id = f.id
LEFT JOIN veeva_event_logs l ON l.form_answers_id = f.id
WHERE f.event_code = $1 and f.event_seq = $2`

	row := db.QueryRow(ctx, query, eventCode, eventSeq)

	var e model.EventAudit
	var veevaErr, veevaReferenceID sql.NullString
	var veevaSyncStatus sql.NullBool
	var completionDate, veevaSyncedAt sql.NullTime
	if err := row.Scan(
		&e.AnswerJSON,
		&e.EventType,
		&e.EventName,
		&e.MeetingID,
		&e.EventStatus,
		&e.ReportingDBStatus,
		&e.IsExceptionalApproval,
		&e.RequesterName,
		&e.RequestUsername,
		&completionDate,
		&veevaSyncStatus,
		&veevaSyncedAt,
		&veevaErr,
		&veevaReferenceID,
	); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}

	if completionDate.Valid {
		e.CompletionDate = completionDate.Time.String()
	}

	if veevaErr.Valid {
		e.VeevaSyncError = veevaErr.String
	}

	if veevaReferenceID.Valid {
		e.VeevaReferenceID = veevaReferenceID.String
	}

	if veevaSyncStatus.Valid && veevaSyncStatus.Bool == true {
		e.VeevaSyncStatus = `synced`
		e.VeevaSyncTime = veevaSyncedAt.Time.String()
	} else {
		e.VeevaSyncStatus = `not synced`
	}

	return &e
}
