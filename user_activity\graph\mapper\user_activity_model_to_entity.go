package mapper

import (
	"errors"
	"strings"

	"github.com/gofrs/uuid"
	"github.com/ihcp/user_activity/graph/entity"
	"github.com/ihcp/user_activity/graph/model"
)

func UserTrackingActionModelToEntity(input model.UserTrackingRequest, userID *string) (entity.UserTrackingEntity, error) {
	var entity entity.UserTrackingEntity
	uuid, err := uuid.FromString(*userID)
	if err != nil {
		return entity, errors.New("User ID format is invalid!")
	}
	entity.UserId = uuid
	if strings.TrimSpace(input.UserIP) != "" {
		entity.UserIp = strings.TrimSpace(input.UserIP)
	}
	if strings.TrimSpace(input.Action) != "" {
		entity.Action = strings.TrimSpace(input.Action)
	}
	if strings.TrimSpace(input.PageName) != "" {
		entity.PageName = strings.TrimSpace(input.PageName)
	}
	if input.EventID != nil && strings.TrimSpace(*input.EventID) != "" {
		entity.MeetingId = strings.TrimSpace(*input.EventID)
	}
	return entity, nil
}
