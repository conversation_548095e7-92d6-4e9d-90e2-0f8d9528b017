package veeva_service

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/jackc/pgx/v4"
	"github.com/jackc/pgx/v4/pgxpool"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"strings"
)

type VeevaHCP struct {
	Id       string `json:"Id"`
	Name     string `json:"Name"`
	Province string `json:"Primary_State_QS__c"`
	City     string `json:"Primary_City_QS__c"`

	HCO struct {
		Name string `json:"Name"`
		Id   string `json:"Id"`
	} `json:"Primary_Parent_vod__r"`

	Specialty struct {
		Name string `json:"Name"`
	} `json:"Specialty_1_QS__r"`
	//Classification struct {
	//	Name string `json:"Name"`
	//} `json:"Specialty_1_QS__r"`

	//Classification struct {
	//	Name string `json:"Name"`
	//} `json:"Specialty_1_QS__r"`
}

type HCOInfo struct {
	City     string
	Province string
}

func (s *Controller) SyncHCOInfo(ctx context.Context, cityName string) []HCOInfo {
	err := s.authenticate()
	if err != nil {
		panic(err)
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := s.auth.InstanceURL

	ss := strings.ReplaceAll(cityName, ` `, `+`)
	fmt.Println(ss)
	url := veevaInstanceURL + fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+Primary_City_QS__c,Primary_State_QS__c+FROM+Account+WHERE+Primary_City_QS__c='%s'+LIMIT+1`, ss)
	//url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+Id+,+IsActive+,+Username+,+ZLG_Active_Directory_Name__c+,+Country+FROM+User+WHERE+ZLG_Active_Directory_Name__c+='damalia'+OR+ZLG_Active_Directory_Name__c+='nandhyani'`
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}
	bearer := "Bearer " + s.auth.AccessToken
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	type T struct {
		TotalSize      int    `json:"totalSize"`
		Done           bool   `json:"done"`
		NextRecordsURL string `json:"nextRecordsUrl"`
		Records        []struct {
			Attributes struct {
				Type string `json:"type"`
				Url  string `json:"url"`
			} `json:"attributes"`
			Province string `json:"Primary_State_QS__c"`
			City     string `json:"Primary_City_QS__c"`
		} `json:"records"`
	}

	var responseUrlValues T
	err = json.Unmarshal(responseBody, &responseUrlValues)
	if err != nil {
		fmt.Println(string(responseBody))
		return nil
	}
	for responseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + responseUrlValues.NextRecordsURL
		var ResponseUrlNextValues T
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			panic(err)
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			fmt.Println(err.Error())
			continue
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, err := ioutil.ReadAll(responseForNextRecord.Body)
		if err != nil {
			panic(err)
		}
		err = json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err != nil {
			fmt.Println(err.Error())
			continue
		}
		responseUrlValues.Records = append(responseUrlValues.Records, ResponseUrlNextValues.Records...)
		responseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}

	rs := make([]HCOInfo, len(responseUrlValues.Records))
	for i, v := range responseUrlValues.Records {
		rs[i] = HCOInfo{
			Province: v.Province,
			City:     v.City,
		}

	}
	return rs
}

type HCPTerritory struct {
	HCPVeevaId   string `json:"ObjectId"`
	Territory2Id string `json:"Territory2Id"`
}

func (s *Controller) RequestHCPByTerritory(ctx context.Context, territoryIds []string) ([]*HCPTerritory, error) {
	if err := s.authenticate(); err != nil {
		return nil, err
	}

	type VeevaResponse struct {
		TotalSize      int             `json:"totalSize"`
		Done           bool            `json:"done"`
		NextRecordsURL string          `json:"nextRecordsUrl"`
		Records        []*HCPTerritory `json:"records"`
	}

	veevaInstanceURL := s.auth.InstanceURL
	bearer := "Bearer " + s.auth.AccessToken
	url := veevaInstanceURL + `/services/data/v59.0/query?q=SELECT+ObjectId,Territory2Id+FROM+ObjectTerritory2Association+WHERE+Territory2Id+IN+('` + strings.Join(territoryIds, `','`) + `')`
	//url := veevaInstanceURL + `/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+FROM+Territory2+LIMIT+1`
	fmt.Println(url)
	var totalResponse VeevaResponse
	f := func(url string, bearer string) VeevaResponse {
		var body []byte
		request, err := http.NewRequest("GET", url, bytes.NewBuffer(body))
		if err != nil {
			panic(err)
		}

		request.Header.Add("Authorization", bearer)
		request.Header.Set("Content-Type", "application/json")

		client := &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}

		response, err := client.Do(request)
		if err != nil {
			panic(err)
		}
		defer response.Body.Close()

		responseBody, err := io.ReadAll(response.Body)
		if err != nil {
			panic(err)
		}

		var res VeevaResponse
		err = json.Unmarshal(responseBody, &res)
		if err != nil {
			log.Println(string(responseBody))
			panic(err)
		}

		m := make(map[string]bool)
		nonDuplicateList := make([]*HCPTerritory, 0)
		for i := range res.Records {
			v := fmt.Sprintf(`%s%s`, res.Records[i].HCPVeevaId, res.Records[i].Territory2Id)
			_, ok := m[v]
			if !ok {
				m[v] = true
			} else {
				nonDuplicateList = append(nonDuplicateList, res.Records[i])
			}

		}

		res.Records = nonDuplicateList
		return res
	}

	res := f(url, bearer)
	totalResponse = res
	for totalResponse.NextRecordsURL != "" {
		res = f(veevaInstanceURL+totalResponse.NextRecordsURL, bearer)
		totalResponse.Records = append(totalResponse.Records, res.Records...)
		totalResponse.NextRecordsURL = res.NextRecordsURL
	}

	return totalResponse.Records, nil
}

func (s *Controller) SaveHCPTerritoryData(ctx context.Context, db *pgxpool.Pool, data []*HCPTerritory) {
	if len(data) == 0 {
		return
	}

	//for i := range data {
	//	fmt.Println(data[i].VeevaTerritoryId, data[i].Email, "-----------")
	//}
	batch := &pgx.Batch{}
	for _, d := range data {
		d := d
		query := `
		UPDATE customer
		SET
			territory_id = $2
		WHERE
			veeva_reference_id = $1
		`
		batch.Queue(
			query,
			d.HCPVeevaId[:15], d.Territory2Id,
		)
	}

	results := db.SendBatch(ctx, batch)
	defer func() {
		err := results.Close()
		if err != nil {
			panic(err)
		}
	}()

	_, err := results.Exec()
	if err != nil {
		panic(err)
	}
}

//func (s *Controller) QueryHCPByCountry(ctx context.Context, db *pgxpool.Pool, countryCodeList []string) {
//	if len(countryCodeList) == 0 {
//		return
//	}
//
//	q := `
//
//	`
//
//	//for i := range data {
//	//	fmt.Println(data[i].VeevaTerritoryId, data[i].Email, "-----------")
//	//}
//	batch := &pgx.Batch{}
//	for _, d := range data {
//		d := d
//		query := `
//		UPDATE customer
//		SET
//			territory_id = $2
//		WHERE
//			veeva_reference_id = $1
//		`
//		batch.Queue(
//			query,
//			d.HCPVeevaId[:15], d.Territory2Id,
//		)
//	}
//
//	results := db.SendBatch(ctx, batch)
//	defer func() {
//		err := results.Close()
//		if err != nil {
//			panic(err)
//		}
//	}()
//
//	_, err := results.Exec()
//	if err != nil {
//		panic(err)
//	}
//}

func (s *Controller) UpdateHCPProvinceData(ctx context.Context, db *pgxpool.Pool, data []HCOInfo) {
	if len(data) == 0 {
		return
	}

	batch := &pgx.Batch{}
	for _, d := range data {
		fmt.Println(fmt.Sprintf(`%s ------- %s`, d.Province, d.City))
		d := d
		query := `
		UPDATE customer
		SET
			province = $2
		WHERE
			city = $1
		`
		batch.Queue(
			query,
			d.City, d.Province,
		)
	}

	results := db.SendBatch(ctx, batch)
	defer func() {
		err := results.Close()
		if err != nil {
			panic(err)
		}
	}()

	_, err := results.Exec()
	if err != nil {
		panic(err)
	}
}

func (s *Controller) GetHCPFromVeevaByCountry(ctx context.Context, territoryID string) []string {
	err := s.authenticate()
	if err != nil {
		panic(err)
	}
	method := "GET"
	var body []byte
	veevaInstanceURL := s.auth.InstanceURL

	//url := veevaInstanceURL + fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+Primary_City_QS__c,Primary_State_QS__c+FROM+Account+WHERE+Primary_City_QS__c='%s'+LIMIT+1`, ss)
	//url := veevaInstanceURL + fmt.Sprintf(`/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+FROM+Account+WHERE+isPersonAccount=true+AND+Status__c='Active'+Limit+1`)
	url := veevaInstanceURL + fmt.Sprintf(`/services/data/v59.0/query?q=SELECT+ObjectId+FROM+ObjectTerritory2Association+WHERE+Territory2Id='%s'+AND+IsDeleted=false`, territoryID)
	//Primary_Parent_vod__c":"0015h00000uiFVCAA2"
	request, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		panic(err)
	}
	bearer := "Bearer " + s.auth.AccessToken
	request.Header.Add("Authorization", bearer)
	request.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	response, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer response.Body.Close()

	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}

	type T struct {
		TotalSize      int    `json:"totalSize"`
		Done           bool   `json:"done"`
		NextRecordsURL string `json:"nextRecordsUrl"`
		Records        []struct {
			ObjectID string `json:"objectId"`
		} `json:"records"`
	}
	var responseUrlValues T
	err = json.Unmarshal(responseBody, &responseUrlValues)
	if err != nil {
		fmt.Println(string(responseBody))
		return nil
	}

	for responseUrlValues.NextRecordsURL != "" {
		url = veevaInstanceURL + responseUrlValues.NextRecordsURL
		var ResponseUrlNextValues T
		requestForNextRecord, err := http.NewRequest(method, url, bytes.NewBuffer(body))
		requestForNextRecord.Header.Add("Authorization", bearer)
		requestForNextRecord.Header.Set("Content-Type", "application/json")
		if err != nil {
			panic(err)
		}
		responseForNextRecord, err := client.Do(requestForNextRecord)
		if err != nil {
			fmt.Println(err.Error())
			continue
		}
		defer responseForNextRecord.Body.Close()
		responseForNextRecordBody, err := ioutil.ReadAll(responseForNextRecord.Body)
		if err != nil {
			panic(err)
		}
		err = json.Unmarshal(responseForNextRecordBody, &ResponseUrlNextValues)
		if err != nil {
			fmt.Println(err.Error())
			continue
		}
		responseUrlValues.Records = append(responseUrlValues.Records, ResponseUrlNextValues.Records...)
		responseUrlValues.NextRecordsURL = ResponseUrlNextValues.NextRecordsURL
	}

	rs := []string{}
	for _, v := range responseUrlValues.Records {
		rs = append(rs, v.ObjectID)
	}
	return rs

}

func (s *Controller) UpdateHCPTerritory(ctx context.Context, db *pgxpool.Pool, hcpIds []string, territoryID string) error {
	if len(hcpIds) == 0 {
		return nil
	}

	batchUpdateFunc := func(hcpIds []string) {
		batch := &pgx.Batch{}
		for i := range hcpIds {
			d := hcpIds[i][0:15]
			//fmt.Println(d, territoryID)
			query := `
		INSERT INTO customer_territories (veeva_reference_id, territory_id)
		VALUES ($1, $2)
		ON CONFLICT (veeva_reference_id, territory_id)
		DO NOTHING	
		`
			batch.Queue(
				query,
				d, territoryID,
			)
			//
			//rs, err := db.Exec(ctx, query, d, territoryID)
			//if err != nil {
			//	panic(err)
			//}
			//fmt.Println("--- ", rs.RowsAffected())
		}

		results := db.SendBatch(ctx, batch)
		defer results.Close()

		rs, err := results.Exec()
		if err != nil {
			panic(err)
		}
		fmt.Println("--- Update HCP: ", len(hcpIds), rs.RowsAffected())
	}

	var hcps []string
	for i := range hcpIds {
		hcps = append(hcps, hcpIds[i])

		if i == len(hcpIds)-1 || i%100 == 0 {
			batchUpdateFunc(hcps)
			hcps = []string{}
		}
	}

	return nil
}
