package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"github.com/ihcp/form/graph/controller"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
)

// UpsertApprovalRoles is the resolver for the upsertApprovalRoles field.
func (r *mutationResolver) UpsertApprovalRoles(ctx context.Context, input *model.UpsertRequest) (*model.ApprovalResponse, error) {
	panic(fmt.Errorf("not implemented"))
}

// UpsertApprovalRoleManagement is the resolver for the upsertApprovalRoleManagement field.
func (r *mutationResolver) UpsertApprovalRoleManagement(ctx context.Context, input *model.UpsertApprovalRoleManagementRequest) (*model.UpsertApprovalRoleManagementResponse, error) {
	response := controller.UpsertForApprovalRoleManagement(&ctx, input)
	return response, nil
}

// DelegateApprover is the resolver for the delegateApprover field.
func (r *mutationResolver) DelegateApprover(ctx context.Context, input model.DelegateApproverInput) (*model.DelegateApproverResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "DelegateApprover", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/DelegateApprover :" + string(inputJson))
	response := controller.DelegateAnotherApprover(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("DeligateApprover", "form/DeligateApprover", time.Since(start), "200")
	return &response, nil
}

// GetApprovalRoleManagement is the resolver for the getApprovalRoleManagement field.
func (r *queryResolver) GetApprovalRoleManagement(ctx context.Context, input *model.ApprovalRolesInput) (*model.ApprovalRolesResponse, error) {
	response := controller.FetchApprovalRoles(&ctx, input)
	return response, nil
}

// GetUserRolesForapprover is the resolver for the getUserRolesForapprover field.
func (r *queryResolver) GetUserRolesForapprover(ctx context.Context, input *model.UserRoles) (*model.GetUserRolesByapprover, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetUserRolesForapprover", err)
	}
	logengine.GetTelemetryClient().TrackEvent("form/GetUserRolesForapprover :" + string(inputJson))
	response := controller.GetUserRolesFromCode(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetUserRolesForapprover", "form/GetUserRolesForapprover", time.Since(start), "200")
	return response, nil
}

// GetApproversOfSameRole is the resolver for the getApproversOfSameRole field.
func (r *queryResolver) GetApproversOfSameRole(ctx context.Context) (*model.SameApproverList, error) {
	start := time.Now().UTC()
	logengine.GetTelemetryClient().TrackEvent("form/GetApproversOfSameRole : No Input")
	response := controller.FetchDeligateApprovers(&ctx)
	logengine.GetTelemetryClient().TrackRequest("GetApproversOfSameRole", "form/GetApproversOfSameRole", time.Since(start), "200")
	return &response, nil
}
