package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertControls is the resolver for the upsertControls field.
func (r *mutationResolver) UpsertControls(ctx context.Context, input model.ControlInput) (*model.UpsertControlResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertControls", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertControls :" + string(inputJson))
	response := controller.UpsertControlData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertControls", "data_maintenance/UpsertControls", time.Since(start), "200")
	return response, nil
}

// Controls is the resolver for the controls field.
func (r *queryResolver) Controls(ctx context.Context, input model.ControlRequest) (*model.ControlResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "Controls", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Controls :" + string(inputJson))
	response := controller.ExportControlData(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("Controls", "data_maintenance/Controls", time.Since(start), "200")
	return response, nil
}
