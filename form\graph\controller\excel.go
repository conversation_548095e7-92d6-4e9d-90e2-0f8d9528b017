package controller

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"path"
	"strings"

	"github.com/ihcp/form/graph/azure"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres/util"
)

func HandleExcelUploadForEmployeeAttendance(ctx context.Context, input model.ExcelUploadRequestForEmployeeAttendance) *model.ExcelUploadResponse {
	if err := checkUserAuthorization(ctx); err != nil {
		return &model.ExcelUploadResponse{
			Error:   true,
			Message: err.Error(),
		}
	}

	var multiUpload bool
	if len(input.URL) > 1 {
		multiUpload = true
	}

	for _, attendanceUrl := range input.URL {
		extension := path.Ext(attendanceUrl)
		if extension != ".xlsx" {
			continue
			// Intended to skip error
			//return &model.ExcelUploadResponse{
			//	Error:   true,
			//	Message: "wrong file extension",
			//}
		}

		if strings.TrimSpace(input.FileName) == "" {
			return &model.ExcelUploadResponse{
				Error:   true,
				Message: "fileName is empty",
			}
		}

		_, err := url.ParseRequestURI(strings.TrimSpace(attendanceUrl))
		if err != nil {
			return &model.ExcelUploadResponse{
				Error:   true,
				Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, err.Error()),
			}
		}

		excelFile, err := azure.DownloadFileFromBlobURL(attendanceUrl)
		if err != nil {
			return &model.ExcelUploadResponse{
				Error:   true,
				Message: fmt.Sprintf("file name: %s - error when downloading file: %s \n %s", input.FileName, attendanceUrl, err.Error()),
			}
		}

		b, err := io.ReadAll(excelFile)
		if err != nil {
			return &model.ExcelUploadResponse{
				Error:   true,
				Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, err.Error()),
			}
		}

		sheetName := "employee"
		_, err, colNumber := util.ReadEmployeeAttendanceExcelFile(b, sheetName)
		if err != nil {
			fmt.Println(err)
			continue
			// Intended to skip error
			//return &model.ExcelUploadResponse{
			//	Error:   true,
			//	Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, err.Error()),
			//}
		}

		if colNumber == 4 {
			validationMessages := ProcessEmployeeAttendanceExcel(attendanceUrl, input.FileName, input.FormAnswerID, ctx, b, multiUpload)
			if validationMessages.Error {
				return &model.ExcelUploadResponse{
					Error:   true,
					Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, "can't not process data"),
				}
			}

		} else if colNumber == 6 {
			validationMessages := ProcessEmployeeAttendanceExcelForApollo(attendanceUrl, input.FileName, input.FormAnswerID, ctx, b, multiUpload)
			if validationMessages.Error {
				return &model.ExcelUploadResponse{
					Error:   true,
					Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, "can't not process data"),
				}
			}

		} else {
			return &model.ExcelUploadResponse{
				Error:   true,
				Message: fmt.Sprintf("file name: %s - error: %s", input.FileName, "format of excel file is wrong"),
			}
		}
	}

	return &model.ExcelUploadResponse{
		Error:   false,
		Message: "Excel has been submitted!",
	}
}
