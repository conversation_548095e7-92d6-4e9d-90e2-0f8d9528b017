package utils

import (
	"log"

	"github.com/ihcp/user_activity/graph/controller"
	"github.com/jasonlvhit/gocron"
)

func ExecuteCronServiceForRemoveBeforeTodayUserActivityList() {
	log.Println("ExecuteCronServiceForRemoveBeforeTodayUserActivityList")
	s := gocron.NewScheduler()
	// s.Every(120).Minutes().From(gocron.NextTick()).Do(controller.UserActivitiesDeleteCron)
	s.Every(1).Day().At("18:30").Do(controller.UserActivitiesDeleteCron)
	<-s.Start()
}
