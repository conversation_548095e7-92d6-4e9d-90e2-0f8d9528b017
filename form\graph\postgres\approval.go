package postgres

import (
	"context"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v4"
	"log"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func FetchApprovalRole(input *model.ApprovalRolesInput, country int) ([]entity.ApprovalRoles, error) {
	functionName := "FetchApprovalRole"
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var inputArgs []interface{}

	queryString = `	select arm.id, c.value as group_name,c.title as group_title , 
	d2.department as department,d2.id as department_id,
	c2.value as activity, c2.title as activity_title, 
	arm.sequence_no ,arm.min_limit ,arm.max_limit, c3.title as country_title, 
	c3.value as country_value, arm.has_condition , arm.has_international,arm.has_level_of_influence, arm.led_by
   from approval_role_management arm 
   inner join code c on arm.group_type = c.id 
   left join departments d2 on arm.department  = d2.id
   inner join code c2 on arm.activity_id  = c2.id 
   inner join code c3 on arm.country  = c3.id
   where arm.is_active = true `
	log.Printf("%s", functionName)
	if input != nil {
		if input.ID != nil {
			queryString += `and arm.id = ? `
			inputArgs = append(inputArgs, input.ID)
		}
		if input.SearchItem != nil {
			queryString += ` AND (c2.value ilike ?
			or d2.department ilike ? or c.value ilike ?
			) `
			inputArgs = append(inputArgs, "%"+*input.SearchItem+"%")
			inputArgs = append(inputArgs, "%"+*input.SearchItem+"%")
			inputArgs = append(inputArgs, "%"+*input.SearchItem+"%")
		} else {
			if input.Activity != nil {
				queryString += `and c2.value ilike ? `
				inputArgs = append(inputArgs, *input.Activity)
			}

			if input.GroupType != nil {
				queryString += `and c.value ilike ? `
				inputArgs = append(inputArgs, *input.GroupType)
			}
			if input.Department != nil {
				queryString += ` and d2.department ilike ? `
				inputArgs = append(inputArgs, *input.Department)
			}
		}

		if input.SequenceNo != nil {
			queryString += `and arm.sequence_no = ? `
			inputArgs = append(inputArgs, input.SequenceNo)
		}
		if input.MaxLimit != nil {
			queryString += `and arm.max_limit = ? `
			inputArgs = append(inputArgs, input.MaxLimit)
		}
		if input.MinLimit != nil {
			queryString += `and arm.min_limit = ? `
			inputArgs = append(inputArgs, input.MinLimit)
		}
		if input.HasCondition != nil {
			queryString += `and arm.has_condition = ? `
			inputArgs = append(inputArgs, input.HasCondition)
		}

		if input.HasInternational != nil {
			queryString += `and arm.has_international = ? `
			inputArgs = append(inputArgs, input.HasInternational)
		}
		if input.HasLevelOfInfluence != nil {
			queryString += `and arm.has_level_of_influence = ? `
			inputArgs = append(inputArgs, input.HasLevelOfInfluence)
		}
		if country > 0 {
			queryString += ` AND arm.country = ? `
			inputArgs = append(inputArgs, country)
		}

		queryString += `order by arm .date_created desc `

	} else {
		if country > 0 {
			queryString += ` AND arm.country = ? `
			inputArgs = append(inputArgs, country)
		}
		queryString += `order by arm .date_created desc `

	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("FetchApprovalRole query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var response []entity.ApprovalRoles

	for rows.Next() {
		var approval entity.ApprovalRoles
		_ = rows.Scan(&approval.ID, &approval.GroupName, &approval.GroupTitle,
			&approval.Department, &approval.DepartmentID,
			&approval.ActivityName, &approval.ActivityTitle,
			&approval.SequenceNo, &approval.MinLimit, &approval.MaxLimit, &approval.CountryTitle,
			&approval.CountryValue, &approval.HasCondition, &approval.HasInternational, &approval.HasLevelOfInfluence, &approval.LedBy)

		response = append(response, approval)
	}
	return response, nil
}

func GetApprovalRules() ([]entity.Approval, error) {
	functionName := "GetApprovalRules"
	if pool == nil {
		pool = GetPool()
	}

	queryString := `select a.description as activity_name , c.value as group_name , c1.value as approval_role 
   from approval_role_management arm 
   inner join activity a on a.id = arm.activity 
   inner join code c on arm.group_type = c.id 
   inner join code c1 on arm.approval_role  = c1.id`

	rows, err := pool.Query(context.Background(), queryString)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalRules query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var response []entity.Approval
	for rows.Next() {
		var approval entity.Approval
		_ = rows.Scan(&approval.ActivityName, &approval.GroupName, &approval.ApprovalRole)

		response = append(response, approval)
	}
	return response, nil
}

func GetUserRolesFormCode(input *model.UserRoles) ([]entity.UserRole, error) {
	functionName := "GetApprovalRules"
	if pool == nil {
		pool = GetPool()
	}
	var inputArgs []interface{}

	queryString := `select ur.id , ur.title , ur.value from roles_permission rp
	inner join user_roles ur on ur.id = rp.user_role_id 
	where approver = true `

	if input != nil {
		if input.SearchItem != nil {
			queryString += `and (ur.value ilike ? or ur.title ilike ?)`
			inputArgs = append(inputArgs, "%"+*input.SearchItem+"%")
			inputArgs = append(inputArgs, "%"+*input.SearchItem+"%")
			queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
		}
	}
	rows, err := pool.Query(context.Background(), queryString, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("GetUserRolesFormCode query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var response []entity.UserRole
	for rows.Next() {
		var approval entity.UserRole
		_ = rows.Scan(&approval.ID, &approval.Title, &approval.Value)

		response = append(response, approval)
	}
	return response, nil
}

func UpdateApproval(id uuid.UUID, isActive bool) error {
	functionName := "UpdateApproval"
	if pool == nil {
		pool = GetPool()
	}
	queryString := `UPDATE approval_role_management set is_active=$2 where id = $1 and is_deleted=false`
	_, err := pool.Exec(context.Background(), queryString, id)
	logengine.GetTelemetryClient().TrackEvent("UpdateApproval query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return err
	}
	return nil
}

func DeleteApproval(id uuid.UUID) error {
	functionName := "DeleteApproval"
	if pool == nil {
		pool = GetPool()
	}
	queryString := `UPDATE approval_role_management set is_deleted=true and is_active=false where id = $1`
	_, err := pool.Exec(context.Background(), queryString, id)
	logengine.GetTelemetryClient().TrackEvent("DeleteApproval query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return err
	}
	return nil
}

func GetGroupIDByApprovalRoleID(roleID string) int {
	if pool == nil {
		pool = GetPool()
	}
	querystring := `SELECT distinct(arm.group_type) FROM approval_role_management arm 
	inner join department_roles dr on dr.department = arm.department 
	and dr.is_active = true and dr.is_deleted = false
	WHERE dr.userrole = $1 AND arm.is_active = true AND arm.is_deleted = false`
	var id int
	var result int
	err := pool.QueryRow(context.Background(), querystring, roleID).Scan(&id)
	if err == nil {
		result = id
	}
	return result
}

//	func GetSequenceIDByApprovalRoleID(approvalRoleID int) int {
//		if pool == nil {
//			pool = GetPool()
//		}
//		querystring := "SELECT sequence_no FROM approval_role_management WHERE id = $1 AND is_active = true AND is_deleted = false"
//		var id int
//		var result int
//		err := pool.QueryRow(context.Background(), querystring, approvalRoleID).Scan(&id)
//		if err == nil {
//			result = id
//		}
//		return result
//	}
func GetApprovalRoleByApprovalID(ApprovalID string, formAnswerID string) ([]int, error) {
	functionName := "GetApprovalRoleByApprovalID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var approvalRole []int
	query := `select approval_role from approvers where approver_id = $1 and form_answer_id = $2 and is_active = true`
	rows, err := pool.Query(context.Background(), query, ApprovalID, formAnswerID)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalRoleByApprovalID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value int
		rows.Scan(&value)
		approvalRole = append(approvalRole, value)
	}
	return approvalRole, nil
}

func GetApproverIDByFormID(formID string) ([]string, error) {
	functionName := "GetApproverIDByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var approverID []string
	query := `select actioned_by from approval_log where form_answer = $1 and is_active = true`
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetApproverIDByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		approverID = append(approverID, value)
	}
	return approverID, nil
}

func GetApprovalRulesBasedOnActivitiesForFace(acivities int, inputModel model.ActivityInputs, countryID int) (map[string][]entity.TempUserSelection, error) {
	functionName := "GetApprovalRulesBasedOnActivitiesForFace"
	log.Println(functionName)
	userSelectionMap := make(map[string][]entity.TempUserSelection)
	if pool == nil {
		pool = GetPool()
	}
	var query1 string
	var args1 []interface{}
	var err error
	queryString1 := `select distinct u.active_directory,u.id, u.position, act.sequence_no from "user" u
	right join (
   select c1.title as role_name ,c.title as role_type ,c1.sequence_no as sequence_no, c1.id as code_id
	   from approval_role_management arm
	   inner join code c2 on c2.id = arm.activity_id
	   inner join code c on arm.group_type = c.id
	   inner join code c1 on arm.approval_role  = c1.id
	   where arm.activity_id in (?)
	  ) act
	  on u.approval_role = act.code_id
	  where u.is_active = true and u.is_deleted = false and u.country = ?
	  ORDER BY act.sequence_no ASC `

	query1, args1, err = sqlx.In(queryString1, acivities, countryID)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalRulesBasedOnActivitiesForFace query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return userSelectionMap, err
	}
	query1 = sqlx.Rebind(sqlx.DOLLAR, query1)
	rows1, err := pool.Query(context.Background(), query1, args1...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error1: %s", functionName, err.Error())
		return userSelectionMap, err
	}
	for rows1.Next() {
		var userSelection entity.TempUserSelection
		err = rows1.Scan(
			&userSelection.Description,
			&userSelection.Value,
			&userSelection.Hint,
			&userSelection.SequenceNo,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return userSelectionMap, err
		}
		userSelection.RoleName = "Product Manager"
		userSelection.RoleType = "Local"
		userSelection.RoleID = 69
		if _, exists := userSelectionMap[userSelection.RoleName]; exists {
			userSelectionMap[userSelection.RoleName] = append(userSelectionMap[userSelection.RoleName], userSelection)
		} else {
			temUserSelectionArray := []entity.TempUserSelection{
				userSelection,
			}
			userSelectionMap[userSelection.RoleName] = temUserSelectionArray
		}

	}
	if inputModel.HasHCPEngagement != nil && *inputModel.HasHCPEngagement == true {
		var userSelectionCluster entity.TempUserSelection
		// groupCodes := codeController.GetValueKeyCodes()["userrole"]
		// userID := groupCodes["clustercomplianceofficer"].ID
		userID := GetRoleIDByValueInUserRoles("clustercomplianceofficer")
		querystring := `select distinct c2.title as role_name ,c.title as role_type ,
			c2.sequence_no as sequence_no, c2.id as code_id
			from approval_role_management arm inner join code c2 on c2.id = arm.approval_role 
			inner join code c on arm.group_type = c.id 
			where arm.approval_role = $1`
		err := pool.QueryRow(context.Background(), querystring, userID).Scan(&userSelectionCluster.RoleName,
			&userSelectionCluster.RoleType,
			&userSelectionCluster.SequenceNo,
			&userSelectionCluster.RoleID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return userSelectionMap, err
		}
		temp, err := GetApproversBasedOnActivities(userID, countryID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return userSelectionMap, err
		}
		for _, users := range temp {
			userSelectionCluster.Description = users.Description
			userSelectionCluster.Value = users.Value
			userSelectionCluster.Hint = users.Hint
			// userSection = append(userSection, tempUserSelection)
		}
		userSelectionMap[userSelectionCluster.RoleName] = append(userSelectionMap[userSelectionCluster.RoleName], userSelectionCluster)

	}
	return userSelectionMap, nil
}

func GetApprovalRulesBasedOnActivities(acivities int, inputModel model.ActivityInputs, countryID int, activityType string) ([]entity.TempUserSelection, error) {
	functionName := "GetApprovalRulesBasedOnActivities"
	log.Println(functionName)
	var result []entity.TempUserSelection
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var query string
	var args []interface{}
	var err error

	switch activityType {
	case "ZP - Hosted Event", "Sponsorship to Healthcare Organizations/Instituitions":
		queryString = `select distinct c1.title as role_name ,c.title as role_type ,
		c1.sequence_no as sequence_no, c1.id as code_id
		from approval_role_management arm 
		inner join code c2 on c2.id = arm.activity_id  
		inner join code c on arm.group_type = c.id 
		inner join code c1 on arm.approval_role  = c1.id
		where arm.activity_id in (?) and (? between min_limit::float and max_limit::float 
		OR (? > min_limit::float AND max_limit IS NULL))
		ORDER BY c1.sequence_no ASC`

		query, args, err = sqlx.In(queryString, acivities, inputModel.TotalExpense, inputModel.TotalExpense)

	default:
		queryString = `select distinct c1.title as role_name ,c.title as role_type 
		,c1.sequence_no as sequence_no, c1.id as code_id
		from approval_role_management arm 
		inner join code c2 on c2.id = arm.activity_id 
		inner join code c on arm.group_type = c.id 
		inner join code c1 on arm.approval_role  = c1.id
		where arm.activity_id in (?)
		ORDER BY c1.sequence_no ASC`
		query, args, err = sqlx.In(queryString, acivities)

	}
	logengine.GetTelemetryClient().TrackEvent("GetApprovalRulesBasedOnActivities query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, err
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, args...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error1: %s", functionName, err.Error())
		return result, err
	}
	for rows.Next() {
		var userSelection entity.TempUserSelection
		err = rows.Scan(

			&userSelection.RoleName,
			&userSelection.RoleType,
			&userSelection.SequenceNo,
			&userSelection.RoleID,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, userSelection)

	}
	if inputModel.HasHCPEngagement != nil && *inputModel.HasHCPEngagement == true {
		if inputModel.TotalExpense < 10000 {
			var userSelectionCluster entity.TempUserSelection
			groupCodes := codeController.GetValueKeyCodes()["userrole"]
			userID := groupCodes["clustercomplianceofficer"].ID
			querystring := `select distinct c2.title as role_name ,c.title as role_type ,
			c2.sequence_no as sequence_no, c2.id as code_id
			from approval_role_management arm inner join code c2 on c2.id = arm.approval_role 
			inner join code c on arm.group_type = c.id 
			where arm.approval_role = $1`
			err := pool.QueryRow(context.Background(), querystring, userID).Scan(&userSelectionCluster.RoleName,
				&userSelectionCluster.RoleType,
				&userSelectionCluster.SequenceNo,
				&userSelectionCluster.RoleID)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - Error: %s", functionName, err.Error())
				return result, err
			}
			result = append(result, userSelectionCluster)
		}
	}
	return result, nil
}

func GetApprovalRolesActivities(input *entity.ApprovalRoleInputEntity) ([]*entity.ApprovalRoleSelections, error) {
	var result []*entity.ApprovalRoleSelections

	groupCodes := codeController.GetValueKeyCodes()["group"]
	regionalID := groupCodes["regional"].ID

	queryString := `WITH roles AS
	(
		SELECT arm.id as id,arm.group_type as groupType, arm.sequence_no,  ur.id as approvalRole, ur.value as userrole, dr1.department as departmentNmae, max_limit,
		dr.department as departmentID, arm.alternate_role as alternateRole,u.id as userID , CONCAT(u.first_name, ' ', u.last_name) as full_name,1 as row_num,
		arm.has_level_of_influence, arm.led_by
		FROM approval_role_management arm
		inner join department_roles dr on dr.department = arm.department and dr.is_active =true and dr.is_deleted =false
		inner join departments dr1 on dr1.id = dr.department 
		inner join user_roles ur on ur.id = dr.userrole and ur.is_active =true and ur.is_deleted =false
		LEFT JOIN "user" u ON (u.country = ? AND ( u.user_role_id = ur.id)
		OR
		(arm.group_type = ? AND u.user_role_id = ur.id))
		AND u.is_active = true AND u.is_deleted = false
		WHERE arm.activity_id IN (?)
		AND arm.is_active = true AND arm.country = ?
		AND ( ( ? BETWEEN arm.min_limit AND arm.max_limit ) OR min_limit IS NULL AND max_limit IS NULL OR (? >= min_limit AND max_limit IS NULL) )`
	if !input.HasInternational {
		if input.HasLevelOfInfluence {
			queryString = queryString + ` AND (arm.has_international = false or (arm.has_international = true and arm.has_level_of_influence = true)) `
		} else {
			queryString = queryString + ` AND arm.has_international = false `
		}
	}

	if !input.HasLevelOfInfluence {
		if input.HasInternational {
			queryString = queryString + ` AND (arm.has_level_of_influence = false or (arm.has_international = true and arm.has_level_of_influence = true)) `
		} else {
			queryString = queryString + ` AND arm.has_level_of_influence = false `
		}
	}

	if input.HasHCPEngagement {
		queryString = queryString + ` AND CASE WHEN arm.has_condition THEN arm.department NOT IN (select id from departments where department = 'Compliance' and is_active = true and is_deleted = false) ELSE true end`
	} else {
		queryString = queryString + ` AND arm.has_condition = false`
	}

	if input.LedBy != "" {
		queryString = queryString + fmt.Sprintf(` AND CASE WHEN array_length(arm.led_by, 1) > 0 THEN '%s' = ANY(arm.led_by) ELSE true END`, input.LedBy)
	}

	queryString = queryString + `
	),alternate as 
		(
			SELECT arm.id as id,arm.group_type as groupType, arm.sequence_no,  ur.id as approvalRole, ur.value as userrole, dr1.department as departmentNmae, max_limit,
			dr.department as departmentID,u2.id as userID , CONCAT(u2.first_name, ' ', u2.last_name) as full_name,2 as row_num
			FROM approval_role_management arm
			inner join department_roles dr on dr.department = arm.alternate_department_id and dr.is_active =true and dr.is_deleted =false
			inner join departments dr1 on dr1.id = dr.department 
			inner join user_roles ur on ur.id = dr.userrole and ur.is_active =true and ur.is_deleted =false
			left join "user" u2 on u2.country = ? and u2.user_role_id = ur.id and u2.is_active = true
			WHERE arm.activity_id IN (?)
			AND arm.is_active = true AND arm.country = ?
			AND ( ( ? BETWEEN arm.min_limit AND arm.max_limit ) OR (min_limit IS NULL AND max_limit IS NULL) OR (? >= min_limit AND max_limit IS NULL) )`

	if !input.HasInternational {
		if input.HasLevelOfInfluence {
			queryString = queryString + ` AND (arm.has_international = false or (arm.has_international = true and arm.has_level_of_influence = true)) `
		} else {
			queryString = queryString + ` AND arm.has_international = false `
		}
	}

	if !input.HasLevelOfInfluence {
		if input.HasInternational {
			queryString = queryString + ` AND (arm.has_level_of_influence = false or (arm.has_international = true and arm.has_level_of_influence = true)) `
		} else {
			queryString = queryString + ` AND arm.has_level_of_influence = false `
		}
	}

	if input.HasHCPEngagement {
		queryString = queryString + `  AND CASE WHEN arm.has_condition THEN arm.department NOT IN (select id from departments where department = 'Compliance' and is_active = true and is_deleted = false) ELSE true end `
	} else {
		queryString = queryString + ` AND arm.has_condition = false`
	}

	if input.LedBy != "" {
		queryString = queryString + fmt.Sprintf(` AND CASE WHEN array_length(arm.led_by, 1) > 0 THEN '%s' = ANY(arm.led_by) ELSE true END`, input.LedBy)
	}

	queryString = queryString + `
	),LOWER_MAX AS 
		(
		SELECT CASE WHEN MIN(max_limit) IS NOT NULL THEN MIN(max_limit) ELSE NULL END as amount FROM roles
		),approval_seq as(
		select distinct 
		(case when roles.userID is null and alternate.groupType is not null 
		then alternate.groupType else roles.groupType end),
		(case when roles.userID is null and alternate.departmentID is not null 
		then alternate.departmentID else roles.departmentID end), 
		roles.sequence_no, 
		(case when roles.userID is null and alternate.approvalRole is not null 
		then alternate.approvalRole else roles.approvalRole end),
		(case when roles.userID is null and alternate.departmentNmae is not null 
		then alternate.departmentNmae else roles.departmentNmae end),  
		(case when roles.userID is null and alternate.userID is not null 
		then alternate.userID else roles.userID end),	
		(case when roles.userID is null and alternate.full_name is not null 
		then alternate.full_name else roles.full_name end),
		(case when roles.userID is null and alternate.userID is not null
		then alternate.row_num else roles.row_num end),
		roles.has_level_of_influence
		from roles 
	    left join alternate on roles.sequence_no = (case when roles.userID is null then alternate.sequence_no end)
		WHERE CASE WHEN (SELECT amount FROM LOWER_MAX) IS NULL THEN roles.max_limit IS NULL ELSE roles.max_limit = (SELECT amount FROM LOWER_MAX) end
		),offline_data1 as(
		select od.user_id as userid,true as bool_field  from offline_date od 
	    inner join approval_seq asq on asq.userid=od.user_id 
	    where od.is_active =true and od.is_deleted =false and (od.start_date <=NOW()::date and od.end_date >=NOW()::date)
	    )
		select distinct  asq.row_num,asq.grouptype,	
		asq.departmentid,	asq.sequence_no,	asq.approvalrole,	asq.departmentnmae,	asq.userid,
		asq.full_name,odb.bool_field,
		asq.has_level_of_influence
		from approval_seq  asq
		left join roles on roles.userID=asq.userid
		left join offline_data1 odb on odb.userid=asq.userid
		where asq.userid is not null
		`
	var args []interface{}
	var err error
	var query string
	var rowNum int

	query, args, err = sqlx.In(queryString,
		input.AuthorCountryID,
		regionalID,
		input.Activities,
		input.AuthorCountryID,
		int(input.ConvertedTotalExpense),
		int(input.ConvertedTotalExpense),
		input.AuthorCountryID,
		input.Activities,
		input.AuthorCountryID,
		int(input.ConvertedTotalExpense),
		int(input.ConvertedTotalExpense),
	)

	if err != nil {
		panic(err)
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)

	//fmt.Println("------------------")
	//fmt.Println(query)
	//fmt.Println(args)

	rows, err := pool.Query(context.Background(), query, args...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		var roleSelection entity.ApprovalRoleSelections
		err = rows.Scan(
			&rowNum,
			&roleSelection.RoleType,
			&roleSelection.Department,
			&roleSelection.SequenceNo,
			&roleSelection.UserRole,
			&roleSelection.UserRoleTitle,
			&roleSelection.UserID,
			&roleSelection.UserFullName,
			&roleSelection.OfflineCheck,
			&roleSelection.HighLoi,
		)
		if err != nil {
			panic(err)
		}

		result = append(result, &roleSelection)

	}
	return result, nil
}

func CheckRequestorByActivities(activity []int, role string, country int) bool {

	pool = GetPool()

	userRole := []string{role}
	var result bool
	for _, val := range activity {
		querystring := `SELECT 1 FROM event_type_access_permission WHERE event_type = $1 and 
		user_role_id @> $2::uuid[] and is_active = true and is_deleted = false and country = $3`
		var hasValue int
		err := pool.QueryRow(context.Background(), querystring, val, userRole, country).Scan(&hasValue)
		if err == nil && hasValue > 0 {
			result = true
		}
	}

	return result
}

func CheckIsExceptionalApproval(formAnswerId string) bool {
	functionName := "CheckIsExceptionalApproval()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	var result bool
	querystring := `SELECT 1 FROM form_answers WHERE is_exceptional_approval = true and 
		id = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func CheckApprovalRoleFromRolesPermission(approvalRoleID string) bool {
	functionName := "CheckApprovalRoleFromRolesPermission()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `SELECT 1 FROM roles_permission WHERE user_role_id = $1 AND approver = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalRoleID).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func CheckApprovalRoleFromRolesPermissionForBothRequestorApprovers(approvalRoleID string) bool {
	functionName := "CheckApprovalRoleFromRolesPermissionForBothRequestorApprovers()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `SELECT 1 FROM roles_permission WHERE user_role_id = $1 AND approver = true AND requestor = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalRoleID).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func CheckApprovalRoleFromRolesPermissionForRequestor(approvalRoleID string) bool {
	log.Println("CheckApprovalRoleFromRolesPermissionForRequestor()")
	pool = GetPool()

	querystring := `SELECT 1 FROM roles_permission WHERE user_role_id = $1 AND requestor = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalRoleID).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
		panic(err)
	}

	return true
}

func GetApproverIDFromFromAnswerIDAndRole(formID string, role int) (string, error) {
	functionName := "GetApproverIDFromFromAnswerIDAndRole()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select approver_id from approvers where form_answer_id = $1 
	and approval_role = $2 + 1 and is_active = true`

	result := ""
	err := pool.QueryRow(context.Background(), query, formID, role).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetApproverIDFromFromAnswerIDAndRole query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func GetRequestorResponseByFormID(formID uuid.UUID) (string, error) {
	functionName := "GetRequestorResponseByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select actioned_by from approval_log where form_answer = $1 
	and status = 121 and is_active = true order by date_created desc`

	result := ""
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetRequestorResponseByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func GeEmailIDByFormID(formID string) ([]string, error) {
	functionName := "GeEmailIDByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var email []string
	query := `select u.email from approvers ap 
	inner join "user" u on ap.approver_id = u.id 
	inner join form_answers fa on ap.form_answer_id = fa.id 
	where fa.id = $1 and ap.group_id = 56 and ap.is_active = true`
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GeEmailIDByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		email = append(email, value)
	}
	return email, nil
}

func GetCodeDescByCatergoryID(category string, ID int) string {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT description FROM code WHERE category = $1 AND id = $2 AND is_active = true AND is_deleted = false"
	var desc string
	var result string
	err := pool.QueryRow(context.Background(), querystring, category, ID).Scan(&desc)
	if err == nil {
		result = desc
	}
	return result
}
func GetUserRoleDescByCatergoryID(ID string) string {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT description FROM user_roles WHERE id = $1 AND is_active = true AND is_deleted = false"
	var desc string
	var result string

	err := pool.QueryRow(context.Background(), querystring, ID).Scan(&desc)
	if err == nil {
		result = desc
	}
	return result
}

func CheckRegionalApproverPresentInApprovers(formAnswerID string) bool {
	functionName := "CheckRegionalApproverPresentInApprovers()"
	groupCodes := codeController.GetValueKeyCodes()["group"]
	groupID := groupCodes["regional"].ID
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from approvers where form_answer_id = $1 and group_id = $2 and is_active = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, groupID).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}
	return result
}

func GetApproversBasedOnActivities(acivities string, countryID int) ([]entity.TempUserByActivity, error) {
	functionName := "GetApproversBasedOnActivities"
	log.Println(functionName)
	var result []entity.TempUserByActivity
	if pool == nil {
		pool = GetPool()
	}
	activitiesValue := GetCodeValueByID(acivities)
	var queryString string
	var query string
	var args []interface{}
	var err error
	if *activitiesValue == "manager" {
		userCodes := codeController.GetValueKeyCodes()["userrole"]
		salesID := userCodes["salesrepresentative"].ID
		log.Println(salesID)
		queryString = `	select distinct concat(u.first_name,' ', u.last_name) as fName,u.id, u.position from "user" u 
		where u.is_active = true and u.is_deleted = false and u.country = ? and not u.approval_role = ? `
		query, args, err = sqlx.In(queryString, countryID, salesID)
	} else {
		queryString = `select distinct concat(u.first_name,' ', u.last_name) as fName,u.id, u.position from "user" u 
	where u.is_active = true and u.is_deleted = false and u.country = ? and u.approval_role = ?`
		query, args, err = sqlx.In(queryString, countryID, acivities)
	}
	logengine.GetTelemetryClient().TrackEvent("GetApproversBasedOnActivities query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, err
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, args...)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error1: %s", functionName, err.Error())
		return result, err
	}
	for rows.Next() {
		var userSelection entity.TempUserByActivity
		err = rows.Scan(
			&userSelection.Description,
			&userSelection.Value,
			&userSelection.Hint,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, userSelection)

	}
	return result, nil
}

func CheckUserApprovalRoleByMasterData(approvalRole string) bool {
	functionName := "CheckUserApprovalRoleByMasterData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	var result bool
	querystring := `select 1 from roles_permission where master_data = true and user_role_id = $1;`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalRole).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func CheckApprovalRoleManagementDataExceptMaxMinLimit(approvalId int, activityId int, sequenceNo int, groupType int, minLimit *int, maxLimt *int) bool {
	functionName := "CheckApprovalRoleManagementDataExceptMaxMinLimit()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var queryString string
	var query string
	var result bool
	var args []interface{}
	var hasValue int
	if minLimit == nil && maxLimt == nil {
		queryString = `select distinct 1 from approval_role_management where approval_role = ? 
		and sequence_no = ? and activity_id = ? and group_type = ? and max_limit is NULL and min_limit is NULL
		and is_active = true and is_deleted = false;`
		query, args, _ = sqlx.In(queryString, approvalId, sequenceNo, activityId, groupType)
	} else if minLimit != nil && maxLimt == nil {
		queryString = `select distinct 1 from approval_role_management where approval_role = ? 
		and sequence_no = ? and activity_id = ? and group_type = ? and max_limit is NULL and min_limit = ?
		and is_active = true and is_deleted = false;`
		query, args, _ = sqlx.In(queryString, approvalId, sequenceNo, activityId, groupType, *minLimit)
	} else if minLimit == nil && maxLimt != nil {
		queryString = `select distinct 1 from approval_role_management where approval_role = ? 
		and sequence_no = ? and activity_id = ? and group_type = ? and max_limit = ? and min_limit is NULL
		and is_active = true and is_deleted = false;`
		query, args, _ = sqlx.In(queryString, approvalId, sequenceNo, activityId, groupType, *maxLimt)
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	err := pool.QueryRow(context.Background(), query, args...).Scan(&hasValue)
	log.Println(hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}
	return result
}

func CheckApprovalRoleManagementDataWithMaxMinLimit(approvalId int, activityId int, sequenceNo int, groupType int, minLimit int, maxLimt int) bool {
	functionName := "CheckUserApprovalRoleByMasterData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	var result bool
	querystring := `select 1 from approval_role_management where approval_role = $1 
	and sequence_no = $2 and activity_id = $3 and group_type = $4 and max_limit = $5 and min_limit = $6
	and is_active = true and is_deleted = false;`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approvalId, sequenceNo, activityId, groupType, maxLimt, minLimit).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	} else {
		result = false
	}

	return result
}

func GetApprovalRoleManagementDataByID(userID string) (entity.GetApprovalRoleManagementMaxMinId, error) {
	functionName := "GetApprovalRoleManagementDataByID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result entity.GetApprovalRoleManagementMaxMinId

	querystring := `select min_limit, max_limit, id from approval_role_management 
	where id = $1 and is_active = true and is_deleted = false;`

	err := pool.QueryRow(context.Background(), querystring, userID).Scan(&result.MinLimit, &result.MaxLimit, &result.ID)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalRoleManagementDataByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, err
	}

	return result, nil
}

// func CheckApprovalRoleManagementMinLimitByMaxLimit(entity entity.ApprovalRoleManagementEntity) bool {
// 	functionName := "CheckApprovalRoleManagementMinLimitByMaxLimit()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}

// 	var result bool
// 	querystring := `select 1 from approval_role_management
// 	where activity_id =$1 and approval_role = $2 and min_limit = $3
// 		and sequence_no = $4 and group_type = $5 and is_active = true and is_deleted = false;`
// 	var hasValue int
// 	err := pool.QueryRow(context.Background(), querystring, entity.ActivityCodeID, entity.ApprovalRole, *entity.MaxLimit, entity.SequenceNo, entity.GroupID).Scan(&hasValue)
// 	if err == nil && hasValue > 0 {
// 		result = true
// 	}

// 	return result
// }

// func CheckApprovalRoleManagementMaxLimitByMinLimit(entity entity.ApprovalRoleManagementEntity) bool {
// 	functionName := "CheckApprovalRoleManagementMaxLimitByMinLimit()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}

// 	var result bool
// 	querystring := `select 1 from approval_role_management
// 	where activity_id =$1 and approval_role = $2 and max_limit = $3
// 		and sequence_no = $4 and group_type = $5 and is_active = true and is_deleted = false;`
// 	var hasValue int
// 	err := pool.QueryRow(context.Background(), querystring, entity.ActivityCodeID, entity.ApprovalRole, *entity.MinLimit, entity.SequenceNo, entity.GroupID).Scan(&hasValue)
// 	if err == nil && hasValue > 0 {
// 		result = true
// 	}

// 	return result
// }

// func ISApprovalRoleManagementDataWithMaxMinLimit(userID string) bool {
// 	functionName := "ISApprovalRoleManagementDataWithMaxMinLimit()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}

// 	var result bool
// 	querystring := `select 1 from approval_role_management where id = $1
// 	and  min_limit is null and is_active = true and is_deleted = false;`
// 	var hasValue int
// 	err := pool.QueryRow(context.Background(), querystring, userID).Scan(&hasValue)
// 	if err == nil && hasValue > 0 {
// 		result = true
// 	}

// 	return result
// }

// func ISApprovalRoleManagementMinLimitIsInRange(entity entity.ApprovalRoleManagementEntity) bool {
// 	functionName := "ISApprovalRoleManagementMinLimitIsInRange()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var queryString string
// 	var query string
// 	var result bool
// 	var args []interface{}
// 	var hasValue int
// 	if entity.ID != nil {
// 		queryString = `select 1 from approval_role_management
// 		where activity_id =$1 and approval_role = $2
// 		 and sequence_no = $3 and group_type = $4 and
// 		 (($5 between min_limit and max_limit) or ($5 > min_limit AND max_limit IS NULL))
// 		 and is_active = true and id <> $6;`
// 		query, args, _ = sqlx.In(queryString, entity.ActivityCodeID, entity.ApprovalRole, entity.SequenceNo, entity.GroupID, *entity.MinLimit, entity.ID)
// 	} else {
// 		queryString = `select 1 from approval_role_management
// 		where activity_id =$1 and approval_role = $2
// 		 and sequence_no = $3 and group_type = $4 and
// 		 (($5 between min_limit and max_limit) or ($5 > min_limit AND max_limit IS NULL))
// 		 and is_active = true ;`
// 		query, args, _ = sqlx.In(queryString, entity.ActivityCodeID, entity.ApprovalRole, entity.SequenceNo, entity.GroupID, *entity.MinLimit)
// 	}
// 	query = sqlx.Rebind(sqlx.DOLLAR, query)
// 	err := pool.QueryRow(context.Background(), query, args...).Scan(&hasValue)
// 	log.Println(hasValue)
// 	if err == nil && hasValue > 0 {
// 		result = true
// 	}
// 	return result
// }

// func ISApprovalRoleManagementMaxLimitIsInRange(entity entity.ApprovalRoleManagementEntity) bool {
// 	functionName := "ISApprovalRoleManagementMaxLimitIsInRange()"
// 	log.Println(functionName)
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var queryString string
// 	var query string
// 	var result bool
// 	var args []interface{}
// 	var hasValue int
// 	if entity.ID != nil {
// 		queryString = `select 1 from approval_role_management
// 		where activity_id =$1 and approval_role = $2
// 		 and sequence_no = $3 and group_type = $4 and
// 		 (($5 between min_limit and max_limit) or ($5 > min_limit AND max_limit IS NULL))
// 		 and is_active = true and id <> $6;`
// 		query, args, _ = sqlx.In(queryString, entity.ActivityCodeID, entity.ApprovalRole, entity.SequenceNo, entity.GroupID, *entity.MaxLimit, entity.ID)
// 	} else {
// 		queryString = `select 1 from approval_role_management
// 		where activity_id =$1 and approval_role = $2
// 		 and sequence_no = $3 and group_type = $4 and
// 		 (($5 between min_limit and max_limit) or ($5 > min_limit AND max_limit IS NULL))
// 		 and is_active = true ;`
// 		query, args, _ = sqlx.In(queryString, entity.ActivityCodeID, entity.ApprovalRole, entity.SequenceNo, entity.GroupID, *entity.MaxLimit)
// 	}
// 	query = sqlx.Rebind(sqlx.DOLLAR, query)
// 	err := pool.QueryRow(context.Background(), query, args...).Scan(&hasValue)
// 	log.Println(hasValue)
// 	if err == nil && hasValue > 0 {
// 		result = true
// 	}
// 	return result
// }

func GetCountryByFormAnsID(input model.ApproverformAnsID) (int, error) {
	functionName := "GetCountryByFormAnsID"
	var countryID int

	querystring := `select country from form_answers where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input.FormAnsID).Scan(&countryID)
	logengine.GetTelemetryClient().TrackEvent("GetCountryByFormAnsID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func GetrequestorIDByFormAnsID(input model.ApproverformAnsID) (string, error) {
	functionName := "GetCountryByFormAnsID"
	var requestorID string

	querystring := `select created_by from form_answers where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input.FormAnsID).Scan(&requestorID)
	logengine.GetTelemetryClient().TrackEvent("GetrequestorIDByFormAnsID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return " ", err
	}
	return requestorID, nil
}

func GetFormAnsStatus(input model.ApproverformAnsID) (int, error) {
	functionName := "GetCountryByFormAnsID"
	var status int

	querystring := `select status from form_answers where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input.FormAnsID).Scan(&status)
	logengine.GetTelemetryClient().TrackEvent("GetrequestorIDByFormAnsID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return status, nil
}

func GetApprovalByFormAnsIDForReturn(input model.ApproverformAnsID) ([]entity.FetchApprovalRoleByFormAnsIDModelToEntity, error) {
	functionName := "GetApprovalByFormAnsID"
	var result []entity.FetchApprovalRoleByFormAnsIDModelToEntity
	pool = GetPool()

	querystring := `select a.approver_id, concat(u.first_name,' ',u.last_name) as "name", 
	ur.value as approval_value, ur.title as approval_title, c1.value as group_value, c1.title as group_title,
	a.sequence_no  as sequence_no ,d2.id as departmentId , d2.department as departmentName from approvers a 
	inner join "user" u on u.id = a.approver_id 
	left join user_roles ur on ur.id = u.user_role_id and ur.is_active = true and ur.is_deleted = false
	inner join department_roles dr on dr.userrole = ur.id and dr.is_active = true and dr.is_deleted = false
	inner join departments d2 on d2.id = dr.department and d2.is_active = true and d2.is_deleted = false
	inner join code c1 on c1.id = a.group_id 
	where a.form_answer_id = $1
	and d2.id is not null
	and a.date_created in (	select distinct date_created from approvers where form_answer_id = $2
	order by date_created desc limit 1) and a.set_number = (select max(set_number) from approvers where form_answer_id = $1)
	order by a.sequence_no asc`

	rows, err := pool.Query(context.Background(), querystring, input.FormAnsID, input.FormAnsID)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalByFormAnsIDForReturn query called")
	if err != nil {
		panic(err)
	}

	for rows.Next() {
		var approval entity.FetchApprovalRoleByFormAnsIDModelToEntity
		err = rows.Scan(
			&approval.ApprovalID, &approval.Name, &approval.ApprovalValue, &approval.ApprovalTitle,
			&approval.GroupValue, &approval.GroupTitle, &approval.SequenceNo,
			&approval.DepartmentID, &approval.DepartmentName,
		)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, approval)
	}
	return result, nil
}

func GetApprovalByFormAnsID(input model.ApproverformAnsID) ([]entity.FetchApprovalRoleByFormAnsIDModelToEntity, error) {
	functionName := "GetApprovalByFormAnsID"
	ctx := context.Background()
	var result []entity.FetchApprovalRoleByFormAnsIDModelToEntity

	pool = GetPool()

	querystring := `select a.approver_id, concat(u.first_name,' ',u.last_name) as "name", 
	ur.value as approval_value, ur.title as approval_title, c1.value as group_value, c1.title as group_title, a.sequence_no  as sequence_no 
	,d2.id as departmentId , d2.department as departmentName from approvers a 
	inner join "user" u on u.id = a.approver_id 
	left join user_roles ur on ur.id = u.user_role_id and ur.is_active = true and ur.is_deleted = false
	left join department_roles dr on dr.userrole = ur.id and dr.is_active = true and dr.is_deleted = false
	left join departments d2 on d2.id = dr.department and d2.is_active = true and d2.is_deleted = false
	inner join code c1 on c1.id = a.group_id 
	where a.form_answer_id = $1
	and d2.id is not null
	and a.set_number = (select max(set_number) from approvers where form_answer_id = $1)
	order by a.sequence_no asc`

	rows, err := pool.Query(ctx, querystring, input.FormAnsID)
	if err != nil {
		panic(err)
	}

	for rows.Next() {
		var approval entity.FetchApprovalRoleByFormAnsIDModelToEntity
		err = rows.Scan(
			&approval.ApprovalID, &approval.Name, &approval.ApprovalValue, &approval.ApprovalTitle,
			&approval.GroupValue, &approval.GroupTitle, &approval.SequenceNo,
			&approval.DepartmentID, &approval.DepartmentName,
		)

		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, approval)
	}
	return result, nil
}

// func GetApprovalRoleManagementDataByActivityApproval(input entity.ApprovalRoleManagementEntity) ([]entity.GetApprovalRoleManagementMaxMinId, error) {
// 	functionName := "GetApprovalRoleManagementDataByActivityApproval"
// 	log.Println(functionName)
// 	var result []entity.GetApprovalRoleManagementMaxMinId
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var queryString string
// 	queryString = `	select min_limit, max_limit, id from approval_role_management
// 		where activity_id =$1 and approval_role = $2
// 		 and sequence_no = $3 and group_type = $4 and
// 		  is_active = true and is_deleted = false;`

// 	rows, err := pool.Query(context.Background(), queryString, input.ActivityCodeID, input.ApprovalRole, input.SequenceNo, input.GroupID)
// 	if err != nil {
// 		log.Printf("%s - Error1: %s", functionName, err.Error())
// 		return result, err
// 	}
// 	for rows.Next() {
// 		var userSelection entity.GetApprovalRoleManagementMaxMinId
// 		err = rows.Scan(
// 			&userSelection.MinLimit, &userSelection.MaxLimit, &userSelection.ID,
// 		)
// 		if err != nil {
// 			log.Printf("%s - Error: %s", functionName, err.Error())
// 			return result, err
// 		}
// 		result = append(result, userSelection)

// 	}
// 	return result, nil
// }

func HasApprovalRoleManagementID(armID *uuid.UUID) (bool, error) {
	functionName := "HasApprovalRoleManagementID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var hasValue int
	var result bool

	querystring := `select 1 from approval_role_management 
	where id = $1`

	err := pool.QueryRow(context.Background(), querystring, armID).Scan(&hasValue)
	if err == nil && hasValue == 1 {
		result = true
	} else {
		if err.Error() == "no rows in result set" {
			result = false
		} else {
			log.Println(err.Error())
			return false, err
		}
	}
	return result, nil
}

func HasExistingApprovalRoleSequence(entity entity.ApprovalRoleManagementEntity, country int) (bool, error) {
	functionName := "HasExistingApprovalRoleSequence()"
	// log.Printf("%v: - activityid: %v, groupid: %v, approvalRole: %v, sequenceNo: %v", functionName, entity.ActivityCodeID, entity.GroupID, entity.ApprovalRole, entity.SequenceNo)
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var queryBuilder strings.Builder
	var args []interface{}
	var result bool
	var hasValue int
	args = append(args, entity.ActivityCodeID, entity.GroupID, entity.SequenceNo, country, entity.HasInternational, entity.Department)
	queryBuilder.WriteString(`SELECT 1 FROM approval_role_management
	WHERE activity_id = $1 AND group_type = $2 AND sequence_no = $3 AND country = $4 And has_international = $5
	AND department = $6 AND is_active = true AND is_deleted = false `)
	var hasID bool
	if entity.ID != nil {
		queryBuilder.WriteString(`AND id <> $7 `)
		args = append(args, entity.ID)
		hasID = true
	}

	if entity.MinLimit == nil && entity.MaxLimit == nil {
		queryBuilder.WriteString(`AND min_limit IS NULL AND max_limit IS NULL `)
	} else {
		args = append(args, entity.MinLimit)
		if hasID {
			queryBuilder.WriteString(`AND min_limit = $8 `)
		} else {
			queryBuilder.WriteString(`AND min_limit = $7 `)

		}
		if entity.MaxLimit == nil {
			queryBuilder.WriteString(`AND max_limit IS NULL `)
		} else {
			if hasID {
				queryBuilder.WriteString(`AND max_limit = $9 `)
			} else {
				queryBuilder.WriteString(`AND max_limit = $8 `)
			}
			args = append(args, entity.MaxLimit)

		}
	}
	err := pool.QueryRow(context.Background(), queryBuilder.String(), args...).Scan(&hasValue)
	logengine.GetTelemetryClient().TrackEvent("HasExistingApprovalRoleSequence query called")
	if err == nil && hasValue == 1 {
		result = true
	} else if err != nil {
		if err.Error() == "no rows in result set" {
			result = false
		} else {
			logengine.GetTelemetryClient().TrackException(err)
			log.Println(err.Error())
			return false, err
		}
	}
	return result, nil
}

func HasExistingApprovalRoleManagement(entity entity.ApprovalRoleManagementEntity, country int) (bool, error) {
	functionName := "HasExistingApprovalRoleManagement()"
	// log.Printf("%v: - activityid: %v, groupid: %v, approvalRole: %v", functionName, entity.ActivityCodeID, entity.GroupID, entity.ApprovalRole)
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var queryBuilder strings.Builder
	//var query string
	var result bool
	var args []interface{}
	var hasValue int

	args = append(args, entity.ActivityCodeID)
	args = append(args, entity.GroupID)
	args = append(args, country)
	args = append(args, entity.HasInternational)

	queryBuilder.WriteString(`
		SELECT 1 FROM approval_role_management
		WHERE activity_id = $1 AND group_type = $2 AND country = $3 and has_international = $4
		AND is_active = true AND is_deleted = false 
		`)
	var hasID bool
	if entity.ID != nil {
		queryBuilder.WriteString(`AND id <> $5 `)
		args = append(args, entity.ID)
		hasID = true
	}
	departmentParamNo := "5"
	minLimitParamNo := "6"
	maxLimitParamNo := "7"
	if hasID {
		departmentParamNo = "6"
		minLimitParamNo = "7"
		maxLimitParamNo = "8"
	}

	if entity.MinLimit == nil && entity.MaxLimit == nil {
		queryBuilder.WriteString(fmt.Sprintf(`AND min_limit IS NULL AND max_limit IS NULL AND department = $%s `, departmentParamNo))
		args = append(args, entity.Department)
	} else {
		args = append(args, entity.Department)
		args = append(args, entity.MinLimit)

		queryBuilder.WriteString(fmt.Sprintf(`
		AND (max_limit IS NULL AND min_limit < $%s
		OR ( min_limit = $%s `, minLimitParamNo, minLimitParamNo))
		if entity.MaxLimit == nil {
			queryBuilder.WriteString(`AND max_limit IS NULL `)
		} else {
			queryBuilder.WriteString(fmt.Sprintf(`AND max_limit = $%s `, maxLimitParamNo))
			args = append(args, entity.MaxLimit)
		}
		queryBuilder.WriteString(fmt.Sprintf(`AND department = $%s `, departmentParamNo))
		queryBuilder.WriteString(fmt.Sprintf(`OR ( min_limit <> $%s OR `, minLimitParamNo))
		if entity.MaxLimit == nil {
			queryBuilder.WriteString(`max_limit IS NOT NULL ) `)
		} else {
			queryBuilder.WriteString(fmt.Sprintf(`max_limit <> $%s )`, maxLimitParamNo))
		}
		queryBuilder.WriteString(fmt.Sprintf(`AND ( $%s BETWEEN min_limit AND max_limit `, minLimitParamNo))
		if entity.MaxLimit != nil {
			queryBuilder.WriteString(fmt.Sprintf(`OR $%s BETWEEN min_limit AND max_limit `, maxLimitParamNo))
		}
		queryBuilder.WriteString(`)))`)
	}
	err := pool.QueryRow(context.Background(), queryBuilder.String(), args...).Scan(&hasValue)
	logengine.GetTelemetryClient().TrackEvent("HasExistingApprovalRoleManagement query called")
	if err == nil && hasValue == 1 {
		result = true
	} else if err != nil {
		if err.Error() == "no rows in result set" {
			result = false
		} else {
			logengine.GetTelemetryClient().TrackException(err)
			log.Println(err.Error())
			return false, err
		}
	}
	return result, nil
}

func CheckFormAnswerIDInApprovers(formAnswerID string) bool {
	functionName := "CheckFormAnswerIDInApprovers()"
	log.Println(functionName)
	pool = GetPool()
	//var result bool
	querystring := `select distinct 1 from approvers where form_answer_id  = $1 `
	var hasValue int
	row := pool.QueryRow(context.Background(), querystring, formAnswerID)
	err := row.Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return true
		}
		panic(err)
	}
	return false

}

func GeRegionalUserIDByFormID(formID string) ([]string, error) {
	functionName := "GeEmailIDByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var userIDs []string
	query := `select approver_id from approvers where form_answer_id = $1 and group_id = 56 and is_active = true`
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GeRegionalUserIDByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value string
		rows.Scan(&value)
		userIDs = append(userIDs, value)
	}
	return userIDs, nil
}

func GetSetNumberFromFormAnswer(formAnswerID string) (int, error) {
	functionName := "GetSetNumberFromFormAnswer"
	var setNumber int

	querystring := `select set_number from approvers 
	where form_answer_id = $1 
	order by date_created desc limit 1`
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&setNumber)
	logengine.GetTelemetryClient().TrackEvent("GetSetNumberFromFormAnswer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return setNumber, nil
}

func GetUserRoleID(userID string) (string, error) {
	functionName := "GetUserRoleID"
	var userRoleID string

	querystring := `select user_role_id from "user" where id = $1 
	and is_active = true and is_deleted = false`
	err := pool.QueryRow(context.Background(), querystring, userID).Scan(&userRoleID)
	logengine.GetTelemetryClient().TrackEvent("GetUserRoleID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return " ", err
	}
	return userRoleID, nil
}

func GetDeligateApprovers(userRoleID string, userId string, country int) ([]entity.FetchDelegateApproverEntity, error) {
	functionName := "GetDeligateApprovers"
	var result []entity.FetchDelegateApproverEntity
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select u.id , concat(u.first_name,' ',u.last_name), ur.title from "user" u 
	inner join user_roles ur on u.user_role_id = ur.id 
	where u.user_role_id = $1
	and u.is_active = true and u.is_deleted = false and u.id <> $2 and u.country = $3`

	rows, err := pool.Query(context.Background(), querystring, userRoleID, userId, country)
	logengine.GetTelemetryClient().TrackEvent("GetDeligateApprovers query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
	}

	for rows.Next() {
		var approver entity.FetchDelegateApproverEntity
		err = rows.Scan(
			&approver.ApproverID, &approver.ApproverName, &approver.UserRole,
		)

		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, approver)
	}
	return result, nil
}
