package entity

import (
	"github.com/ihcp/upload/graph/model"
	uuid "github.com/satori/go.uuid"
)

type CustomerExcelInput struct {
	ID               *uuid.UUID
	Name             string
	City             string
	CustomerNo       string
	Gender           string
	CountryName      string
	Country          int
	CountryNo        int
	TeamName         string
	TeamID           *uuid.UUID
	EmployeeName     string
	EmployeeID       *uuid.UUID
	Position         string
	SpDesc           string
	CMSLClass        string
	IsActive         bool
	IsDeleted        bool
	TeamMemberID     *uuid.UUID
	CMSLClassID      int
	GenderID         int
	ActiveDirectory  string
	Author           *uuid.UUID
	SpeakerWeight    float64
	VeevareferenceId string
	Organization     string
	AccountType      string
}

type CustomerExcel struct {
	ID             *uuid.UUID
	Country        int
	Team           string
	VPosition      string
	EmpFirstName   string
	EmpLastName    string
	CustomerNumber int
	CustomerName   string
	VSpDesc        string
	CmslClass      int
	City           string
	Gender         int
}

type ExportCustomerExcel struct {
	IsExcel        bool
	ID             *uuid.UUID
	Country        int
	Team           string
	VPosition      string
	EmpFirstName   string
	EmpLastName    string
	CustomerNumber int
	CustomerName   string
	VSpDesc        string
	CmslClass      int
	City           string
	Gender         int
}

type CustomerUpserInput struct {
	ID            *uuid.UUID
	Name          string
	City          string
	CustomerNo    int
	Gender        string
	CountryName   string
	Country       int
	CountryNo     int
	TeamName      string
	ActiveDirName string
	Position      string
	SpDesc        string
	CMSLClass     string
	IsActive      bool
	IsDeleted     bool
	TeamMemberID  *uuid.UUID
	CMSLClassID   int
	GenderID      int
}

func (c *CustomerExcelInput) ValidateExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(c.ID) != uuid.Nil && c.Name == "" && c.City == "" && c.CustomerNo == "" && c.Gender == "" && c.CountryNo == 0 && c.TeamName == "" && c.EmployeeName == "" && c.Position == "" && c.SpDesc == "" && c.CMSLClass == "" {
		c.IsDeleted = false
		c.IsActive = false
	} else {
		if c.Name == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.City == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " City is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.CustomerNo == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Customer Number is invalid"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.TeamName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Team name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.EmployeeName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Employee name is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.Position == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Position is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.SpDesc == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " SpDesc is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if c.ActiveDirectory == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " Active Directory is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
	}
}
