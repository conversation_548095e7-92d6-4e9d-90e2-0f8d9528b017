INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values (51,56,FALSE,TRUE,2,'[0,1499)',0,1499,94,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,NULL,NULL,92,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (53,55,TRUE,FALSE,3,NULL,NULL,NULL,92,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,50000]',0,50000,100,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,FALSE,TRUE,3,'[0,1499)',0,1499,94,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (54,56,FALSE,TRUE,7,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (54,56,FALSE,TRUE,7,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,6,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (54,56,FALSE,TRUE,7,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (53,56,FALSE,TRUE,5,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (54,56,FALSE,TRUE,6,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,50000]',0,50000,100,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,3,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,3,'[0,50000]',0,50000,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,420,TRUE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,89,TRUE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[0,50000]',0,50000,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,98,TRUE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (413,56,FALSE,TRUE,8,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,10000]',0,10000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (413,56,FALSE,TRUE,8,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (51,55,FALSE,TRUE,3,NULL,NULL,NULL,92,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (51,56,TRUE,FALSE,4,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (50,55,TRUE,FALSE,3,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (0,55,FALSE,TRUE,3,NULL,50001,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (47,55,TRUE,FALSE,1,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (49,55,TRUE,FALSE,2,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (0,55,FALSE,TRUE,4,NULL,50001,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,TRUE,FALSE,5,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (0,55,FALSE,TRUE,2,NULL,50001,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (53,56,FALSE,TRUE,5,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,10001,NULL,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,10001,NULL,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,10001,NULL,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,FALSE,TRUE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,FALSE,TRUE,3,NULL,10001,50000,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,FALSE,TRUE,2,NULL,10001,50000,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,FALSE,TRUE,1,NULL,10001,50000,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,418,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,56,TRUE,FALSE,3,NULL,0,10000,418,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,56,TRUE,FALSE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,56,TRUE,FALSE,3,NULL,0,10000,419,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,56,TRUE,FALSE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,10001,NULL,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,10001,NULL,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,10001,NULL,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,FALSE,TRUE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,FALSE,TRUE,3,NULL,10001,50000,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,FALSE,TRUE,2,NULL,10001,50000,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,FALSE,TRUE,1,NULL,10001,50000,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,419,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,89,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,89,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,89,FALSE,53,56,7,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,420,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,420,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,420,FALSE,53,56,7,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,98,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,98,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,98,FALSE,53,56,7,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,1,NULL,NULL,NULL,96,FALSE,53,56,7,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,1,'[0,50000]',0,50000,100,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,1,'[50001,)',50001,NULL,100,FALSE,53,56,7,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (51,56,TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (413,56,TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (48,55,TRUE,FALSE,2,NULL,NULL,NULL,93,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (54,56,FALSE,TRUE,6,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (413,56,FALSE,TRUE,4,'[0,1499)',0,1499,94,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,10000]',0,10000,89,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,TRUE,FALSE,4,NULL,NULL,NULL,92,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,10000]',0,10000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,8,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,NULL,NULL,NULL,96,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,NULL,NULL,NULL,93,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,50000]',0,50000,100,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,7,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (48,55,TRUE,FALSE,1,NULL,NULL,NULL,92,FALSE,50,55,7,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Manager' and is_active = true));


INSERT INTO approval_role_management
(approval_role, group_type, is_active, is_deleted, sequence_no, limit_range, min_limit, max_limit, activity_id, has_condition, alternate_role, alternate_group_type, country, has_international, alternate_department_id, department)
values (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (54,56,FALSE,TRUE,7,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (413,56,TRUE,FALSE,7,'[10001,50000]',10001,50000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,89,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (413,56,TRUE,FALSE,6,'[0,10000]',0,10000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,89,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (51,56,FALSE,TRUE,2,'[0,1499)',0,1499,94,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[50001,)',50001,NULL,89,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (48,55,TRUE,FALSE,1,'[50001,)',50001,NULL,100,FALSE,53,56,10,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,TRUE,FALSE,4,'[0,10000]',0,10000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,FALSE,TRUE,3,NULL,NULL,NULL,92,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,56,FALSE,TRUE,5,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,'1500',94,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Manager' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,0,10000,101,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,116,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Medical' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,116,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,0,10000,116,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,10001,NULL,116,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,10001,NULL,116,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,10001,NULL,116,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,4,NULL,10001,NULL,116,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,4,NULL,0,10000,116,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,55,FALSE,TRUE,1,NULL,10001,50000,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (51,55,TRUE,FALSE,4,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,101,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,101,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,NULL,NULL,92,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (51,55,TRUE,FALSE,4,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (0,55,FALSE,TRUE,4,NULL,10001,50000,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,56,TRUE,FALSE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,10001,NULL,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,10001,NULL,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,10001,NULL,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,FALSE,TRUE,4,NULL,10001,NULL,418,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,FALSE,TRUE,3,NULL,10001,50000,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,FALSE,TRUE,2,NULL,10001,50000,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,FALSE,TRUE,1,NULL,10001,50000,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,TRUE,FALSE,3,NULL,0,10000,418,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,3,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,3,'[0,50000]',0,50000,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (50,55,TRUE,FALSE,3,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,418,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,98,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,2,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[0,50000]',0,50000,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (413,56,FALSE,TRUE,4,'[0,1499)',0,1499,94,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,98,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (413,56,FALSE,TRUE,5,NULL,NULL,NULL,92,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,NULL,NULL,NULL,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,50000]',0,50000,100,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,8,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,6,'[0,10000]',0,10000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (413,56,FALSE,TRUE,7,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (0,56,TRUE,FALSE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,3,NULL,10001,NULL,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,10001,NULL,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,10001,NULL,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,FALSE,TRUE,4,NULL,10001,NULL,419,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,FALSE,TRUE,3,NULL,10001,50000,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (0,55,FALSE,TRUE,2,NULL,10001,50000,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,FALSE,TRUE,1,NULL,10001,50000,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (0,56,TRUE,FALSE,3,NULL,0,10000,419,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (0,55,TRUE,FALSE,2,NULL,0,10000,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (0,55,TRUE,FALSE,1,NULL,0,10000,419,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,1,'[0,50000]',0,50000,100,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,89,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,89,FALSE,53,56,10,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,420,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,NULL,NULL,NULL,92,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,1,NULL,NULL,NULL,96,FALSE,53,56,10,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[0,10000]',0,10000,98,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,98,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,98,FALSE,53,56,10,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (413,55,FALSE,TRUE,3,NULL,10001,50000,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (49,55,TRUE,FALSE,3,NULL,NULL,NULL,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (50,55,TRUE,FALSE,4,NULL,NULL,NULL,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (54,56,FALSE,TRUE,6,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (51,55,FALSE,TRUE,4,'[0,50000]',0,50000,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (54,56,TRUE,FALSE,6,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (49,55,TRUE,FALSE,2,'[0,1499]',0,1499,86,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (51,56,TRUE,FALSE,3,'[0,1499]',0,1499,86,TRUE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,TRUE,FALSE,4,'[0,1499]',0,1499,86,TRUE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,FALSE,TRUE,4,NULL,NULL,NULL,92,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[10001,50000]',10001,50000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (413,56,TRUE,FALSE,5,'[0,1499]',0,1499,86,TRUE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[50001,)',50001,NULL,98,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (53,56,FALSE,TRUE,3,'[0,1499)',0,1499,94,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,55,FALSE,TRUE,5,NULL,NULL,NULL,96,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,55,FALSE,TRUE,2,NULL,10001,50000,93,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,FALSE,TRUE,5,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (53,56,FALSE,TRUE,5,'[0,50000]',0,50000,100,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (54,56,FALSE,TRUE,6,'[50001,)',50001,NULL,100,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (413,56,TRUE,FALSE,6,'[0,10000]',0,10000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (47,55,TRUE,FALSE,1,'[0,10000]',0,10000,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'BU Head' and is_active = true)),
    (53,56,TRUE,FALSE,5,'[0,10000]',0,10000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (49,55,TRUE,FALSE,3,'[0,10000]',0,10000,420,TRUE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Compliance' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (413,56,TRUE,FALSE,7,'[10001,50000]',10001,50000,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (54,56,TRUE,FALSE,7,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Regional Franchise Head' and is_active = true)),
    (53,56,TRUE,FALSE,6,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Medical' and is_active = true)),
    (51,56,TRUE,FALSE,5,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Marketing' and is_active = true)),
    (50,55,TRUE,FALSE,4,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,FALSE,NULL,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true)),
    (413,56,TRUE,FALSE,8,'[50001,)',50001,NULL,420,FALSE,NULL,NULL,10,TRUE,NULL,(select id from departments where department = 'Regional Commercial Solutions' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,89,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[10001,50000]',10001,50000,420,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,'[50001,)',50001,NULL,420,FALSE,53,56,10,FALSE,(select id from departments where department = 'Regional Medical' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true)),
    (48,55,TRUE,FALSE,2,NULL,NULL,NULL,93,FALSE,50,55,10,FALSE,(select id from departments where department = 'Country Commercial Solution Head' and is_active = true),(select id from departments where department = 'Country Medical' and is_active = true));