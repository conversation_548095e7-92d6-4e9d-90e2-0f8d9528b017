package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"github.com/99designs/gqlgen/graphql"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/joho/godotenv"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"log"
	"net/http"
	"os"
	"runtime/debug"

	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/go-chi/chi"
	"github.com/ihcp/login/auth"
	"github.com/rs/cors"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/gorilla/websocket"
	"github.com/ihcp/data_maintenance/graph/generated"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/data_maintenance/graph/resolvers"
	"github.com/ihcp/data_maintenance/graph/util"
)

const defaultPort = "8080"

var devMode = flag.Bool("dev", false, "Start in development mode env=.dev.env")
var stgMode = flag.Bool("stg", false, "Start in development mode env=.stg.env")
var prodMode = flag.Bool("production", false, "Start in development mode env=.prod.env")
var disableTelemetry = flag.Bool("no_telemetry", false, "Inactive telemetry client")

func main() {
	err := godotenv.Load(".dev.env")
	if err != nil {
		log.Println("error loading env", err)
	}
	readInputParams()
	// ---
	logengine.New(*disableTelemetry)
	// ---
	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}
	// ---
	postgres.InitDbPool()
	pool := postgres.GetPool()
	// --- Load code into memory
	codeController.InitCodes(pool)
	// ---
	go util.StartCronService()
	// ---
	router := chi.NewRouter()
	router.Use(cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowCredentials: true,
		Debug:            false,
	}).Handler)

	srv := handler.NewDefaultServer(generated.NewExecutableSchema(generated.Config{Resolvers: resolvers.InitResolver()}))
	srv.AddTransport(&transport.Websocket{
		Upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Check against your desired domains here
				return r.Host == "*"
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	})

	srv.SetRecoverFunc(panicRecover())
	srv.SetErrorPresenter(func(ctx context.Context, e error) *gqlerror.Error {
		var err *gqlerror.Error
		if errors.As(e, &err) {
			return err
		}
		return graphql.DefaultErrorPresenter(ctx, e)
	})

	router.Use(auth.AuthMiddleware(pool))
	router.Handle("/", playground.Handler("GraphQL playground", "/query"))
	router.Handle("/query", srv)
	//http.Handle("/", playground.Handler("GraphQL playground", "/query"))
	//http.Handle("/query", srv)

	log.Printf("--- Connect to http://localhost:%s/ for GraphQL playground", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func readInputParams() {
	var env string
	var envFilePath string
	flag.Parse()
	if *devMode {
		env = `dev`
	} else if *stgMode {
		env = `stg`
	} else if *prodMode {
		env = `prod`
	}

	if env == `` {
		return
	}

	envFilePath = fmt.Sprintf(`.%s.env`, env)
	log.Println("--- Loading environment variables from:", envFilePath)
	if err := godotenv.Load(envFilePath); err != nil {
		panic(err)
	}
}

// When a panic happens
func panicRecover() func(ctx context.Context, err interface{}) error {
	return func(ctx context.Context, err interface{}) error {
		//1. print trace log
		stackTrace := string(debug.Stack())
		log.Println(fmt.Sprintf("Recovering from panic: %s\n-------  Stack Trace:\n%s-------  Recovering ends  -------", err, stackTrace))

		//2. response with error = internal error
		return &gqlerror.Error{
			Path:    graphql.GetPath(ctx),
			Message: `internal_error`,
		}
	}
}
