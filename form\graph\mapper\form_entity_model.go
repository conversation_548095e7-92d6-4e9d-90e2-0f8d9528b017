package mapper

import (
	"errors"
	"log"
	"sort"
	"strconv"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/model"
	//uuid "github.com/satori/go.uuid"
)

func UserRoleSelectionEntityToModel(selections []*entity.ApprovalRoleSelections, userId string) (*model.ApprovalRolesUserSelectionResponse, error) {

	var resultEntity model.ApprovalRolesUserSelectionResponse
	roleMap := make(map[int]*model.UserByApprovalRole)
	// code := codeController.GetIdKeyCodes()["userrole"]
	groupType := codeController.GetIdKeyCodes()["group"]

	if len(selections) == 0 {
		return nil, errors.New("You have exceeded the maximum limit!")
	}
	for _, userSelection := range selections {
		if _, ok := roleMap[userSelection.SequenceNo]; !ok {

			roleModel := &model.UserByApprovalRole{
				DepartmentID:   userSelection.Department,
				DepartmentName: userSelection.UserRoleTitle,
				SequenceNo:     userSelection.SequenceNo,
				DepartmentType: groupType[userSelection.RoleType].Title.String,
				HighLoi:        &userSelection.HighLoi,
			}
			roleMap[userSelection.SequenceNo] = roleModel
		}
		if userSelection.UserID.String == userId {
			continue
		}

		if userSelection.Department != roleMap[userSelection.SequenceNo].DepartmentID {
			continue
		}
		user := &model.UserSelection{}
		if !userSelection.OfflineCheck.Bool {
			user = &model.UserSelection{
				Description: userSelection.UserFullName.String,
				Value:       userSelection.UserID.String,
				RoleID:      &userSelection.UserRole,
			}
		} else {
			continue
		}
		roleMap[userSelection.SequenceNo].UserSelection = append(roleMap[userSelection.SequenceNo].UserSelection, user)
	}
	for _, v := range roleMap {
		resultEntity.UsersByapprovalRole = append(resultEntity.UsersByapprovalRole, v)
	}
	sort.Slice(resultEntity.UsersByapprovalRole[:], func(i, j int) bool {
		return resultEntity.UsersByapprovalRole[i].SequenceNo < resultEntity.UsersByapprovalRole[j].SequenceNo
	})
	return &resultEntity, nil
}
func CodeDescriptionAndValueEntityToModelForAllDropdownValues(fetchmeetingMode []entity.Dropdown, fetchRole []entity.Dropdown, fetchExpertLevelInternational []entity.Dropdown, fetchCountry []entity.Dropdown, fetchExpertLevel []entity.Dropdown, fetchTypeOfEventExpenses []entity.Dropdown) *model.GetActivitiesAndEventSelectionAllDropDownValues {
	var resultEntity model.GetActivitiesAndEventSelectionAllDropDownValues
	var meetingMode []*model.Dropdown
	var country []*model.Dropdown
	var expertLevelInternational []*model.Dropdown
	var expertLevel []*model.Dropdown
	var role []*model.Dropdown
	var typeOfEventExpenses []*model.Dropdown

	for _, value := range fetchCountry {
		Country := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		country = append(country, Country)
	}
	for _, value := range fetchmeetingMode {
		MeetingMode := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		meetingMode = append(meetingMode, MeetingMode)
	}
	for _, value := range fetchExpertLevel {
		ExpertLevel := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		expertLevel = append(expertLevel, ExpertLevel)
	}
	for _, value := range fetchExpertLevelInternational {
		ExpertLevelInternational := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		expertLevelInternational = append(expertLevelInternational, ExpertLevelInternational)
	}
	for _, value := range fetchRole {
		Role := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		role = append(role, Role)
	}
	for _, value := range fetchTypeOfEventExpenses {
		TypeOfEventExpenses := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		typeOfEventExpenses = append(typeOfEventExpenses, TypeOfEventExpenses)
	}

	resultEntity.Country = country
	resultEntity.Role = role
	resultEntity.ExpertLevel = expertLevel
	resultEntity.ExpertLevelInternational = expertLevelInternational
	resultEntity.TypeOfEventExpenses = typeOfEventExpenses
	resultEntity.MeetingMode = meetingMode

	return &resultEntity
}

func CodeDescriptionAndValueEntityToModel(status []entity.Dropdown, activity []entity.DropdownIDAndDescription, event []entity.DropdownIDAndDescription, team []entity.Dropdown, product []entity.Dropdown, productOwner []entity.Dropdown, fetchRequestors []entity.Dropdown) *model.GetActivitiesAndEventSelection {
	var resultEntity model.GetActivitiesAndEventSelection
	var statusModel []*model.Dropdown
	var teamModel []*model.Dropdown
	var productModel []*model.Dropdown
	var activityModel []*model.Dropdown
	var eventModel []*model.Dropdown
	var productOwnerModel []*model.Dropdown
	var fetchRequestorsModel []*model.Dropdown
	for _, value := range status {
		statusDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		statusModel = append(statusModel, statusDropdown)
	}
	for _, value := range productOwner {
		productOwn := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		productOwnerModel = append(productOwnerModel, productOwn)
	}
	for _, value := range fetchRequestors {
		eachRequestor := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		fetchRequestorsModel = append(fetchRequestorsModel, eachRequestor)
	}
	for _, value := range team {
		teamDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		teamModel = append(teamModel, teamDropdown)
	}
	for _, value := range product {
		productDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		productModel = append(productModel, productDropdown)
	}

	for _, value := range activity {
		if value.Description == "Sponsorship to individual HCPs - Within ZP Location" ||
			value.Description == "Sponsorship to individual HCPs - Outside ZP Location" ||
			value.Description == "Sponsorship to individual HCPs - Within Local Country" {
			continue
		}
		activityDropdown := &model.Dropdown{
			Value:       strconv.Itoa(int(value.ID)),
			Description: value.Description,
		}
		activityModel = append(activityModel, activityDropdown)
	}
	for _, value := range event {
		eventDropdown := &model.Dropdown{
			Value:       strconv.Itoa(int(value.ID)),
			Description: value.Description,
		}
		eventModel = append(eventModel, eventDropdown)
	}
	resultEntity.ActivityType = activityModel
	resultEntity.EventType = eventModel
	resultEntity.Status = statusModel
	resultEntity.Team = teamModel
	resultEntity.Product = productModel
	resultEntity.ProductOwner = productOwnerModel
	resultEntity.RequestorList = fetchRequestorsModel

	return &resultEntity
}
func ExportAuditTrailEntityToExcelModel(auditTrailData []entity.ExcelAuditTrailData) []*entity.RequestorFormAnswerListExcel {
	var responseExcel []*entity.RequestorFormAnswerListExcel
	for _, item := range auditTrailData {
		resExcel := entity.RequestorFormAnswerListExcel{}
		// var rowData []interface{}
		var data []interface{}
		if item.MeetingId.String != "" {
			data = append(data, item.MeetingId.String)
		} else {
			data = append(data, "-")
		}
		if item.ApproverName.String != "" {
			data = append(data, item.ApproverName.String)
		} else {
			data = append(data, "-")
		}
		if item.UserRole.String != "" {
			data = append(data, item.UserRole.String)
		} else {
			data = append(data, "-")
		}
		if item.DateCreated.String != "" {
			data = append(data, item.DateCreated.String)
		} else {
			data = append(data, "-")
		}
		if item.Comment.String != "" {
			data = append(data, item.Comment.String)
		} else {
			data = append(data, "-")
		}
		if item.Action.String != "" {
			data = append(data, item.Action.String)
		} else {
			data = append(data, "-")
		}
		resExcel.Data = data
		responseExcel = append(responseExcel, &resExcel)
	}
	return responseExcel

}
func CodeDescriptionAndValueForRequestorEntityToModel(requestor []entity.Dropdown) []*model.Dropdown {
	var requestorModel []*model.Dropdown

	for _, value := range requestor {
		requestorDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		requestorModel = append(requestorModel, requestorDropdown)
	}

	return requestorModel
}

func FetchTypeApprovalListEntityToModel(requestor []entity.ApproverListByType) []*model.ApproversByType {
	var requestorModel []*model.ApproversByType

	for _, value := range requestor {
		requestorDropdown := model.ApproversByType{
			UserID:         value.ApproverId,
			RoleID:         value.UserRoleId,
			SequenceNo:     value.SequenceNo,
			DepartmentID:   value.DepartmentID,
			DepartmentName: value.DepartmentName,
			UserName:       value.UserName,
		}
		requestorModel = append(requestorModel, &requestorDropdown)
		log.Println(requestorDropdown)
	}
	return requestorModel
}

func EmployeeManagementFilterEntityToModel(requestorActiveDirectory []entity.Dropdown, actionBy []entity.Dropdown, actionType []entity.Dropdown) *model.GetEmployeeManagementFilterDropdown {
	var response model.GetEmployeeManagementFilterDropdown
	var RequestorActiveDirectory []*model.Dropdown
	var ActionBy []*model.Dropdown
	var ActionType []*model.Dropdown
	for _, value := range requestorActiveDirectory {
		RequestorActiveDirectoryDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		RequestorActiveDirectory = append(RequestorActiveDirectory, RequestorActiveDirectoryDropdown)
	}
	for _, value := range actionBy {
		ActionByDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		ActionBy = append(ActionBy, ActionByDropdown)
	}
	for _, value := range actionType {
		ActionTypeDropdown := &model.Dropdown{
			Description: value.Description,
			Value:       value.Value,
		}
		ActionType = append(ActionType, ActionTypeDropdown)
	}
	response.ActionBy = ActionBy
	response.ActionType = ActionType
	response.RequestorActiveDirectory = RequestorActiveDirectory
	return &response
}
func GetFmvAuditlogsEntityToModel(res []entity.FmvAuditlogsEntity) []*model.GetFmvAuditlogsData {
	var response []*model.GetFmvAuditlogsData
	for _, item := range res {
		var result model.GetFmvAuditlogsData
		if item.Description.String != "" {
			Description := item.Description.String
			result.Description = Description
		}
		if item.MaxLimit.String != "" {
			MaxLimit := item.MaxLimit.String
			result.MaxLimit = MaxLimit
		}
		if item.CreatedBy.String != "" {
			CreatedBy := item.CreatedBy.String
			result.CreatedBy = strings.TrimSpace(CreatedBy)
		}
		if item.Currency.String != "" {
			Currency := item.Currency.String
			result.Currency = Currency
		}
		if item.Date.String != "" {
			Date := item.Date.String
			result.Date = Date
		}
		if item.Status.String != "" {
			Status := item.Status.String
			result.Status = Status
		}
		response = append(response, &result)
	}
	return response
}
