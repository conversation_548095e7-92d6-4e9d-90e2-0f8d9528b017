input ExceptionalApprovalManagementInput{
    id: String
    userId: String
    isDelete: Boolean
    sequencenNo: Int
    groupType: String

}
type ExceptionalApprovalManagementResponse{
    error: Boolean!
    message: String!
}
# type ExceptionalApproverManagement{
#     id: String
#     userId:String
#     userName: String
#     userRole: String
#     userRoleValue: String
#     userRoleTitle: String
#     countryValue: String
#     countryId:Int
#     departmentId:String
#     departmentName:String
#     createdBy:String
#     createdAt:Int
#     updatedBy:String
#     updatedAt:Int
# }

type UserSelection {
    description: String
    hint: String 
    value: String
    roleId: String
}

type UserByApprovalRole {
    departmentName: String
    sequenceNo: Int
    departmentType: String
    departmentID: String 
    userSelection: [UserSelection]
}

input FetchExceptionalApprovalManagementInput{
    userId: String
    isCompliance:Boolean
}
type FetchExceptionalApprovalManagementResponse{
    error: Boolean!
    message: String!
    data: [UserByApprovalRole!]! 
}
extend type Mutation{
    upsertExceptionalApprovalManagement(input: ExceptionalApprovalManagementInput!): ExceptionalApprovalManagementResponse!
}
extend type Query{
    FetchExceptionalApprovalManagement(input: FetchExceptionalApprovalManagementInput!): FetchExceptionalApprovalManagementResponse!
}
