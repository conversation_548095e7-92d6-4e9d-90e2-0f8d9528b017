package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertOfflineDate is the resolver for the upsertOfflineDate field.
func (r *mutationResolver) UpsertOfflineDate(ctx context.Context, input model.OfflineDateInput) (*model.UpsertOfflineDateResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertOfflineDate", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertOfflineDate :" + string(inputJson))
	response := controller.UpsertforOfflineDate(ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertofflineDate", "data_maintenance/UpsertofflineDate", time.Since(start), "200")
	return response, nil
}

// GetOfflineDate is the resolver for the getOfflineDate field.
func (r *queryResolver) GetOfflineDate(ctx context.Context) (*model.GetOfflineDateResponse, error) {
	start := time.Now().UTC()
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Material : No Input")
	response := controller.FetchOfflineDate(ctx)
	logengine.GetTelemetryClient().TrackRequest("GetofflineDate", "data_maintenance/GetofflineDate", time.Since(start), "200")
	return response, nil
}
