package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/azure"
	"github.com/jackc/pgx/v4"
	"log"
	"net/url"
	"path"
	"strconv"
	"strings"
	"time"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"

	wkhtml "github.com/SebastiaanKlippert/go-wkhtmltopdf"
)

func GetAllHcpExpenses(ctx context.Context, tx pgx.Tx, formAnswerId string) (*[]string, *[]string) {
	functionName := "GetAdminFormAnswerList()"
	log.Println(functionName)

	queryString := `with approval as (select fa.id,fa.answers  from form_answers fa where fa.id =? )
	, groups AS(
					SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM approval , jsonb_array_elements(answers) with ordinality
					arr(item_object)
					where arr.item_object->>'title' in ('HCP Engagement Info')
			), tab_section AS (
					SELECT id,  arr.item_object->'childForm'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality       
					arr(item_object)
			), group_questions AS (
					SELECT id,arr.item_object->>'sequenceNo' as row_no  , arr.item_object->'questionAnswers' as group_questions  FROM tab_section, jsonb_array_elements(group_section) with ordinality
					arr(item_object)

			), question_grp AS (
					SELECT id, arr.item_object->'id',row_no,arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
					arr(item_object)
			), questions AS (
					SELECT id,row_no,row_number() over() as row_num, arr.item_object->>'id' as qn_id, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
					arr(item_object)
					where questions not  IN ('null','[]')
			),only_cost as(
			 select id,row_num, row_no,arr.item_object->>'id' as qn_id, 
			 (case when (arr.item_object->>'value' ~ '^[0-9\.]+$') then (arr.item_object->>'value') 
			 else (arr.item_object->>'value') 
			 end) as qn_values
			,arr.item_object->>'description' as des FROM questions, jsonb_array_elements(qn_values) with ordinality
					arr(item_object)
			),all_details as(
				SELECT id,row_no::int4,(case 
				 when ((array_agg(qn_values))[4]~ '^[0-9\.]+$') then (array_agg(qn_values))[4]
			when ((array_agg(qn_values))[3]~ '^[0-9\.]+$') then (array_agg(qn_values))[3] 
			 when ((array_agg(qn_values))[2]~ '^[0-9\.]+$') then (array_agg(qn_values))[2]
			when ((array_agg(qn_values))[1]~ '^[0-9\.]+$') then (array_agg(qn_values))[1] 
			end) as total_cost,
			(case when((array_agg(des))[1]='Type of Meal') then
			(array_agg(qn_values))[1] when (((array_agg(des))[1]='No of Days' or (array_agg(des))[1]='Cost per Day')) then
			'Accomodation'
			when (((array_agg(des))[1]='Name of Expense')) then
			'Others'
			else (array_agg(des))[1] end) as type_of_expense 
			FROM only_cost
				 where des not in ('Do you want to add any attachment?','Remarks') 
				 group by row_num,row_no,id
			      union all
                  SELECT id,generate_series(0, (SELECT max(row_no::int4)::int4 FROM only_cost), 1) as row_no,'' ,'' from only_cost
                  group by id
	              order by row_no asc , type_of_expense desc
				 )
				 select ARRAY_AGG(total_cost) as expense_cost,ARRAY_AGG(type_of_expense) as name_of_expense from all_details
				 where (total_cost<>''or type_of_expense='')  and type_of_expense <>'Total Cost'
					group by id`
	var inputargs []interface{}
	inputargs = append(inputargs, formAnswerId)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := tx.Query(ctx, queryString, inputargs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var hcpCostList *[]string
	var hcpCostExpenseList *[]string
	for rows.Next() {
		err := rows.Scan(&hcpCostList, &hcpCostExpenseList)
		if err != nil {
			panic(err)
		}
	}

	return hcpCostList, hcpCostExpenseList
}
func GetAdminAnswerListForReport(ctx context.Context, tx pgx.Tx, formAnswerId string) *entity.FormAnswerForAdmins {
	functionName := "GetAdminFormAnswerList()"
	log.Println(functionName)

	codes2 := codeController.GetValueKeyCodes()["typeofemail"]
	completedMailStatusID := codes2["formcompleted"].ID
	queryString := `WITH fa AS 
	(	
	SELECT fa.id, CONCAT(u.first_name ,' ', u.last_name) as "name", u.id as userId,
	u.active_directory,
	fa.date_created,
	fa.event_code as eventCode,
	fa.event_seq as eventSeq,
	c.title as title,
	c.value as value,
	c1.title as countrytitle,c1.value as countryvalue,
	c2.title as currencytitle, 
	c2.value as currencyvalue,
	fa.total_cost, 
	fa.answers,
	fa.status,
	(select max (date_created) from email_log where event_id = fa.id and type_of_email = ?) as completion_date
	from form_answers fa
	inner join "user" u on fa.created_by = u.id
	inner join code c on fa.status  = c.id
	inner join code c1 on fa.country = c1.id 
	inner join code c2 on fa.currency = c2.id 
	and fa.id = ?
	), groups AS 
	( 
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
	arr(item_object)
	), tab_section AS ( 
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
	), group_questions AS (  
		SELECT id, arr.item_object->'questionAnswers' as group_questions,ROW_NUMBER() OVER () AS row_num_loi FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT id, arr.item_object->'id' as question_grp_id ,arr.item_object->'answers' as questions,row_num_loi,ROW_NUMBER() OVER () AS row_num  FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS
	(
		SELECT id,row_num_loi,row_num, arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'id' IN ('typeOfEngagement','loi-question-2-upload-2','loi-question-1','loi-question-2','loi-remarks-1','loi-remarks-2','level-of-influence','type-of-ihcp-activity','remarks-1','question-1','position','name','product-owner', 'type-of-event', 'activity-name', 'activity-start-date', 'activity-end-date', 'team', 'product','venue','govt-or-non-govt-hcp',
		'event-organizer','speaker-name','specialty','hco-institute-name','type-of-event-expenses','event-owner','proposed-honorarium') or arr.item_object->>'title' IN ('Expenses')
	),cost_answer as
	(
		SELECT id, arr.item_object as value FROM questions, jsonb_array_elements(qn_values) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'description' IN ('Total Cost') and qn_values not IN ('null')
	), bod_answer AS (
		SELECT id,row_num,qn_title, qn_values#>>'{0,value}' as value
		FROM questions 
		WHERE qn_id IN ('remarks-1','question-1','position','name')
	), loi_answer AS (
		SELECT id,row_num_loi,qn_title, qn_values#>>'{0,value}' as value
		FROM questions WHERE qn_id IN ('loi-question-1','loi-question-2','loi-question-2-upload-2','loi-remarks-1','loi-remarks-2','level-of-influence'))
	SELECT fa.id, fa.completion_date, fa.name, fa.active_directory, fa.date_created,fa.eventcode, fa.eventseq, fa.title, fa.value, fa.countrytitle, fa.countryvalue, fa.currencytitle, fa.currencyvalue, fa.total_cost,
	(select array(SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('activity-start-date','activity-end-date','activity-name','product','venue','event-organizer','event-owner') AND questions.id = fa.id)) as activity_start_date,
	(select array (SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('govt-or-non-govt-hcp','speaker-name','specialty','hco-institute-name','proposed-honorarium') AND questions.id = fa.id)) as govtnongovthcp,
 	(select array(SELECT value#>>'{id}' FROM  cost_answer WHERE cost_answer.id = fa.id)) as eventExpenses,
	(select array(SELECT value#>>'{value}' FROM  cost_answer WHERE cost_answer.id = fa.id)) as expenseCost,
	(select array (SELECT jsonb_array_elements(qn_values)->>'description'::text FROM  questions WHERE qn_id in ('type-of-event','type-of-ihcp-activity') AND questions.id = fa.id)) as event_type,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'team' AND questions.id = fa.id) as team,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-name' AND questions.id = fa.id) as activity_name,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'product' AND questions.id = fa.id) as product,
    (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'product-owner' AND questions.id = fa.id) as product_owner,
    (select  array_to_json(array(select jsonb_build_object(
    'Name', (SELECT  qn_values#>>'{0,value}'   FROM questions WHERE qn_id = 'name' AND row_num = ba.row_num),
    'Position', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'position' AND row_num = ba.row_num),
    'Question', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'question-1' AND row_num = ba.row_num),
    'Remarks', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'remarks-1' AND row_num = ba.row_num))   from bod_answer ba where ba.id=fa.id   group by row_num ORDER BY row_num))) as BOD,
    (select  array_to_json(array(select jsonb_build_object(
    'levelOfInfluence', (SELECT  qn_values#>>'{0,value}'   FROM questions WHERE qn_id = 'level-of-influence' AND row_num_loi = la.row_num_loi),
    'Remarks1', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'loi-remarks-1' AND row_num_loi = la.row_num_loi),
    'Question1', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'loi-question-1' AND row_num_loi = la.row_num_loi),
    'Upload', (SELECT qn_values#>>'{0,value}' FROM questions WHERE qn_id = 'loi-question-2-upload-2' AND row_num_loi = la.row_num_loi))   from loi_answer la where la.id=fa.id   group by row_num_loi ORDER BY row_num_loi))) as LOI,
    (select array(SELECT (qn_values#>>'{0,value}') FROM  questions  where qn_id in ('typeOfEngagement') AND questions.id = fa.id)) as engagement,
	fa.userId FROM fa ORDER BY fa.date_created desc`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, completedMailStatusID, formAnswerId)
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := tx.Query(ctx, queryString, inputArgs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var answerEntity entity.FormAnswerForAdmins
	for rows.Next() {
		err := rows.Scan(&answerEntity.ID, &answerEntity.CompletionTime,
			&answerEntity.RequestorName, &answerEntity.RequestorActiveDirectory,
			&answerEntity.CreatedDate, &answerEntity.EventCode,
			&answerEntity.EventSeq, &answerEntity.Status,
			&answerEntity.StatusValue, &answerEntity.CountryTitle,
			&answerEntity.CountryValue, &answerEntity.CurrencyTitle,
			&answerEntity.CurrencyValue, &answerEntity.TotalCost,
			&answerEntity.SingleDataArray, &answerEntity.GovtNonGovtHcp,
			&answerEntity.EventExpenses, &answerEntity.ExpenseCost,
			&answerEntity.EventTypes, &answerEntity.Team,
			&answerEntity.ActivityName, &answerEntity.Product, &answerEntity.ProductOwner, &answerEntity.BoardOfDirectory, &answerEntity.LevelOfInfluence, &answerEntity.TypeOfEngagements,
			&answerEntity.UserID)
		if err != nil {
			panic(err)
		}
	}
	return &answerEntity
}

func InsertReportingDB(ctx context.Context, tx pgx.Tx, input entity.FormAnswerForAdmins, hcpCostList *[]string, hcpExpenseList *[]string) {
	log.Printf("%s", "InsertReportingDB()")

	query := `INSERT INTO reporting_db
	(form_answer_id, completion_date, requestor_name, active_directory, date_created, event_code, event_seq, status_title, status_value, country_title, country_value, currency_title, currency_value, total_cost, activity_start_date, govt_non_govt_hcp, event_expenses, expense_cost, event_type, created_by, team, product, activity_name,product_owner_name,hcp_cost_list,hcp_type_of_expense,board_of_directors_for_hco_sponsorship,level_of_influence,type_of_engagements)
	VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23,$24,$25,$26,$27,$28,$29);
	`
	_, err := tx.Exec(ctx, query,
		input.ID,
		input.CompletionTime,
		input.RequestorName,
		input.RequestorActiveDirectory,
		input.CreatedDate,
		input.EventCode,
		input.EventSeq,
		input.Status,
		input.StatusValue,
		input.CountryTitle,
		input.CountryValue,
		input.CurrencyTitle,
		input.CurrencyValue,
		input.TotalCost,
		input.SingleDataArray,
		input.GovtNonGovtHcp,
		input.EventExpenses,
		input.ExpenseCost,
		input.EventTypes, input.UserID, input.Team, input.Product, input.ActivityName, input.ProductOwner, hcpCostList, hcpExpenseList, input.BoardOfDirectory, input.LevelOfInfluence, pq.Array(input.TypeOfEngagements))
	if err != nil {
		panic(err)
	}
}

func UpdateReportingDB(ctx context.Context, tx pgx.Tx, input entity.FormAnswerForAdmins, hcpCostList *[]string, hcpExpenseList *[]string) {
	log.Printf("%s", "UpdateReportingDB()")

	query := `UPDATE reporting_db
	SET 
		completion_date = $2, requestor_name = $3,
	active_directory = $4, date_created = $5,
	event_code = $6, event_seq = $7, status_title = $8,
	status_value = $9, country_title = $10,
	country_value = $11, currency_title = $12, currency_value = $13,
	total_cost = $14,
	activity_start_date = $15,
	govt_non_govt_hcp = $16,
	event_expenses = $17, expense_cost = $18,
	event_type = $19,
	country = NULL, created_by = $20
	, team = $21, product = $22, activity_name = $23,product_owner_name=$24, hcp_cost_list=$25,hcp_type_of_expense=$26,board_of_directors_for_hco_sponsorship=$27,level_of_influence=$28,type_of_engagements=$29,
	updated_date = now() WHERE form_answer_id = $1 ;
	`
	_, err := tx.Exec(ctx, query, input.ID, input.CompletionTime, input.RequestorName,
		input.RequestorActiveDirectory, input.CreatedDate, input.EventCode, input.EventSeq,
		input.Status, input.StatusValue, input.CountryTitle, input.CountryValue,
		input.CurrencyTitle, input.CurrencyValue, input.TotalCost, input.SingleDataArray,
		input.GovtNonGovtHcp, input.EventExpenses, input.ExpenseCost, input.EventTypes,
		input.UserID, input.Team, input.Product, input.ActivityName, input.ProductOwner, hcpCostList, hcpExpenseList, input.BoardOfDirectory, input.LevelOfInfluence, input.TypeOfEngagements)
	if err != nil {
		panic(err)
	}
}

func CheckFormAnswerIdPresentInReportingDatabase(ctx context.Context, tx pgx.Tx, formAnswerID string) bool {
	functionName := "CheckFormAnswerIdPresentInReportingDatabase()"
	log.Println(functionName)

	querystring := `select 1 from reporting_db where form_answer_id = $1`
	var hasValue int
	err := tx.QueryRow(ctx, querystring, formAnswerID).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
		panic(err)
	}

	return true
}

// AlldataFetchQuery -> DONT USE THIS. SEARCH FOR THE OTHER WA
func AlldataFetchQuery(formAnswerID string) ([]entity.BasicInfoDataForPDF, []entity.HcpDataForPDF, entity.HcpExpenseDataForPDF, error) {
	pool = GetPool()

	var ArrayBasicInfoDataForPDF []entity.BasicInfoDataForPDF
	var ArrayHCPDataExpenseForPDF entity.HcpExpenseDataForPDF
	var ArrayHCPDataForPDF []entity.HcpDataForPDF
	querystring := `      
        
        
	WITH fa AS 
	(	
	SELECT fa.answers ,rd.requestor_name 
	from form_answers fa 
	inner join reporting_db rd on rd.form_answer_id =fa.id 
	where fa.id  = $1
	), groups AS (
		SELECT  requestor_name ,arr.item_object->'sequenceNo' as row_no,arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa , jsonb_array_elements(answers) with ordinality
					arr(item_object)
					where arr.item_object->>'sequenceNo'  in ('2','1')
	), tab_section AS (
		SELECT  requestor_name,row_no, arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
		order by row_no desc
	), group_questions AS (
		SELECT  requestor_name,row_no,arr.item_object->'sequenceNo' as row_num_basic,arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT  requestor_name,row_no,arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
		where row_num_basic<> '2'
	), questions AS (
		SELECT  requestor_name,row_no,arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, (case when arr.item_object->'values' in ('[]',null) then '[{"value": ""}]' 
		else arr.item_object->'values' end) as qn_values  FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
	)
		,allbasicinfoData as(select  '1','',qn_title, (case when qn_title='Event Requestor' then requestor_name when
		arr.item_object->>'value' = 'true' then 'Yes' when arr.item_object->>'value' = 'false' then 'No' else arr.item_object->>'value' end)  as value
		FROM questions, jsonb_array_elements(qn_values) with ordinality
		arr(item_object) where 
		case when (qn_title in ('Type of IHCP activity') and arr.item_object->>'value'='100') 
		then  qn_id in ('type-of-ihcp-activity','type-of-event','event-requestor','event-owner','country','activity-name','activity-start-date','activity-end-date','duration-of-the-activity','product','country','team','therapeutic-area','venue','target-audience','material-code-number','meeting-objective','no-of-hcps','no-of-non-hcps','employee-no','do-you-want-to-add-any-attachment-for-agenda-or-proposal','attendees-attachments','proposal-attachments','upload','promotional-or-non-promotional-event','meeting-mode','details-of-the-virtual-event','linked-event-id','product-owner','sponsorship-to-hco-requesting-hco','sponsorship-to-hco-recipient','general-remarks','Activity Type','Event Type','multi-product')
		when (qn_title in ('Type of IHCP activity') and arr.item_object->>'value'='99') 
		then  qn_id in ('type-of-ihcp-activity','type-of-event','event-requestor','event-owner','country','activity-start-date','activity-end-date','duration-of-the-activity','name-of-organization','nature-of-hcohci',
			'name-of-contact-person','purpose-of-grant','in-return-will-zuellig-pharma-be-benefiting-from-this-grant?','details-of-the-zuellig-pharma-be-benefiting-from-this-grant','any-red-flags?',
			'details-of-the-red-flags','has-the-red-flag-been-resolved?','has-the-red-flag-been-resolved-provide-more-details','is-the-recipient-owned-or-controlled-by-a-government-or-government-official',
			'details-of-the-recipient-owned-or-controlled-by-a-government','upload-documents-of-ddq','upload-documents-of-request-letter','upload-documents-of-screening-report','Activity Type')
		else qn_id in ('type-of-ihcp-activity','type-of-event','event-requestor','event-owner','country','activity-name','activity-start-date','activity-end-date','duration-of-the-activity','product','country','team','therapeutic-area','venue','target-audience','material-code-number','meeting-objective','no-of-hcps','no-of-non-hcps','employee-no','do-you-want-to-add-any-attachment-for-agenda-or-proposal','attendees-attachments','proposal-attachments','upload','promotional-or-non-promotional-event','meeting-mode','details-of-the-virtual-event','linked-event-id','product-owner','general-remarks','Activity Type','Event Type','multi-product')
		end
		UNION ALL
		select '1','','Submission Date',
	(EXTRACT(EPOCH FROM fa.date_created::timestamp)::int)::text
	from form_answers fa where fa.id  = $1
		union all
		select '1','','Change Approval Summary',
	fa.change_request_summary 
	from form_answers fa where fa.id  = $1
	union all
		select '1','','Recall Summary',
	fa.recall_summary
	from form_answers fa where fa.id  = $1)
	,fa1 AS 
		(	
		SELECT fa.answers 
		from form_answers fa where
		 fa.id  = $1
		), groups1 AS 
		(
			SELECT  arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa1 , jsonb_array_elements(answers) with ordinality
						arr(item_object)
						where arr.item_object->>'title' in ('HCP Engagement Info')
		), tab_section1 AS (
			SELECT   arr.item_object->'form'->'groupAnswer' as group_section FROM groups1, jsonb_array_elements(section) with ordinality
			arr(item_object)
		), group_questions1 AS (
			SELECT  arr.item_object->'sequenceNo' as row_no,arr.item_object->'questionAnswers' as group_questions FROM tab_section1, jsonb_array_elements(group_section) with ordinality
			arr(item_object)
		), question_grp1 AS (
			SELECT  row_no,arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions1, jsonb_array_elements(group_questions) with ordinality
			arr(item_object)
		), questions1 AS (
			SELECT  row_no,arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, (case when arr.item_object->'values' is null then '[{"value": ""}]' 
			else arr.item_object->'values' end) as qn_values  FROM question_grp1, jsonb_array_elements(questions) with ordinality
			arr(item_object) 
			WHERE arr.item_object->>'id' IN ('typeOfEngagement', 'individualCategory','govt-or-non-govt-hcp', 'type-of-hcp', 'remarks', 'speaker-name', 'base-type','hcp-country','specialty',
			'hco-institute-name','level-of-influence','loi-question-1','loi-remarks-1','loi-question-2','loi-remarks-2','loi-question-2-upload-2','proposed-honorarium','to-the-best-of-your-knowledge-does-the-hcp-or-an-immediate-family-member-work-for-the-government','does-the-hcp-influence-in-purchasing-or-reimbursement-or-pricing-or-drug-approval-or-similar','does-the-hcp-influence-in-public-health-policy-or-laws-or-regulations',
			'does-the-hcp-serve-on-international-non-governmental-health-organization','what-is-the-hcp-sphere-of-influence','expert-level','expert-level-international-others',
			'preparation-time','service-time','hourly-rate','more-than-2-lectures-meetings','role','proposed-honorarium',
			'do-you-want-to-add-any-attachment','upload','remarks'
			) 
		)
			,allhcpData as(SELECT '2',row_no::text,qn_title,  (case  when
			arr.item_object->>'value' = 'true' then 'Yes' when arr.item_object->>'value' = 'false' then 'No' else arr.item_object->>'value' end)  as value FROM questions1, jsonb_array_elements(qn_values) with ordinality
			arr(item_object))
			 ,fa2 AS 
		(	
		SELECT fa.answers 
		from form_answers fa where
		 fa.id  = $1
		 
		), groups2 AS 
		(
			SELECT  arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa2 , jsonb_array_elements(answers) with ordinality
						arr(item_object)
						where arr.item_object->>'title' in ('HCP Engagement Info')
		), tab_section2 AS (
			SELECT   arr.item_object->'childForm'->'groupAnswer' as group_section FROM groups2, jsonb_array_elements(section) with ordinality
			arr(item_object)
			
		), group_questions2 AS (
			SELECT  arr.item_object->'sequenceNo' as row_no,arr.item_object->'questionAnswers' as group_questions FROM tab_section2, jsonb_array_elements(group_section) with ordinality
			arr(item_object)
		), question_grp2 AS (
			SELECT  row_no,arr.item_object->'sequenceNo' as row_num,  arr.item_object->'answers' 
			as questions FROM group_questions2, jsonb_array_elements(group_questions) with ordinality
			arr(item_object)
		), questions2 AS (
			SELECT  row_no,row_num,arr.item_object->'sequenceNo' as row_seq,arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, (case when arr.item_object->'values' is null then '[{"value": ""}]' else arr.item_object->'values' end) as qn_values  FROM question_grp2, jsonb_array_elements(questions) with ordinality
			arr(item_object) 
		),allHcpExpenseData2 as 
			( 
			SELECT (row_no::int8+1) as row_no,row_num,row_seq, (case when arr.item_object->>'value'='true' then 'Yes' when arr.item_object->>'value'='false' then 'No' else arr.item_object->>'value' end)   as value,arr.item_object->>'description' as description 
			FROM questions2, jsonb_array_elements(qn_values) with ordinality
			arr(item_object)),
			allhcpexpensedata as(
			select '3',row_no::text	,value	,description from allHcpExpenseData2
			where row_num<>'10')
			
			select * from allbasicinfoData
			union all 
			select * from allhcpData
			union all
			select * from allhcpexpensedata
`
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return ArrayBasicInfoDataForPDF, ArrayHCPDataForPDF, ArrayHCPDataExpenseForPDF, err
	}

	m := make(map[string]bool)
	for rows.Next() {
		var HCPexpenseDataForPDF entity.DataexpensePdf
		var HCPDataForPDF entity.HcpDataForPDF
		var BasicInfoDataForPDF entity.BasicInfoDataForPDF
		var seq, rownum, title, value sql.NullString
		rows.Scan(&seq, &rownum, &title, &value)
		if seq.String == "1" {
			BasicInfoDataForPDF.Title = title
			BasicInfoDataForPDF.Value = value
			if _, ok := m[title.String]; !ok {
				m[title.String] = true
				ArrayBasicInfoDataForPDF = append(ArrayBasicInfoDataForPDF, BasicInfoDataForPDF)
			}
		}

		if seq.String == "2" {
			HCPDataForPDF.Seq1 = rownum
			HCPDataForPDF.Title = title
			HCPDataForPDF.Value = value
			ArrayHCPDataForPDF = append(ArrayHCPDataForPDF, HCPDataForPDF)
		}
		if seq.String == "3" {
			HCPexpenseDataForPDF.Seq1 = rownum
			HCPexpenseDataForPDF.Value = title
			HCPexpenseDataForPDF.Title = value
			ArrayHCPDataExpenseForPDF.DataexpensePdf = append(ArrayHCPDataExpenseForPDF.DataexpensePdf, HCPexpenseDataForPDF)
		}
	}

	return ArrayBasicInfoDataForPDF, ArrayHCPDataForPDF, ArrayHCPDataExpenseForPDF, err
}
func BasicinfoBODDataAndAttendeesForPDF(formAnswerID string) ([]entity.BodAndAttendeesDataForPDF, error) {
	functionName := "BodAndAttendeesDataForPDF()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var ArrayBodAndAttendeesDataForPDF []entity.BodAndAttendeesDataForPDF
	querystring := `SELECT 1,
    (json_data->>'Name')::text AS name,
    (json_data->>'Position')::text AS position,
    (json_data->>'Question')::text AS question,
    (case when (json_data->>'Remarks')::text = '' then '-' else 
        (json_data->>'Remarks')::text end ) AS remarks
FROM (
    SELECT jsonb_array_elements(board_of_directors_for_hco_sponsorship) AS json_data
    FROM reporting_db rd where rd.form_answer_id =$1
) subquery_alias

union all

SELECT 2,
employee_data ->> 'lastName' AS last_name,
    employee_data ->> 'title' AS title,
     employee_data ->> 'affiliation' AS organization,
     date_created
FROM 
    (SELECT 
        fa.id,fa.form_answers_id,fa.is_veeva,fa.is_active,jsonb_array_elements(event_attendances_internal_jsonb) AS employee_data,(EXTRACT(EPOCH FROM fa.date_created::timestamp)::integer)::text as date_created
     FROM 
        form_attendances fa 
        left join veeva_event_logs vel on vel.form_answers_id =fa.form_answers_id 
        where fa.event_attendances_internal_jsonb not in  ('null') 
        ) AS employees
        where is_active=true and form_answers_id=$1
	`
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return ArrayBodAndAttendeesDataForPDF, err
	}

	for rows.Next() {
		var BodAndAttendeesDataForPDF entity.BodAndAttendeesDataForPDF
		rows.Scan(&BodAndAttendeesDataForPDF.Seq1, &BodAndAttendeesDataForPDF.Name, &BodAndAttendeesDataForPDF.Position, &BodAndAttendeesDataForPDF.Question, &BodAndAttendeesDataForPDF.Remarks)
		ArrayBodAndAttendeesDataForPDF = append(ArrayBodAndAttendeesDataForPDF, BodAndAttendeesDataForPDF)
	}

	return ArrayBodAndAttendeesDataForPDF, nil
}
func EventExpenseData(formAnswerID string) ([]entity.EventExpenseDataForPDF, error) {
	functionName := "EventExpenseData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var ArrayEventExpenseDataForPDF []entity.EventExpenseDataForPDF
	querystring := `WITH fa AS 
	(	
	SELECT fa.id ,fa.answers 
	from form_answers fa where
	 fa.id =$1
	), groups AS (
		SELECT  arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa , jsonb_array_elements(answers) with ordinality
					arr(item_object)
					where arr.item_object->>'sequenceNo' in ('4')
	), tab_section AS (
		SELECT   arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
		
	), group_questions AS (
		SELECT  row_number() over()as row_no ,arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT  row_no,arr.item_object->'sequenceNo' as row_num,  arr.item_object->'answers' 
		as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS (
		SELECT  row_no,row_num,arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, (case when arr.item_object->'values' is null then '[{"value": ""}]' else arr.item_object->'values' end) as qn_values  FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
	)
		,data as(
		SELECT row_no, (case when arr.item_object->>'value'='true' then 'Yes' when arr.item_object->>'value'='false' then 'No' else arr.item_object->>'value' end)   as value, case when arr.item_object->>'description' is  null then qn_title else arr.item_object->>'description' end  as description FROM questions, jsonb_array_elements(qn_values) with ordinality
		arr(item_object)where qn_title <>'Event Expense Attachment Available') 
		,data1 as(
		SELECT row_no, (CASE
					WHEN COUNT(description) > 1 THEN jsonb_agg(value)
					else jsonb_agg(value)
				END ) as value  , description from data
				GROUP by row_no,description)
		SELECT
		   jsonb_object_agg(description, value) AS json_data
		FROM
			data1
		GROUP BY
			row_no
`
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return ArrayEventExpenseDataForPDF, err
	}

	for rows.Next() {
		var EventExpenseDataForPDF entity.EventExpenseDataForPDF
		rows.Scan(&EventExpenseDataForPDF.Val)
		ArrayEventExpenseDataForPDF = append(ArrayEventExpenseDataForPDF, EventExpenseDataForPDF)
	}

	return ArrayEventExpenseDataForPDF, nil
}
func PostActivityAttachments(formAnswerID string) ([]entity.PostActivityAttachmentsData, error) {
	functionName := "PostActivityAttachments()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var PostActivityAttachmentsDataarray []entity.PostActivityAttachmentsData
	querystring := `with post_activity_data as(select type_desc ,category_desc ,url  from form_answer_attachments faa 
		where faa.form_answer_id =$1 and faa.is_active=true and faa.is_deleted=false),
		final_data as(SELECT type_desc,category_desc, (CASE
							WHEN COUNT(category_desc) > 1 THEN jsonb_agg(url)
							else jsonb_agg(url)
						END ) as value   from post_activity_data
						GROUP by type_desc,category_desc )
						select type_desc,
				   jsonb_agg(category_desc) as category_desc,jsonb_agg(value) as value 
				FROM
					final_data
				GROUP BY
					type_desc
`
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return PostActivityAttachmentsDataarray, err
	}

	for rows.Next() {
		var PostActivityAttachmentsData entity.PostActivityAttachmentsData
		rows.Scan(&PostActivityAttachmentsData.TypeDesc, &PostActivityAttachmentsData.CategoryDesc, &PostActivityAttachmentsData.Value)
		PostActivityAttachmentsDataarray = append(PostActivityAttachmentsDataarray, PostActivityAttachmentsData)
	}

	return PostActivityAttachmentsDataarray, nil
}
func ApproverList(formAnswerID string) ([]entity.ApproverPdfDataList, error) {
	functionName := "ApproverList()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var ApproverPdfDataList []entity.ApproverPdfDataList
	querystring := `select  "status",  "actionedByName" ,  "ApprovalRole",
	case when "LatestCreate"::text is null then '-' else "LatestCreate"::text end, case when "LastComment" is null then '-' else "LastComment" end from  (
   select t1.* from (select distinct on (ap.group_id,ap.sequence_no) c1.title as "status", c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
   ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
   inner join "user" u on ap.approver_id = u.id 
   inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
   left join user_roles ur on ap.user_role_id = ur.id 
   LEFT JOIN lateral (
   select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
   al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
   where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	order by ap.group_id,ap.sequence_no, ap.date_created desc) as t1
inner join (
select approver_id ,max("CreatedDate") as MaxDateTime from (
select distinct on (ap.group_id,ap.sequence_no) c1.title as "status", c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
   ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
   inner join "user" u on ap.approver_id = u.id 
   inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
   left join user_roles ur on ap.user_role_id = ur.id 
   LEFT JOIN lateral (
   select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
   al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
   where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	order by ap.group_id,ap.sequence_no, ap.date_created desc) 
	as approverTemp group by approver_id ) groupedt1
   on t1.approver_id = groupedt1.approver_id
   AND t1."CreatedDate" = groupedt1.MaxDateTime ) as finalApprover
`
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return ApproverPdfDataList, err
	}

	for rows.Next() {
		var ApproverPdfData entity.ApproverPdfDataList
		rows.Scan(&ApproverPdfData.Status, &ApproverPdfData.ActionedByName, &ApproverPdfData.ApprovalRole, &ApproverPdfData.LatestCreate, &ApproverPdfData.LastComment)
		ApproverPdfDataList = append(ApproverPdfDataList, ApproverPdfData)
	}

	return ApproverPdfDataList, nil
}

func readProductList(m []map[string]string) ([]string, []string) {
	if len(m) == 0 {
		return []string{}, []string{}
	}

	var clientList, productList []string
	distinctMap := make(map[string]bool)
	for i := range m {
		for cl, p := range m[i] {
			if _, ok := distinctMap[cl]; !ok && cl != "" {
				distinctMap[cl] = true
				clientList = append(clientList, cl)
			}

			if p != "" {
				productList = append(productList, p)
			}
		}
	}

	return clientList, productList
}

func processClientAndProductData(d []entity.BasicInfoDataForPDF) []entity.BasicInfoDataForPDF {
	var clientList, productList []string
	for i := range d {
		if d[i].Title.String == "Products" && d[i].Value.String != "" {
			m := make([]map[string]string, 0) // eg: [{"the-client-name":"the-product-name"}, {"the-client-name":"the-product-name-2"},... ]
			if err := json.Unmarshal([]byte(d[i].Value.String), &m); err != nil {
				log.Println("could not marshal", err)
				panic(err)
			}

			clientList, productList = readProductList(m)
			break
		}

		if d[i].Title.String == "Product" && len(productList) == 0 {
			l := strings.Split(d[i].Value.String, `,`)
			for i := range l {
				if l[i] != "" && l[i] != "undefined" {
					productList = append(productList, strings.TrimSpace(l[i]))
				}

			}
			continue
		}

		if d[i].Title.String == "Client" && len(clientList) == 0 {
			l := strings.Split(d[i].Value.String, `,`)

			for i := range l {
				if l[i] != "" && l[i] != "undefined" {
					clientList = append(clientList, strings.TrimSpace(l[i]))
				}

			}
			continue
		}
	}

	for i := 0; i < len(d); i++ {
		if d[i].Title.String == "Client" {
			d = append(d[:i], d[i+1:]...)
			i--
		} else if d[i].Title.String == "Products" {
			d = append(d[:i], d[i+1:]...)
			i--
		} else if d[i].Title.String == "Product" {
			d = append(d[:i], d[i+1:]...)
			i--
		}
	}

	//fmt.Println("==================", clientList, "=========go", productList)

	if len(clientList) >= 0 {
		d = append(d, entity.BasicInfoDataForPDF{
			Title: sql.NullString{
				String: "Client",
				Valid:  true,
			},
			Value: sql.NullString{
				String: strings.Join(clientList, `, `),
				Valid:  true,
			},
		})
	}

	if len(productList) >= 1 {
		d = append(d, entity.BasicInfoDataForPDF{
			Title: sql.NullString{
				String: "Primary Product",
				Valid:  true,
			},
			Value: sql.NullString{
				String: productList[0],
				Valid:  true,
			},
		})
	}

	if len(productList) >= 2 {
		d = append(d, entity.BasicInfoDataForPDF{
			Title: sql.NullString{
				String: "Secondary Product",
				Valid:  true,
			},
			Value: sql.NullString{
				String: productList[1],
				Valid:  true,
			},
		})
	}

	if len(productList) >= 3 {
		d = append(d, entity.BasicInfoDataForPDF{
			Title: sql.NullString{
				String: "Tertiary Product",
				Valid:  true,
			},
			Value: sql.NullString{
				String: productList[2],
				Valid:  true,
			},
		})
	}

	return d
}

func NewPDFGeneratorInView(ctx context.Context,
	ledBy string, formID string, CountryCurrency string, UsdCurrency string, convertRate float64, StartDate string, EndDate string,
	hcpTotalCost string, eventExpenseTotalCost string, totalcost string, meetingId string, hcptotalCostusd string, eventExpenseusd string, Submission string) (string, error) {
	pdfg, err := wkhtml.NewPDFGenerator()
	if err != nil {
		return "", err
	}

	basicData, hcpData, hcpExpenses, _ := AlldataFetchQuery(formID)
	basicData = processClientAndProductData(basicData)

	eventExpenses, _ := EventExpenseData(formID)
	PostActivityAttachmentsAlldata, _ := PostActivityAttachments(formID)
	ApproverList, _ := ApproverList(formID)
	BasicInfoBODDataAndAttendeesForPDF, _ := BasicinfoBODDataAndAttendeesForPDF(formID)
	countryCurrency := CountryCurrency
	nextPage1 := `<!DOCTYPE html>
	<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<meta name ="x-apple-disable-message-reformatting" />
		<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
	
		<title>Ez Flow</title>
		<style>
		table {
			border-collapse: collapse;
			border-top: none;
		}
			/* Define the styles for the fullBorder class */
			.innerdataTable {
				border: 1px solid #000;
				border-top: none;
				height: 100%;
			}
			.image {
				width: 100%;
				height: 100%;
			background-image: url(data:image/jpeg;base64,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);
	}
	.new-page {
		page-break-before: always;
	}
	.expense {
		border-bottom: 1px solid black;
		padding-bottom: 5px; /* Optional: Add some space between text and line */
	}
	
		</style>
	</head>
	<body style="font-family: 'Noto Sans SC'; margin: 0;padding: 0; overflow-x: hidden; background-color: #F7F7F7; width: 99.6%;">
		<div class="innerdataTable image" style="background-repeat: no-repeat; background-position: top center;">
			<table class="custTable" width="100%" cellpadding="0" cellspacing="0" border="0" style="padding: 1rem 1rem;">
				<tr>
					<td colspan="4">
						<div style="width: 100%;
						background-image: url(data:image/png;base64,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); padding: 10px 0; width: 100%; float: left;">

					`
	var nextPage string
	nextPage = nextPage1
	nextPage += `<h1 style="text-align: left;color: white; float: left; margin: 0; padding: 0 0 0 1rem;">Basic Info</h1>
	<div class="priceDiv" style="display: inline-block; float: right; padding-right: 1.7rem; opacity: 0;">
		<h2 style="color: white; font-weight: 500; margin: 0;">thb 0.00</h2>
		<h2 style="color: white;font-weight: 500; margin: 0;">US$ 0.00</h2>
	</div>                            
</div>
<h2 style="text-align: left; padding: 1.5rem 1.5rem; margin: 0;">Meeting ID :  ` + meetingId + `</h2>                        
</td>
</tr>	`
	basicDataind := 0
	k := 0
	if len(ledBy) == 0 {
		ledBy = `N/A`
	}

	basicData = append([]entity.BasicInfoDataForPDF{entity.BasicInfoDataForPDF{
		Title: sql.NullString{
			String: `Led By`,
		},
		Value: sql.NullString{
			String: ledBy,
		},
	}}, basicData...)

	for basicDataind < len(basicData) {

		var val1 entity.BasicInfoDataForPDF
		var val2 entity.BasicInfoDataForPDF
		if basicDataind+1 >= len(basicData) {
			val1 = basicData[basicDataind]
			val1.Title.String = val1.Title.String + " :"
			k = 1
		} else {
			val1 = basicData[basicDataind]
			val2 = basicData[basicDataind+1]
			val1.Title.String = val1.Title.String + " :"
			val2.Title.String = val2.Title.String + " :"
			k = 2
		}
		if val1.Title.String == "Type of IHCP activity :" {
			val1.Title.String = "Activity Type :"
			codes := codeController.GetIdKeyCodes()["activitytype"]
			value, _ := strconv.Atoi(val1.Value.String)
			val1.Value.String = codes[value].Title.String
		}
		if val1.Title.String == "Type of Event :" {
			val1.Title.String = "Event Type :"
			if (val1.Value.String) != "" {
				value, _ := strconv.Atoi(val1.Value.String)
				codes1 := codeController.GetIdKeyCodes()["activityeventtype"]
				val1.Value.String = codes1[value].Title.String
			}
		}
		if val2.Title.String == "Type of IHCP activity :" {
			val2.Title.String = "Activity Type :"
			codes := codeController.GetIdKeyCodes()["activitytype"]
			value, _ := strconv.Atoi(val2.Value.String)
			val2.Value.String = codes[value].Title.String
		}
		if val2.Title.String == "Type of Event :" {
			val2.Title.String = "Event Type :"
			if (val2.Value.String) != "" {
				value, _ := strconv.Atoi(val2.Value.String)
				codes := codeController.GetIdKeyCodes()["activityeventtype"]
				val2.Value.String = codes[value].Title.String
			}
		}
		if val1.Title.String == "Activity Start Date :" {
			val1.Value.String = StartDate
		}
		if val2.Title.String == "Activity Start Date :" {
			val2.Value.String = StartDate
		}
		if val1.Title.String == "Activity End Date :" {
			val1.Value.String = EndDate
		}
		if val2.Title.String == "Activity End Date :" {
			val2.Value.String = EndDate
		}
		if val1.Title.String == "Submission Date :" {
			val1.Value.String = Submission
		}
		if val2.Title.String == "Submission Date :" {
			val2.Value.String = Submission
		}
		_, err := url.ParseRequestURI(val1.Value.String)
		if err == nil {
			val1.Value.String = path.Base(val1.Value.String)
		}
		_, err1 := url.ParseRequestURI(val2.Value.String)
		if err1 == nil {
			val2.Value.String = path.Base(val2.Value.String)
		}

		nextPage += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Title.String + ` </h3> 
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Value.String + `</h3> 
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val2.Title.String + `</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val2.Value.String + `</h3>
					</td>
				</tr>
			</table>
		</td>
	</tr> `
		basicDataind += k
	}
	nextPageForBod := `
	<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Board of Director :" + ` </h3> 
						</td>
					</tr>
				</table>
			</td>
		</tr> 
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr style= "background-color: lightgray;">
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0;padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Name" + ` </h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Position" + `</h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Question" + `</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Remarks" + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
	nextPageForAttendees := `
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr style= "background-color: lightgray;">
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0;padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Name" + ` </h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Specialty" + `</h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Organization" + `</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0;font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Created Date" + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
	var flagBodCheck bool
	var flagAttendeesCheck bool
	for _, val := range BasicInfoBODDataAndAttendeesForPDF {
		if val.Seq1.Int16 == 1 {
			flagBodCheck = true
			nextPageForBod += `
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Name.String + ` </h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Position.String + `</h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Question.String + `</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Remarks.String + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
		}
		if val.Seq1.Int16 == 2 {
			flagAttendeesCheck = true
			if val.Remarks.String != "" {
				EventDate, _ := strconv.Atoi(val.Remarks.String)
				t := time.Unix(int64(EventDate), 0)
				val.Remarks.String = t.Format("02 Jan, 2006 15:04")
			}
			nextPageForAttendees += `
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Name.String + ` </h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Position.String + `</h3> 
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Question.String + `</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Remarks.String + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
		}
	}
	if flagBodCheck == true {
		nextPage += nextPageForBod
	}
	nextPage += `</table>
	</div> 
</body>
<div class="new-page"></div>
</html>
`
	nextPage2 := nextPage1
	nextPage2 += `<h1 style="text-align: left;color: white; float: left; margin: 0; padding: 0 0 0 1rem;">Hcp Engagement Info</h1>
	<div class="priceDiv" style="display: inline-block; float: right; padding-right: 1.7rem; opacity: 1;">
		<h2 style="color: white; font-weight: 500; margin: 0;"> ` + hcpTotalCost + `</h2>
		<h2 style="color: white;font-weight: 500; margin: 0;">` + hcptotalCostusd + `</h2>
	</div>                            
</div>
<h2 style="text-align: left; padding: 1.5rem 1.5rem; margin: 0;opacity: 0;">Meeting ID :</h2>                        
</td>
</tr>	`
	var seq1 string
	var seq2 = "1"
	var seq3 = "1"
	var seq4 string
	j := 0
	for i := 0; i < len(hcpData); i += 2 {
		if i+1 >= len(hcpData) {
			break
		}
		val1 := hcpData[i]
		val2 := hcpData[i+1]

		seq1 = hcpData[i].Seq1.String
		if seq2 != seq1 {

			nextPage2 += `<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr class="expense">
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;"> Expenses </h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
			for j < len(hcpExpenses.DataexpensePdf) {

				seq4 = hcpExpenses.DataexpensePdf[j].Seq1.String
				if seq2 != seq4 {
					seq3 = seq4
					break
				}
				if seq3 != seq4 {
					seq3 = seq4
					break
				}
				val1 := hcpExpenses.DataexpensePdf[j]
				_, err := url.ParseRequestURI(val1.Value.String)
				if err == nil {
					val1.Value.String = path.Base(val1.Value.String)
				}

				if val1.Title.String == "RegistrationFee" || val1.Title.String == "Ground Transportation" || val1.Title.String == "Airfare" {
					totalCost, err := strconv.ParseFloat(val1.Value.String, 64)
					if err == nil {
						totalCost = totalCost * convertRate
						val1.Value.String = countryCurrency + " " + val1.Value.String + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
					} else {
						val1.Value.String = countryCurrency + " " + val1.Value.String + " / " + UsdCurrency + "0.00"
					}
				}

				if val1.Title.String == "No of Meal" {
					val2 := hcpExpenses.DataexpensePdf[j+1]
					val3 := hcpExpenses.DataexpensePdf[j+2]
					totalCost, err := strconv.ParseFloat(val3.Value.String, 64)
					if err == nil {
						totalCost = totalCost * convertRate
						val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
					} else {
						val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + "0.00"
					}
					val1.Title.String = val1.Title.String + " " + val1.Value.String + " X " + val2.Title.String + " " + val2.Value.String
					val1.Value.String = val3.Title.String + " " + val3.Value.String
					j += 2
				}

				if val1.Title.String == "No of Days" {
					val2 := hcpExpenses.DataexpensePdf[j+1]
					val3 := hcpExpenses.DataexpensePdf[j+2]
					totalCost, err := strconv.ParseFloat(val3.Value.String, 64)
					if err == nil {
						totalCost = totalCost * convertRate
						val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
					} else {
						val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + "0.00"
					}

					val1.Value.String = val1.Title.String + " " + val1.Value.String + " X " + val2.Title.String + " " + val2.Value.String + " : " + val3.Title.String + " " + val3.Value.String
					val1.Title.String = "Accommodation "
					j += 2
				}

				nextPage2 += `
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Title.String + ` :</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Value.String + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
				j += 1
			}
			seq2 = seq1
		}
		_, err := url.ParseRequestURI(val1.Value.String)
		if err == nil {
			val1.Value.String = path.Base(val1.Value.String)
		}
		_, err1 := url.ParseRequestURI(val2.Value.String)
		if err1 == nil {
			val2.Value.String = path.Base(val2.Value.String)
		}
		if val1.Seq1.String != val2.Seq1.String {
			nextPage2 += `
			<tr> 
			<td style="padding: 0.5rem 2rem;"">
				<table width="100%" cellpadding="0" cellspacing="0" border="0">
					<tr>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Title.String + ` :</h3>
						</td>
						<td style="width: 45px; padding: 0.5rem;">
							<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Value.String + `</h3>
						</td>
					</tr>
				</table>
			</td>
		</tr> `
			i = i - 1
		} else {
			nextPage2 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Title.String + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Value.String + `</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val2.Title.String + `:</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val2.Value.String + `</h3>
					</td>
				</tr>
			</table>
		</td>
	</tr> `
		}
	}
	flag := j
	for j < len(hcpExpenses.DataexpensePdf) {

		seq4 = hcpExpenses.DataexpensePdf[j].Seq1.String
		if seq2 != seq4 {
			seq3 = seq4
			break
		}
		if flag == j {
			nextPage2 += `<
				<tr> 
				<td style="padding: 0.5rem 2rem;"">
					<table width="100%" cellpadding="0" cellspacing="0" border="0">
						<tr>
							<td style="width: 45px; padding: 0.5rem;">
								<h3 class="expense" style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;"> Expenses </h3>
							</td>
						</tr>
					</table>
				</td>
			</tr> `
		}
		val1 := hcpExpenses.DataexpensePdf[j]
		_, err := url.ParseRequestURI(val1.Value.String)
		if err == nil {
			val1.Value.String = path.Base(val1.Value.String)
		}

		if val1.Title.String == "No of Meal" {
			val2 := hcpExpenses.DataexpensePdf[j+1]
			val3 := hcpExpenses.DataexpensePdf[j+2]
			totalCost, err := strconv.ParseFloat(val3.Value.String, 64)
			if err == nil {
				totalCost = totalCost * convertRate
				val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
			} else {
				val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + "0.00"
			}
			val1.Title.String = val1.Title.String + " " + val1.Value.String + " X " + val2.Title.String + " " + val2.Value.String
			val1.Value.String = val3.Title.String + " " + val3.Value.String
			j += 2
		}

		if val1.Title.String == "No of Days" {
			val2 := hcpExpenses.DataexpensePdf[j+1]
			val3 := hcpExpenses.DataexpensePdf[j+2]
			totalCost, err := strconv.ParseFloat(val3.Value.String, 64)
			if err == nil {
				totalCost = totalCost * convertRate
				val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
			} else {
				val3.Value.String = countryCurrency + " " + val3.Value.String + " / " + UsdCurrency + "0.00"
			}

			val1.Value.String = val1.Title.String + " " + val1.Value.String + " X " + val2.Title.String + " " + val2.Value.String + " : " + val3.Title.String + " " + val3.Value.String
			val1.Title.String = "Accomodation "
			j += 2
		}
		nextPage2 += `
	<tr> 
	<td style="padding: 0.5rem 2rem;"">
		<table width="100%" cellpadding="0" cellspacing="0" border="0">
			<tr>
				<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Title.String + ` :</h3>
				</td>
				<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val1.Value.String + `</h3>
				</td>
			</tr>
		</table>
	</td>
</tr> `
		j += 1
	}
	nextPage2 += `</table>
		</div>
	</body>
	<div class="new-page"></div>
	</html>`

	nextPage4 := nextPage1
	nextPage4 += `<h1 style="text-align: left;color: white; float: left; margin: 0; padding: 0 0 0 1rem;">Event Expense Info</h1>
	<div class="priceDiv" style="display: inline-block; float: right; padding-right: 1.7rem; opacity: 1;">
		<h2 style="color: white; font-weight: 500; margin: 0;"> ` + eventExpenseTotalCost + ` </h2>
		<h2 style="color: white;font-weight: 500; margin: 0;"> ` + eventExpenseusd + ` </h2>
	</div>                            
</div>
<h2 style="text-align: left; padding: 1.5rem 1.5rem; margin: 0;opacity: 0;">Meeting ID : TH-SLS-20240213-3526</h2>                        
</td>
</tr>	`

	for _, val := range eventExpenses {
		var allValue entity.EventExpenseData
		json.Unmarshal([]byte(val.Val), &allValue)
		codes1 := codeController.GetValueKeyCodes()["eventexpensetype"]
		TypeOfEventExpenses := codes1[allValue.TypeOfEventExpenses[0]].Title.String
		if TypeOfEventExpenses == "Meals" {
			for rowNo, NoOfPeople := range allValue.NoOfPeople {
				totalCost, err := strconv.ParseFloat(allValue.TotalCost[rowNo], 64)
				if err == nil {
					totalCost = totalCost * convertRate
					allValue.TotalCost[rowNo] = countryCurrency + " " + allValue.TotalCost[rowNo] + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					allValue.TotalCost[rowNo] = countryCurrency + " " + allValue.TotalCost[rowNo] + " / " + UsdCurrency + "0.00"
				}
				valTotalCost := "Number of People:" + NoOfPeople + " X " + "Fee per head :" + allValue.Cost[rowNo] + " : " + "Total Cost :" + allValue.TotalCost[rowNo]
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + valTotalCost + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		} else if TypeOfEventExpenses == "Accomodation" {
			for index, NoOfPeople := range allValue.NoOfPeople {
				totalCost, err := strconv.ParseFloat(allValue.TotalCost[index], 64)
				if err == nil {
					totalCost = totalCost * convertRate
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + "0.00"
				}
				valTotalCost := "Number of Days:" + NoOfPeople + " X " + "Fee per head :" + allValue.Cost[index] + " : " + "Total Cost :" + allValue.TotalCost[index]
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + valTotalCost + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		} else if TypeOfEventExpenses == "Logisticsmaterials" {
			for index, TypeofTransport := range allValue.TypeofTransport {
				totalCost, err := strconv.ParseFloat(allValue.TotalCost[index], 64)
				if err == nil {
					totalCost = totalCost * convertRate
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + "0.00"
				}
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Type of Transport" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeofTransport + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Total Cost" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + allValue.TotalCost[index] + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		} else if TypeOfEventExpenses == "Meetingpackage" {

			for index, TypeOfMeetingPackage := range allValue.TypeOfMeetingPackage {
				totalCost, err := strconv.ParseFloat(allValue.TotalCost[index], 64)
				if err == nil {
					totalCost = totalCost * convertRate
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + "0.00"
				}
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Type of Meeting Package" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfMeetingPackage + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Total Cost" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + allValue.TotalCost[index] + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		} else if TypeOfEventExpenses == "Others" {
			for index, TypeofOthers := range allValue.TypeofOthers {
				totalCost, err := strconv.ParseFloat(allValue.TotalCost[index], 64)
				if err == nil {
					totalCost = totalCost * convertRate
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					allValue.TotalCost[index] = countryCurrency + " " + allValue.TotalCost[index] + " / " + UsdCurrency + "0.00"
				}
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Type of Others" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeofOthers + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Total Cost" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + allValue.TotalCost[index] + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		} else {
			for _, TotalCost := range allValue.TotalCost {
				totalCost, err := strconv.ParseFloat(TotalCost, 64)
				if err == nil {
					totalCost = totalCost * convertRate
					TotalCost = countryCurrency + " " + TotalCost + " / " + UsdCurrency + fmt.Sprintf("%0.3f", totalCost)
				} else {
					TotalCost = countryCurrency + " " + TotalCost + " / " + UsdCurrency + "0.00"
				}
				valTotalCost := "Total Cost :" + TotalCost
				nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + TypeOfEventExpenses + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + valTotalCost + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
			}
		}
		for _, val := range allValue.EventExpenseAttachment {
			var EventExpenseAttachment string
			_, err := url.ParseRequestURI(val)
			if err == nil {
				EventExpenseAttachment = path.Base(val)
			}
			nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Attachments" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + EventExpenseAttachment + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
		}
		for _, val := range allValue.Remarks {
			nextPage4 += `
		<tr> 
		<td style="padding: 0.5rem 2rem;"">
			<table width="100%" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "Remarks" + ` :</h3>
					</td>
					<td style="width: 45px; padding: 0.5rem;">
						<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val + `</h3>
				</tr>
			</table>
		</td>
	</tr> `
		}

	}
	nextPage4 += `</table>
	</div>
</body>
<div class="new-page"></div>
</html>`

	nextPage3 := nextPage1
	nextPage3 += `<h1 style="text-align: left;color: white; float: left; margin: 0; padding: 0 0 0 1rem;">Post Activity Attachments</h1>
	<div class="priceDiv" style="display: inline-block; float: right; padding-right: 1.7rem; opacity: 0;">
		<h2 style="color: white; font-weight: 500; margin: 0;opacity: 0"> ₱ 2,067,500.00 </h2>
		<h2 style="color: white;font-weight: 500; margin: 0;opacity: 0"> US$ 36,860.40 </h2>
	</div>                            
</div>                      
</td>
</tr>	`

	for _, val := range PostActivityAttachmentsAlldata {
		nextPage3 += `
	<tr> 
	<td style="padding: 0.5rem 2rem;"">
		<table width="100%" cellpadding="0" cellspacing="0" border="0">
			<tr>
				<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.TypeDesc + ` :</h3>
				</td>
			</tr>
		</table>
	</td>
</tr> `
		for i, value := range val.CategoryDesc {
			nextPage3 += `
	<tr> 
	<td style="padding: 0.5rem 2rem;"">
		<table width="100%" cellpadding="0" cellspacing="0" border="0">
			<tr>
				<td style="width: 45px; padding: 0.5rem;">
					<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "&nbsp;&nbsp;" + value + ` </h3>
				</td>
			</tr>
		</table>
	</td>
</tr> `
			for _, value := range val.Value[i] {
				_, err := url.ParseRequestURI(value)
				if err == nil {
					value = path.Base(value)
				}
				nextPage3 += `
<tr> 
<td style="padding: 0.5rem 2rem;"">
<table width="100%" cellpadding="0" cellspacing="0" border="0">
	<tr>
		<td style="width: 45px; padding: 0.5rem;">
			<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "&nbsp;&nbsp;&nbsp;&nbsp;" + value + ` </h3>
		</td>
	</tr>
</table>
</td>
</tr> `
			}
		}
	}
	if flagAttendeesCheck {
		nextPage3 += nextPageForAttendees
	}

	if len(PostActivityAttachmentsAlldata) == 0 {
		nextPage3 += `</table> <div style="text-align: center;">
        <p>No attachments to display!</p>
    </div>`
	} else {
		nextPage3 += `</table>`
	}

	nextPage3 += `
</div>
</body>
<div class="new-page"></div>
</html>`

	nextPage5 := nextPage1
	nextPage5 += `<h1 style="text-align: left;color: white; float: left; margin: 0; padding: 0 0 0 1rem;">Approved By</h1>
	<div class="priceDiv" style="display: inline-block; float: right; padding-right: 1.7rem; opacity: 0;">
		<h2 style="color: white; font-weight: 500; margin: 0;opacity: 0"> ₱ 2,067,500.00 </h2>
		<h2 style="color: white;font-weight: 500; margin: 0;opacity: 0"> US$ 36,860.40 </h2>
	</div>                            
</div>                     
</td>
</tr>	`

	for _, val := range ApproverList {
		nextPage5 += `
<tr> 
<td style="padding: 0.5rem 2rem;"">
	<table width="100%" cellpadding="0" cellspacing="0" border="0">
		<tr>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.ApprovalRole.String + ` </h3>
			</td>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "&nbsp;" + val.ActionedByName.String + ` </h3>
			</td>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "&nbsp;-" + ` </h3>
			</td>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + "&nbsp;" + val.LastComment.String + ` </h3>
			</td>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.Status.String + ` </h3>
			</td>
			<td style="width: 45px; padding: 0.5rem;">
				<h3 style="margin: 0; padding: 0; font-weight: 400; line-height: 1.4;word-break: break-all;">` + val.LatestCreate.String + ` </h3>
			</td>
		</tr>
	</table>
</td>
</tr> `
	}

	nextPage5 += `</table>
</div>
</body>
<div style="text-align: right;font-size: 24px;">
<br>
        <div>` + totalcost + `</div>
    </div>
<div class="new-page"></div>
</html>`

	pdfg.AddPage(wkhtml.NewPageReader(strings.NewReader((nextPage + nextPage2 + nextPage4 + nextPage3 + nextPage5))))
	err = pdfg.Create()
	if err != nil {
		return "", err
	}

	// --------- For Testing ----------- write html file to local
	//name := fmt.Sprintf(`dowload_%s.pdf`, meetingId)
	//err = os.WriteFile(name, pdfg.Buffer().Bytes(), 0644)
	//if err != nil {
	//	panic(err)
	//}
	//blobURL := ""
	// For Testing

	blobURL, err := azure.UploadPDFToBlob(pdfg.Buffer().Bytes(), "download-"+meetingId+".pdf")
	return blobURL, err
}
func CalculateTotalHCPExpenseFromEventID(formAnswerId string) ([]string, int, error) {
	functionName := "CalculateTotalExpenseFromEventID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var CalculateTotalExpense []string
	var country int
	querystring := `with approval as (select fa.id,fa.country,fa.answers  from form_answers fa where fa.id =$1 )
	, groups AS(
					SELECT id,country, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM approval , jsonb_array_elements(answers) with ordinality
					arr(item_object)
					where arr.item_object->>'title' in ('HCP Engagement Info')
			), tab_section AS (
					SELECT id,country,  arr.item_object->'childForm'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
					arr(item_object)
			), tab_section_form AS (
					SELECT id,country,  arr.item_object->'form'->'groupAnswer' as group_section_form FROM groups, jsonb_array_elements(section) with ordinality
					arr(item_object)
			), group_questions AS (
					SELECT id,country,arr.item_object->>'sequenceNo' as row_no  , arr.item_object->'questionAnswers' as group_questions  FROM tab_section, jsonb_array_elements(group_section) with ordinality
					arr(item_object)
 
			), group_questions_form AS (
					SELECT id,country,arr.item_object->>'sequenceNo' as row_no  , arr.item_object->'questionAnswers' as group_questions_form  FROM tab_section_form, jsonb_array_elements(group_section_form) with ordinality
					arr(item_object)
 
			), question_grp AS (
					SELECT id,country, arr.item_object->'id',row_no,arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
					arr(item_object)
			), question_grp_form AS (
					SELECT id,country, arr.item_object->'id',row_no,arr.item_object->'answers' as questions_form FROM group_questions_form, jsonb_array_elements(group_questions_form) with ordinality
					arr(item_object)
			), questions AS (
					SELECT id,country,row_no,row_number() over() as row_num, arr.item_object->>'id' as qn_id, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
					arr(item_object)
					where questions not  IN ('null','[]')
			), questions_form AS (
					SELECT id,country,row_no,row_number() over() as row_num, arr.item_object->>'id' as qn_id, arr.item_object->'values' as qn_values_form FROM question_grp_form, jsonb_array_elements(questions_form) with ordinality
					arr(item_object)
					where questions_form not  IN ('null','[]') and arr.item_object->>'id'='proposed-honorarium'
			),only_cost as(
			 select id,country,row_num, row_no,arr.item_object->>'id' as qn_id,
			 (case when (arr.item_object->>'value' ~ '^[0-9\.]+$') then (arr.item_object->>'value')
			 else (arr.item_object->>'value')
			 end) as qn_values
			,arr.item_object->>'description' as des FROM questions, jsonb_array_elements(qn_values) with ordinality
					arr(item_object)	
			),only_cost_form as(
					select id,country,row_num, row_no,arr.item_object->>'id' as qn_id_form,
			 (case when (arr.item_object->>'value' ~ '^[0-9\.]+$') then (arr.item_object->>'value')
			 else (arr.item_object->>'value')
			 end) as qn_values
			,arr.item_object->>'description' as des FROM questions_form, jsonb_array_elements(qn_values_form) with ordinality
					arr(item_object))
			,all_details as(
			SELECT id,country,row_no::int4,(case
				 when ((array_agg(qn_values))[4]~ '^[0-9\.]+$') then (array_agg(qn_values))[4]
			when ((array_agg(qn_values))[3]~ '^[0-9\.]+$') then (array_agg(qn_values))[3]
			 when ((array_agg(qn_values))[2]~ '^[0-9\.]+$') then (array_agg(qn_values))[2]
			when ((array_agg(qn_values))[1]~ '^[0-9\.]+$') then (array_agg(qn_values))[1]
			end) as total_cost,
			(case when((array_agg(des))[1]='Type of Meal') then
			(array_agg(qn_values))[1] when (((array_agg(des))[1]='No of Days' or (array_agg(des))[1]='Cost per Day')) then
			'Accomodation'
			when (((array_agg(des))[1]='Name of Expense')) then
			'Others'
			else (array_agg(des))[1] end) as type_of_expense
			FROM only_cost
				 where des not in ('Do you want to add any attachment?','Remarks')
				 group by row_num,row_no,id,country
				 union all
				 select id,country,row_no::int4,(array_agg(qn_values))[1],(array_agg(des))[1] FROM only_cost_form
				 group by id,row_num,row_no,country
			      union all
                  SELECT id,country,generate_series(0, (SELECT max(row_no::int4)::int4 FROM only_cost), 1) as row_no,'' ,'' from only_cost
                  group by id,country
	              order by row_no asc , type_of_expense desc
	              )		
				 select country,ARRAY_AGG(total_cost) as expense_cost from all_details
				 where (total_cost<>''or type_of_expense='')  and type_of_expense <>'Total Cost'
					group by id,country union all 
					select country, ARRAY[''] as expense_cost from tab_section
				   group by id,country`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&country, &CalculateTotalExpense)
	if err != nil {
		return CalculateTotalExpense, country, err
	}

	return CalculateTotalExpense, country, nil
}
