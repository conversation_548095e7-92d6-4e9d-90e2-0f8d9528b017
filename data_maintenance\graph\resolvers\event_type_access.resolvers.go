package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertEventTypeAccessPermission is the resolver for the upsertEventTypeAccessPermission field.
func (r *mutationResolver) UpsertEventTypeAccessPermission(ctx context.Context, input model.EventTypeAccessPermissionUpsertInput) (*model.UpsertEventTypeAccessPermissionResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertEventTypeAccessPermission", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertEventTypeAccessPermission :" + string(inputJson))
	response := controller.UpsertEventTypeAccessPermission(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("UpsertEventTypeAccessPermission", "data_maintenance/UpsertEventTypeAccessPermission", time.Since(start), "200")
	return response, nil
}

// GetEventTypeAccessPermission is the resolver for the getEventTypeAccessPermission field.
func (r *queryResolver) GetEventTypeAccessPermission(ctx context.Context, input *model.EventTypeAccessPermissionInput) (*model.EventTypeAccessPermissionResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "GetEventTypeAccessPermission", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/GetEventTypeAccessPermission :" + string(inputJson))
	response := controller.FetchEventTypeAccessPermission(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("GetEventTypeAccessPermission", "data_maintenance/GetEventTypeAccessPermission", time.Since(start), "200")
	return response, nil
}
