INSERT INTO "user" (created_by,is_active,is_deleted,employee_code,first_name,country,active_directory,last_name,approval_role,"position",email,user_role,user_role_id) VALUES
	 ('00000000-0000-0000-0000-000000000000'::uuid,true,false,'IP0067','<PERSON><PERSON>',10,'ndthanh','Nguyen',0,NULL,'<EMAIL>',NULL,
	 (select id from user_roles where value = 'admin' and is_active= true and is_deleted= false));
	 
INSERT INTO "user" (created_by,is_active,is_deleted,first_name,country,active_directory,last_name,approval_role,"position",email,user_role,user_role_id) VALUES
	 ('00000000-0000-0000-0000-000000000000'::uuid,true,false,'Mengkoung',185,'mkseng','Seng',0,NULL,'<EMAIL>',NULL,
	 (select id from user_roles where value = 'admin' and is_active= true and is_deleted= false));	
	
INSERT INTO "user" (created_by,is_active,is_deleted,employee_code,first_name,country,active_directory,last_name,approval_role,"position",email,user_role,user_role_id) VALUES
	 ('00000000-0000-0000-0000-000000000000'::uuid,true,false,'IP0062','Thi Tuong Van',10,'vttvan','Vo',0,NULL,'<EMAIL>',NULL,
	(select id from user_roles where value = 'admin' and is_active= true and is_deleted= false));
	 
INSERT INTO "user" (created_by,is_active,is_deleted,employee_code,first_name,country,active_directory,last_name,approval_role,"position",email,user_role,user_role_id) VALUES
	 ('00000000-0000-0000-0000-000000000000'::uuid,true,false,'7648','Clarisse',7,'waylim','Lim',0,NULL,'<EMAIL>',NULL,
	 (select id from user_roles where value = 'admin' and is_active= true and is_deleted= false));	