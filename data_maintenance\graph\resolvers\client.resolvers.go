package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.49

import (
	"context"
	"encoding/json"
	"log"
	"time"

	"github.com/ihcp/data_maintenance/graph/controller"
	"github.com/ihcp/data_maintenance/graph/generated"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/ihcp/data_maintenance/graph/model"
)

// UpsertClient is the resolver for the upsertClient field.
func (r *mutationResolver) UpsertClient(ctx context.Context, input model.ClientInput) (*model.UpsertClientResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "UpsertClient", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/UpsertClient :" + string(inputJson))
	response := controller.UpsertClientData(&ctx, input)
	logengine.GetTelemetryClient().TrackRequest("UpsertClient", "data_maintenance/UpsertClient", time.Since(start), "200")
	return response, nil
}

// ClientListExport is the resolver for the clientListExport field.
func (r *queryResolver) ClientListExport(ctx context.Context, input model.ClientRequest) (*model.ClientResponse, error) {
	start := time.Now().UTC()
	inputJson, err := json.Marshal(input)
	if err != nil {
		log.Printf("%s - Error: %s", "Client", err)
	}
	logengine.GetTelemetryClient().TrackEvent("data_maintenance/Client :" + string(inputJson))
	response := controller.ExportClientExcel(&ctx, &input)
	logengine.GetTelemetryClient().TrackRequest("Client", "data_maintenance/Client", time.Since(start), "200")
	return response, nil
}

// Mutation returns generated.MutationResolver implementation.
func (r *Resolver) Mutation() generated.MutationResolver { return &mutationResolver{r} }

// Query returns generated.QueryResolver implementation.
func (r *Resolver) Query() generated.QueryResolver { return &queryResolver{r} }

type mutationResolver struct{ *Resolver }
type queryResolver struct{ *Resolver }
