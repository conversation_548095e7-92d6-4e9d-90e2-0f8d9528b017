alter table approval_role_management add column "department" uuid   ;

ALTER TABLE approval_role_management ADD CONSTRAINT fk_department FOREIGN KEY ("department") REFERENCES departments(id) ;

update approval_role_management set department = (select department from department_roles dr inner join user_roles ur on dr.userrole = ur.id and ur.is_active=true and ur.is_deleted = false inner join code c1 on c1.value = ur.value where c1.id = 393 and dr.is_active= true and dr.is_deleted= false) where approval_role = 393

	UPDATE approval_role_management SET department = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur
	 ON ur.value = code.value
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.approval_role = subquery.id

    UPDATE approval_role_management SET alternate_department_id = subquery.department
	FROM (SELECT code.id, code.value as c_id, ur.id as r_id, ur.value r_value, dr.department FROM code 
	 LEFT JOIN user_roles ur 
	 ON ur.value = code.value and ur.is_active =true and ur.is_deleted = false
	 LEFT JOIN department_roles dr
	 ON dr.userrole = ur.id and dr.is_active =true and dr.is_deleted = false
	 WHERE code.category = 'UserRole') as subquery
	WHERE approval_role_management.alternate_role = subquery.id