package mapper

import (
	"strings"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func FetchDepartmentEntityToModel(input []entity.FetchDepartment) []*model.Department {
	var outEntity []*model.Department
	for _, departmentData := range input {
		var department model.Department
		department.ID = departmentData.ID
		department.Department = departmentData.Department
		outEntity = append(outEntity, &department)
	}
	return outEntity
}

func MapDepartmentModelToEntity(inputModel model.DepartmentInput, userUUID uuid.UUID) (*entity.Department, *model.UpsertDepartmentResponse) {
	result := &model.UpsertDepartmentResponse{Error: false}
	var entity entity.Department
	var uid uuid.UUID
	var err error

	if inputModel.ID != nil {
		uid, err = uuid.FromString(strings.TrimSpace(*(inputModel).ID))
		if err != nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Unable to convert to uuid"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}
	entity.ID = &uid
	if inputModel.ID == nil {
		if inputModel.Department != nil {
			if postgres.HasDepartment(*inputModel.Department) {
				result.Error = true
				result.ValidationErrors = []*model.ValidationMessage{}
				errorMessage := &model.ValidationMessage{Message: "Department Name already exist"}
				result.ValidationErrors = append(result.ValidationErrors, errorMessage)
			} else {
				entity.Department = *inputModel.Department
			}
		} else {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "Department can not be blank"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	} else {
		if !entity.IsDelete {
			if inputModel.Department != nil {
				entity.Department = *inputModel.Department
			}
		}
	}
	entity.UserId = &userUUID

	if inputModel.IsDeleted != nil {
		entity.IsDelete = *inputModel.IsDeleted
	}
	if entity.IsDelete == true {
		if inputModel.ID == nil {
			result.Error = true
			result.ValidationErrors = []*model.ValidationMessage{}
			errorMessage := &model.ValidationMessage{Message: "ID can not be blank"}
			result.ValidationErrors = append(result.ValidationErrors, errorMessage)
		}
	}

	return &entity, result
}
