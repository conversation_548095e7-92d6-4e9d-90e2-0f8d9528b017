package postgres

import (
	"context"
	"log"

	codeEntity "github.com/ihcp/code/entity"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func UpsertTeamMemberCustomerForExcel(tx pgx.Tx, entity *entity.TeamMemberCustomer, userUUID uuid.UUID) error {
	functionName := "UpsertTeamMemberCustomerForExcel()"
	log.Println(functionName)
	queryString := `INSERT INTO team_member_customer (customer,team_member,is_active,created_by) VALUES ($1,$2,$3,$4)`
	var inputargs []interface{}
	inputargs = append(inputargs, entity.Customer, entity.TeamMember, true, userUUID)
	_, err := tx.Exec(context.Background(), queryString, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("UpsertTeamMemberCustomerForExcel query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	return err
}

func updateTeamMemberCustomer(tx pgx.Tx, entity entity.CustomerExcelInput, cmslClasses map[string]codeEntity.Code, teamMemberID uuid.UUID) error {
	functionName := "updateTeamMemberCustomer()"
	log.Println(functionName)
	return nil
}
func GetTeamMemberByCustomerId(customerId *uuid.UUID) *entity.TeamMemberCustomer {
	functionName := "GetTeamMemberByCustomerId()"
	if pool == nil {
		pool = GetPool()
	}
	var entity entity.TeamMemberCustomer
	query := `SELECT id,team_member from team_member_customer where customer = $1 AND is_deleted=false AND is_active=true`
	err := pool.QueryRow(context.Background(), query, &customerId).Scan(&entity.ID, &entity.TeamMember)
	logengine.GetTelemetryClient().TrackEvent("GetTeamMemberByCustomerId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil
	}

	return &entity
}

func IsTeamMembersCustomerExists(customerID *uuid.UUID) bool {

	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT 1 FROM team_member_customer WHERE customer = $1 AND is_active = true AND is_deleted = false"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, customerID).Scan(&hasValue)
	logengine.GetTelemetryClient().TrackEvent("IsTeamMembersCustomerExists query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return false
	}
	return true
}
