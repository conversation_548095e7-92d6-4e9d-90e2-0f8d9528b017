package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"github.com/99designs/gqlgen/graphql"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/teams_service"
	util "github.com/ihcp/form/graph/utils"
	"github.com/ihcp/teams"
	"github.com/vektah/gqlparser/v2/gqlerror"
	"log"
	"net/http"
	"os"
	"runtime/debug"

	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/99designs/gqlgen/graphql/handler/transport"
	"github.com/99designs/gqlgen/graphql/playground"
	"github.com/go-chi/chi"
	"github.com/gorilla/websocket"
	"github.com/ihcp/form/graph/generated"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/resolvers"
	"github.com/ihcp/login/auth"

	"github.com/joho/godotenv"
	"github.com/rs/cors"
)

const defaultPort = "8080"

var prodMode = flag.Bool("production", false, "Start in production mode env=.prod.env")
var cronMode = flag.Bool("cronjob", false, "Start server and run cronjob")

func main() {

	flag.Parse()
	envPath := ".dev.env"
	if *prodMode {
		envPath = ".prod.env"
	}

	err := godotenv.Load(envPath)
	if err != nil {
		log.Println("error when loading .env file", err, "- Use cloud variables")
	}

	teams_service.InitPackageVariable(initTeamsWebhook())
	postgres.InitDbPool()
	pool := postgres.GetPool()
	codeController.InitCodes(pool)

	port := os.Getenv("PORT")
	if port == "" {
		port = defaultPort
	}

	//util.ManualSyncEventsToVeeva()
	//return

	//ctx := context.Background()
	//This sync territory data then save to the tbl `territories`
	//util.ExecuteCronServiceSyncTerritoryDataFromVeeva(ctx)

	//Sync territory id of active users in ezflow then update to tbl `user`
	//util.ExecuteCronServiceSyncUserTerritoryFromVeeva(ctx)

	//           Sync HCP Province
	//util.ExecuteCronServiceSyncHCPProvinceFromVeeva(ctx)

	//           Sync HCP Territory ID
	//util.ExecuteCronServiceSyncHCPTerritoryFromVeeva(ctx) // deprecated

	//Sync all HCP Data
	//util.ExecuteCronServiceSyncHCPDataFromVeeva(ctx)

	//return

	teams_service.SendMsg(context.Background(), fmt.Sprintf(`Starting Ezflow %v... Form service`, os.Getenv(`ENVIRONMENT`)), "", "")

	if *cronMode {
		log.Println(`Starting cronjob`)
		go util.SyncEventsToVeeva()

		go util.ExecuteCronServiceToSendEmailToApprover()

		go util.ExecuteCronServiceForVeevaGetAllCreatedDataForCustomer()
		go util.ExecuteCronServiceForVeevaGetAllModifiedDataForCustomer()
		go util.ExecuteCronServiceForVeevaGetAllveevaUsersDataForModifiedData()
		go util.ExecuteCronServiceForVeevaGetAllveevaUsersDataForCreatedData()
		go util.ExecuteCronServiceForVeevaGetAllveevaProductDataForCreatedData()
		go util.ExecuteCronServiceForVeevaGetAllveevaProductDataForModifiedData()
		go util.ExecuteCronServiceForVeevaGetAllAttendanceList()
		//controller.CheckStatusFromVeevaAttendanceController2()

		go util.ExecuteCronServiceForEmailAlertForVeevaFailureEvent()
		go util.ExecuteCronServiceForVeevaGetAllAttendanceFailureList()
	}

	router := chi.NewRouter()
	router.Use(cors.New(cors.Options{
		AllowedOrigins:   []string{"*"},
		AllowCredentials: true,
		Debug:            false,
		AllowedHeaders:   []string{"*"},
	}).Handler)

	srv := handler.NewDefaultServer(generated.NewExecutableSchema(generated.Config{Resolvers: &resolvers.Resolver{}}))
	srv.AddTransport(&transport.Websocket{
		Upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				// Check against your desired domains here
				return r.Host == "*"
			},
			ReadBufferSize:  1024,
			WriteBufferSize: 1024,
		},
	})

	srv.SetRecoverFunc(panicRecover(teams_service.Get()))
	srv.SetErrorPresenter(func(ctx context.Context, e error) *gqlerror.Error {
		var err *gqlerror.Error
		if errors.As(e, &err) {
			return err
		}
		return graphql.DefaultErrorPresenter(ctx, e)
	})

	router.Use(auth.AuthMiddleware(pool))
	router.Handle("/", playground.Handler("GraphQL playground", "/query"))
	router.Handle("/query", srv)
	router.Handle("/healthz", http.HandlerFunc(healthcheck))
	http.Handle("/", http.FileServer(http.Dir("/tmp")))
	log.Printf("connect to http://localhost:%s/ for GraphQL playground", port)
	log.Fatal(http.ListenAndServe(":"+port, router))
}

func healthcheck(w http.ResponseWriter, r *http.Request) {
	// Set response headers
	w.Header().Set("Content-Type", "application/json")
	// Check if the service is healthy (you can add your own health check logic here)
	isHealthy := true
	// If the service is healthy, return a 200 OK response
	if isHealthy {
		w.WriteHeader(http.StatusOK)
		fmt.Fprintf(w, `{"status": "ok"}`)
	} else {
		// If the service is not healthy, return a 500 Internal Server Error response
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, `{"status": "error"}`)
	}

}

func panicRecover(teamsWH *teams.WebhookClient) func(ctx context.Context, err interface{}) error {
	return func(ctx context.Context, err interface{}) error {
		//1. print trace log

		stackTrace := string(debug.Stack())
		msg := fmt.Sprintf("+++ Recovering from panic: %s\n-------   Stack Trace:\n%s-------  Recovering ends   -------", err, stackTrace)
		log.Println(msg)
		go func() {
			if err := teamsWH.SendPanicMessage(context.Background(), err, stackTrace, graphql.GetPath(ctx).String()); err != nil {
				log.Println(`--- Error happens when sending teams_service msg`)
			}
		}()
		//2. response with error = internal error
		return &gqlerror.Error{
			Path:    graphql.GetPath(ctx),
			Message: `internal_error`,
		}
	}
}

func initTeamsWebhook() *teams.WebhookClient {
	if os.Getenv("TEAMS_WEBHOOK_URL") == "" {
		return nil
	}

	cfg := &teams.WebhookConfig{
		URL:         os.Getenv("TEAMS_WEBHOOK_URL"),
		ServiceName: "EZFLOW - FORM SERVICE ",
		EnvName:     os.Getenv("environment"),
	}
	return teams.NewWebhookClient(cfg)
}
