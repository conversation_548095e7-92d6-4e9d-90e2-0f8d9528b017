package mapper

import (
	"errors"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	uuid "github.com/satori/go.uuid"
)

func MapdepartmentRolesModelToEntity(inputModel *model.DepartmentRolesInput, userUUID uuid.UUID) (*entity.DepartmentRolesEntity, error) {
	var entity entity.DepartmentRolesEntity

	if inputModel.IsDelete != nil {
		isDelete := *(inputModel).IsDelete
		entity.IsDeleted = isDelete
	} else {
		entity.IsDeleted = false
	}
	if inputModel.ID != nil {
		uuid, err := uuid.FromString(*inputModel.ID)
		if err != nil {
			return nil, errors.New("ID format is invalid!")
		} else {
			entity.ID = &uuid
		}
	}
	entity.CreatedBy = userUUID.String()

	if !entity.IsDeleted {

		if inputModel.Department == nil {
			return nil, errors.New("Department cannot be blank")
		}
		if inputModel.UserRole == nil {
			return nil, errors.New("User Role cannot be blank")
		}
		if inputModel.ID == nil {
			if postgres.HasDepartmentID(*inputModel.Department) {
				return nil, errors.New("Department Roles already existed")
			}
		}

		if inputModel.Department != nil {
			if *inputModel.Department == "" {
				return nil, errors.New("Department cannot be blank")
			} else {
				departmentID, deptErr := postgres.GetDepartmentIDByDepartment(*inputModel.Department)
				if deptErr != nil {
					return nil, errors.New("Invalid department")
				} else {
					entity.DepartmentID = departmentID
				}
			}
		}

		if inputModel.UserRole != nil {
			if inputModel.UserRole == nil {
				return nil, errors.New("User Role cannot be blank")
			} else {
				for _, value := range inputModel.UserRole {
					userRoleID, userErr := postgres.GetUserRoleIDByUserRole(*value)
					if userErr != nil {
						return nil, errors.New("Invalid user role")
					} else {
						entity.RoleID = append(entity.RoleID, userRoleID)
					}
				}
			}
		}

	} else {
		if inputModel.ID == nil {
			return nil, errors.New("ID cannot be blank")
		}
	}

	return &entity, nil
}

func FetchDepartmentRolesEntityToModel(input []entity.DepartmentRolesFetchEntity) []*model.DepartmentRole {
	var outEntity []*model.DepartmentRole
	roleMap := make(map[string]*model.DepartmentRole)
	for _, item := range input {
		if _, ok := roleMap[item.Department.String]; !ok {
			deptRoles := &model.DepartmentRole{
				ID:         item.ID.String,
				Department: item.Department.String,
			}
			roleMap[item.Department.String] = deptRoles
		}
		userRoles := &model.UserRolesValue{
			ID:          item.UserRoleID.String,
			Value:       item.UserRole.String,
			Title:       item.UserRoleTitle.String,
			Description: item.UserRoleDesc.String,
		}
		roleMap[item.Department.String].UserRole = append(roleMap[item.Department.String].UserRole, userRoles)
	}
	for _, v := range roleMap {
		outEntity = append(outEntity, v)
	}
	return outEntity
}
