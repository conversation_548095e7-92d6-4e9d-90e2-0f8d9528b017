package resolvers

import (
	"context"
	"errors"
	"os"
)

// This file will not be regenerated automatically.
//
// It serves as dependency injection for your app, add any dependencies you require here.

type Resolver struct{}

// Authentication Default
func (r *Resolver) Authentication() error {
	return nil
}

// MapToolAuthentication Use for Maptool Service
func (r *Resolver) MapToolAuthentication(ctx context.Context) error {
	var t string
	if t = os.Getenv("MAPTOOL_AUTH_TOKEN"); t == "" {
		return errors.New(`unauthenticated request`)
	}

	clientToken, ok := ctx.Value(`MAPTOOL_AUTH_TOKEN`).(string)
	if !ok {
		return errors.New(`unauthenticated request`)
	}

	if clientToken != t {
		return errors.New(`invalid token`)
	}

	// success
	return nil
}
