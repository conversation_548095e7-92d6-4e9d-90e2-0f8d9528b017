CREATE TABLE form_submission_changes (
	id uuid NOT NULL DEFAULT public.uuid_generate_v4(),
	form_answer_id uuid NOT null,
	question_id text not NULL,
	original text not null,
	new text not null ,
	created_by uuid not NULL,
	date_created timestamptz NOT NULL DEFAULT now()
);
ALTER TABLE form_submission_changes ADD CONSTRAINT fk_form_submission_changes FOREIGN KEY (form_answer_id) REFERENCES form_answers(id);
