package mail_service

import (
	"context"
	"fmt"
	"log"
	"os"
	"strings"

	codeController "github.com/ihcp/code/controller"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

type Client struct {
	apiKey string
	client *sendgrid.Client
}

func New() *Client {
	apiKey := os.Getenv("SENDGRID_API_KEY")
	return &Client{
		apiKey: apiKey,
		client: sendgrid.NewSendClient(apiKey),
	}
}

func (c *Client) SendMail(ctx context.Context,
	from, to string,
	subject string,
	plainTextContent, htmlContent string,
	attachments []*mail.Attachment,

) error {
	sender := mail.NewEmail(from, from)
	receiver := mail.NewEmail(to, to)
	m := mail.NewSingleEmail(sender, subject, receiver, plainTextContent, htmlContent)

	for _, attachment := range attachments {
		m.AddAttachment(attachment)
	}

	_, err := c.client.Send(m)
	return err
}

func (c *Client) SendEmailToMany(ctx context.Context,
	from string, to []string,
	subject string,
	plainTextContent, htmlContent string,
	attachments []*mail.Attachment,

) error {
	emailList := codeController.GetTitleKeyCodes()["emaillist"]["DeveloperEmails"].Value
	emailList = strings.ReplaceAll(emailList, `"`, "")
	emails := strings.Split(emailList, ",")

	sender := mail.NewEmail(from, from)
	m := mail.NewV3Mail()
	content := mail.NewContent("text/html", htmlContent)
	m.SetFrom(sender)
	m.AddContent(content)
	m.Subject = subject

	whitelistMap := make(map[string]struct{})
	for _, email := range emails {
		whitelistMap[email] = struct{}{}
	}

	// Initialize receiver list
	var receiverList []*mail.Email

	// If environment is dev or uat, check the whitelist
	if os.Getenv("ENVIRONMENT") != "production" {
		for _, emailAddr := range to {
			if _, exists := whitelistMap[emailAddr]; exists {
				receiverList = append(receiverList, mail.NewEmail(emailAddr, emailAddr))
			}
		}
	} else {
		// In production, send to all emails
		for _, emailAddr := range to {
			receiverList = append(receiverList, mail.NewEmail(emailAddr, emailAddr))
		}
	}

	// Print the receiver list (for demonstration)
	for _, receiver := range receiverList {
		fmt.Println("Email to send:", receiver.Address)
	}

	personalization := mail.NewPersonalization()
	personalization.AddTos(receiverList...)
	m.AddPersonalizations(personalization)

	for _, attachment := range attachments {
		m.AddAttachment(attachment)
	}

	// Debugger
	//

	response, err := c.client.Send(m)
	if err != nil {
		log.Println("===", response.StatusCode)
		log.Println("===", response.Body)
		log.Println("===", response.Headers)
	}

	log.Println("------------- Sending email out ---------------------")
	log.Println("From: ", from, " To:")
	for _, addr := range receiverList {
		log.Println(addr)
	}
	log.Println("Err:", err)

	log.Println("-----------------------------------------------------")

	return err
}
