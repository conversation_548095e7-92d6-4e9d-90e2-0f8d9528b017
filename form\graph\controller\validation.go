package controller

import (
	"context"
	"errors"
	"fmt"
	"log"
	"math"
	"sync"

	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/mapper"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/login/auth"
)

func ValidateHonorarium(ctx context.Context, input model.HonorariumDetails) *model.MaxHonorariumResponse {
	var response model.MaxHonorariumResponse
	userId := auth.GetUserID(ctx)
	if userId == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	userCountryID, err := postgres.GetCountryByUserID(userId)
	if err != nil {
		log.Printf("%s - Error: %s", err.Error())
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	var customerCountry int
	if *input.HcpType == "local" {
		var err error
		if input.CustomerID == nil || *input.CustomerID == "" {
			response.Error = true
			response.Message = "Please Provide Customer id!"
			return &response
		}
		customerCountry, err = postgres.GetCountryByCustomerID(input.CustomerID)
		if err != nil {
			response.Error = true
			response.Message = "Unable to fetch the customer country!"
			return &response
		}
	}
	code := codeController.GetValueKeyCodes()["country"]
	phMDI := code["ph"].ID
	phZPC := code["phzpc"].ID
	if (customerCountry == phMDI || customerCountry == phZPC) && *input.HcpType == "local" {
		entity, err := mapper.HonorariumValidation(&input)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		var baseTypeValue int
		if entity.Role == "speaker" {
			baseTypeValue, err = postgres.GetInbaseOrOutbaseValue(entity)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
		} else {
			baseTypeValue, err = postgres.GetInbaseOrOutbaseValueForOtherRoles(entity)
			if err != nil {
				response.Error = true
				response.Message = err.Error()
				return &response
			}
		}
		maxHonorarium := entity.SpeakerWeight * float64(baseTypeValue)
		response.Error = false
		response.Message = "Max honorarium successfully calculated !!"
		response.MaxHonorarium = math.Ceil(maxHonorarium)

	} else {
		wg := &sync.WaitGroup{}
		mux := &sync.Mutex{}
		errorChanel := make(chan error)

		var calculatedMaxHonorarium float64
		var calculatedMaxHonorariumCurrencyExchange float64
		// var calculatedRoleLimitCurrencyExchange float64
		var roleHonorariumLimit float64
		var roleLimit entity.HLimits
		wg.Add(1)
		go func(wg *sync.WaitGroup, mux *sync.Mutex) {
			var err error
			defer wg.Done()
			if *input.HcpType == "internationalothers" || *input.HcpType == "foreign" {
				calculatedMaxHonorarium, err = calculateMaxHonorariumForInternational(input)
				if err != nil {
					log.Println(err)
					errorChanel <- err
				}
				// roleLimit, err = postgres.GetTierHonorariumLimitForInternational(input)
				// if err != nil {
				// 	errorChanel <- err
				// }
				// calculatedRoleLimitCurrencyExchange, err = postgres.ConvertedCurrencyValue(userCountryID, 0, roleLimit.Limit, *input.HcpType)
				// if err != nil {
				// 	errorChanel <- err
				// }
			} else if *input.HcpType == "regional" {

				roleLimit, err = postgres.GetTierHonorariumLimit(input, &customerCountry)
				if err != nil {
					errorChanel <- err
				}
				log.Println("rolelimit", roleLimit.Limit)
			} else {
				tierLimit, err := postgres.GetTierHonorariumLimit(input, &customerCountry)
				if err != nil {
					errorChanel <- err
				}

				calculatedMaxHonorarium = calculateMaxHonorarium(tierLimit, input)
			}
		}(wg, mux)
		wg.Add(1)
		go func(wg *sync.WaitGroup, mux *sync.Mutex) {
			var err error
			defer wg.Done()

			roleHonorariumLimit, err = postgres.FetchRoleMaxLimit(input, &customerCountry)
			if err != nil {
				errorChanel <- err
			}
		}(wg, mux)
		go func() {
			wg.Wait()
			close(errorChanel)
		}()
		var lastErr error
		for eachError := range errorChanel {
			if eachError != nil {
				fmt.Printf("Error present during max honorarium calculation")
				if lastErr == nil {
					lastErr = errors.New(eachError.Error())
				} else {
					lastErr = errors.New(lastErr.Error() + "\n" + eachError.Error())
				}
			}
		}
		if lastErr != nil {
			response.Error = true
			response.Message = lastErr.Error()
			return &response
		}
		var maxHonorarium float64
		if *input.HcpType == "internationalothers" || *input.HcpType == "foreign" {
			// maxHonorarium = getLeastMaxHonorarium(calculatedMaxHonorarium, calculatedRoleLimitCurrencyExchange)
			maxHonorarium = calculatedMaxHonorarium
		} else if *input.HcpType == "regional" {
			maxHonorarium = roleLimit.Limit
		} else {
			maxHonorarium = getLeastMaxHonorarium(calculatedMaxHonorarium, roleHonorariumLimit)
		}

		if (*input.HcpType == "local" && customerCountry != userCountryID) || *input.HcpType == "regional" {
			calculatedMaxHonorariumCurrencyExchange, err = postgres.ConvertedCurrencyValue(userCountryID, customerCountry, maxHonorarium, *input.HcpType)
			if err != nil {
				log.Printf("%s - Error: %s", err.Error())
				response.Error = true
				response.Message = err.Error()
				return &response
			}
		} else {
			calculatedMaxHonorariumCurrencyExchange = maxHonorarium
		}
		response.Error = false
		response.Message = "Max honorarium successfully calculated !!"
		response.MaxHonorarium = math.Ceil(calculatedMaxHonorariumCurrencyExchange)
	}

	return &response
}

func calculateMaxHonorarium(limit entity.HLimits, input model.HonorariumDetails) float64 {

	maxHonorarium := 0.0
	prepTime := input.PreparationTime // 1400
	serviceTime := input.ServiceTime  // 500
	tierLimit := limit.Limit          // 750
	mins := 60

	convertServiceToHours := float64(*serviceTime) / float64(mins)           //To convert to hours   8.33
	convertPrepToHours := float64(*prepTime) / float64(mins)                 //To convert to hours   23.333
	maxHonorarium = (convertPrepToHours + convertServiceToHours) * tierLimit // 23745
	fmt.Printf("\ncalculateMaxHonorarium :%v", maxHonorarium)
	return math.Ceil(maxHonorarium)
}

func calculateMaxHonorariumForInternational(input model.HonorariumDetails) (float64, error) {
	var err error
	maxHonorarium := 0.0
	prepTime := input.PreparationTime
	serviceTime := input.ServiceTime
	if input.HourlyRate == nil {
		log.Println("error")
		return 0, err
	}
	hourlyRate := input.HourlyRate
	mins := 60

	convertServiceToHours := float64(*serviceTime) / float64(mins)                      //To convert to hours   8.33
	convertPrepToHours := float64(*prepTime) / float64(mins)                            //To convert to hours   23.333
	maxHonorarium = (convertPrepToHours + convertServiceToHours) * float64(*hourlyRate) // 23745
	fmt.Printf("\ncalculateMaxHonorarium :%v", maxHonorarium)
	return math.Ceil(maxHonorarium), nil
}

func getLeastMaxHonorarium(calculated float64, roleBased float64) float64 {
	calcWise := calculated
	roleWise := roleBased
	if calcWise <= roleWise {
		return calcWise
	} else {
		return roleWise
	}
}

func SendEmailToApproverIfNotTakenActionInThreeDays() error {
	functionName := "SendEmailToApproverIfNotTakenActionInThreeDays()"
	log.Println(functionName)
	formAnsIDs, _ := postgres.GetAllFormAnsFromApprover()
	for _, formAnsID := range formAnsIDs {
		approverIDs, _ := postgres.GetAllApproverIdFromApproverByFormAnsID(formAnsID)
		for _, approverId := range approverIDs {
			dateDiff, _ := postgres.GetDateDifference(formAnsID, approverId)
			if dateDiff >= 3 {
				if postgres.CheckFirstApprover(formAnsID, approverId) {
					if postgres.IsActionTakenInApprovalLog(formAnsID, approverId) == false {
						emailData, _ := postgres.GetFormAnswerDetails(formAnsID)
						data := mapper.SetEmailData(emailData)
						email, err := postgres.GetEmailIDFromUserID(approverId)
						if err != nil {
							log.Printf("%s - Error: %s ", err.Error())
						} else {
							approverName, _ := postgres.GetNameByUserID(approverId)
							emailContent := util.SendEmailCronForApproverReminder(email, data, approverName)
							emailCode := codeController.GetValueKeyCodes()["typeofemail"]
							approverStatus := emailCode["requireapproval"].ID
							newEmailContent := strip.StripTags(emailContent)
							_ = postgres.InsertEmailLog(formAnsID, approverStatus, newEmailContent, "00000000-0000-0000-0000-000000000000", approverId)
						}

					}
				} else {
					if postgres.CheckPreviousApproverActionForAllApprovers(formAnsID, approverId) {
						if postgres.IsActionTakenInApprovalLog(formAnsID, approverId) == false {
							emailData, _ := postgres.GetFormAnswerDetails(formAnsID)
							data := mapper.SetEmailData(emailData)
							email, err := postgres.GetEmailIDFromUserID(approverId)
							if err != nil {
								log.Printf("%s - Error: %s ", err.Error())
							} else {
								approverName, _ := postgres.GetNameByUserID(approverId)
								emailContent := util.SendEmailCronForApproverReminder(email, data, approverName)
								emailCode := codeController.GetValueKeyCodes()["typeofemail"]
								approverStatus := emailCode["requireapproval"].ID
								newEmailContent := strip.StripTags(emailContent)
								_ = postgres.InsertEmailLog(formAnsID, approverStatus, newEmailContent, "00000000-0000-0000-0000-000000000000", approverId)
							}

						}
					}
				}
			}
		}
	}

	return nil
}
