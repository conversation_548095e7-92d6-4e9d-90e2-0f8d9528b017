package postgres

//
//import (
//	"context"
//	"database/sql"
//	"encoding/json"
//	"log"
//	"os"
//	"strings"
//
//	codeController "github.com/ihcp/code/controller"
//	"github.com/ihcp/form/graph/entity"
//	"github.com/ihcp/form/graph/logengine"
//	"github.com/ihcp/form/graph/model"
//	"github.com/jackc/pgtype"
//	"github.com/jmoiron/sqlx"
//	uuid "github.com/satori/go.uuid"
//)
//
//func CheckHcpEngagment(formAnswerID string) ([]string, []string) {
//	log.Println("CheckHcpEngagment postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var res []string
//	var amountArray []string
//	querystring := `	WITH fa AS
//	(
//	SELECT fa.id,fa.event_code as event_code ,fa.event_seq as event_seq ,
//	fa.answers	from form_answers fa
//	), groups AS
//	(
//		SELECT id,event_code   ,event_seq , arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
//		arr(item_object)
//	), tab_section AS (
//		SELECT id,event_code ,event_seq ,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
//		arr(item_object)
//	), group_questions AS (
//		SELECT id,event_code ,event_seq , arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
//		arr(item_object)
//	), question_grp AS (
//		SELECT id,event_code ,event_seq , arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
//		arr(item_object)
//	), questions AS
//	(
//		SELECT id,event_code ,event_seq , arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
//		arr(item_object)
//		WHERE arr.item_object->>'id' IN ('speaker-name','proposed-honorarium') or arr.item_object->>'title' IN ('Expenses')
//	),fetch_details as (
//		select id,
//		unnest ((select array(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id in( 'proposed-honorarium') AND questions.id = fa.id )) )as proposed_honorarium,
//		unnest ((select array(SELECT qn_values#>>'{0,id}' FROM  questions WHERE qn_id = 'speaker-name' AND questions.id = fa.id))) as customer_id
//		from fa
//	)
//	select fd.proposed_honorarium,c.supplier_code  from fetch_details fd
//	left join customer c on c.id::text =fd.customer_id::text
//	where fd.id::text =$1 `
//	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
//	if err != nil {
//		log.Println(err)
//	}
//	for rows.Next() {
//		var resSupplierCode sql.NullString
//		var resAmount sql.NullString
//		rows.Scan(
//			&resAmount,
//			&resSupplierCode,
//		)
//		amountArray = append(amountArray, resAmount.String)
//		res = append(res, resSupplierCode.String)
//	}
//	return res, amountArray
//}
//func GetLineSecretoryNameFromUserID(UserId *string) (string, error) {
//	log.Println("GetLineSecretoryNameFromUserID postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var LineSecretaryName string
//	query := `select  concat(first_name,' ',last_name)  from "user" u  where u.id =$1 and u.is_active =true and u.is_deleted =false`
//	err := pool.QueryRow(context.Background(), query, UserId).Scan(&LineSecretaryName)
//	if err != nil {
//		return LineSecretaryName, err
//	}
//	return LineSecretaryName, nil
//}
//func GetLineSecretoryName(UserId *string) (string, string, error) {
//	log.Println("GetEventAribaUrlResponse postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var LineSecretaryAD, LineSecretaryUserRoleID string
//	query := `select u.active_directory,u.user_role_id  from "user" u  where u.id =$1 and u.is_active =true and u.is_deleted =false`
//	err := pool.QueryRow(context.Background(), query, UserId).Scan(&LineSecretaryAD, &LineSecretaryUserRoleID)
//	if err != nil {
//		return LineSecretaryAD, LineSecretaryUserRoleID, err
//	}
//	return LineSecretaryAD, LineSecretaryUserRoleID, nil
//
//}
//func GetEventAribaUrlResponse(formId *string) (entity.GetEventAribaUrlResponse, error) {
//	log.Println("GetEventAribaUrlResponse postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var EventAribaUrlResponse entity.GetEventAribaUrlResponse
//	querystring := ` select
//	(select c2.description  from code c2 where c2.id =fa.country) as country_code,
//		fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,2,values,0,value}' as activity_name,
//		fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}' as activity_start_date,
//		fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}' as activity_end_date,
//		concat(fa.event_code,'-',fa.event_seq) as event_id,
//		fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values,0,value}' as event_requstor,
//		fa.answers#>>'{3,sectionAnswer,0,form,groupAnswer,0,questionAnswers,4,answers,0,values,0,value}' as expense,
//		fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,15,values,0,value}' as no_of_Hcps,
//		upper( (string_to_array(c.value ,'|')::text[])[2])  as envent_Expense,
//		(select concat(u.first_name,' ',u.last_name )from "user" u  where u.id =fa.created_by) as user_name
//		FROM form_answers fa
//		inner join code c on c.id =fa.currency where fa.id = $1`
//	err := pool.QueryRow(context.Background(), querystring, formId).Scan(&EventAribaUrlResponse.CountryCode, &EventAribaUrlResponse.ActivityNameField, &EventAribaUrlResponse.StarDate,
//		&EventAribaUrlResponse.EndDate, &EventAribaUrlResponse.MeetingId, &EventAribaUrlResponse.EventRequester, &EventAribaUrlResponse.EventExpenseAmount,
//		&EventAribaUrlResponse.NoOfHcps, &EventAribaUrlResponse.EventExpense, &EventAribaUrlResponse.UserName)
//	if err != nil {
//		return EventAribaUrlResponse, err
//	}
//	return EventAribaUrlResponse, nil
//}
//
//func CheckFormAnswerIDInEventAriba(FormAnswerID string, intregationType string) bool {
//	log.Println("checkFormAnswerIDInEventAriba postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var response bool
//	var check int
//	querystring := `select 1 from  event_logs  el where el.form_answer_uid::text =$1 and el.integration_type =$2 `
//	err := pool.QueryRow(context.Background(), querystring, FormAnswerID, intregationType).Scan(&check)
//	if err == nil {
//		response = true
//	}
//	return response
//}
//func CheckFormAnswerId(FormAnswerID string) bool {
//	log.Println("CheckFormAnswerId postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var response bool
//	var check int
//	querystring := ` select 1 from  form_answers where  id::text= $1 `
//	err := pool.QueryRow(context.Background(), querystring, FormAnswerID).Scan(&check)
//	if err == nil {
//		return true
//	}
//	return response
//}
//func FetchAribaEventLogsValues(input *entity.AribaEventLogsRequest, country *int) ([]*entity.AribaEventLogsData, error) {
//	var AribaEventDATA []*entity.AribaEventLogsData
//	log.Println(" FetchAribaEventLogsValues postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	getIntregationtype := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	var inputargs []interface{}
//	querystring := `select el.id ,el.form_answer_uid ,
//	eld.event_details#>>'{ariba_reference_id}' as ariba_reference_id,
//	el.status ,EXTRACT(EPOCH FROM el.date_created::timestamp)::text as date_created,
//	EXTRACT(EPOCH FROM el.date_retry::timestamp)::text as date_retry,
//	eld.event_details#>>'{ariba_url}' as ariba_url ,
//	(eld.event_details#>>'{amount_not_exist}')::boolean as amount_not_exist,
//	el.error_message
//	,fa.event_code,fa.event_seq ,
//	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}' as activity_start_date,
//	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}' as activity_end_date,
//	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,2,values,0,value}' as activity_name
//	from event_logs el
//	inner join form_answers fa on fa.id = el.form_answer_uid
//	inner join event_log_details eld on eld.event_log_uid = el.id
//	where el.integration_type = ? and fa.country =?`
//	inputargs = append(inputargs, getIntregationtype, country)
//	if input.ID != "" {
//		querystring += ` and el.id=? `
//		inputargs = append(inputargs, input.ID)
//	}
//	if input.FormAnswerID != "" {
//		querystring += ` and el.form_answer_uid=? `
//		inputargs = append(inputargs, input.FormAnswerID)
//	}
//	if input.AribaReferenceID != "" {
//		querystring += ` and eld.event_details#>>'{ariba_reference_id}'=? `
//		inputargs = append(inputargs, input.AribaReferenceID)
//	}
//	querystring += `order by el.date_created desc `
//
//	if input.Limit > 0 {
//		if input.PageNo > 0 {
//			querystring = querystring + ` limit ? offset ?`
//			inputargs = append(inputargs, input.Limit)
//			inputargs = append(inputargs, input.PageNo)
//		} else {
//			querystring = querystring + ` limit ? offset ?`
//			inputargs = append(inputargs, input.Limit)
//			inputargs = append(inputargs, 0)
//		}
//	}
//	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
//	rows, err := pool.Query(context.Background(), querystring, inputargs...)
//	if err != nil {
//		return nil, err
//	}
//	for rows.Next() {
//		var ele entity.AribaEventLogsData
//		err := rows.Scan(
//			&ele.Id,
//			&ele.FormAnswerId,
//			&ele.AribaReferenceId,
//			&ele.Status,
//			&ele.DateCreated,
//			&ele.DateRetry,
//			&ele.AribaUrl,
//			&ele.AmountNotExist,
//			&ele.ErrorMessage,
//			&ele.EventCode,
//			&ele.Eventseq,
//			&ele.StartDate,
//			&ele.EndDate,
//			&ele.ActivityName,
//		)
//		if err != nil {
//			return nil, err
//		}
//		AribaEventDATA = append(AribaEventDATA, &ele)
//	}
//	return AribaEventDATA, nil
//}
//
//func InsertAribaEventLogsForApproved(input entity.UpsertAribaEventLogsRequest, amountNotExist bool) (bool, error) {
//	log.Println(" InsertAribaEventLogsForApproved postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var checkStatus bool
//	if strings.TrimSpace(input.ErrorMessage) == "" {
//		checkStatus = true
//	}
//	var id uuid.UUID
//	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	query := `Insert into event_logs (form_answer_uid,	status,	error_message,	integration_type,is_apollo)
//	Values($1,$2,$3,$4,false)RETURNING id`
//	err := pool.QueryRow(context.Background(), query, input.FormAnswerId, checkStatus, input.ErrorMessage, intregationType).Scan(&id)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	var aribaJsonValues entity.AribaJSonDataStore
//	aribaJsonValues.AribaReferenceId = input.AribaReferenceId
//	aribaJsonValues.AribaUrl = input.AribaUrl
//	aribaJsonValues.AmountNotExist = amountNotExist
//	aribaJsonAllData, err := json.Marshal(aribaJsonValues)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	queryString := `insert into event_log_details(event_log_uid,	event_details,payload)
//	values($1,$2,$3)`
//	_, err = pool.Exec(context.Background(), queryString, id, aribaJsonAllData, input.Payload)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	return checkStatus, nil
//}
//
//func CheckAribaFormIdExist(FormAnswerID string) bool {
//	log.Println(" CheckAribaFormIdExist postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var check int
//	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	query := `select 1 from event_logs el where el.form_answer_uid =$1 and el.integration_type =$2`
//	err := pool.QueryRow(context.Background(), query, FormAnswerID, intregationType).Scan(&check)
//	if err != nil {
//		return false
//	}
//	return true
//
//}
//
//func UpdateAuditTrailEventData(formAnsID string, userID *string, LineSecretaryUserRoleID string, isApollo *bool) error {
//	functionName := "UpdateAuditTrailEventData"
//	if pool == nil {
//		pool = GetPool()
//	}
//	auditTrailCode := codeController.GetValueKeyCodes()["approvedstatus"]
//	Action := auditTrailCode["linesecretaryapproved"].ID
//	Comment := ""
//	querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
//	_, err := pool.Exec(context.Background(), querystring, formAnsID, userID, 0, LineSecretaryUserRoleID, Action, Comment, isApollo)
//	if err != nil {
//		logengine.GetTelemetryClient().TrackException(err)
//		log.Printf("%s - error: {%s}", functionName, err.Error())
//		return err
//	}
//	return nil
//}
//
//func UpdateAuditTrailEventDataForReturnLineSecretory(input model.LineSceretoryReturnRequest, userID *string, LineSecretaryUserRoleID string) error {
//	functionName := "UpdateAuditTrailEventDataForReturnLineSecretory"
//	if pool == nil {
//		pool = GetPool()
//	}
//	auditTrailCode := codeController.GetValueKeyCodes()["approvedstatus"]
//	Action := auditTrailCode["linesecretaryreturned"].ID
//	querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
//	_, err := pool.Exec(context.Background(), querystring, input.FormAnswerID, userID, 0, LineSecretaryUserRoleID, Action, input.Comment, input.IsApollo)
//	if err != nil {
//		logengine.GetTelemetryClient().TrackException(err)
//		log.Printf("%s - error: {%s}", functionName, err.Error())
//		return err
//	}
//	return nil
//}
//
//func UpdateAribaEventLogsForApproved(input entity.UpsertAribaEventLogsRequest, amountNotExist bool) (bool, error) {
//	log.Println(" UpdateAribaEventLogsForApproved postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var checkStatus bool
//	if strings.TrimSpace(input.ErrorMessage) == "" {
//		checkStatus = true
//	}
//	var id uuid.UUID
//	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	query := `update event_logs set status = $2 , error_message = $3 , date_retry = now() where form_answer_uid = $1 and integration_type=$4 RETURNING id`
//	err := pool.QueryRow(context.Background(), query, input.FormAnswerId, checkStatus, input.ErrorMessage, intregationType).Scan(&id)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	var aribaJsonValues entity.AribaJSonDataStore
//	aribaJsonValues.AribaReferenceId = input.AribaReferenceId
//	aribaJsonValues.AribaUrl = input.AribaUrl
//	aribaJsonValues.AmountNotExist = amountNotExist
//	aribaJsonAllData, err := json.Marshal(aribaJsonValues)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	queryString := `update event_log_details set event_details = $2,payload=$3 where event_log_uid = $1`
//	_, err = pool.Exec(context.Background(), queryString, id, aribaJsonAllData, input.Payload)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	return checkStatus, nil
//}
//
//func UpdateAribaEventLogsForRetry(input entity.UpsertAribaEventLogsRequest, amountNotExist bool) (bool, error) {
//	log.Println(" UpdateAribaEventLogsForRetry postgres()")
//	if pool == nil {
//		pool = GetPool()
//	}
//	var checkStatus bool
//	if strings.TrimSpace(input.ErrorMessage) == "" {
//		checkStatus = true
//	}
//	var id uuid.UUID
//	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	query := `update event_logs set status = $2 , error_message = $3 , date_retry = now() where form_answer_uid = $1 and integration_type=$4 RETURNING id`
//	err := pool.QueryRow(context.Background(), query, input.FormAnswerId, checkStatus, input.ErrorMessage, intregationType).Scan(&id)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	var aribaJsonValues entity.AribaJSonDataStore
//	aribaJsonValues.AribaReferenceId = input.AribaReferenceId
//	aribaJsonValues.AribaUrl = input.AribaUrl
//	aribaJsonValues.AmountNotExist = amountNotExist
//	aribaJsonAllData, err := json.Marshal(aribaJsonValues)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	queryString := `update event_log_details set event_details = $2,payload=$3 where event_log_uid = $1`
//	_, err = pool.Exec(context.Background(), queryString, id, aribaJsonAllData, input.Payload)
//	if err != nil {
//		checkStatus = false
//		return checkStatus, err
//	}
//	return checkStatus, nil
//}
//
//func GetRoleIDByValueInUserRolesCcO(roleValue string) (string, error) {
//	functionName := "GetRoleIDByValueInUserRolesCcO()"
//	log.Println(functionName)
//	var codeValue string
//	if pool == nil {
//		pool = GetPool()
//	}
//	var value string
//	queryString := `select id from user_roles where value = $1 and is_active = true and is_deleted = false `
//	err := pool.QueryRow(context.Background(), queryString, roleValue).Scan(&value)
//	logengine.GetTelemetryClient().TrackEvent("GetRoleIDByValueInUserRoles query called")
//	if err != nil {
//		logengine.GetTelemetryClient().TrackException(err)
//		return "", err
//	} else {
//		codeValue = value
//	}
//	return codeValue, nil
//}
//
//func CheckStatusAribaEventLogsController() ([]string, error) {
//	functionName := "GetUnsynchronizedEvents()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	intregationType := os.Getenv("INTEGRATION_TYPE_FOR_ARIBA")
//	queryString := `select form_answer_uid from event_logs where status=false and integration_type = $1`
//	var formId []string
//	var formAnswerId string
//	rows, err := pool.Query(context.Background(), queryString, intregationType)
//	if err != nil {
//		return formId, err
//	}
//	for rows.Next() {
//		rows.Scan(
//			&formAnswerId,
//		)
//		if err != nil {
//			return formId, err
//		}
//		formId = append(formId, formAnswerId)
//	}
//	return formId, err
//}
//
//func GetPaymentAndAttandenceBlobUrl(formanswerId string) (string, []string) {
//	functionName := "GetPaymentAndAttandenceBlobUrl()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	var EventType sql.NullString
//	var descriptionCategoryForAttendance string
//	query := `select fa.answers#>>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values,0,description}' as event_type from form_answers fa  where fa.id =$1`
//	pool.QueryRow(context.Background(), query, formanswerId).Scan(&EventType)
//	descriptionTypeForAttendance := os.Getenv("FORM_ANSWER_ATTACHMENTS_TYPE_DESCRIPTION_FOR_PROOF_OF_ATTENDANCE")
//	if EventType.String != "ZP - Hosted Event" {
//		descriptionCategoryForAttendance = os.Getenv("FORM_ANSWER_ATTACHMENTS_CATEGORY_DESCRIPTION_FOR_PROOF_OF_ATTENDANCE")
//	} else {
//		descriptionCategoryForAttendance = "Signed Attendance list"
//	}
//	descriptionTypeForservice := os.Getenv("FORM_ANSWER_ATTACHMENTS_TYPE_DESCRIPTION_FOR_PROOF_OF_DELIVERY")
//	descriptionCategoryForservice := os.Getenv("FORM_ANSWER_ATTACHMENTS_CATEGORY_DESCRIPTION_FOR_SERVICE")
//	var getUrlforAttandence sql.NullString
//	query1 := `select faa.url from form_answer_attachments faa where  faa.type_desc =$2 and faa.category_desc =$3 and faa.form_answer_id =$1 and faa.is_active =true and faa.is_deleted =false	`
//	pool.QueryRow(context.Background(), query1, formanswerId, descriptionTypeForAttendance, descriptionCategoryForAttendance).Scan(&getUrlforAttandence)
//	var getUrlforService sql.NullString
//	var serviceBlobUrlArray []string
//	querystring1 := `select faa.url from form_answer_attachments faa where  faa.type_desc =$2 and faa.category_desc =$3 and faa.form_answer_id =$1 and faa.is_active =true and faa.is_deleted =false	`
//	rows, _ := pool.Query(context.Background(), querystring1, formanswerId, descriptionTypeForservice, descriptionCategoryForservice)
//	for rows.Next() {
//		rows.Scan(
//			&getUrlforService,
//		)
//		serviceBlobUrlArray = append(serviceBlobUrlArray, getUrlforService.String)
//	}
//	return getUrlforAttandence.String, serviceBlobUrlArray
//}
//func GetAribaEventCountries() (string, error) {
//	functionName := "GetAibaEventCountries()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	queryString := `select value from code where category ='Ariba Countries' and is_active = true and is_deleted = false`
//	var countries string
//	rows, err := pool.Query(context.Background(), queryString)
//	if err != nil {
//		return countries, err
//	}
//	for rows.Next() {
//		rows.Scan(
//			&countries,
//		)
//		if err != nil {
//			return countries, err
//		}
//	}
//	return countries, err
//}
//
//func GetSupplierCountries() (string, error) {
//	functionName := "GetSupplierCountries()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	queryString := `select value from code where category ='Supplier Countries' and is_active = true and is_deleted = false`
//	var countries string
//	rows, err := pool.Query(context.Background(), queryString)
//	if err != nil {
//		return countries, err
//	}
//	for rows.Next() {
//		rows.Scan(
//			&countries,
//		)
//		if err != nil {
//			return countries, err
//		}
//	}
//	return countries, err
//}
//func LineSceretoryActivityExist(formId string) bool {
//	functionName := "LineSceretoryActivityExist()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	var value int
//	query := `select 1 from line_secretary_activity lsa where lsa.form_answers_id =$1 and lsa.is_active =true and lsa.is_deleted =false`
//	err := pool.QueryRow(context.Background(), query, formId).Scan(&value)
//	if err != nil {
//		return false
//	}
//	return true
//}
//
//func UpdateLineSceretoryActivity(formId string) {
//	functionName := "UpdateLineSceretoryActivity()"
//	if pool == nil {
//		pool = GetPool()
//	}
//	var value int
//	query := `update line_secretary_activity set is_active =false ,is_deleted =true where form_answers_id =$1`
//	err := pool.QueryRow(context.Background(), query, formId).Scan(&value)
//	if err != nil {
//		log.Println(functionName, " : ", err.Error())
//	}
//}
//
//func LineSceretoryActivityStoreInDb(input model.LineSceretoryReturnRequest, answer pgtype.JSONB, userID *string) error {
//	functionName := "LineSceretoryActivityStoreInDb()"
//	if pool == nil {
//		pool = GetPool()
//	}
//	LineSecretory := codeController.GetValueKeyCodes()["approvedstatus"]
//	returnlineScretoryStatus := LineSecretory["linesecretaryreturned"].ID
//	ApprovedStatusTitle := LineSecretory["linesecretaryreturned"].Title
//	ApprovedStatusValue := LineSecretory["linesecretaryreturned"].Value
//	query := `INSERT INTO line_secretary_activity
//	(form_answers_id, actioned_by, date_created, "comments", status, is_active, is_deleted)
//	VALUES( $1, $2, now(), $3, $4, true, false);`
//	_, err := pool.Exec(context.Background(), query, input.FormAnswerID, userID, input.Comment, returnlineScretoryStatus)
//	if err != nil {
//		log.Println(functionName, " : ", err.Error())
//		return err
//	}
//	queryString := `update form_answers set status =$1 where id=$2 `
//	_, err = pool.Exec(context.Background(), queryString, returnlineScretoryStatus, input.FormAnswerID)
//	if err != nil {
//		log.Println(functionName, " : ", err.Error())
//		return err
//	}
//	querystring := `update reporting_db  set status_title  =$1, status_value =$2,updated_date =now() where form_answer_id =$3 `
//	_, err = pool.Exec(context.Background(), querystring, ApprovedStatusTitle, ApprovedStatusValue, input.FormAnswerID)
//	if err != nil {
//		log.Println(functionName, " : ", err.Error())
//		return err
//	}
//	return nil
//}
