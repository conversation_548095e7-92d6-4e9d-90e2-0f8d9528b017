package controller

import (
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/upload/graph/model"
)

func getExcelTemplateURL(input *model.ExcelTemplateRequest) *model.AzureExcelUploadResponse {
	templateModel := &model.AzureExcelUploadResponse{}
	codes := codeController.GetValueKeyCodes()["exceltemplate"]
	url := codes[input.TemplateType].Description
	if url.String == "" {
		templateModel.Error = true
		templateModel.Message = "Excel template could not be found!"
	} else {
		templateModel.URL = url.String
	}
	return templateModel
}
