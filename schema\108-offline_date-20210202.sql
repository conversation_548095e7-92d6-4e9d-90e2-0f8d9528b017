create table offline_date (
id uuid primary key not null default public.uuid_generate_v4(),
user_id uuid not null ,
is_active bool not null default true ,
is_deleted bool not null default false ,
start_date date not null ,
end_date date not null ,
created_by uuid NOT NULL,
date_created timestamptz NOT NULL DEFAULT now(),
last_modified timestamptz NULL,
modified_by uuid NULL
) ;


alter table offline_date add  constraint fk_user_id foreign key (user_id) references "user"(id) ;