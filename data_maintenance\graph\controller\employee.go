package controller

import (
	"context"
	"errors"
	"log"
	"path"
	"reflect"
	"strings"
	"time"

	excelizev2 "github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/ihcp/data_maintenance/graph/azure"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/mapper"
	"github.com/ihcp/data_maintenance/graph/model"
	"github.com/ihcp/data_maintenance/graph/postgres"
	"github.com/ihcp/login/auth"
	uuid "github.com/satori/go.uuid"
)

func ExportEmployeeExcel(ctx *context.Context, input *model.EmployeeRequest) *model.EmployeeResponse {
	var response model.EmployeeResponse
	var userUUID uuid.UUID
	var err error

	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}

	if !postgres.UserIsAdmin(*userID) {
		response.Error = true
		response.Message = "User is not authorized to access"
		return &response
	}

	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		}
	}

	exportEmployee, err := mapper.ExportEmployeeInputModelToEntity(input, userUUID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	employeeCount, countErr := postgres.EmployeeCount(exportEmployee.Country)
	if countErr != nil {
		response.Error = true
		response.Message = countErr.Error()
		return &response
	}
	employeeInfo, err := postgres.GetEmployeeExcelInfo(input, exportEmployee)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputModels, outModelsExcel := mapper.ExportEmployeeInfoEntityToModel(employeeInfo)
	if input.IsExcel {
		url, err := createExportEmployeeExcel(outModelsExcel)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		response.URL = url
	} else {
		response.Data = outputModels
	}
	response.TotalCount = employeeCount
	return &response
}

func createExportEmployeeExcel(input []*entity.EmployeeExcelData) (string, error) {
	if len(input) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "employee"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)

	// Populate excel 1 row header columns
	typeInfo := reflect.TypeOf(entity.EmployeeExcel{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
		headerRow = append(headerRow, strings.ToLower(typeInfo.Field(fieldIndex).Name))
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	for index, item := range input {
		rowNumber := index + 2

		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "employeeExcel" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }
	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}

func UpsertEmployeeData(ctx *context.Context, inputModel model.EmployeeInput) *model.UpsertResponse {
	var upsertResponse model.UpsertResponse
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	var userUUID uuid.UUID
	var err error
	if userID == nil && approvalRole == nil {
		upsertResponse.Error = true
		upsertResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return &upsertResponse
	} else {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			upsertResponse.Error = true
			upsertResponse.Message = err.Error()
			return &upsertResponse
		}
	}
	entity, err := mapper.MapEmployeeModelToEntity(&inputModel, userUUID, *country)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	err = postgres.ValidateUpsertEmployee(entity)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	err = postgres.ValidateTeamID(entity)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	err, message := postgres.UpsertEmployee(entity, userUUID)
	if err != nil {
		upsertResponse.Error = true
		upsertResponse.Message = err.Error()
		return &upsertResponse
	}
	upsertResponse.Message = message
	return &upsertResponse
}

func ExportEmployeeByRole(ctx *context.Context, input *model.EmployeeRoleRequest) *model.EmployeeResponse {
	var response model.EmployeeResponse
	employeeRoleID, err := mapper.EmployeeByRoleInputModelToEntity(input)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	employeeInfo, err := postgres.GetEmployeeInfoByRole(employeeRoleID)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	outputModels := mapper.EmployeeByRoleInfoEntityToModel(employeeInfo)
	response.Data = outputModels
	return &response
}

func EmployeeAuditLogs(ctx *context.Context, input model.EmployeeAuditLogsRequest) *model.EmployeeAuditLogsResponse {
	var response model.EmployeeAuditLogsResponse
	var err error
	var userUUID uuid.UUID
	userID := auth.GetUserID(*ctx)
	approvalRole := auth.GetApprovalRole(*ctx)
	country := auth.GetCountry(*ctx)
	if userID == nil && approvalRole == nil {
		response.Error = true
		response.Message = "You are not authorized to login please contact your country ezflow admin."
		return &response
	}
	if userID != nil {
		userUUID, err = uuid.FromString(*userID)
		if err != nil {
			log.Printf("%s - Error: %s ", err.Error())
		}
	}
	EmployeeAutitlogsEntity, err := mapper.ExportEmployeeAuditLogsInputModelToEntity(input, country)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	employeeInfo, err := postgres.GetExportEmployeeAuditLogsData(EmployeeAutitlogsEntity)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	if input.IsExcel {
		outModelsExcel := mapper.ExportEmployeeAuditEntityToModel(employeeInfo)
		url, err := createExportEmployeeAuditExcelGenerate(outModelsExcel)
		if err != nil {
			response.Error = true
			response.Message = err.Error()
			return &response
		}
		postgres.InsertIntoUploadDb(url, path.Base(url), "usermanagementlogs", userUUID)
		response.URL = url
	} else {
		response.Data = employeeInfo
	}
	response.TotalCount = len(employeeInfo)
	return &response

}

func createExportEmployeeAuditExcelGenerate(input []entity.EmployeeExcelData) (string, error) {
	if len(input) == 0 {
		return "", errors.New("No data found")
	}
	sheetName := "employeeAuditLogsExcel"
	f := excelizev2.NewFile()
	f.SetSheetName("Sheet1", sheetName)
	streamWriter, err := f.NewStreamWriter(sheetName)

	// Populate excel 1 row header columns
	typeInfo := reflect.TypeOf(entity.EmployeeExcelDataResponse{})
	var headerRow []interface{}
	for fieldIndex := 0; fieldIndex < typeInfo.NumField(); fieldIndex++ {
		headerRow = append(headerRow, strings.ToLower(typeInfo.Field(fieldIndex).Name))
	}
	cell, _ := excelizev2.CoordinatesToCellName(1, 1)
	if err := streamWriter.SetRow(cell, headerRow); err != nil {
		log.Println(err)
	}
	for index, item := range input {
		rowNumber := index + 2

		cell, _ := excelizev2.CoordinatesToCellName(1, rowNumber)
		if err := streamWriter.SetRow(cell, item.Data); err != nil {
			log.Println(err)
		}
	}
	if err := streamWriter.Flush(); err != nil {
		log.Println(err)
	}
	filename := "employeeAuditLogsExcel" + time.Now().Format("20060102150405") + ".xlsx"
	// For saving the file locally
	// if err := f.SaveAs(filename); err != nil {
	// 	println(err.Error())
	// }
	blobURL, err := azure.UploadBytesToBlob(getBytesFromFileV2(f), filename)
	if err != nil {
		return "", err
	}
	return blobURL, nil
}
