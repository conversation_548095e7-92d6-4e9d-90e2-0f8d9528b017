package entity

import (
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/upload/graph/model"
	uuid "github.com/satori/go.uuid"
)

type ClientExcelInput struct {
	ID          *uuid.UUID
	CountryNo   int
	ClientName  string
	CountryName string
	IsActive    bool
	IsDeleted   bool
}

func (o *ClientExcelInput) ValidateClientExcelData(row int, validationMessages []*model.ExcelValidationMessage) {
	if *(o.ID) != uuid.Nil && o.CountryName == "" && o.ClientName == "" {
		o.IsDeleted = false
		o.IsActive = false
	} else {
		codes := codeController.GetTitleKeyCodes()["country"]
		_, countryExists := codes[o.CountryName]
		if !countryExists {
			errorMessage := &model.ExcelValidationMessage{Row: (row), Message: " Country does not exist"}
			validationMessages = append(validationMessages, errorMessage)
		}
		if o.ClientName == "" {
			errorMessage := &model.ExcelValidationMessage{Row: row, Message: " ClientName is blank"}
			validationMessages = append(validationMessages, errorMessage)
		}
	}
}
