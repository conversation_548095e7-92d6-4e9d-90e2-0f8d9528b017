package controller

import (
	"context"
	"io/ioutil"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/ihcp/login/auth"
	"github.com/ihcp/upload/graph/azure"
	"github.com/ihcp/upload/graph/model"
	"github.com/ihcp/upload/graph/util"
)

func HandleExcelUpload(ctx *context.Context, input *model.ExcelUploadRequest) *model.ExcelUploadResponse {
	uploadResponse := &model.ExcelUploadResponse{}
	response := &model.ExcelUploadResponse{}
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		uploadResponse.Error = true
		uploadResponse.Message = "You are not authorized to login please contact your country ezflow admin."
		return uploadResponse
	}
	_, err := url.ParseRequestURI(strings.TrimSpace(input.URL))
	if err != nil {
		uploadResponse.Error = true
		uploadResponse.Message = err.Error()
		return uploadResponse
	}
	if strings.TrimSpace(input.FileName) == "" {
		uploadResponse.Error = true
		uploadResponse.Message = "Please provide valid Filename!"
		return uploadResponse
	}
	excelFile, downloadErr := azure.DownloadFileFromBlobURL(input.URL)
	if downloadErr == nil {
		excelBytes, _ := ioutil.ReadAll(excelFile)
		sheetName := util.GetSheetName(excelBytes)
		switch sheetName {
		case "customer":
			_, err := util.ParseExcel(excelBytes, sheetName, 17)
			if err != "" {
				uploadResponse.Error = true
				uploadResponse.Message = err
				return uploadResponse
			} else {
				go processCustomerExcel(input, ctx, excelBytes)
			}
			break
		case "employee":
			_, err := util.ParseExcel(excelBytes, sheetName, 12)
			if err != "" {
				uploadResponse.Error = true
				uploadResponse.Message = err
				return uploadResponse
			} else {
				go processEmployeeExcel(input, ctx, excelBytes)
			}
			break
		case "material":
			_, err := util.ParseExcel(excelBytes, sheetName, 7)
			if err != "" {
				uploadResponse.Error = true
				uploadResponse.Message = err
				return uploadResponse
			} else {
				go processMaterialExcel(input, ctx, excelBytes)
			}
			break
		case "client":
			_, err := util.ParseExcel(excelBytes, sheetName, 4)
			if err != "" {
				uploadResponse.Error = true
				uploadResponse.Message = err
				return uploadResponse
			} else {
				go processClientExcel(input, ctx, excelBytes)
			}
			break
		default:
			uploadResponse.Error = true
			uploadResponse.Message = "Incorrect sheet name!"
			return uploadResponse
		}
		if response.Error == true {
			uploadResponse.Error = response.Error
			uploadResponse.Message = response.Message
			return uploadResponse
		} else {
			uploadResponse.Error = false
			uploadResponse.Message = "Excel has been submitted!"
			return uploadResponse
		}
	} else {
		uploadResponse.Error = true
		uploadResponse.Message = downloadErr.Error()
		return uploadResponse
	}
	// return uploadResponse
}

func AzureExcelUpload(ctx *context.Context, input *model.AzureExcelUploadInput) *model.AzureExcelUploadResponse {
	var response model.AzureExcelUploadResponse

	content, err := ioutil.ReadAll(input.File.File)
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}

	if strings.TrimSpace(input.FileName) == "" {
		response.Error = true
		response.Message = "Please provide a filename"
		return &response
	}

	now := time.Now()
	sec := now.Unix()
	url, err := azure.UploadBytesToBlob(content, input.FileName+"-"+strconv.FormatInt(sec, 10)+".xlsx")
	//url, err := azure.UploadBytesToBlob(content, "Material Template"+".xlsx")
	if err != nil {
		response.Error = true
		response.Message = err.Error()
		return &response
	}
	response.URL = url
	return &response
}

func GetExcelTemplateURL(input *model.ExcelTemplateRequest) *model.AzureExcelUploadResponse {
	response := getExcelTemplateURL(input)
	return response
}

func ListExcelUploads(ctx *context.Context, input *model.ExcelStatusRequest) *model.ExcelStatusResponse {
	//userId, authRole := postgres.GetUserIdAuthRoleByActiveDirName(input.UserActiveDirectoryName)
	userID := auth.GetUserID(*ctx)
	if userID == nil {
		emptyResult := &model.ExcelStatusResponse{}
		return emptyResult
	}
	response := getUploadList(ctx, input)

	return response
}
