
input offlineDateInput{
id : String
startDate : Int!
endDate : Int!
is_deleted: Boolean
}

type UpsertOfflineDateResponse{
    error: Boolean!
    message: String!
}

type offlineDate{
    ID: String!
    startDate: Int
    endDate: Int
}

type GetOfflineDateResponse {
  error: Boolean!
  message: String!
  offlineDate: [offlineDate]!
}

extend type Query {
    getOfflineDate: GetOfflineDateResponse!
}

extend type Mutation {
    upsertOfflineDate(input: offlineDateInput!): UpsertOfflineDateResponse!
}