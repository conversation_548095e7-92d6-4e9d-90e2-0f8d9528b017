package postgres

import (
	"context"
	"encoding/xml"
	"errors"
	"io/ioutil"
	"log"
	"math/bits"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/jackc/pgx"
)

func ProxyAuthenticateUserSuperAdmin(input *entity.ProxyUserLogin) (*entity.ProxyUserAuth, error) {
	var userEntityResult entity.ProxyUserAuth
	var userEntityPermission []entity.Permission
	var userEntityPermissionNewArray []entity.Permission
	var jwtToken string
	var resultErr error

	userEntityPermission, resultErr = GetUserForAuthentication(input.ActiveDirectoryName)
	if resultErr != nil && resultErr.Error() == pgx.ErrNoRows.Error() {
		return &userEntityResult, errors.New("User does not exist in eZFlow!")
	} else if resultErr != nil {
		return &userEntityResult, resultErr
	} else {
		for _, val := range userEntityPermission {
			var userEntityPermissionNew entity.Permission
			jwtToken, resultErr = GetUserTokenInformation(input.ActiveDirectoryName, val.CountryDescription)
			userEntityPermissionNew = val
			userEntityPermissionNew.JWTToken = jwtToken
			userEntityPermissionNewArray = append(userEntityPermissionNewArray, userEntityPermissionNew)
		}
		// userEntityResult.JWTToken = jwtToken
	}
	userEntityResult.ActiveDirectory = input.ActiveDirectoryName
	userEntityResult.AccessControl = userEntityPermissionNewArray

	return &userEntityResult, nil
}
func ProxyAuthenticateUserAdmin(input *entity.ProxyUserLogin, country *int) (*entity.ProxyUserAuth, error) {
	var userEntityResult entity.ProxyUserAuth
	var userEntityPermission []entity.Permission
	var userEntityPermissionNewArray []entity.Permission
	var jwtToken string
	var resultErr error
	codes := codeController.GetIdKeyCodes()["country"]
	CountryTitle := codes[*country].Title
	userEntityPermission, resultErr = GetUserForRegenerate(input.ActiveDirectoryName, CountryTitle.String)
	if resultErr != nil && resultErr.Error() == pgx.ErrNoRows.Error() {
		return &userEntityResult, errors.New("User does not exist in eZFlow!")
	} else if resultErr != nil {
		return &userEntityResult, resultErr
	} else {
		for _, val := range userEntityPermission {
			var userEntityPermissionNew entity.Permission
			jwtToken, resultErr = GetUserTokenInformation(input.ActiveDirectoryName, val.CountryDescription)
			userEntityPermissionNew = val
			userEntityPermissionNew.JWTToken = jwtToken
			userEntityPermissionNewArray = append(userEntityPermissionNewArray, userEntityPermissionNew)
		}
		// userEntityResult.JWTToken = jwtToken
	}
	userEntityResult.ActiveDirectory = input.ActiveDirectoryName
	userEntityResult.AccessControl = userEntityPermissionNewArray

	return &userEntityResult, nil
}

//func AuthenticateUser(userEntity *entity.UserLogin) (*entity.UserAuth, error) {
//	functionName := "AuthenticateUser()"
//	adHost := os.Getenv("IHCP_AD_HOST")
//	adPort := os.Getenv("IHCP_AD_PORT")
//	var userEntityResult entity.UserAuth
//	var userEntityPermission []entity.Permission
//	var jwtToken string
//	var resultErr error
//	graphqlClient := graphql.NewClient("http://" + adHost + ":" + adPort + "/query")
//	graphqlRequest := graphql.NewRequest(`
//        {
//            checkAuthentication(request:{username:"` + userEntity.ActiveDirectoryName + `",password:"` + userEntity.Password + `"}){
//				authenticated
//				eZRxJWT
//				eZTradeJWT
//			  }
//        }
//	`)
//	var graphqlResponse interface{}
//	logengine.GetTelemetryClient().TrackEvent("AuthenticateUser query called")
//	if err := graphqlClient.Run(context.Background(), graphqlRequest, &graphqlResponse); err != nil {
//		logengine.GetTelemetryClient().TrackException(err)
//		log.Printf("%s - error: %s", functionName, err.Error())
//		return &userEntityResult, errors.New("Error authenticating username and password")
//	}
//	gqlResponseMap := graphqlResponse.(map[string]interface{})
//	if result, value := gqlResponseMap["checkAuthentication"]; value {
//		checkAuthenticationMap := result.(map[string]interface{})
//		isAuthenticated := checkAuthenticationMap["authenticated"].(bool)
//		if isAuthenticated {
//			userEntityPermission, resultErr = GetUserForAuthentication(userEntity.ActiveDirectoryName)
//			if resultErr != nil && resultErr.Error() == pgx.ErrNoRows.Error() {
//				return &userEntityResult, errors.New("User does not exist in eZFlow!")
//			} else if resultErr != nil {
//				return &userEntityResult, resultErr
//			} else if len(userEntityPermission) == 0 {
//				return &userEntityResult, errors.New("Invalid Credentials / Account does not exist")
//			} else {
//				if len(userEntityPermission) != 1 {
//					jwtToken, resultErr = GetUserTokenInformation(userEntity.ActiveDirectoryName, "")
//					if resultErr != nil {
//						return &userEntityResult, resultErr
//					}
//					userEntityResult.JWTToken = jwtToken
//				} else if len(userEntityPermission) == 1 {
//					jwtToken, resultErr = GetUserTokenInformation(userEntity.ActiveDirectoryName, userEntityPermission[0].CountryDescription)
//					if resultErr != nil {
//						return &userEntityResult, resultErr
//					}
//					userEntityResult.JWTToken = jwtToken
//				}
//			}
//			userEntityResult.ActiveDirectory = userEntity.ActiveDirectoryName
//			userEntityResult.AccessControl = userEntityPermission
//
//		} else {
//			return &userEntityResult, errors.New("User credentials is not valid!")
//		}
//	}
//	return &userEntityResult, nil
//}

var (
	SecretKey = []byte("A6CF26F631A6DEF753D4697834146")
)

func GetUserTokenInformation(adname string, country string) (string, error) {
	token := jwt.New(jwt.SigningMethodHS256)
	/* Create a map to store our claims */
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		panic("Something went wrong")
	}
	/* Set token claims */
	claims["loggedInAs"] = adname
	claims["country"] = country
	claims["iat"] = time.Now().Unix()
	claims["exp"] = time.Now().Add(time.Hour * 24).Unix()

	tokenString, err := token.SignedString(SecretKey)
	if err != nil {
		log.Fatal("Error in Generating key")
	}

	return tokenString, nil
}

func AuthenticateUserWithCountry(input *entity.Usercountry, adname string) (*entity.UserAuth, error) {
	var userEntityResult entity.UserAuth
	var userEntityPermission []entity.Permission
	var jwtToken string
	var resultErr error

	userEntityPermission, resultErr = GetUserForRegenerate(adname, input.Country)
	if resultErr != nil && resultErr.Error() == pgx.ErrNoRows.Error() {
		return &userEntityResult, errors.New("User does not exist in eZFlow!")
	} else if resultErr != nil {
		return &userEntityResult, resultErr
	} else {
		jwtToken, resultErr = GetUserTokenInformation(adname, input.Country)
		userEntityResult.JWTToken = jwtToken
	}
	userEntityResult.ActiveDirectory = adname
	userEntityResult.AccessControl = userEntityPermission

	return &userEntityResult, nil
}

func Combinations(set []string, n int) (subsets [][]string) {
	length := uint(len(set))

	if n > len(set) {
		n = len(set)
	}
	for subsetBits := 1; subsetBits < (1 << length); subsetBits++ {
		if n > 0 && bits.OnesCount(uint(subsetBits)) != n {
			continue
		}

		var subset []string

		for object := uint(0); object < length; object++ {
			if (subsetBits>>object)&1 == 1 {
				subset = append(subset, set[object])
			}
		}
		subsets = append(subsets, subset)
	}
	return subsets
}

func InsertConvertedCurrencyInDB() error {
	functionName := "InsertConvertedCurrencyInDB()"
	log.Printf("%s", functionName)
	codeValue := GetCodeValueByCatagory("ConversionList")
	codeValueArray := strings.Split(codeValue, ",")
	codeValueArray = append(codeValueArray, "US|USD")
	combinationArray := Combinations(codeValueArray, 2)
	for _, val := range combinationArray {
		index1 := strings.Split(val[0], "|")[1]
		index2 := strings.Split(val[1], "|")[1]
		err := InsertConvertedCurrency(index1, index2, val[0], val[1])
		if err != nil {
			return err
		}
	}

	return nil
}

func InsertConvertedCurrency(currency1 string, currency2 string, currencyCode1 string, currencyCode2 string) error {

	// functionName := "InsertConvertedCurrency()"
	// log.Printf("%s", functionName)
	var count int
	var count1 = 0
	var err error
	currentTime := time.Now()
	timeNow := currentTime.Format("20060102")
RETRY:
	resp, err := http.Get("https://zpprodapiportalapimgmt.apimanagement.ap1.hana.ondemand.com/prd/CV_ABAP_Reports_ExchangeRates/InputParam(Source_Currency='" + currency1 + "',Target_Currency='" + currency2 + "'%20,Current_Date='" + timeNow + "')/Result")
	if err != nil {
		if os.Getenv("CURRENCY_MAX_ATTEMPT") != "" {
			if count == 0 {
				count, err = strconv.Atoi(os.Getenv("CURRENCY_MAX_ATTEMPT"))
				if err != nil {
					log.Println(err)
					return err
				}
			}
		} else {
			count = 2
		}
		for i := count1; i < count; i++ {
			count1++
			goto RETRY
		}
		log.Println(err)
		return err
	}

	defer resp.Body.Close()
	bodyBytes, _ := ioutil.ReadAll(resp.Body)

	// Convert response body to string
	bodyString := string(bodyBytes)
	var q entity.Config
	xml.Unmarshal([]byte(bodyString), &q)
	codes := codeController.GetValueKeyCodes()["currency"]
	codes1 := codeController.GetValueKeyCodes()["country"]
	curr1 := codes[strings.ToLower(currencyCode1)].ID
	curr2 := codes[strings.ToLower(currencyCode2)].ID
	countryCode1 := codes1[strings.ToLower(currencyCode1)[0:2]].ID
	countryCode2 := codes1[strings.ToLower(currencyCode2)[0:2]].ID
	if q.Value != 0 {
		err = InsertCurrencyExchange(countryCode1, curr1, countryCode2, curr2, q.Value)
		if err != nil {
			log.Println(err)
			return err
		}
	}
	return nil
}

func InsertCurrencyExchange(sourceCountry int, sourceCurrency int, targetCountry int, targetCurrency int, targetValue float64) error {
	// functionName := "insertCurrencyExchange()"
	// log.Printf("%s", functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	query := `	insert into currency_exchange (country,currency_code,target_currency_code,target_rate,target_country) values ($1,$2,$3,$4,$5)`
	_, err = tx.Exec(context.Background(), query, sourceCountry, sourceCurrency, targetCurrency, targetValue, targetCountry)
	if err != nil {
		log.Println("%s - Error: %s", err.Error())
		return errors.New("Failed to insert data")
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return errors.New("Failed to commit controls data")
	}
	return err
}
