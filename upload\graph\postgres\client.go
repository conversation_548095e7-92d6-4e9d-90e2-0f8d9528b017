package postgres

import (
	"context"
	"log"
	"strings"

	"github.com/ihcp/upload/graph/entity"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	uuid "github.com/satori/go.uuid"
)

func UpsertExcelClient(entities []*entity.ClientExcelInput, userUUID uuid.UUID) {
	functionName := "UpsertExcelClient()"
	if pool == nil {
		pool = GetPool()
	}
	existingDivisions = map[int]map[string]uuid.UUID{}
	for _, item := range entities {
		tx, err := pool.Begin(context.Background())
		if err != nil {
			log.Printf("%s - Error: Failed to begin transaction", functionName)
			continue
		}
		defer tx.Rollback(context.Background())
		if *(item.ID) == uuid.Nil && ClientExistingCheckInDB(item.ClientName, item.CountryNo) {
			err := createClient(tx, item, userUUID)
			if err != nil {
				tx.Rollback(context.Background())
				log.Printf("%s - error: {%s}", functionName, err.Error())
			}
		} else if *(item.ID) != uuid.Nil && (ClientExistingCheckInDB(item.ClientName, item.CountryNo) || item.IsDeleted) {
			if *(item.ID) != uuid.Nil && item.CountryName == "" && item.ClientName == "" {
				err := deleteClient(tx, item, userUUID)
				if err != nil {
					tx.Rollback(context.Background())
					log.Printf("%s - error: {%s}", functionName, err.Error())
				}
			} else {
				err := updateClient(tx, item, userUUID)
				if err != nil {
					tx.Rollback(context.Background())
					log.Printf("%s - error: {%s}", functionName, err.Error())
				}
			}
		}

		txErr := tx.Commit(context.Background())
		if txErr != nil {
			log.Printf("%s - Error: Failed to commit Client data", functionName)
		}
	}
}
func updateClient(tx pgx.Tx, entity *entity.ClientExcelInput, userUUID uuid.UUID) error {
	if pool == nil {
		pool = GetPool()
	}
	var inputArg []interface{}
	query := `update product_owner set date_created=now(),last_modified=now(),is_active=?,is_deleted=?,country=? `
	inputArg = append(inputArg, entity.IsActive, entity.IsDeleted, entity.CountryNo)
	if strings.TrimSpace(entity.ClientName) != "" {
		query += ` ,owner_name=? `
		inputArg = append(inputArg, entity.ClientName)
	}
	query += ` WHERE id= ? `
	inputArg = append(inputArg, entity.ID)
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	_, err := tx.Exec(context.Background(), query, inputArg...)
	if err != nil {
		return err
	}
	return err

}
func createClient(tx pgx.Tx, entity *entity.ClientExcelInput, userUUID uuid.UUID) error {
	if pool == nil {
		pool = GetPool()
	}
	querystring := `INSERT INTO product_owner
	( date_created, last_modified, is_active, is_deleted, country, owner_name)
	VALUES( now(), now(), true, false, $1, $2);`
	_, err := tx.Exec(context.Background(), querystring, entity.CountryNo, entity.ClientName)
	if err != nil {
		return err
	}
	return err
}
func deleteClient(tx pgx.Tx, entity *entity.ClientExcelInput, userUUID uuid.UUID) error {
	functionName := "deleteMaterial()"
	log.Printf("%s", functionName)
	var err error
	query := `UPDATE product_owner set is_active=$2,is_deleted=$3,last_modified=now() where id=$1`
	_, err = tx.Exec(context.Background(), query, entity.ID, entity.IsActive, entity.IsDeleted)

	return err
}
func ClientExistingCheckInDB(clientName string, countryNo int) bool {
	if pool == nil {
		pool = GetPool()
	}
	querystring := "select 1 from product_owner po where po.country =$1 and lower(po.owner_name) =$2"
	var val int
	err := pool.QueryRow(context.Background(), querystring, countryNo, strings.ToLower(clientName)).Scan(&val)
	if err == nil {
		return false
	}
	return true
}
