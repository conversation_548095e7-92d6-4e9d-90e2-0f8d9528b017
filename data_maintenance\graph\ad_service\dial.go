package ad_service

import (
	"crypto/tls"
	"github.com/go-ldap/ldap/v3"
	"net"
	"os"
	"time"
)

func (s *Service) dial() {
	config := &config{
		internalLDAPURL:  os.<PERSON>env("INTERNAL_LDAP_URL"),
		internalBaseDN:   os.Getenv("INTERNAL_BASE_DN"),
		internalUsername: os.Getenv("INTERNAL_USER_NAME"),
		internalPassword: os.<PERSON>env("INTERNAL_PASSWORD"),
		externalLDAPURL:  os.<PERSON>env("EXTERNAL_LDAP_URL"),
		externalBaseDN:   os.<PERSON>env("EXTERNAL_BASE_DN"),
		externalUsername: os.<PERSON>env("EXTERNAL_USER_NAME"),
		externalPassword: os.Getenv("EXTERNAL_PASSWORD"),
	}
	var dialer = &net.Dialer{
		Timeout: 10 * time.Second,
	}

	var err error
	internalConn, err := ldap.DialURL(
		config.internalLDAPURL,
		ldap.DialWithTLSConfig(&tls.Config{
			InsecureSkipVerify: true,
		}),
		ldap.DialWithDialer(dialer),
	)
	if err != nil {
		panic(err)
	}

	externalConn, err := ldap.DialURL(
		config.externalLDAPURL,
		ldap.DialWithTLSConfig(&tls.Config{
			InsecureSkipVerify: true,
		}),
		ldap.DialWithDialer(dialer),
	)
	if err != nil {
		panic(err)
	}

	s.config = config
	s.internalConn = internalConn
	s.externalConn = externalConn
}
