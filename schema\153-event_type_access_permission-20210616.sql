 
    delete from event_type_access_permission where country = 8 

 
 
 INSERT INTO event_type_access_permission (user_role,country,is_active,is_deleted,created_by,last_modified,modified_by,event_type,user_role_id) VALUES
 ('{68,69,46,71,72}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 86, (select array(select id from user_roles where value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 92, (select array(select id from user_roles where value = 'marketaccessmanager' or  value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'medicalmanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
('{69,46,71,72}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 93, (select array(select id from user_roles where value = 'marketaccessmanager' or  value = 'productmanager' or value = 'salesmanager' or value = 'medicalmanager' or value = 'clustercomplianceofficer' or value = 'admin' and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 8, false, true, '00000000-0000-0000-0000-000000000000',NULL,NULL, 94, (select array(select id from user_roles where value = 'marketaccessmanager' or  value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'medicalmanager'  and is_active = true and is_deleted = false))),
('{47}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 96, (select array(select id from user_roles where value = 'bumanager' or  value = 'countrymedical' or value = 'countrycommercialsolutionhead' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
('{68,69,46,71,72}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 98,(select array(select id from user_roles where value = 'marketaccessmanager' or  value = 'productmanager' or value = 'salesrepresentative' or value = 'salesmanager' or value = 'medicalmanager' or value = 'clustercomplianceofficer' or value = 'admin'  and is_active = true and is_deleted = false))),
('{47,46,69}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 100, (select array(select id from user_roles where value = 'bumanager' or  value = 'productmanager' or value = 'clustercomplianceofficer' or value = 'salesmanager' or value = 'admin' or value = 'countrymedical' and is_active = true and is_deleted = false))),
('{69,47}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 101,(select array(select id from user_roles where value = 'bumanager' or  value = 'productmanager' and is_active = true and is_deleted = false))),
('{48,69,46,47}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 116, (select array(select id from user_roles where value = 'countrymedical' or  value = 'productmanager' or value = 'bumanager' or value = 'clustercomplianceofficer' and is_active = true and is_deleted = false))),
('{48,69,47}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 418,(select array(select id from user_roles where value = 'countrymedical' or  value = 'productmanager' or value = 'bumanager' and is_active = true and is_deleted = false))),
('{48}', 8, true, false, '00000000-0000-0000-0000-000000000000',NULL,NULL, 419, (select array(select id from user_roles where value = 'countrymedical' and is_active = true and is_deleted = false)));
 