package postgres

import (
	"context"
	"log"

	"github.com/ihcp/data_maintenance/graph/entity"
	"github.com/ihcp/data_maintenance/graph/logengine"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func GetTeamMemberIdByEmployeeAndTeamName(position, teamName, activeDirectory string, countryId int) (uuid.UUID, error) {
	functionName := "GetTeamMemberIdByEmployeeAndTeamName()"
	if pool == nil {
		pool = GetPool()
	}
	var teamMemberId uuid.UUID
	queryString := `SELECT tm.id from team_member tm 
                    inner join team t on t.id=tm.team 
                    inner join "user" emp on emp.id=tm.employee 
					WHERE  
					tm.v_position = $1 and emp.active_directory=$2 AND t.name = $3 AND t.country = $4`
	err := pool.QueryRow(context.Background(), queryString, position, activeDirectory, teamName, countryId).Scan(&teamMemberId)
	logengine.GetTelemetryClient().TrackEvent("GetTeamMemberIdByEmployeeAndTeamName query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return uuid.Nil, err
	}
	return teamMemberId, nil
}

func GetTeamMemberByEmployeeId(employeeId *uuid.UUID) *entity.TeamMember {
	functionName := "GetTeamMemberByEmployeeId()"
	if pool == nil {
		pool = GetPool()
	}
	var entity entity.TeamMember
	query := `SELECT id,v_position,team from team_member where employee = $1 AND is_deleted=false AND is_active=true`
	err := pool.QueryRow(context.Background(), query, employeeId).Scan(&entity.ID, &entity.Position, &entity.Team)
	logengine.GetTelemetryClient().TrackEvent("GetTeamMemberByEmployeeId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil
	}

	return &entity
}

func UpsertTeamMemberForExcel(tx pgx.Tx, entity *entity.TeamMember, userUUID uuid.UUID) error {
	functionName := "UpsertTeamMemberForExcel()"
	log.Println(functionName)
	query := `INSERT INTO team_member (team,employee,v_position,is_active,created_by) VALUES ($1,$2,$3,$4,$5)`

	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.Team, entity.Employee, entity.Position, entity.IsActive, userUUID)

	_, err := tx.Exec(context.Background(), query, inputArgs...)
	logengine.GetTelemetryClient().TrackEvent("UpsertTeamMemberForExcel query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err)
		return err
	}
	return nil
}

func deleteTeamMemberByEmployeeAndTeamId(tx pgx.Tx, teamId, employeeId *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "deleteTeamMemberByEmployeeId()"
	log.Println(functionName)
	query := `UPDATE team_member set is_deleted=true,is_active=false,modified_by=$3,last_modified=now() where employee=$1 AND team=$2`
	_, err := tx.Exec(context.Background(), query, employeeId, teamId, userUUID)
	logengine.GetTelemetryClient().TrackEvent("deleteTeamMemberByEmployeeAndTeamId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	return nil
}

func updateTeamMember(tx pgx.Tx, entity *entity.TeamMember, id *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "updateTeamMember()"
	log.Println(functionName)
	query := `UPDATE team_member set v_position=$2,employee=$3,team=$4,modified_by=$5,last_modified=now() where id = $1`
	_, err := tx.Exec(context.Background(), query, id, entity.Position, entity.Employee, entity.Team, userUUID)
	logengine.GetTelemetryClient().TrackEvent("updateTeamMember query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	return nil
}

func employeeCountForTeam(teamId uuid.UUID) (int, error) {
	functionName := "employeeCountForTeam()"
	if pool == nil {
		pool = GetPool()
	}
	count := 0
	query := `select count(id) from team_member where team=$1 and is_deleted=false`
	err := pool.QueryRow(context.Background(), query, teamId).Scan(&count)
	logengine.GetTelemetryClient().TrackEvent("employeeCountForTeam query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	return count, err
}

func GetTeamMemberIDByEmployeeIDAndTeamID(employeeID *uuid.UUID, teamID *uuid.UUID, position string) (*uuid.UUID, error) {
	functionName := "GetTeamMemberIDByEmployeeIDAndTeamID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var teamMemberID uuid.UUID
	query := `SELECT id from team_member where team = $1 AND employee = $2 AND v_position = $3 AND is_deleted = false AND is_active = true`
	err := pool.QueryRow(context.Background(), query, employeeID, teamID, position).Scan(&teamMemberID)
	logengine.GetTelemetryClient().TrackEvent("GetTeamMemberIDByEmployeeIDAndTeamID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &teamMemberID, nil
}
