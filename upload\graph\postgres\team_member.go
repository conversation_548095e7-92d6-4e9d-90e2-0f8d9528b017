package postgres

import (
	"context"
	"log"

	"github.com/ihcp/upload/graph/entity"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func GetTeamMemberByTeamEmployee(teamID *uuid.UUID, employeeID *uuid.UUID) (uuid.UUID, error) {
	functionName := "GetTeamMemberByTeamEmployee()"
	if pool == nil {
		pool = GetPool()
	}
	var teamMemberID uuid.UUID
	queryString := `SELECT id FROM team_member WHERE team = $1 AND employee = $2 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), queryString, teamID, employeeID).Scan(&teamMemberID)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return uuid.Nil, err
	}
	return teamMemberID, nil
}

func GetTeamByTeamName(teamName string, countryId int) (uuid.UUID, error) {
	functionName := "GetTeamByTeamName()"
	if pool == nil {
		pool = GetPool()
	}
	var teamID uuid.UUID
	queryString := `SELECT team.id FROM team WHERE name = $1 AND country = $2 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), queryString, teamName, countryId).Scan(&teamID)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return uuid.Nil, err
	}
	return teamID, nil
}

func GetTeamMemberIdByEmployeeAndTeamName(position, teamName, activeDirectory string, countryId int) (uuid.UUID, error) {
	functionName := "GetTeamMemberIdByEmployeeAndTeamName()"
	if pool == nil {
		pool = GetPool()
	}
	var teamMemberId uuid.UUID
	queryString := `SELECT tm.id from team_member tm 
                    inner join team t on t.id=tm.team 
                    inner join "user" emp on emp.id=tm.employee 
					WHERE  
					tm.v_position = $1 and emp.active_directory=$2 AND t.name = $3 AND t.country = $4`
	err := pool.QueryRow(context.Background(), queryString, position, activeDirectory, teamName, countryId).Scan(&teamMemberId)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return uuid.Nil, err
	}
	return teamMemberId, nil
}

func GetTeamMemberByEmployeeId(employeeId *uuid.UUID) *entity.TeamMember {
	functionName := "GetTeamMemberByEmployeeId()"
	if pool == nil {
		pool = GetPool()
	}
	var entity entity.TeamMember
	query := `SELECT id,v_position,team from team_member where employee = $1 AND is_deleted=false AND is_active=true`
	err := pool.QueryRow(context.Background(), query, employeeId).Scan(&entity.ID, &entity.Position, &entity.Team)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil
	}

	return &entity
}

func UpsertTeamMemberForExcel(tx pgx.Tx, entity *entity.TeamMember, userUUID uuid.UUID) error {
	functionName := "UpsertTeamMember()"
	log.Println(functionName)
	query := `INSERT INTO team_member (team,employee,v_position,is_active,created_by) VALUES ($1,$2,$3,$4,$5)`

	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.Team, entity.Employee, entity.Position, entity.IsActive, userUUID)
	_, err := tx.Exec(context.Background(), query, inputArgs...)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err)
		return err
	}
	return nil
}

func deleteTeamMemberByEmployeeAndTeamId(tx pgx.Tx, teamId, employeeId *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "deleteTeamMemberByEmployeeId()"
	log.Println(functionName)
	query := `UPDATE team_member set is_deleted=true,is_active=false,modified_by=$3,last_modified=now() where employee=$1 AND team=$2`
	_, err := tx.Exec(context.Background(), query, employeeId, teamId, userUUID)

	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	return nil
}

func updateTeamMember(tx pgx.Tx, entity *entity.TeamMember, id *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "updateTeamMember()"
	log.Println(functionName)
	query := `UPDATE team_member set v_position=$2,employee=$3,team=$4,modified_by=$5,last_modified=now() where id = $1`
	_, err := tx.Exec(context.Background(), query, id, entity.Position, entity.Employee, entity.Team, userUUID)

	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	return nil
}

func employeeCountForTeam(teamId uuid.UUID) (int, error) {
	functionName := "employeeCountForTeam()"
	if pool == nil {
		pool = GetPool()
	}
	count := 0
	query := `select count(id) from team_member where team=$1 and is_deleted=false`
	err := pool.QueryRow(context.Background(), query, teamId).Scan(&count)

	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	return count, err
}
