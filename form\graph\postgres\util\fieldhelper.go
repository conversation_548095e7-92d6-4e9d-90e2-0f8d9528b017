package util

import (
	"bytes"
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"github.com/ihcp/form/graph/teams_service"
	"github.com/ihcp/mail_service"
	"html/template"
	"io"
	"log"

	"math"
	"net/http"
	"os"
	"path"
	"strconv"
	"strings"
	"time"

	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	suuid "github.com/satori/go.uuid"
	"gopkg.in/gomail.v2"
)

const emailLink = "Please click here to <a href='https://ezflow.zuelligpharma.com/'>login</a>"

func UUIDV4ToString(id [16]byte) string {
	u, err := suuid.FromBytes(id[:])
	if err != nil {
		log.Println(err.Error())
	}
	return u.String()
}

func Date(date string) string {
	t, _ := time.Parse(time.RFC3339, date)
	// fmt.Println("MM/DD/YYYY : \n", parse_time.Format("01/02/2006"))
	return t.Format("01/02/2006")
}

func BytesToUUIDV4(id [16]byte) suuid.UUID {
	u, err := suuid.FromBytes(id[:])
	if err != nil {
		log.Println("Failed to parse UUID from byte array")
	}
	return u
}

func StringToUUID4(uuidstr string) suuid.UUID {
	uid, err := suuid.FromString(uuidstr)
	if err != nil {
		log.Printf("Something went wrong: %s", err)
	}
	return uid
}

func IsValidDate(date string) error {
	const (
		layoutISO = "01/02/2006"
		layoutUS  = "2 January, 2006"
	)
	_, err := time.Parse(layoutISO, date)
	if err != nil {
		log.Println(err)
	}
	return err
}

func GetCurrentTime() time.Time {
	timeNow := time.Now().UTC().Add(0 * time.Hour)
	return timeNow
}

func GetTimeUnixTimeStamp(date time.Time) int {
	timestamp := int(date.Unix())
	return timestamp
}

func ConvertStringToTime(date string) time.Time {
	const (
		layoutISO = "01/02/2006 15:04"
	)
	dateTime, _ := time.Parse(layoutISO, date)
	return dateTime
}

//func SendMailCustomised(addr, from, subject, body string, to []string) error {
//	smtpconfig := strings.Split(addr, ":")
//	r := strings.NewReplacer("\r\n", "", "\r", "", "\n", "", "%0a", "", "%0d", "")
//
//	for i := range to {
//		to[i] = r.Replace(to[i])
//	}
//
//	if smtpconfig == nil {
//		panic("smtp server not found !!")
//	}
//
//	smtpPort, err := strconv.Atoi(smtpconfig[1])
//	if err != nil {
//		return err
//	}
//	m := gomail.NewMessage()
//	m.SetHeader("From", from)
//	m.SetHeader("To", to...)
//	m.SetHeader("Subject", subject)
//	m.SetBody("text/html", body)
//	d := gomail.NewDialer(smtpconfig[0], smtpPort, "", "")
//	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
//	if err := d.DialAndSend(m); err != nil {
//		log.Println(err)
//		return err
//	}
//
//	return nil
//}

func SendEmailScenarioOne(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}

	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/requestApproval.html")
	if err != nil {
		panic(err)
	}
	var buffer bytes.Buffer

	var eventCode, requestorName, activityName, eventName, teamName, eventDate, siteUrl, apolloUrl string
	var totalCost float64
	for _, item := range data {
		eventCode = item.EventCode
		requestorName = item.RequesterName
		activityName = item.ActivityName
		eventName = item.EventName
		teamName = item.Team
		eventDate = item.EventDate
		totalCost = item.TotalCost
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		Requestor    string
		ActivityName string
		EventName    string
		TeamName     string
		EventDate    string
		TotalCost    float64
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		Requestor:    requestorName,
		ActivityName: activityName,
		EventName:    eventName,
		TeamName:     teamName,
		EventDate:    eventDate,
		TotalCost:    totalCost,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event requires your approval"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	//code = codeController.GetTitleKeyCodes()["serverip"]
	//serverIP := code["ServerIP"].Value
	//err := SendMailCustomised(serverIP, from, subject, buffer.String(), to)
	//if err != nil {
	//	fmt.Println(err)
	//	return ""
	//}

	return htmlContent
}

func SendEmailScenarioTwo(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}

	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/requestorResponse.html")
	if err != nil {
		panic(err)
	}
	var buffer bytes.Buffer

	var eventCode, activityName, eventDate, siteUrl, apolloUrl string
	var totalCost float64
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		eventDate = item.EventDate
		totalCost = item.TotalCost
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		EventDate    string
		TotalCost    float64
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		EventDate:    eventDate,
		TotalCost:    totalCost,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event requires your response"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func SendEmailScenarioThree(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/resubmitted.html")
	if err != nil {
		panic(err)
	}

	var buffer bytes.Buffer

	var eventCode, activityName, teamName, eventDate, siteUrl, apolloUrl string
	var totalCost float64
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		teamName = item.Team
		eventDate = item.EventDate
		totalCost = item.TotalCost
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		TeamName     string
		EventDate    string
		TotalCost    float64
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		TeamName:     teamName,
		EventDate:    eventDate,
		TotalCost:    totalCost,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event has been resubmitted"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func SendEmailScenarioFourA(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/rejected.html")
	if err != nil {
		panic(err)
	}
	var buffer bytes.Buffer

	var eventCode, activityName, siteUrl, apolloUrl string
	var totalCost float64
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		totalCost = item.TotalCost
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		TotalCost    float64
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		TotalCost:    totalCost,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "We regret to inform you that the following event has been rejected"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func SendMailForVeevaEventFailure(bloburl string, filename string, uploadUrlProduct string, filenameProduct string, uploadUrlCustomer string, filenameCustomer string, uploadUrlUser string, filenameUser string, uploadUrlAttendance string, filenameAttendance string) error {
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value
	code = codeController.GetTitleKeyCodes()["serverip"]
	addr := code["ServerIP"].Value
	subject := "Veeva Fail Event And Product , User and Customer  on Date " + time.Now().Format("2006-01-02 15:04:05")
	to := strings.Split(os.Getenv("VEEVA_EMAIL_ALEART_TO_MAIL"), ",")
	cc := strings.Split(os.Getenv("VEEVA_EMAIL_ALEART_CC_MAIL"), ",")
	excelData, _ := DownloadExcelFile(bloburl)
	excelDataProduct, _ := DownloadExcelFile(uploadUrlProduct)
	excelDataCustomer, _ := DownloadExcelFile(uploadUrlCustomer)
	excelDataUser, _ := DownloadExcelFile(uploadUrlUser)
	excelDataAttendance, _ := DownloadExcelFile(uploadUrlAttendance)
	smtpconfig := strings.Split(addr, ":")
	r := strings.NewReplacer("\r\n", "", "\r", "", "\n", "", "%0a", "", "%0d", "")

	for i := range to {
		to[i] = r.Replace(to[i])
	}
	for i := range cc {
		cc[i] = r.Replace(cc[i])
	}

	if smtpconfig == nil {
		return errors.New("smtp not found !!")
	}
	smtpPort, err := strconv.Atoi(smtpconfig[1])
	if err != nil {
		return err
	}
	m := gomail.NewMessage()
	m.SetHeader("From", from)
	m.SetHeader("To", to...)
	m.SetHeader("Subject", subject)
	m.SetHeader("Cc", cc...)
	m.SetBody("text/plain", "Please find attachment for Veeva fail events.")

	// Attach the Excel file
	m.Attach(filename, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(excelData)
		return err
	}))
	m.Attach(filenameProduct, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(excelDataProduct)
		return err
	}))
	m.Attach(filenameCustomer, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(excelDataCustomer)
		return err
	}))
	m.Attach(filenameUser, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(excelDataUser)
		return err
	}))
	m.Attach(filenameAttendance, gomail.SetCopyFunc(func(w io.Writer) error {
		_, err := w.Write(excelDataAttendance)
		return err
	}))
	d := gomail.NewDialer(smtpconfig[0], smtpPort, "", "")
	d.TLSConfig = &tls.Config{InsecureSkipVerify: true}
	if err := d.DialAndSend(m); err != nil {
		log.Println(err, cc, to)
		return err
	}
	return nil
}

func DownloadExcelFile(blobURL string) ([]byte, error) {
	response, err := http.Get(blobURL)
	if err != nil {
		return nil, err
	}
	defer response.Body.Close()

	data, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func SendEmailScenarioFive(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}

	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/approved.html")
	if err != nil {
		panic(err)
	}

	var buffer bytes.Buffer

	var eventCode, activityName, siteUrl, apolloUrl string
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event has been approved"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func SendEmailScenarioSix(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)

	t, err := template.ParseFiles(rootPath + "/templates/returned.html")
	if err != nil {
		panic(err)
	}

	var buffer bytes.Buffer

	var eventCode, activityName, siteUrl, apolloUrl string
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event has been Returned"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func SendEmailToRequesterAfterEventSubmission(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}

	e, err := os.Executable()
	if err != nil {
		panic(err)
	}

	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/eventSubmit.html")
	if err != nil {
		panic(err)
	}

	var eventCode, apolloUrl, siteUrl string
	for _, item := range data {
		eventCode = item.EventCode
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}

	var buffer bytes.Buffer
	if err := t.Execute(&buffer, struct {
		EventCode string
		SiteUrl   string
		ApolloUrl string
	}{
		EventCode: eventCode,
		SiteUrl:   siteUrl,
		ApolloUrl: apolloUrl,
	}); err != nil {
		panic(err)
	}

	subject := "Thank you, you have successfully submitted the event for approval"
	htmlContent := buffer.String()
	client := mail_service.New()

	go func() {
		if err := client.SendEmailToMany(context.Background(),
			from, to, subject, "", htmlContent, nil); err != nil {
			teams_service.Send(context.Background(),
				fmt.Sprintf(`Error happened when sending email for %s`, "event submission"), err.Error(), "")

		}
	}()

	return htmlContent
}

func SendEmailCronForApproverReminder(emailID string, data []*entity.EmailStruct, approverName string) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/reminder.html")
	if err != nil {
		panic(err)
	}
	var buffer bytes.Buffer

	var eventCode, submittedOn, siteUrl, apolloUrl string
	for _, item := range data {
		eventCode = item.EventCode
		submittedOn = item.EventSubmittedDate
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}

	t.Execute(&buffer, struct {
		Name        string
		EventCode   string
		SubmittedOn string
		SiteUrl     string
		ApolloUrl   string
	}{
		EventCode:   eventCode,
		Name:        approverName,
		SubmittedOn: submittedOn,
		SiteUrl:     siteUrl,
		ApolloUrl:   apolloUrl,
	})
	subject := "Friendly reminder for event action"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func EventCompletionEmailToApprovers(emailID string, data []*entity.EmailStruct) string {
	// Sender data.
	code := codeController.GetTitleKeyCodes()["smtpconfig"]
	from := code["SMTP Email"].Value

	// Receiver email address.
	to := []string{
		emailID,
	}
	e, err := os.Executable()
	if err != nil {
		panic(err)
	}
	rootPath := path.Dir(e)
	t, err := template.ParseFiles(rootPath + "/templates/completed.html")
	if err != nil {
		panic(err)
	}

	var buffer bytes.Buffer

	var eventCode, activityName, siteUrl, apolloUrl string
	for _, item := range data {
		eventCode = item.EventCode
		activityName = item.ActivityName
		siteUrl = item.SiteURL
		apolloUrl = item.ApolloUrl
	}
	t.Execute(&buffer, struct {
		EventCode    string
		ActivityName string
		SiteUrl      string
		ApolloUrl    string
	}{
		EventCode:    eventCode,
		ActivityName: activityName,
		SiteUrl:      siteUrl,
		ApolloUrl:    apolloUrl,
	})
	subject := "The following event has been completed"
	htmlContent := buffer.String()

	client := mail_service.New()
	if err := client.SendEmailToMany(context.Background(),
		from, to, subject, "", htmlContent, nil); err != nil {
		panic(err)
	}

	return htmlContent
}

func ConvertToEventID(eventCode string, eventSeq int) string {
	formAnsSeq := PadNumberWithZero(eventSeq)
	eventID := eventCode + "-" + formAnsSeq
	return eventID
}

func ValidateURL(URL string) (bool, string) {
	var valid bool = false
	var fileName string
	if strings.Contains(strings.ToLower(URL), "http") || strings.Contains(strings.ToLower(URL), "https") {
		valid = true
		splitedURL := strings.Split(URL, "/")
		length := len(splitedURL)
		fileName = splitedURL[length-1]
	}
	return valid, fileName
}

func PadNumberWithZero(value int) string {
	return fmt.Sprintf("%04d", value)
}

func ConvertLocalCurrencyToUSD(localAmount float64, exchangeRate float64) (float64, error) {
	if exchangeRate <= 0 {
		return 0, errors.New("Exchange rate can't be equal or lesser than 0.")
	}
	convertedAmount := localAmount * exchangeRate
	return math.Ceil(convertedAmount), nil
}
