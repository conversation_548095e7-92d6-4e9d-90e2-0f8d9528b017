package postgres

import (
	"context"
	"errors"
	"log"

	"github.com/ihcp/upload/graph/entity"
	"github.com/ihcp/upload/graph/logengine"
	"github.com/ihcp/upload/graph/model"
	"github.com/jackc/pgx/v4"
	uuid "github.com/satori/go.uuid"
)

func UpsertExcelEmployee(entities []*entity.EmployeeExcelInput, uploadId *uuid.UUID, userUUID uuid.UUID) {
	functionName := "UpsertExcelEmployee()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var usermanagementlogsMessage string
	// existingTeams = map[int]map[string]uuid.UUID{}
	deletedTeams := make(map[uuid.UUID]int)
	for index, item := range entities {
		tx, err := pool.Begin(context.Background())
		if err != nil {
			log.Printf("%s - Error: Failed to begin transaction", functionName)
		}
		defer tx.Rollback(context.Background())
		teamId := item.TeamID
		if *(item.ID) == uuid.Nil {
			err := createEmployee(tx, item, teamId, userUUID)
			if err != nil {
				tx.Rollback(context.Background())
				return
			}
		} else {
			if item.IsActive {
				if !CheckActiveUserWithRespectToIdForBulkUpload(item.IsActive, item.IsDeleted, item.ID) {
					usermanagementlogsMessage = "Activate"
				} else {
					usermanagementlogsMessage = "Modified"
				}
			} else {
				usermanagementlogsMessage = "Deactivate"
			}

			if item.ID != nil && item.TeamName == "" && item.Position == "" && item.EmployeeCode == "" && item.FirstName == "" && item.LastName == "" && item.VeevaReferenceId == "" {
				err := deleteEmployee(tx, item, userUUID)
				if err != nil {
					tx.Rollback(context.Background())
					log.Printf("%s - error: {%s}", functionName, err.Error())
					return
				}
			} else {
				err := updateEmployee(tx, item, teamId, index, uploadId, userUUID, usermanagementlogsMessage)
				if err != nil {
					tx.Rollback(context.Background())
					return
				}
			}
		}

		// else {
		// 	team_member := GetTeamMemberByEmployeeId(item.ID)
		// 	var teamId uuid.UUID
		// 	if team_member != nil {
		// 		teamId = *team_member.Team
		// 	}
		// 	if teamId != uuid.Nil {
		// 		err := deleteEmployee(tx, item, &teamId, userUUID)
		// 		if err != nil {
		// 			tx.Rollback(context.Background())
		// 			log.Printf("%s - error: {%s}", functionName, err.Error())
		// 			return
		// 		}
		// 		_, isExits := deletedTeams[teamId]
		// 		if !isExits {
		// 			deletedTeams[teamId] = 0
		// 		}
		// 	}
		// }

		txErr := tx.Commit(context.Background())
		if txErr != nil {
			log.Printf("%s - Error: Failed to commit employee data", functionName)
		}
	}

	if len(deletedTeams) > 0 {
		for teamID, _ := range deletedTeams {
			count, err := employeeCountForTeam(teamID)
			if err != nil && count == 0 {
				deleteTeam(teamID)
			}
		}
	}
}

func CheckActiveUserWithRespectToIdForBulkUpload(IsActive bool, IsDeleted bool, ID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result int
	query := `select 1 from "user" u where u.is_active =$1 and u.is_deleted =$2 and u.id =$3`
	err := pool.QueryRow(context.Background(), query, IsActive, IsDeleted, ID).Scan(&result)
	if err != nil {
		return false
	}
	return true
}

func createEmployee(tx pgx.Tx, item *entity.EmployeeExcelInput, teamId *uuid.UUID, userUUID uuid.UUID) error {
	functionName := "createEmployee()"
	var employeeID uuid.UUID
	query := `INSERT INTO "user" (first_name,last_name,country,employee_code,is_active,is_deleted,created_by,active_directory,approval_role,email,user_role_id,veeva_reference_id) VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12) RETURNING(id)`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, item.FirstName, item.LastName, item.Country, item.EmployeeCode, item.IsActive, item.IsDeleted, userUUID, item.ActiveDirectory, 0, item.Email, item.ApprovalRole, item.VeevaReferenceId)
	err := tx.QueryRow(context.Background(), query, inputArgs...).Scan(&employeeID)
	if err != nil {
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return err
	}
	CreateUsermanagementlogsBulkUploadData(tx, &employeeID, &item.ApprovalRole, item.FirstName, item.LastName, item.Email, item.ActiveDirectory, item.EmployeeCode, userUUID, "Created")
	if teamId != nil {
		return UpsertTeamMemberForExcel(tx, mapTeamMember(teamId, &employeeID, item.Position), userUUID)
	}
	return err

}

func updateEmployee(tx pgx.Tx, entity *entity.EmployeeExcelInput, teamId *uuid.UUID, index int, uploadId *uuid.UUID, userUUID uuid.UUID, usermanagementlogsMessage string) error {
	functionName := "updateEmployee()"
	log.Printf("%s", functionName)
	var validationResult model.ValidationResult
	previousTeamMember := GetTeamMemberByEmployeeId(entity.ID)
	query := `UPDATE "user" set first_name=$2,last_name=$3,country=$4,employee_code=$5,is_active=$6,modified_by=$7,last_modified=now(), email = $8 ,user_role_id = $9,veeva_reference_id=$10 where id=$1 AND is_deleted = false`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.ID, entity.FirstName, entity.LastName, entity.Country, entity.EmployeeCode, entity.IsActive, userUUID, entity.Email, entity.ApprovalRole, entity.VeevaReferenceId)
	commandTag, err := tx.Exec(context.Background(), query, inputArgs...)
	if err != nil {
		return err
	}

	if commandTag.RowsAffected() != 1 {
		err = errors.New("invalid id")
		if uploadId != &uuid.Nil && index < 0 {
			errorMessage := &model.ExcelValidationMessage{Row: (index + 1), Message: err.Error()}
			validationResult.ExcelValidationMessages = append(validationResult.ExcelValidationMessages, errorMessage)
			InsertUploadValidationErrors(nil, &validationResult, uploadId, uuid.UUID{})
		}
		return err
	}
	CreateUsermanagementlogsBulkUploadData(tx, entity.ID, &entity.ApprovalRole, entity.FirstName, entity.LastName, entity.Email, entity.ActiveDirectory, entity.EmployeeCode, userUUID, usermanagementlogsMessage)
	if previousTeamMember != nil && teamId != nil {
		if previousTeamMember.Team.String() != teamId.String() {
			err := deleteTeamMemberByEmployeeAndTeamId(tx, previousTeamMember.Team, entity.ID, userUUID)
			if err != nil {
				return err
			}
			return UpsertTeamMemberForExcel(tx, mapTeamMember(teamId, entity.ID, entity.Position), userUUID)
		} else if previousTeamMember.Position != entity.Position {
			return updateTeamMember(tx, mapTeamMember(teamId, entity.ID, entity.Position), previousTeamMember.ID, userUUID)
		}
	} else if previousTeamMember == nil && teamId != nil {
		return UpsertTeamMemberForExcel(tx, mapTeamMember(teamId, entity.ID, entity.Position), userUUID)
	}
	return nil
}

func deleteEmployee(tx pgx.Tx, entity *entity.EmployeeExcelInput, userUUID uuid.UUID) error {
	functionName := "deleteEmployee()"
	log.Printf("%s \n", functionName)
	query := `UPDATE "user" set is_active = $3 , is_deleted = $4,modified_by=$2,last_modified=now() where id=$1`
	var inputArgs []interface{}
	inputArgs = append(inputArgs, entity.ID, userUUID, entity.IsActive, entity.IsDeleted)
	_, err := tx.Exec(context.Background(), query, inputArgs...)
	if err != nil {
		return err
	}
	CreateUsermanagementlogsBulkUploadData(tx, entity.ID, &entity.ApprovalRole, entity.FirstName, entity.LastName, entity.Email, entity.ActiveDirectory, entity.EmployeeCode, userUUID, "Deactivate")
	// if teamID != nil {
	// 	return deleteTeamMemberByEmployeeAndTeamId(tx, teamID, entity.ID, userUUID)
	// }
	return err
}

func CreateUsermanagementlogsBulkUploadData(tx pgx.Tx, employeeID *uuid.UUID, userRoleID *string, firstName string, lastName string, email string, activeDirectory string, employeeCode string, userUUID uuid.UUID, usermanagementlogsMessage string) {
	functionName := "CreateUsermanagementlogsBulkUploadData"
	querystring := `INSERT INTO user_management_logs
	(user_id, user_role, first_name, last_name, email, active_directory, employee_code, action_by, action_type, action_date)
	VALUES( $1, $2, $3, $4, $5, $6, $7, $8, $9, now());`
	_, err := tx.Exec(context.Background(), querystring, employeeID, userRoleID, firstName, lastName, email, activeDirectory, employeeCode, userUUID, usermanagementlogsMessage)
	logengine.GetTelemetryClient().TrackEvent("CreateUsermanagementlogsData query called")
	if err != nil {
		log.Printf("%s - error: {%s}  %s", functionName, err.Error())
	}
}

func CheckExistingTeams(entity *entity.EmployeeExcelInput) *uuid.UUID {
	functionName := "checkExistingTeams()"
	existingTeams := make(map[int]map[string]uuid.UUID)
	log.Printf("%s \n", functionName)
	var err error
	teamID := uuid.Nil
	_, countryExists := existingTeams[entity.Country]
	if !countryExists {
		teamID, err = handleTeam(entity, entity.Country)
		if err != nil {
			return &uuid.Nil
		}
		existingTeams[entity.Country] = map[string]uuid.UUID{
			entity.TeamName: teamID,
		}
	} else {
		_, teamExists := existingTeams[entity.Country][entity.TeamName]
		if !teamExists {
			teamID, err = handleTeam(entity, entity.Country)
			if err != nil {
				return &uuid.Nil
			}
			existingTeams[entity.Country][entity.TeamName] = teamID
		}
		teamID = existingTeams[entity.Country][entity.TeamName]
	}
	log.Printf("%s - TeamId: %v ", functionName, teamID)
	return &teamID
}

func handleTeam(input *entity.EmployeeExcelInput, countryId int) (uuid.UUID, error) {
	functionName := "handleTeam()"
	log.Println(functionName)
	id, err := GetTeamIdByCountryAndName(int64(countryId), input.TeamName)
	teamID := uuid.Nil
	if err != nil {
		teamID, err := UpsertTeamForExcel(mapTeamFromEmployee(input.TeamName, int64(countryId)))

		if err != nil {
			return uuid.Nil, err
		}
		return *teamID, nil
	} else {
		teamID = *id
		return teamID, nil
	}
}

func mapTeamMember(teamId, employeeId *uuid.UUID, position string) *entity.TeamMember {
	id := uuid.NewV4()
	return &entity.TeamMember{
		ID:        &id,
		Team:      teamId,
		Employee:  employeeId,
		Position:  position,
		IsActive:  true,
		IsDeleted: false,
	}
}

func mapTeamFromEmployee(teamName string, countryId int64) *entity.Team {
	id := uuid.NewV4()
	return &entity.Team{
		ID:        &id,
		Name:      teamName,
		Country:   countryId,
		IsActive:  true,
		IsDeleted: false,
		CreatedBy: nil,
	}
}

func GetEmployeeIdByActiveDirName(activeDirectoryName string) (*uuid.UUID, error) {
	functionName := "GetEmployeeIdByActiveDir()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var employeeID uuid.UUID
	query := `SELECT id from "user" where active_directory = $1 AND is_deleted = false AND is_active = true`
	err := pool.QueryRow(context.Background(), query, activeDirectoryName).Scan(&employeeID)
	if err != nil {
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &employeeID, nil
}

func hasEmployeeID(employeeID *uuid.UUID) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	queryString := `select 1 from "user" where id = $1 AND is_active = true AND is_deleted = false`
	var hasValue int
	err := pool.QueryRow(context.Background(), queryString, employeeID).Scan(&hasValue)
	if err == nil {
		result = true
	} else {
		result = false
	}
	return result
}

// func UpsertEmployee(inputEntity *entity.Employee) (err error, message string) {
// 	functionName := "UpsertEmployee()"
// 	message = ""
// 	tx, err := pool.Begin(context.Background())
// 	if err != nil {
// 		log.Printf("%s - error: {%s}", functionName, err.Error())
// 		return errors.New("Failed to begin transaction"), message
// 	}
// 	defer tx.Rollback(context.Background())
// 	existingTeams = map[int]map[string]uuid.UUID{}
// 	countries := codeController.GetIdKeyCodes()["country"]
// 	excelInputEntity := entity.EmployeeExcelInput{
// 		Country:         countries[inputEntity.Country].ID,
// 		TeamName:        inputEntity.Team,
// 		Position:        inputEntity.Position,
// 		FirstName:       inputEntity.FirstName,
// 		LastName:        inputEntity.LastName,
// 		EmployeeCode:    inputEntity.EmployeeCode,
// 		ActiveDirectory: inputEntity.ActiveDirectory,
// 	}
// 	if !inputEntity.IsDeleted {
// 		var teamID *uuid.UUID
// 		if excelInputEntity.TeamName != "" {
// 			teamID = checkExistingTeams(tx, &excelInputEntity)
// 		}
// 		if inputEntity.ID == nil {
// 			excelInputEntity.IsActive = true
// 			excelInputEntity.IsDeleted = false
// 			err := createEmployee(tx, &excelInputEntity, teamID, userUUID)
// 			if err != nil {
// 				tx.Rollback(context.Background())
// 				log.Printf("%s - error: {%s}", functionName, err.Error())
// 				return err, message
// 			}
// 			message = "Record created successfully"
// 		} else {
// 			excelInputEntity.ID = inputEntity.ID
// 			excelInputEntity.IsActive = true
// 			excelInputEntity.IsDeleted = false
// 			err := updateEmployee(tx, &excelInputEntity, teamID, -1, &uuid.Nil)
// 			if err != nil {
// 				tx.Rollback(context.Background())
// 				log.Printf("%s - error: {%s}", functionName, err.Error())
// 				return err, message
// 			}
// 			message = "Record updated successfully"
// 		}
// 	} else {
// 		if inputEntity.ID != nil {
// 			excelInputEntity.ID = inputEntity.ID
// 			teamMember := GetTeamMemberByEmployeeId(inputEntity.ID)
// 			var teamID uuid.UUID
// 			if teamMember != nil {
// 				teamID = *teamMember.Team
// 			}
// 			err := deleteEmployee(tx, &excelInputEntity, &teamID)
// 			if err != nil {
// 				tx.Rollback(context.Background())
// 				log.Printf("%s - error: {%s}", functionName, err.Error())
// 				return err, message
// 			}
// 			message = "Record deleted successfully"
// 		} else {
// 			return errors.New("Please provide employee ID"), message
// 		}
// 	}

// 	txErr := tx.Commit(context.Background())
// 	if txErr != nil {
// 		log.Printf("%s - error: {%s}", functionName, err.Error())
// 		return errors.New("Failed to commit employee data"), message
// 	}
// 	return err, message
// }

func GetUserCountryByActiveDir(userID uuid.UUID) (int, error) {
	functionName := "GetUserCountryByActiveDir()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var countryID int
	query := `SELECT country FROM "user" WHERE id = $1 AND is_active = true AND is_deleted = false`
	err := pool.QueryRow(context.Background(), query, userID).Scan(&countryID)
	if err != nil {
		log.Printf("%s - err: %s", functionName, err.Error())
		return 0, err
	}
	return countryID, nil
}

func CheckActivDirNameInUser(activeDir string, userID *uuid.UUID, country int) bool {
	functionName := "CheckActivDirNameInUser()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	if userID != nil {
		querystring := `select 1 from "user" ur where upper(ur.active_directory)  = upper($1)  and ur.country = $2 and ur.id <> $3
		and ur.is_deleted = false`
		var hasValue int
		err := pool.QueryRow(context.Background(), querystring, activeDir, country, *userID).Scan(&hasValue)
		if err == nil && hasValue > 0 {
			result = true
		}
	} else {
		querystring := `select 1 from "user" ur where upper(ur.active_directory)  = upper($1)  and ur.country = $2
		and ur.is_deleted = false`
		var hasValue int
		err := pool.QueryRow(context.Background(), querystring, activeDir, country).Scan(&hasValue)
		if err == nil && hasValue > 0 {
			result = true
		}
	}
	return result
}

func GetActivDirNameInUserByID(userID uuid.UUID) string {
	functionName := "GetActivDirNameInUserByID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result string
	querystring := `select active_directory from "user" where id = $1 and is_active = true and is_deleted = false`
	var hasValue string
	err := pool.QueryRow(context.Background(), querystring, userID).Scan(&hasValue)
	if err == nil {
		result = hasValue
	}
	return result
}
