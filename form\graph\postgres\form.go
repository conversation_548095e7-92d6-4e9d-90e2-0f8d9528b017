package postgres

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	strip "github.com/grokify/html-strip-tags-go"
	codeController "github.com/ihcp/code/controller"
	"github.com/ihcp/form/graph/entity"
	"github.com/ihcp/form/graph/logengine"
	"github.com/ihcp/form/graph/model"
	"github.com/ihcp/form/graph/postgres/util"
	"github.com/ihcp/form/graph/veeva"
	"github.com/jackc/pgx/v4"
	"github.com/jmoiron/sqlx"
	"log"
	"slices"
	"strconv"
	"strings"

	"github.com/jackc/pgtype"

	suuid "github.com/satori/go.uuid"
	uuid "github.com/satori/go.uuid"
)

func GetActivitySelections() ([]entity.FetchActivities, error) {
	functionName := "GetActivities()"
	log.Println(functionName)
	var response []entity.FetchActivities
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select id, description from activity`
	rows, err := pool.Query(context.Background(), queryString)
	logengine.GetTelemetryClient().TrackEvent("GetActivitySelections query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var activity entity.FetchActivities
		rows.Scan(&activity.ID, &activity.Description)
		response = append(response, activity)
	}
	return response, nil
}

func CreateFormTemplate(input *entity.FormEntity) error {
	functionName := "CreateFormTemplate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `INSERT INTO form (name,type,design,version_no,is_active,created_by) VALUES ($1,$2,$3,$4,$5,$6)`
	_, err := pool.Exec(context.Background(), queryString, "Form Design", 1, input.Design, 1, true, uuid.Nil)
	logengine.GetTelemetryClient().TrackEvent("CreateFormTemplate query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return err
	}
	return nil
}

type EventAccessControl struct {
	Id          int
	Description string
}

func GetUserEventAccessControl(ctx context.Context, userRoleID string, country int) []*EventAccessControl {
	pool = GetPool()

	q := `SELECT c.id, c.description 
	FROM code c
	JOIN event_type_access_permission etcp on etcp.event_type = c.id 
	WHERE
		c.is_active=true AND etcp.country = $1 and etcp.is_active = true and etcp.is_deleted = false  and $2=ANY(etcp.user_role_id)`

	var rs []*EventAccessControl
	rows, err := pool.Query(ctx, q, country, userRoleID)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	for rows.Next() {
		obj := EventAccessControl{}
		err := rows.Scan(&obj.Id, &obj.Description)
		if err != nil {
			panic(err)
		}

		rs = append(rs, &obj)
	}

	return rs
}

func FetchFormTemplate(ctx context.Context) *entity.FormEntity {
	log.Println("FetchFormTemplate()")
	pool = GetPool()

	queryString := `SELECT id,design from form where is_deleted = false and is_active = true ORDER BY version_no desc LIMIT 1` //for testing validation.
	var form entity.FormEntity
	if err := pool.QueryRow(ctx, queryString).Scan(&form.ID, &form.Design); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}

		panic(err)
	}

	return &form
}

func GetFormAnswerByUUID(answerID uuid.UUID) *pgtype.JSONB {
	var d pgtype.JSONB
	err := pool.QueryRow(context.Background(), `SELECT answers from form_answers where id = $1 `, answerID).Scan(&d)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}

	return &d
}

func FetchFormAnswers(loggedInId *uuid.UUID, input *entity.FormAnswerFetchEntity, approvalRoleID *string) (*entity.FormAnswerEntity, string) {
	log.Println("FetchFormAnswers()")

	pool := GetPool()

	approvalRoleName := func() string {
		rs := GetCodeValueByID(*approvalRoleID)
		return *rs
	}()

	d := GetFormAnswerByUUID(input.FormId)

	if d == nil {
		return nil, error.Error(errors.New("event not found"))
	}

	formAnswerAttachment, err := FetchFormAnswersAttachment(&input.FormId)
	if err != nil {
		return nil, err.Error()
	}

	if IsLoggedInIsOwner(loggedInId, &input.FormId) || IsLoggedUserEventRequestor(loggedInId, &input.FormId) {
		return &entity.FormAnswerEntity{
			Design:      *d,
			Attachments: formAnswerAttachment,
		}, ""

	} else if slices.Contains([]string{"admin", "clustercomplianceofficer", "superadmin", "support"}, approvalRoleName) {
		if !IsFormIDDraft(&input.FormId) {
			return &entity.FormAnswerEntity{
				Design:      *d,
				Attachments: formAnswerAttachment,
			}, ""
		}
		return nil, "You do not have permission to access the selected event!"

	} else if approvalRoleName == "finance" {
		if IsFormIDCompleted(&input.FormId) {
			return &entity.FormAnswerEntity{
				Design:      *d,
				Attachments: formAnswerAttachment,
			}, ""
		}
		return nil, "can not access in-completed event"

	} else { // approver
		queryString := `SELECT fa.answers FROM form_answers fa 
			INNER JOIN approvers ap ON fa.id = ap.form_answer_id 
			WHERE fa.id = $1 
			AND ap.approver_id = $2 and ap.is_active = true and ap.is_deleted = false`
		err := pool.QueryRow(context.Background(), queryString, input.FormId, loggedInId).Scan(&d)
		if err != nil {
			if errors.Is(err, sql.ErrNoRows) {
				return nil, error.Error(errors.New("you do not have permission to access the selected event"))
			}
			panic(err)
		}
	}

	return &entity.FormAnswerEntity{
		Design:      *d,
		Attachments: formAnswerAttachment,
	}, ""
}

func IsFormIDDraft(formAnswerID *uuid.UUID) bool {
	functionName := "IsFormIDApproved()"
	log.Println(functionName)
	action := "draft"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedAction := codes[action].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 and status = $2"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approvedAction).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func FetchFormAnswersAttachment(formAnswerID *uuid.UUID) ([]entity.FormAnswerAttachmentEntity, error) {
	functionName := "FetchFormAnswersAttachment()"
	log.Println(functionName)
	var form []entity.FormAnswerAttachmentEntity
	if pool == nil {
		pool = GetPool()
	}
	values := GetAttachmentTypeByID(formAnswerID)
	for _, value := range values {
		var form1 []entity.FormAnswerAttachmentCategoryEntity
		var attachmentType entity.FormAnswerAttachmentEntity
		categories := GetAttachmentCategoryByID(formAnswerID, value)
		for _, category := range categories {

			var attachmentCategory entity.FormAnswerAttachmentCategoryEntity
			urls, err := GetAttachmentURLByID(formAnswerID, value, category)
			logengine.GetTelemetryClient().TrackEvent("FetchFormAnswersAttachment query called")
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - Error: %s", functionName, err.Error())
				return nil, err
			}
			attachmentCategory.URL = urls
			attachmentCategory.Category = category
			form1 = append(form1, attachmentCategory)

		}
		attachmentType.Value = value
		attachmentType.Categories = form1
		form = append(form, attachmentType)
	}

	return form, nil
}

func GetAttachmentTypeByID(ID *uuid.UUID) []string {
	var result []string
	querystring := `
	SELECT distinct type_desc
	FROM form_answer_attachments 
	WHERE 
		form_answer_id = $1
		AND is_active = true
		AND is_deleted = false`

	rows, err := GetPool().Query(context.Background(), querystring, ID)
	if err != nil {
		panic(err)
	}

	for rows.Next() {
		var value string
		if err := rows.Scan(&value); err != nil {
			panic(err)
		}
		result = append(result, value)
	}
	return result
}

func GetAttachmentCategoryByID(ID *uuid.UUID, typeValue string) []string {
	functionName := "GetAttachmentTypeByID()"
	if pool == nil {
		pool = GetPool()
	}
	var result []string
	querystring := `select distinct category_desc from form_answer_attachments 
	where form_answer_id = $1 and type_desc = $2 and is_active = true and is_deleted = false`

	rows, err := pool.Query(context.Background(), querystring, ID, typeValue)
	logengine.GetTelemetryClient().TrackEvent("GetAttachmentCategoryByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil
	}
	for rows.Next() {
		var value string
		_ = rows.Scan(&value)
		result = append(result, value)
	}
	return result

}

func GetAttachmentURLByID(ID *uuid.UUID, typeValue string, categoryValue string) ([]string, error) {
	functionName := "GetAttachmentURLByID()"
	if pool == nil {
		pool = GetPool()
	}
	var form []string

	if typeValue == "Changes" {
		queryString := `select url from form_answer_attachments 
		where form_answer_id = $1 and type_desc = $2 and is_active = true and is_deleted = false`
		rows, err := pool.Query(context.Background(), queryString, ID, typeValue)
		logengine.GetTelemetryClient().TrackEvent("GetAttachmentURLByID query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return nil, err
		}
		for rows.Next() {
			var value string
			_ = rows.Scan(&value)
			form = append(form, value)
		}
	} else {
		queryString := `select url from form_answer_attachments 
	where form_answer_id = $1 and type_desc = $2
	and category_desc = $3 and is_active = true and is_deleted = false`
		rows, err := pool.Query(context.Background(), queryString, ID, typeValue, categoryValue)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return nil, err
		}
		for rows.Next() {
			var value string
			_ = rows.Scan(&value)
			form = append(form, value)
		}
	}
	return form, nil
}

func FetchRequesterCountry(formAnswerId string) string {
	functionName := "FetchRequesterCountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result string
	querystring := `select c.value from form_answers fa 
	inner join code c on c.id = fa.country
	where fa.id = $1`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("FetchRequesterCountry query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
	}
	return result
}
func FetchRequesterCurrency(formAnswerId string) (string, pgtype.JSONB, float64, string, string) {
	functionName := "FetchRequesterCurrency()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result string
	var boardOfDirectors pgtype.JSONB
	codes := codeController.GetValueKeyCodes()["country"]
	usdValue := codes["us"].ID
	var value sql.NullFloat64
	var ConversionRate float64
	var ownerActiveDirectory sql.NullString
	var requestorName sql.NullString
	querystring := `select SPLIT_PART(c.value, '|', 2) as currency,(select target_rate from currency_exchange where 
	country = (select id from code where value = left (c.value,2) and category = 'Country' and is_active =true ) 
	and target_country = $2 order by date_created desc limit 1) as conversion_rate,
	rd.board_of_directors_for_hco_sponsorship,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,id}' as owner_ad,
	rd.requestor_name
	from form_answers fa 
	inner join code c on c.id = fa.currency
	inner join reporting_db rd on rd.form_answer_id =fa.id 
	where fa.id=$1 and fa.is_deleted=false`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId, usdValue).Scan(&result, &value, &boardOfDirectors, &ownerActiveDirectory, &requestorName)
	logengine.GetTelemetryClient().TrackEvent("FetchRequesterCurrency query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
	}
	if value.Float64 != 0.0 {
		ConversionRate = 1 / value.Float64
	} else {
		ConversionRate = 0
	}
	return result, boardOfDirectors, ConversionRate, ownerActiveDirectory.String, requestorName.String
}

func IsLoggedUserEventRequestor(createdBy *uuid.UUID, formAnswerId *uuid.UUID) bool {
	pool := GetPool()

	var hasValue int
	err := pool.QueryRow(context.Background(), "SELECT 1 FROM form_answers WHERE created_by = $1 and id = $2 ", createdBy, formAnswerId).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
	}

	return true
}

func IsLoggedInIsOwner(created_by *uuid.UUID, formAnswerId *uuid.UUID) bool {
	pool = GetPool()

	querystring := `select 1
	from form_answers fa where id = $2 and 
	(replace ((fa.answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')::text ,'"','' ) 
	=(select CONCAT(u.first_name ,' ', u.last_name) from "user" u where u.id = $1))`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, created_by, formAnswerId).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
	}

	return true
}

func CheckFormAnswerCountry(formAnsID string, countryID int) bool {
	functionName := "CheckFormAnswerCountry()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE country = $1 and id = $2 "
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, countryID, formAnsID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

//func IsLoggedInIsOwner(created_by *uuid.UUID, formAnswerId *uuid.UUID) bool {
//	functionName := "IsLoggedInIsOwner()"
//	log.Println(functionName)
//	if pool == nil {
//		pool = GetPool()
//	}
//	var result bool
//	querystring := `select 1
//	from form_answers fa where id = $2 and
//	(replace ((fa.answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,value}')::text ,'"','' )
//	=(select CONCAT(u.first_name ,' ', u.last_name) from "user" u where u.id = $1))`
//	var hasValue int
//	err := pool.QueryRow(context.Background(), querystring, created_by, formAnswerId).Scan(&hasValue)
//	if err == nil {
//		result = true
//	}
//	return result
//}

func IsFormIDCompleted(formAnswerID *uuid.UUID) bool {
	functionName := "IsFormIDApproved()"
	log.Println(functionName)
	action := "completed"
	codes := codeController.GetValueKeyCodes()["formanswertype"]
	completedAction := codes[action].ID
	codes1 := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedAction := codes1["approved"].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 and status in ($2,$3)"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approvedAction, completedAction).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func IsFormIDExists(formAnswerID *uuid.UUID) bool {
	log.Println("> Checking IsFormIDExists()")
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 "
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CreateFormAnswerLogs(ctx context.Context, answer pgtype.JSONB, createdBy *uuid.UUID, formAnswerId string, status int, isApollo *bool) error {
	formAnswerIdUUID, err := uuid.FromString(formAnswerId)
	if err != nil {
		panic(err)
	}

	pool = GetPool()

	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)
	queryString := `INSERT INTO form_answer_logs
					(form_answers_id, created_by, answers, status,is_apollo)
					VALUES($1, $2, $3, $4,$5);`
	_, err = tx.Exec(ctx, queryString, formAnswerIdUUID, createdBy, answer, status, isApollo)
	if err != nil {
		panic(err)
	}

	if err := tx.Commit(context.Background()); err != nil {
		panic(err)
	}

	return nil
}

func CreateFormAnswerLogs2(ctx context.Context, tx pgx.Tx, answer pgtype.JSONB, createdBy *uuid.UUID, formAnswerId string, status int, isApollo *bool) {
	formAnswerIdUUID, err := uuid.FromString(formAnswerId)
	if err != nil {
		panic(err)
	}

	queryString := `INSERT INTO form_answer_logs
					(form_answers_id, created_by, answers, status,is_apollo)
					VALUES($1, $2, $3, $4,$5);`
	_, err = tx.Exec(ctx, queryString, formAnswerIdUUID, createdBy, answer, status, isApollo)
	if err != nil {
		panic(err)
	}
}

func getStatusCodeByString(s string) int {
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	return code[s].ID
}

func CreateFormAnswer(ctx context.Context, tx pgx.Tx, answer *entity.AnswerEntity, input model.FormAnswerSubmissionRequest, eventCode string) (string, string) {
	pendingStatus := getStatusCodeByString("pending")
	draftStatus := getStatusCodeByString("draft")
	approvedStatus := getStatusCodeByString("approved")

	localStatus := pendingStatus
	if answer.Country == 900001 {
		localStatus = approvedStatus
	}

	var form entity.FormAnswerEventEntity

	if input.IsDraft == true {
		queryString := `INSERT INTO form_answers 
						(form_id,answers,created_by,status,country,currency,event_code,total_cost,is_change_request,change_request_summary,is_apollo) 
						VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11) RETURNING id,event_seq`
		err := tx.QueryRow(ctx, queryString, answer.FormId, answer.Answer, answer.CreatedBy, draftStatus, answer.Country, answer.Currency, eventCode, input.TotalCost, input.IsChangeRequest, input.ChangeRequestSummary, input.IsApollo).Scan(&form.ID, &form.EventCode)
		if err != nil {
			panic(err)
		}

		CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, form.ID, draftStatus, input.IsApollo)
	} else {
		queryString := `INSERT INTO form_answers 
						(form_id,answers,created_by,status,local_approval_status,regional_approval_status,country,currency,event_code,total_cost,is_change_request,change_request_summary,is_apollo) 
						VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11,$12,$13) RETURNING id,event_seq`
		err := tx.QueryRow(context.Background(), queryString, answer.FormId, answer.Answer, answer.CreatedBy, pendingStatus, localStatus, pendingStatus, answer.Country, answer.Currency, eventCode, input.TotalCost, input.IsChangeRequest, input.ChangeRequestSummary, input.IsApollo).Scan(&form.ID, &form.EventCode)
		if err != nil {
			panic(err)
		}

		CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, form.ID, pendingStatus, input.IsApollo)

	}

	//if err := tx.Commit(ctx); err != nil {
	//	panic(err)
	//}

	return form.ID, util.PadNumberWithZero(*form.EventCode)
}

func UpdateFormAnswer(ctx context.Context, tx pgx.Tx, answer *entity.AnswerEntity, input model.FormAnswerSubmissionRequest) (string, string) {
	pendingStatus := getStatusCodeByString("pending")
	draftStatus := getStatusCodeByString("draft")
	returnStatusID := getStatusCodeByString("return")
	recallStatusId := getStatusCodeByString("recalled")

	var eventID, formID string
	if input.IsDraft == true {
		if CheckFormAnswerInReturnStage(*input.ID) {
			var inputArgs []interface{}
			querystring := `UPDATE form_answers SET total_cost=? `
			inputArgs = append(inputArgs, input.TotalCost)
			if answer != nil {
				querystring += ` ,answers=? ,country=? ,currency=?`
				inputArgs = append(inputArgs, answer.Answer, answer.Country, answer.Currency)
				if answer.FormId != nil {
					querystring += ` ,form_id=?`
					inputArgs = append(inputArgs, answer.FormId)
				}

				if answer.CreatedBy != nil {
					querystring += ` ,created_by=?`
					inputArgs = append(inputArgs, answer.CreatedBy)
				}
			}
			if input.IsChangeRequest != nil {
				querystring += ` ,is_change_request=?`
				inputArgs = append(inputArgs, input.IsChangeRequest)
			}
			if input.ChangeRequestSummary != nil {
				querystring += ` ,change_request_summary=?`
				inputArgs = append(inputArgs, input.ChangeRequestSummary)
			}
			querystring += ` WHERE id = ? RETURNING id, CONCAT(event_code,'-',event_seq)`
			inputArgs = append(inputArgs, input.ID)
			querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

			err := tx.QueryRow(ctx, querystring, inputArgs...).Scan(&formID, &eventID)
			if err != nil {
				panic(err)
			}
			CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, formID, returnStatusID, input.IsApollo)

		} else if CheckFormAnswerInRecalledStage(*input.ID) {
			var inputArgs []interface{}
			querystring := `UPDATE form_answers SET total_cost=?,status=? `
			inputArgs = append(inputArgs, input.TotalCost, recallStatusId)
			if answer != nil {
				querystring += `,answers=?,country=?,currency=? `
				inputArgs = append(inputArgs, answer.Answer, answer.Country, answer.Currency)

			}
			if answer.FormId != nil {
				querystring += `,form_id=? `
				inputArgs = append(inputArgs, answer.FormId)
			}
			if answer.CreatedBy != nil {
				querystring += `,created_by=? `
				inputArgs = append(inputArgs, answer.CreatedBy)
			}
			if input.IsChangeRequest != nil {
				querystring += `,is_change_request=? `
				inputArgs = append(inputArgs, input.IsChangeRequest)
			}
			if input.ChangeRequestSummary != nil {
				querystring += `,change_request_summary=? `
				inputArgs = append(inputArgs, input.ChangeRequestSummary)
			}
			querystring += `WHERE id = ? RETURNING id, CONCAT(event_code,'-',event_seq)`
			inputArgs = append(inputArgs, input.ID)
			querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

			err := tx.QueryRow(ctx, querystring, inputArgs...).Scan(&formID, &eventID)
			if err != nil {
				panic(err)
			}
			CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, formID, recallStatusId, input.IsApollo)
		} else {
			var inputArgs []interface{}
			querystring := `UPDATE form_answers SET total_cost=?,status=? `
			inputArgs = append(inputArgs, input.TotalCost, draftStatus)
			if answer != nil {
				querystring += `,answers=?,country=?,currency=? `
				inputArgs = append(inputArgs, answer.Answer, answer.Country, answer.Currency)

			}
			if answer.FormId != nil {
				querystring += `,form_id=? `
				inputArgs = append(inputArgs, answer.FormId)
			}
			if answer.CreatedBy != nil {
				querystring += `,created_by=? `
				inputArgs = append(inputArgs, answer.CreatedBy)
			}
			if input.IsChangeRequest != nil {

				querystring += `,is_change_request=? `
				inputArgs = append(inputArgs, input.IsChangeRequest)
			}
			if input.ChangeRequestSummary != nil {
				querystring += `,change_request_summary=? `
				inputArgs = append(inputArgs, input.ChangeRequestSummary)
			}
			querystring += `WHERE id = ? RETURNING id, CONCAT(event_code,'-',event_seq)`
			inputArgs = append(inputArgs, input.ID)
			querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

			err := tx.QueryRow(ctx, querystring, inputArgs...).Scan(&formID, &eventID)
			if err != nil {
				panic(err)
			}
			CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, formID, draftStatus, input.IsApollo)
		}
	} else {
		if CheckRegionalApproverPresentInApprovers(*input.ID) {
			var inputArgs []interface{}
			querystring := `UPDATE form_answers SET total_cost=?,status=?,local_approval_status=?,regional_approval_status=?  `
			inputArgs = append(inputArgs, input.TotalCost, pendingStatus, pendingStatus, pendingStatus)
			if answer != nil {
				querystring += `,answers=?,country=?,currency=? `
				inputArgs = append(inputArgs, answer.Answer, answer.Country, answer.Currency)
				if answer.FormId != nil {
					querystring += `,form_id=? `
					inputArgs = append(inputArgs, answer.FormId)
				}
				if answer.CreatedBy != nil {
					querystring += `,created_by=? `
					inputArgs = append(inputArgs, answer.CreatedBy)
				}
			}
			if input.IsChangeRequest != nil {
				querystring += `,is_change_request=? `
				inputArgs = append(inputArgs, input.IsChangeRequest)
			}
			if input.ChangeRequestSummary != nil {

				querystring += `,change_request_summary=? `
				inputArgs = append(inputArgs, input.ChangeRequestSummary)
			}
			querystring += `WHERE id = ? RETURNING id, CONCAT(event_code,'-',event_seq)`
			inputArgs = append(inputArgs, input.ID)
			querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

			err := tx.QueryRow(ctx, querystring, inputArgs...).Scan(&formID, &eventID)
			if err != nil {
				panic(err)
			}
			CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, formID, pendingStatus, input.IsApollo)
		} else {
			var where bool
			var inputargs []interface{}
			querystring := `UPDATE form_answers SET total_cost=?,status=?,local_approval_status=?  `
			inputargs = append(inputargs, input.TotalCost, pendingStatus, pendingStatus, pendingStatus)
			if answer != nil {
				querystring += `,answers=?,country=?,currency=? `
				inputargs = append(inputargs, answer.Answer, answer.Country, answer.Currency)
				if answer.FormId != nil {
					querystring += `,form_id=? `
					inputargs = append(inputargs, answer.FormId)
					where = true
				}
				if answer.CreatedBy != nil {
					if where {
						querystring += `,created_by=? `
						inputargs = append(inputargs, answer.CreatedBy)
					} else {
						querystring += `created_by=? `
						inputargs = append(inputargs, answer.CreatedBy)
						where = true
					}
				}
			}
			if input.IsChangeRequest != nil {
				querystring += `,is_change_request=? `
				inputargs = append(inputargs, input.IsChangeRequest)
			}
			if input.ChangeRequestSummary != nil {

				querystring += `,change_request_summary=? `
				inputargs = append(inputargs, input.ChangeRequestSummary)
			}
			querystring += `WHERE id = ? RETURNING id, CONCAT(event_code,'-',event_seq)`
			inputargs = append(inputargs, input.ID)
			querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

			err := tx.QueryRow(ctx, querystring, inputargs...).Scan(&formID, &eventID)
			if err != nil {
				panic(err)
			}
			CreateFormAnswerLogs2(ctx, tx, answer.Answer, answer.CreatedBy, formID, pendingStatus, input.IsApollo)
		}
	}

	return formID, eventID
}

func CreateApprovalRoles(ctx context.Context, answer *entity.ApproversEntity, changeRequestSummary *string, formId string, userID string, data []*entity.EmailStruct, isApollo *bool) error {
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["pending"].ID
	statusValue := code["pending"].Value
	statusTitle := code["pending"].Title
	approvedStatus := code["approved"].ID
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	var firstApprover string
	localFirstApprover := ""
	regionalFirstApprover := ""
	var localTemp = 0
	var regionalTemp = 0
	localGroupID := 55
	regionalGroupID := 56
	for _, value := range answer.ApproverRoles {
		log.Println(value.DepartmentID)
		approverQueryString := `INSERT INTO approvers (form_answer_id, approver_id, approval_role, user_role_id,created_by, group_id, sequence_no, status,department_id,set_number,is_apollo) VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9,$10,$11) `
		logengine.GetTelemetryClient().TrackEvent("CreateApprovalRoles query called")
		_, err = tx.Exec(context.Background(), approverQueryString, formId, value.UserID, 0, value.RoleID, answer.CreatedBy, value.GroupID, value.SequenceNo, value.StatusID, value.DepartmentID, value.SetNumber, isApollo)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			panic(err)
		}

		if localTemp == 0 && value.GroupID == localGroupID {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < localTemp && value.GroupID == 55 {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		}
		if regionalTemp == 0 && value.GroupID == regionalGroupID {
			regionalTemp = value.SequenceNo
			regionalFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < regionalTemp && value.GroupID == 56 {
			regionalTemp = value.SequenceNo
			regionalFirstApprover = value.UserID
		}

	}

	if localTemp > 0 {
		firstApprover = localFirstApprover
	} else {
		firstApprover = regionalFirstApprover
	}
	var inputArgs []interface{}
	if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == false {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?`
		inputArgs = append(inputArgs, status, approvedStatus, status)
		if changeRequestSummary != nil {
			querystring += `,change_request_summary=? `
			inputArgs = append(inputArgs, changeRequestSummary)

		}

		querystring += `WHERE id = ?`
		inputArgs = append(inputArgs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(ctx, querystring, inputArgs...)
		if err != nil {
			panic(err)
		}
	} else if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?`
		inputArgs = append(inputArgs, status, status, status)
		if changeRequestSummary != nil {
			querystring += `,change_request_summary=? `
			inputArgs = append(inputArgs, changeRequestSummary)
		}
		querystring += `WHERE id = ?`
		inputArgs = append(inputArgs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(ctx, querystring, inputArgs...)
		if err != nil {
			panic(err)
		}

	} else if answer.RegionalApproverPresent == false && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?`
		inputArgs = append(inputArgs, status, status)
		if changeRequestSummary != nil {
			querystring += `,change_request_summary=? `
			inputArgs = append(inputArgs, changeRequestSummary)
		}

		querystring += `WHERE id = ?`
		inputArgs = append(inputArgs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(ctx, querystring, inputArgs...)
		if err != nil {
			panic(err)
		}
	}

	_, err = tx.Exec(ctx, `UPDATE reporting_db
	SET 
		status_title = $2,
		status_value = $3,
		updated_date = now()
	WHERE form_answer_id = $1 `, formId, statusTitle, statusValue)
	if err != nil {
		panic(err)
	}

	pendingStatus := code["pending"].ID
	InsertStatusNotification2(context.Background(), tx, formId, userID, pendingStatus, firstApprover)

	emailCode := codeController.GetValueKeyCodes()["typeofemail"]
	approverStatus := emailCode["requireapproval"].ID
	requesterStatus := emailCode["formsubmitted"].ID

	requesterEmail, _ := GetEmailIDFromUserID(userID)
	if requesterEmail != "" {
		requesterEmailContent := util.SendEmailToRequesterAfterEventSubmission(requesterEmail, data)
		InsertEmailLog2(tx, formId, requesterStatus, strip.StripTags(requesterEmailContent), userID, userID)
	}

	formIdUuid, _ := uuid.FromString(formId)
	emailOwner, ownerId := GetOwnersEmailIDFromFormAnswerID2(formIdUuid)
	if emailOwner != "" && ownerId != "" && emailOwner != requesterEmail {
		emailOwnerContent := util.SendEmailToRequesterAfterEventSubmission(emailOwner, data)
		InsertEmailLog2(tx, formId, requesterStatus, strip.StripTags(emailOwnerContent), userID, ownerId)
	}

	email := GetEmailIDFromUserID2(firstApprover)
	if email != "" {
		approverEmailContent := util.SendEmailScenarioOne(email, data)
		InsertEmailLog2(tx, formId, approverStatus, strip.StripTags(approverEmailContent), userID, firstApprover)
	}

	if err := tx.Commit(context.Background()); err != nil {
		panic(err)
	}
	return nil
}

func CreatedbyfromFormId(FormId string) (string, error) {
	functionName := "CreatedbyfromFormId()"
	if pool == nil {
		pool = GetPool()
	}
	log.Println(functionName)
	var inputargs []interface{}
	var CreatedBy string
	querystring := `select fa.created_by  from form_answers fa where fa.id =$1`
	inputargs = append(inputargs, FormId)
	err := pool.QueryRow(context.Background(), querystring, inputargs...).Scan(&CreatedBy)
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return "", err
	}
	return CreatedBy, nil

}

func CreateApprovalRolesForExceptional(answer *entity.ApproversEntity, formId string, userID string, data []*entity.EmailStruct, isApollo *bool) error {
	functionName := "CreateApprovalRolesForExceptional()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["pending"].ID
	approvedStatus := code["approved"].ID
	defer tx.Rollback(context.Background())
	var firstApprover string
	log.Println(firstApprover)
	localFirstApprover := ""
	rigonalFirstApprover := ""
	var localTemp = 0
	var rigonalTemp = 0
	localGroupID := 55
	regionalGroupID := 56
	for _, value := range answer.ApproverRoles {
		log.Println(value.DepartmentID)
		approverQueryString := `INSERT INTO approvers (form_answer_id, approver_id, approval_role, user_role_id,created_by, group_id, sequence_no, status,department_id,set_number,is_apollo) VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9,$10,$11) `
		_, err = tx.Exec(context.Background(), approverQueryString, formId, value.UserID, 0, value.RoleID, answer.CreatedBy, value.GroupID, value.SequenceNo, value.StatusID, value.DepartmentID, value.SetNumber, isApollo)
		logengine.GetTelemetryClient().TrackEvent("CreateApprovalRoles query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
		if localTemp == 0 && value.GroupID == localGroupID {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < localTemp && value.GroupID == 55 {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		}
		if rigonalTemp == 0 && value.GroupID == regionalGroupID {
			rigonalTemp = value.SequenceNo
			rigonalFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < rigonalTemp && value.GroupID == 56 {
			rigonalTemp = value.SequenceNo
			rigonalFirstApprover = value.UserID
		}

	}

	if localTemp > 0 {
		firstApprover = localFirstApprover
	} else {
		firstApprover = rigonalFirstApprover
	}
	var inputargs []interface{}
	if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == false {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?`
		inputargs = append(inputargs, status, approvedStatus, status)
		querystring += `WHERE id = ?`
		inputargs = append(inputargs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	} else if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?`
		inputargs = append(inputargs, status, status, status)
		querystring += `WHERE id = ?`
		inputargs = append(inputargs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	} else if answer.RegionalApproverPresent == false && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?`
		inputargs = append(inputargs, status, status)
		querystring += `WHERE id = ?`
		inputargs = append(inputargs, formId)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}

	emailCode := codeController.GetValueKeyCodes()["typeofemail"]
	approverStatus := emailCode["requireapproval"].ID

	email, err := GetEmailIDFromUserID(firstApprover)
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
	} else {
		approverEmailContent := util.SendEmailScenarioOne(email, data)
		newApproverEmailContent := strip.StripTags(approverEmailContent)
		err = InsertEmailLog(formId, approverStatus, newApproverEmailContent, userID, firstApprover)
		if err != nil {
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
		logengine.GetTelemetryClient().TrackException(txErr)
	}
	return nil
}

func GetValuesFromOtherTable(source string, countryID int) []*model.Values {
	pool = GetPool()
	queryString := getSQLQueryFromTableName(source, countryID)
	if queryString == `` {
		return []*model.Values{}
	}
	rows, err := pool.Query(context.Background(), queryString)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var response []*model.Values
	for rows.Next() {
		var value model.Values
		err := rows.Scan(&value.Value, &value.Description)
		if err != nil {
			panic(err)
		}
		response = append(response, &value)
	}

	return response
}

func getSQLQueryFromTableName(tableName string, countryID int) string {
	country := strconv.Itoa(countryID)
	query := `SELECT `
	from := ` FROM "` + tableName + `"`
	if tableName == "material" {
		query += ` id as value,group_name as description ` + from + ` where is_deleted = false AND is_active = true AND country = ` + country
	} else if tableName == "product_owner" {
		query += ` id as value,owner_name as description ` + from + ` where is_deleted = false AND is_active = true AND country = ` + country
	} else if tableName == "hcp_tier" {
		query += ` id as value,tier_name as description ` + from
	} else if tableName == "expense_limit" || tableName == "role" {
		query += ` id as value,description ` + from
	} else if tableName == "activity" {
		query += ` id as value,description ` + from + ` where is_deleted = false AND is_active = true AND country = ` + country
	} else if tableName == "prep_time" {
		query += ` id as value,prep_method as description ` + from
	} else if tableName == "user" {
		query += ` id as value,concat_ws( ' ',coalesce(first_name, '') , coalesce(last_name, '')) as description` + from + ` where is_deleted = false AND is_active = true AND country = ` + country
	} else if tableName == "customer" {
		query += ` id as value,name as description ` + from + ` where is_deleted = false AND is_active = true AND country = ` + country
	} else if tableName == "team" {
		query += ` id as value,name as description ` + from + ` where is_deleted = false AND is_active = true AND country = ` + country + `order by name`
	} else {
		return ``
	}
	return query
}

func GetRequestorResponse(userID string) (map[string][]entity.RequestorResponse, error) {
	functionName := "GetRequestorResponse()"
	if pool == nil {
		pool = GetPool()
	}

	queryString := `
	SELECT fa.id, CONCAT(au.first_name, ' ', au.last_name) as name, al.comments, c.title, c.value, fa.date_created  FROM form_answers fa
	INNER JOIN approval_log al
	ON al.form_answer = fa.id
	INNER JOIN "user" u
	ON u.id = fa.created_by
	INNER JOIN code c
	ON c.id = al.status
	INNER JOIN "user" au
	ON al.actioned_by = au.id
	WHERE u.id = $1 AND c.value = 'pendingresponse'
	ORDER BY fa.date_created, al.date_created desc
	`

	rows, err := pool.Query(context.Background(), queryString, userID)
	logengine.GetTelemetryClient().TrackEvent("GetRequestorResponse query called")

	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var result map[string][]entity.RequestorResponse = make(map[string][]entity.RequestorResponse)
	for rows.Next() {
		var entityLog entity.RequestorResponse
		err := rows.Scan(&entityLog.ID, &entityLog.Name, &entityLog.Comment, &entityLog.Status, &entityLog.StatusValue, &entityLog.Date)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result[entityLog.ID.String] = append(result[entityLog.ID.String], entityLog)
	}

	return result, nil
}

func GetNotificationStatus(userID string, limit int, lastID string) ([]entity.ListNotificationStatusEntity, error) {
	functionName := "GetNotificationStatus()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	queryString := `select scn.id,scn.form_answer ,scn.has_read ,scn.status ,
	concat(fa.event_code ,'-', fa.event_seq),scn.actioned_by ,
	scn.date_created
	from status_change_notification scn inner join form_answers fa ON scn.form_answer = fa.id 
	where `
	var inputargs []interface{}
	if userID != suuid.Nil.String() {
		queryString += ` scn.receiver = ? `
	}
	inputargs = append(inputargs, userID)
	if lastID != "" {
		queryString += ` and (scn.date_created, scn.id) < ( (SELECT date_created FROM status_change_notification 
			WHERE id = ?), ?) `
		inputargs = append(inputargs, lastID, lastID)
	}
	queryString += ` and scn.is_active = true and scn.is_deleted =false ORDER by scn.date_created desc`

	if limit > 0 {
		queryString = queryString + ` limit ? `
		inputargs = append(inputargs, limit)
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("GetNotificationStatus query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	var result []entity.ListNotificationStatusEntity
	for rows.Next() {
		var notificationList entity.ListNotificationStatusEntity
		err := rows.Scan(&notificationList.ID, &notificationList.FormAnswerID, &notificationList.HasRead, &notificationList.Status, &notificationList.EventID, &notificationList.ActionedBy, &notificationList.DateCreated)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s", functionName, err.Error())
			return result, err
		}
		result = append(result, notificationList)
	}
	return result, nil
}

func GetFormAnswerStatusByFormAnswerID(formAnswerID string) bool {
	functionName := "GetFormAnswerStatusByFormAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	status := codes["approved"].ID
	var result bool
	querystring := "select 1 from form_answers where id = $1 and status = $2"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, status).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func GetPendingApprovalCount(userID string, Page int, limit int, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilter string, sortElements []string, dateFilterStart string, dateFilterEnd string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) (int, error) {
	functionName := "GetPendingApprovalCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result int
	queryString := `with approver_event as (
		select form_answer_id, MAX(set_number) as max_set FROM approvers
		WHERE approver_id = ?
		GROUP BY form_answer_id
		), approval as
		(   select ap.id, 
			ap.form_answer_id,
			CONCAT(u.first_name ,' ', u.last_name) as "name",
			fa.date_created,fa.is_recall,
			fa.country as country,
			status.title, 
			status.value, 
			ap.approver_id,
			ap.group_id ,ap.sequence_no ,
			grp_type.value as group_type,
			c.title as countrytitle,
			c.value as countryvalue,
			c2.title as currencytitle, 
			c2.value as currencyvalue,
			fa.total_cost,
			fa.event_code as eventCode,
			fa.event_seq as eventSeq,
			fa.answers,
			fa.change_request_summary,
			fa.is_exceptional_approval,
			fa.status as statuss, 
			ae.max_set as max_sets,
            rd.activity_start_date [2] as activity_name,
            rd.team  as team,
            rd.activity_start_date [3] as activity_start_date,
            rd.activity_start_date [4] as activity_end_date,
            rd.activity_start_date [5] as product,
			rd.event_type as activity,
            rd.event_type [1] as type_of_activity,
            rd.event_type [2]  as event_type,
			ap.is_active ,ap.is_deleted ,
			row_number ()
			over (partition by ap.form_answer_id order by ap.group_id, ap.sequence_no) as seq
			from approvers ap
			inner join form_answers fa on fa.id = ap.form_answer_id and fa.is_deleted =false
			inner join code status on status.id = ap.status
			inner join code grp_type on grp_type.id = ap.group_id
			inner join "user" u on fa.created_by = u.id
			inner join code c on fa.country = c.id 
			inner join code c2 on fa.currency = c2.id 
			inner join reporting_db rd on rd.form_answer_id  =fa.id 
			inner join approver_event ae
			ON ae.form_answer_id = ap.form_answer_id AND ap.set_number = ae.max_set
			where ap.form_answer_id IN (SELECT form_answer_id FROM approver_event)
			AND (status.value <> 'approved'  OR (status.value = 'approved' AND ap.approver_id = ?))
			and ap.id not in (select distinct a.id from approvers a inner join approvers a2 on a.form_answer_id = a2.form_answer_id 
			where a.approver_id = a2.created_by and a.set_number = a2.set_number and a.is_active = false and a.is_deleted = true  )
			ORDER BY fa.date_created desc 
			),form_set_value as (select distinct  a2.form_answer_id from approvers a2 
			inner join approval ap on ap.max_sets=a2.set_number 
			where a2.created_by = ?  and a2.is_active = true and a2.is_deleted = false  group by a2.form_answer_id )
		 SELECT  count(id)
			 FROM approval WHERE approver_id = ?
			 and form_answer_id not in (select distinct form_answer_id from form_set_value )
	 `
	var inputargs []interface{}
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)

	if searchItem != "" {
		queryString = queryString + ` and 
		(name ilike ? or
		eventcode ilike ? or
		replace((to_char(eventseq, '99990999')), ' ', '') ilike ? or 
		(select CONCAT (eventcode,'-', replace((to_char(eventseq, '99990999')), ' ', ''))) ilike ? or
		(CASE WHEN statuss = 398 THEN 'Cancelled' WHEN statuss = 397 THEN 'Return' WHEN statuss = 59 THEN 'Rejected' when statuss = 67 then 'Completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'Pending Approval'ELSE value END) ilike ? or 
		(CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) ilike ? or 
		activity_name ilike ? or 
	    type_of_activity ilike ? or 
	    event_type ilike ? or
	    team ilike ? or
	    product ilike ? or
	   total_cost::text ilike ?) 
	   `

		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%",
		)
	}

	if activityFilter != nil {
		queryString = queryString + ` AND activity && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		queryString = queryString + ` AND activity && ?
		`
		inputargs = append(inputargs, eventFilter)
	}

	if statusFilter != nil || exceptionalStatusFilter != nil {
		queryString = queryString + ` and   ((is_exceptional_approval =true and (CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 452 THEN 'recalled' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) = any( ? )) or((CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 452 THEN 'recalled' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) = any( ? ) and is_exceptional_approval =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}

	if totalCostRange != nil {
		queryString = queryString + ` AND (total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilter != "" {
		queryString = queryString + ` AND (? between
			(case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end)  and 
				(case when
					(activity_end_date) ~ '^[0-9\.]+$'
					then (to_timestamp((activity_end_date):: integer))::timestamp::date end))
		`
		inputargs = append(inputargs, dateFilter)
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			queryString = queryString + ` and ((( case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (activity_end_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_end_date):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			queryString = queryString + ` and (( case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			queryString = queryString + ` and (( case when
                (activity_end_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_end_date):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			queryString = queryString + ` and ((approval.date_created::date >= ? ) and 
                (approval.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			queryString = queryString + ` and (approval.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			queryString = queryString + ` and (approval.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)

	err := pool.QueryRow(context.Background(), queryString, inputargs...).Scan(&result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func GetPendingApproval(userID string, Page int, limit int, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilter string, sortElements []string, dateFilterStart string, dateFilterEnd string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) ([]entity.ListPendingApprovalEntity, float64, error) {
	functionName := "GetPendingApproval()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var maxTotalCost float64
	queryString := `with approver_event as (
		select form_answer_id, MAX(set_number) as max_set FROM approvers
		WHERE approver_id = ?
		GROUP BY form_answer_id
		), approval as
		(   select ap.id, 
			ap.form_answer_id,
			CONCAT(u.first_name ,' ', u.last_name) as "name",
			fa.date_created,fa.is_recall,
			fa.country as country,
			fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,46,values,0,value}' as multi_client_product, 
			status.title, 
			status.value, 
			ap.approver_id,
			ap.group_id ,ap.sequence_no ,
			grp_type.value as group_type,
			c.title as countrytitle,
			c.value as countryvalue,
			c2.title as currencytitle, 
			c2.value as currencyvalue,
			fa.total_cost,
			fa.event_code as eventCode,
			fa.event_seq as eventSeq,
			fa.answers,
			fa.change_request_summary,
			fa.is_exceptional_approval,
			fa.status as statuss, 
			ae.max_set as max_sets,
            rd.activity_start_date [2] as activity_name,
            rd.team  as team,
            rd.activity_start_date [3] as activity_start_date,
            rd.activity_start_date [4] as activity_end_date,
            rd.activity_start_date [5] as product,
			rd.event_type as activity,
            rd.event_type [1] as type_of_activity,
            rd.event_type [2]  as event_type,
			ap.is_active ,ap.is_deleted ,
			row_number ()
			over (partition by ap.form_answer_id order by ap.group_id, ap.sequence_no) as seq
			from approvers ap
			inner join form_answers fa on fa.id = ap.form_answer_id and fa.is_deleted =false
			inner join code status on status.id = ap.status
			inner join code grp_type on grp_type.id = ap.group_id
			inner join "user" u on fa.created_by = u.id
			inner join code c on fa.country = c.id 
			inner join code c2 on fa.currency = c2.id 
			inner join reporting_db rd on rd.form_answer_id  =fa.id 
			inner join approver_event ae
			ON ae.form_answer_id = ap.form_answer_id AND ap.set_number = ae.max_set
			where ap.form_answer_id IN (SELECT form_answer_id FROM approver_event)
			AND (status.value <> 'approved'  OR (status.value = 'approved' AND ap.approver_id = ?))
			and ap.id not in (select distinct a.id from approvers a inner join approvers a2 on a.form_answer_id = a2.form_answer_id 
			where a.approver_id = a2.created_by and a.set_number = a2.set_number and a.is_active = false and a.is_deleted = true  )
			ORDER BY fa.date_created desc 
			),form_set_value as (select distinct  a2.form_answer_id from approvers a2 
			inner join approval ap on ap.max_sets=a2.set_number 
			where a2.created_by = ?  and a2.is_active = true and a2.is_deleted = false  group by a2.form_answer_id )
		 SELECT is_recall,form_answer_id, id, name, date_created,is_exceptional_approval, 
		 CASE WHEN statuss = 398 THEN 'Cancelled' WHEN statuss = 397 THEN 'Return' when statuss = 67 then 'Completed' ELSE title END, CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' when statuss = 67 then 'completed'  ELSE value END, statuss, 
			CASE WHEN (SELECT 1	FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND (ap.group_type = 'local' or ap.country = 900001)
			AND ap.value <> 'approved' and approval.is_active = true and approval.is_deleted = false LIMIT 1) = 1 THEN seq ELSE 1 END AS sequence_no, 
			 group_type, total_cost,
             activity_start_date,
             activity_end_date,
			 team,
			 activity_name,
             product,
             multi_client_product,
             type_of_activity,
             event_type,
			 countrytitle, countryvalue,
			 currencytitle, currencyvalue ,eventCode,eventSeq, change_request_summary 
			 ,(select (extract(epoch from max(ut.accessed_datetime))::integer)::text from user_tracking ut where ut.event_id =form_answer_id::text and ut."action"= 'Recall')   
			 ,(select (extract(epoch from max(ut.accessed_datetime))::integer)::text from user_tracking ut where ut.event_id =form_answer_id::text and ut."action"= 'Recall Cancel') 
			 FROM approval WHERE approver_id = ?
			 and form_answer_id not in (select distinct form_answer_id from form_set_value )
    `
	var inputargs []interface{}
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)

	if searchItem != "" {
		queryString = queryString + ` and 
		(name ilike ? or
		eventcode ilike ? or
		replace((to_char(eventseq, '99990999')), ' ', '') ilike ? or 
		(select CONCAT (eventcode,'-', replace((to_char(eventseq, '99990999')), ' ', ''))) ilike ? or
		(CASE WHEN statuss = 398 THEN 'Cancelled' WHEN statuss = 397 THEN 'Return' WHEN statuss = 59 THEN 'Rejected' when statuss = 67 then 'Completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'Pending Approval'ELSE value END) ilike ? or 
		(CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) ilike ? or 
		activity_name ilike ? or 
	    type_of_activity ilike ? or 
	    event_type ilike ? or
	    team ilike ? or
	    product ilike ? or
	   total_cost::text ilike ?) 
	   `

		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%",
		)
	}

	if activityFilter != nil {
		queryString = queryString + ` AND activity && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		queryString = queryString + ` AND activity && ?
		`
		inputargs = append(inputargs, eventFilter)
	}

	if statusFilter != nil || exceptionalStatusFilter != nil {
		queryString = queryString + ` and   ((is_exceptional_approval =true and (CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 452 THEN 'recalled' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) = any( ? )) or((CASE WHEN statuss = 398 THEN 'cancelled' WHEN statuss = 397 THEN 'return' WHEN statuss = 59 THEN 'rejected' when statuss = 452 THEN 'recalled' when statuss = 67 then 'completed'
		when value = 'pending' and (CASE WHEN (SELECT 1 FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND ap.group_type = 'local'
		AND ap.value <> 'approved' LIMIT 1) = 1 THEN seq ELSE 1 END) = 1 then 'pendingapproval'ELSE value END) = any( ? ) and is_exceptional_approval =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}

	if totalCostRange != nil {
		queryString = queryString + ` AND (total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilter != "" {
		queryString = queryString + ` AND (? between
			(case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end)  and 
				(case when
					(activity_end_date) ~ '^[0-9\.]+$'
					then (to_timestamp((activity_end_date):: integer))::timestamp::date end))
		`
		inputargs = append(inputargs, dateFilter)
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			queryString = queryString + ` and ((( case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (activity_end_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_end_date):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			queryString = queryString + ` and (( case when
                (activity_start_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_start_date):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			queryString = queryString + ` and (( case when
                (activity_end_date) ~ '^[0-9\.]+$'
                then (to_timestamp((activity_end_date):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			queryString = queryString + ` and ((approval.date_created::date >= ? ) and 
                (approval.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			queryString = queryString + ` and (approval.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			queryString = queryString + ` and (approval.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}
	if sortElements != nil {
		sort := strings.Join(sortElements, ",")
		queryString = queryString + ` ORDER BY (case when (value='pending' and statuss=57  and (CASE WHEN (SELECT 1	FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND (ap.group_type = 'local' or ap.country = 900001)
		AND ap.value <> 'approved' and approval.is_active = true and approval.is_deleted = false LIMIT 1) = 1 THEN seq ELSE 1 end)=1) then 1 else 2 end) asc , ` + sort + ` ,date_created desc`
	} else {
		queryString = queryString + ` ORDER BY (case when (value='pending' and statuss=57  and (CASE WHEN (SELECT 1	FROM approval ap WHERE ap.form_answer_id = approval.form_answer_id AND (ap.group_type = 'local' or ap.country = 900001)
		AND ap.value <> 'approved' and approval.is_active = true and approval.is_deleted = false LIMIT 1) = 1 THEN seq ELSE 1 end)=1) then 1 else 2 end) asc,activity_start_date desc , eventSeq desc`
	}

	if limit > 0 {
		if Page > 0 {
			queryString = queryString + ` limit ? offset ?`

			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, Page)
		} else {
			queryString = queryString + ` limit ? offset ?`
			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, 0)
		}
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputargs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	var result []entity.ListPendingApprovalEntity
	for rows.Next() {
		var pendingList entity.ListPendingApprovalEntity
		var clientProductRawStr sql.NullString
		var clientProductList []map[string]string
		err := rows.Scan(
			&pendingList.IsRecall,
			&pendingList.FormAnswerID, &pendingList.ID, &pendingList.Name, &pendingList.SubmissionDate, &pendingList.IsExceptionalApprover, &pendingList.Status, &pendingList.StatusValue, &pendingList.FormStatus,
			&pendingList.SequenceNo, &pendingList.GroupType, &pendingList.TotalCost, &pendingList.StartDate, &pendingList.EndDate, &pendingList.Team, &pendingList.ActivityName,
			&pendingList.ProductName, &clientProductRawStr,
			&pendingList.ActivityType, &pendingList.EventType, &pendingList.CountryTitle, &pendingList.CountryValue, &pendingList.CurrencyTitle, &pendingList.CurrencyValue, &pendingList.EventCode,
			&pendingList.EventSeq, &pendingList.ChangeRequestSummary, &pendingList.RecallAccessTime, &pendingList.RecallCancelTime)
		if err != nil {
			panic(err)
		}

		if clientProductRawStr.Valid {
			// purposely ignore the error as it should cause no harm in this context.
			json.Unmarshal([]byte(clientProductRawStr.String), &clientProductList)
			// will reassign client and product when multi-products exist
			_, productList := readProductList(clientProductList)
			if len(productList) != 0 {
				pendingList.ProductName = sql.NullString{
					String: strings.Join(productList, `, `),
					Valid:  true,
				}
			}
		}
		result = append(result, pendingList)
	}
	maxTotalCost, err = FetchMaxTotalCostForApprover(userID)
	if err != nil {
		log.Printf("%s - Error: %s", functionName, err.Error())
		return result, 0, err
	}
	return result, maxTotalCost, nil
}

func FetchMaxTotalCostForApprover(userId string) (float64, error) {
	functionName := "FetchMaxTotalCostForApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result sql.NullFloat64
	querystring := `with approver_event as (
		select form_answer_id, MAX(set_number) as max_set FROM approvers
		WHERE approver_id = $1
		GROUP BY form_answer_id
		) select max(fa.total_cost)
		from approvers ap
		inner join form_answers fa on fa.id = ap.form_answer_id and fa.is_deleted =false
		inner join approver_event ae
		ON ae.form_answer_id = ap.form_answer_id AND ap.set_number = ae.max_set
		where ap.form_answer_id IN (SELECT form_answer_id FROM approver_event)`

	err := pool.QueryRow(context.Background(), querystring, userId).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	return result.Float64, nil
}

func GetApproverRoleByFormID(formID string) ([]int, error) {
	functionName := "GetApproverRoleByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var approverID []int
	query := `select approval_role from "user" 
	where id in (select approved_by from approval_log where form_answer = $1 and is_active = true)`
	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetApproverRoleByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value int
		rows.Scan(&value)
		approverID = append(approverID, value)
	}
	return approverID, nil
}

func GetSequenceByApproverID(ApproverID string) ([]int, error) {
	functionName := "GetSequenceByApproverID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var sequenceID []int
	query := `select sequence_no from approver where approver_id = $1`
	rows, err := pool.Query(context.Background(), query, ApproverID)
	logengine.GetTelemetryClient().TrackEvent("GetSequenceByApproverID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var value int
		rows.Scan(&value)
		sequenceID = append(sequenceID, value)
	}
	return sequenceID, nil
}

func FetchActiveDirectoryValue(country *int) ([]entity.Dropdown, error) {
	functionName := "FetchActiveDirectoryValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select u.active_directory ,u.id  from "user" u 
	inner join roles_permission rp on rp.user_role_id =u.user_role_id 
	where u.country =$1 and  rp.requestor  =true`

	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Description, &answerEntity.Value)
		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchActionByMasterDataAccessValue(country *int) ([]entity.Dropdown, error) {
	functionName := "FetchActionByMasterDataAccessValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select concat(u.first_name ,' ',u.last_name ) ,u.id  from "user" u 
	inner join roles_permission rp on rp.user_role_id =u.user_role_id 
	where u.country =$1 and rp.master_data =true`

	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Description, &answerEntity.Value)
		result = append(result, answerEntity)
	}
	return result, nil
}

func GetApproverAcceptStatus(approverID string) bool {
	functionName := "GetApproverAcceptStatus()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM approval_log WHERE actioned_by = $1 AND status = 58 and is_active = true"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approverID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func InsertApprovalLog(ctx context.Context, inputEntity *entity.SubmissionFormApprovalEntity, isApollo *bool) (err error, message string) {
	functionName := "InsertApprovalLog()"
	log.Println(functionName)
	message = ""
	now := util.GetCurrentTime()
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	requestorResponseStatusCode := codes["requestorresponse"].ID

	approverRequestAction := codes["pendingresponse"].ID
	groupCodes := codeController.GetValueKeyCodes()["group"]
	groupID := groupCodes["local"].ID
	rejectedID := codes["rejected"].ID
	returnStatusCode := codes["return"].ID
	pool = GetPool()
	sendToVeeva := func(formAnswerID string, status string) {
		veeva.GetAuthRest()
		UpsertVeevaEventLogs(ctx, formAnswerID, false, false, "", "", "")
		go InsertIntoVeeva(formAnswerID, status)
	}

	tx, err := pool.Begin(ctx)
	if err != nil {
		panic(err)
	}
	defer tx.Rollback(ctx)

	if inputEntity.Action == requestorResponseStatusCode {
		querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
		_, err := tx.Exec(ctx, querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, 0, inputEntity.ApprovalRole, approverRequestAction, inputEntity.Comment, isApollo)
		if err != nil {
			panic(err)

		}

		querystring = "UPDATE approvers SET status = $3 ,modified_by = $4, last_modified = $5 WHERE form_answer_id = $1 AND approver_id = $2 and is_active = true"
		_, err = tx.Exec(ctx, querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, approverRequestAction, inputEntity.ApprovedBy, now)
		if err != nil {
			panic(err)
		}

	} else {
		if inputEntity.Action == returnStatusCode {
			querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
			_, err := tx.Exec(ctx, querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, 0, inputEntity.ApprovalRole, inputEntity.Action, inputEntity.Comment, isApollo)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - error: {%s}", functionName, err.Error())
				return err, message
			}
			querystring = `UPDATE approvers SET status = $3 ,modified_by = $2, last_modified = $4 WHERE form_answer_id = $1 and is_active = true`
			_, err = tx.Exec(ctx, querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, inputEntity.Action, now)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - error: {%s}", functionName, err.Error())
				return err, message
			}
			if inputEntity.GroupStatus == groupID {
				if !CheckRegionalApproverPresentInApprovers(inputEntity.FormAnswerID.String()) {
					querystring = `update form_answers set status = $1, local_approval_status = $2, is_exceptional_approval = $4 where id = $3`
					_, err = tx.Exec(ctx, querystring, inputEntity.Action, inputEntity.Action, inputEntity.FormAnswerID, inputEntity.IsExceptionalApprover)
					if err != nil {
						logengine.GetTelemetryClient().TrackException(err)
						log.Printf("%s - error1: {%s}", functionName, err.Error())
						return err, message
					}
					queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now(), exceptionalapprover = $4
						WHERE form_answer_id = $1 `
					_, err = tx.Exec(ctx, queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue, inputEntity.IsExceptionalApprover)
					if err != nil {
						logengine.GetTelemetryClient().TrackException(err)
						log.Printf("%s - Error: %s ", functionName, err.Error())
						return err, message
					}
				} else {
					querystring = `update form_answers set status = $1, local_approval_status = $2, regional_approval_status = $4, is_exceptional_approval = $5 where id = $3`
					_, err = tx.Exec(ctx, querystring, inputEntity.Action, inputEntity.Action, inputEntity.FormAnswerID, inputEntity.Action, inputEntity.IsExceptionalApprover)
					if err != nil {
						logengine.GetTelemetryClient().TrackException(err)
						log.Printf("%s - error2: {%s}", functionName, err.Error())
						return err, message
					}
					queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now() ,exceptionalapprover = $4
						WHERE form_answer_id = $1 `
					_, err = tx.Exec(ctx, queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue, inputEntity.IsExceptionalApprover)
					if err != nil {
						logengine.GetTelemetryClient().TrackException(err)
						log.Printf("%s - Error: %s ", functionName, err.Error())
						return err, message
					}
				}

			} else {
				querystring = `update form_answers set status = $1, regional_approval_status = $2, local_approval_status = $4, is_exceptional_approval = $5 where id = $3`
				_, err = tx.Exec(ctx, querystring, inputEntity.Action, inputEntity.Action, inputEntity.FormAnswerID, inputEntity.Action, inputEntity.IsExceptionalApprover)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - error3: {%s}", functionName, err.Error())
					return err, message
				}
				queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now() ,exceptionalapprover = $4
						WHERE form_answer_id = $1 `
				_, err = tx.Exec(ctx, queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue, inputEntity.IsExceptionalApprover)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - Error: %s ", functionName, err.Error())
					return err, message
				}
			}

		} else {
			querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
			_, err := tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, 0, inputEntity.ApprovalRole, inputEntity.Action, inputEntity.Comment, isApollo)
			if err != nil {
				panic(err)
			}
			querystring = `UPDATE approvers SET status = $3 ,modified_by = $4, last_modified = $5 WHERE form_answer_id = $1 AND approver_id = $2 and is_active = true`
			_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, inputEntity.Action, inputEntity.ApprovedBy, now)
			if err != nil {
				panic(err)
			}
			receiverID, _, _ := GetCreatedByIDFromFormAnswerID(inputEntity.FormAnswerID)
			if inputEntity.GroupStatus == groupID {
				if inputEntity.Action == rejectedID {
					querystring = "UPDATE form_answers SET local_approval_status = $2, status = $3 WHERE id = $1"
					_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.Action, inputEntity.Action)
					if err != nil {
						panic(err)
					}
					queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now() 
						WHERE form_answer_id = $1 `
					_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue)
					if err != nil {
						panic(err)
					}
					err = InsertStatusNotification(inputEntity.FormAnswerID.String(), inputEntity.ApprovedBy.String(), inputEntity.Action, receiverID)
					if err != nil {
						panic(err)
					}
				} else {
					if !inputEntity.IsCompliance {
						if inputEntity.ApprovedStatus == 58 {
							if !CheckRegionalApproverPresentInApprovers(inputEntity.FormAnswerID.String()) {
								querystring = "UPDATE form_answers SET local_approval_status = $2, status= $3 WHERE id = $1"
								_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedStatus, inputEntity.ApprovedStatus)
								if err != nil {
									panic(err)
								}

								queryString := `UPDATE reporting_db
									SET status_title = $2, status_value = $3, updated_date = now() 
									WHERE form_answer_id = $1 `
								_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue)
								if err != nil {
									panic(err)
								}

								sendToVeeva(inputEntity.FormAnswerID.String(), "Approved")

								err = InsertStatusNotification(inputEntity.FormAnswerID.String(), inputEntity.ApprovedBy.String(), inputEntity.Action, receiverID)
								if err != nil {
									panic(err)
								}

							} else {
								querystring = "UPDATE form_answers SET local_approval_status = $2 WHERE id = $1"
								_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedStatus)
								if err != nil {
									panic(err)
								}

								err = InsertStatusNotification(inputEntity.FormAnswerID.String(), inputEntity.ApprovedBy.String(), inputEntity.Action, receiverID)
								if err != nil {
									panic(err)
								}
							}
						}
					}
				}
			} else {
				if inputEntity.Action == rejectedID {
					querystring = "UPDATE form_answers SET regional_approval_status = $2, status = $3 WHERE id = $1"
					_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.Action, inputEntity.Action)
					if err != nil {
						panic(err)
					}
					queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now() 
						WHERE form_answer_id = $1 `
					_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue)
					if err != nil {
						panic(err)
					}
					err = InsertStatusNotification(inputEntity.FormAnswerID.String(), inputEntity.ApprovedBy.String(), inputEntity.Action, receiverID)
					if err != nil {
						panic(err)
					}
				} else {
					if !inputEntity.IsCompliance {
						if inputEntity.ApprovedStatus == 58 {
							querystring = "UPDATE form_answers SET regional_approval_status = $2, status = $3 WHERE id = $1"
							_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedStatus, inputEntity.ApprovedStatus)
							if err != nil {
								panic(err)
							}

							sendToVeeva(inputEntity.FormAnswerID.String(), "Approved")

							codes1 := codeController.GetIdKeyCodes()["approvedstatus"]
							approvedStatusTitle := codes1[inputEntity.ApprovedStatus].Title
							approvedStatusValue := codes1[inputEntity.ApprovedStatus].Value

							queryString := `
								UPDATE reporting_db
								SET status_title = $2, status_value = $3, updated_date = now() 
								WHERE form_answer_id = $1 `
							_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, approvedStatusTitle, approvedStatusValue)
							if err != nil {
								panic(err)
							}

							err = InsertStatusNotification(inputEntity.FormAnswerID.String(), inputEntity.ApprovedBy.String(), inputEntity.Action, receiverID)
							if err != nil {
								panic(err)
							}
						}
					}
				}
			}
		}
	}

	if err := tx.Commit(context.Background()); err != nil {
		panic(err)
	}
	return nil, "Approval action submitted successfully"
}

func UpdateRequestorResponse(inputEntity *entity.SubmissionFormApprovalEntity, isApollo *bool) (err error, message string) {
	functionName := "UpdateRequestorResponse()"
	log.Println(functionName)
	action := "pending"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	pendingAction := codes[action].ID

	responseAction := "requestorresponse"
	requestorResponseAction := codes[responseAction].ID

	pendingResponseAction := codes["pendingresponse"].ID

	message = ""
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("UpdateRequestorResponse query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to begin transaction"), message
	}
	defer tx.Rollback(context.Background())
	if inputEntity.FormAnswerID != uuid.Nil {
		querystring := "INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)"
		commandTag, insertErr := tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.RequestorID, 0, inputEntity.ApprovalRole, requestorResponseAction, inputEntity.Comment, isApollo)
		if insertErr != nil {
			log.Printf("%s - error: {%s}", functionName, insertErr.Error())
			logengine.GetTelemetryClient().TrackException(insertErr)
			return err, message
		}
		if commandTag.RowsAffected() != 1 {
			message = "Invalid requestor response data"
			return err, message
		} else {
			message = "Requestor response update successfully"
		}

		// querystring := "UPDATE requestor_response SET status = $4, requestor_comment = $2 WHERE form_answer_id = $1 AND status = $3 RETURNING(approver_id)"
		// var dbApproverID string
		// err := tx.QueryRow(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.Comment, pendingAction, approvedAction).Scan(&dbApproverID)
		// if err != nil {
		// 	tx.Rollback(context.Background())
		// 	log.Printf("%s - error: {%s}", functionName, err.Error())
		// 	message = "Failed to update in requestor response"
		// 	return err, message
		// }
		timenow := util.GetCurrentTime()
		approverQueryString := `
		UPDATE approvers SET status = $3, last_modified = $4, modified_by = $5
		FROM (
			SELECT * FROM approvers WHERE form_answer_id = $1 AND status = $2 AND is_active = true LIMIT 1
		) as approver
		WHERE approvers.form_answer_id = $1 AND approvers.approver_id = approver.approver_id
		`

		//approverUpdateQueryString := "UPDATE approvers SET status = $3 WHERE form_answer_id = $1 AND approver_id = $2"
		approverCommandTag, err := tx.Exec(context.Background(), approverQueryString, inputEntity.FormAnswerID, pendingResponseAction, pendingAction, timenow, inputEntity.RequestorID)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - error: {%s}", functionName, err.Error())
			message = "Failed to update in approver status"
			return err, message
		}
		if approverCommandTag.RowsAffected() != 1 {
			message = "Invalid requestor response data"
			return err, message
		} else {
			message = "Requestor response update successfully"
		}
		txErr := tx.Commit(context.Background())
		if txErr != nil {
			message = "Failed to commit requestor response data"
			return err, message
		}
	}

	return err, message
}

func InsertFormAnswerAttachmentId(inputEntity *entity.FormAttachment, answer pgtype.JSONB, userID *string, isApollo *bool) (err error, message string) {
	functionName := "InsertFormAnswerAttachment()"
	log.Println(functionName)
	action1 := "completed"
	codes1 := codeController.GetValueKeyCodes()["formanswertype"]
	completedId := codes1[action1].ID
	completedValue := codes1[action1].Value
	completedTitle := codes1[action1].Title
	message = ""
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertFormAnswerAttachmentId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to begin transaction"), message
	}
	defer tx.Rollback(context.Background())
	insertFormAnswerQueryString := "UPDATE form_answers SET attachment = $1 ,status = $2 WHERE id = $3"
	_, err = tx.Exec(context.Background(), insertFormAnswerQueryString, inputEntity.Attachments, completedId, inputEntity.FormAnswerID)
	log.Println(err)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to insert attachment data in form_answers"), message
	} else {
		queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now(),completion_date=now() 
						WHERE form_answer_id = $1 `
		_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, completedTitle, completedValue)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err, message
		}
		query := `INSERT INTO form_answer_logs (form_answers_id, created_by, answers, status,is_apollo) VALUES($1, $2, $3, $4,$5);`
		_, err = tx.Exec(context.Background(), query, inputEntity.FormAnswerID, userID, answer, completedId, isApollo)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err, message
		}
		message = "successfully added attachment !"
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		message = "Failed to commit requestor response data"
		return err, message
	}

	func(input string) {
		veeva.GetAuthRest()
		InsertIntoVeeva(input, "Completed")
	}(inputEntity.FormAnswerID.String())

	return err, message
}
func FetchFormAnswersForLinkedEvents(input *entity.FormAnswerFetchEntity, approvalRoleID *string) (*entity.FormAnswerEntity, string, string) {
	functionName := "FetchFormAnswersForLinkedEvents()"
	log.Println(functionName)
	var form entity.FormAnswerEntity
	if pool == nil {
		pool = GetPool()
	}
	var name sql.NullString
	logengine.GetTelemetryClient().TrackEvent("FetchFormAnswersForLinkedEvents query called")
	queryString := `SELECT fa.answers, concat(u.first_name,' ',u.last_name)  from form_answers fa
	inner join "user" u on u.id =fa.created_by 
	where fa.id = $1`
	err := pool.QueryRow(context.Background(), queryString, input.FormId).Scan(&form.Design, &name)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, "", err.Error()
	}
	formAnswerAttachment, err := FetchFormAnswersAttachment(&input.FormId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, "", err.Error()
	}
	form.Attachments = formAnswerAttachment
	return &form, name.String, ""
}

func DeleteFormAnswerAttachments(formAnswerID uuid.UUID) error {
	functionName := "DeleteFormAnswerAttachments()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("DeleteFormAnswerAttachments query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	insertFormAnswerQueryString := "Update form_answer_attachments set is_active = false, is_deleted = true where form_answer_id = $1 "
	_, err = tx.Exec(context.Background(), insertFormAnswerQueryString, formAnswerID)
	log.Println(err)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to delete attachment data in form_answers")
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return err
	}
	return err
}

func InsertFormAnswerAttachment(entity entity.FormAttachmentInput, isApollo *bool) (id string, err error) {
	functionName := "InsertFormAnswerAttachment()"
	log.Println(functionName)
	id = ""
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		return id, errors.New("Failed to begin transaction")
	}
	defer tx.Rollback(context.Background())
	var attachmentId string
	insertUploadsQueryString := "INSERT INTO form_answer_attachments (form_answer_id ,type_desc ,category_desc ,url ,is_deleted ,is_active,is_apollo) VALUES($1, $2, $3, $4, false, true,$5) RETURNING(id)"
	err = tx.QueryRow(context.Background(), insertUploadsQueryString, entity.FormAnswerId, entity.Type, entity.Description, entity.Url, isApollo).Scan(&attachmentId)
	log.Println(err)
	logengine.GetTelemetryClient().TrackEvent("InsertFormAnswerAttachment query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return id, errors.New("Failed to insert attachment data")
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return id, errors.New("Failed to commit requestor response data")

	}

	return attachmentId, nil

}

func IsFormAnswerExists(formAnswerID *uuid.UUID) bool {
	functionName := "IsFormAnswerExists()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 "
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

func IsFormAnswerExistsInFormAnswerAttachment(formAnswerID uuid.UUID) bool {
	functionName := "IsFormAnswerExistsInFormAnswerAttachment()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "select 1 from form_answer_attachments where form_answer_id = $1 and is_active = true and is_deleted = false"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

// func IsFormIDExistsInRequestorResponse(formAnswerID *uuid.UUID) bool {
// 	functionName := "IsFormIDExistsInRequestorResponse()"
// 	log.Println(functionName)
// 	action := "pending"
// 	codes := codeController.GetValueKeyCodes()["approvedstatus"]
// 	pendingAction := codes[action].ID
// 	if pool == nil {
// 		pool = GetPool()
// 	}
// 	var result bool
// 	querystring := "SELECT 1 FROM requestor_response WHERE form_answer_id = $1 and status = $2"
// 	var hasValue int
// 	err := pool.QueryRow(context.Background(), querystring, formAnswerID, pendingAction).Scan(&hasValue)
// 	if err == nil {
// 		result = true
// 	}
// 	return result
// }

func IsFormIDApproved(formAnswerID *uuid.UUID) bool {
	functionName := "IsFormIDApproved()"
	log.Println(functionName)
	action := "approved"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedAction := codes[action].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 and status = $2"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approvedAction).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func AlreadyApprovedByLocalApprovers(formAnswerID string) bool {
	functionName := "AlreadyApprovedByLocalApprovers()"
	log.Println(functionName)
	action := "approved"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedAction := codes[action].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM form_answers WHERE id = $1 AND local_approval_status = $2"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approvedAction).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckActionableFormAnswer(formAnswerID string, userID string) bool {
	functionName := "CheckActionableFormAnswer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["approved"].ID
	querystring := `with approval as
	(
	select id ,form_answer_id ,approver_id, status ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers where status <> $3 AND form_answer_id = $2)
	select 1 from approval where row_number = 1 and approver_id = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, userID, formAnswerID, status).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckFormFirstApproverApproved(formAnswerID string, userRole int) bool {
	functionName := "CheckActionableFormAnswer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["approved"].ID
	querystring := `select 1 from approvers where form_answer_id = $1
	and approval_role = ($3 - 1) and status = $2 `
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, status, userRole).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}
	return result
}

func CheckApproverRoleSequenceOneOrNot(formAnswerID string, userRole int) bool {
	functionName := "CheckApproverRoleSequenceOneOrNot()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from approvers where form_answer_id = $1
	and approval_role = ($2-1)`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, userRole).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}
	return result
}

func CheckLocalApproversApprovedFormAnswer(formAnswerID string) bool {
	functionName := "CheckActionableFormAnswer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["approved"].ID
	regionalApprovalStatus := code["pending"].ID
	querystring := `select 1 from form_answers where id = $1 
	and local_approval_status = $2 and regional_approval_status = $3`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, status, regionalApprovalStatus).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckRequestedResponseData(formAnswerID string, userID string) bool {
	functionName := "CheckRequestedResponseData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from requestor_response where form_answer_id = $1
	and requestor = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, userID).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

func GetRequestorResponseFormAnswerListCount(country int, userID string, Page int, limit int, status string, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool, product string, productOwner string, team string, requestor string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) (int, error) {
	functionName := "GetRequestorResponseFormAnswerListCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	codes2 := codeController.GetIdKeyCodes()["country"]
	countryValue := codes2[country].Value
	var result int
	querystring := ` SELECT count(rd.form_answer_id) from reporting_db rd
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false 
	WHERE rd.country_value = ? 
	and (rd.created_by = ? or rd.activity_start_date [1]=(select CONCAT(u.first_name ,' ', u.last_name) from "user" u where u.id = ?)) `
	var inputargs []interface{}
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}
	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}
	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}
	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}

	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)

	err := pool.QueryRow(context.Background(), querystring, inputargs...).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	return result, nil
}

func GetRequestorResponseFormAnswerList(country int, userID string, Page int, limit int, status string, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool, product string, productOwner string, team string, requestor string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string, recallActionType string) ([]entity.FormAnswer, float64, error) {
	functionName := "GetRequestorResponseFormAnswerList()"
	var result []entity.FormAnswer
	if pool == nil {
		pool = GetPool()
	}

	codes2 := codeController.GetIdKeyCodes()["country"]
	countryValue := codes2[country].Value

	querystring := ` SELECT rd.form_answer_id,rd.exceptionalapprover, rd.completion_date, rd.requestor_name, 
	rd.active_directory, rd.date_created ,  rd.status_title, 
	rd.status_value, rd.country_title, rd.country_value,rd.event_code, rd.event_seq, rd.currency_title, rd.currency_value, 
	rd.total_cost, rd.activity_start_date[3],rd.activity_start_date [4],rd.team,rd.activity_name ,rd.product ,rd.activity_start_date[7]  , rd.govt_non_govt_hcp,rd.activity_start_date[6] ,rd.event_expenses, 
	rd.expense_cost, rd.event_type[1],rd.event_type [2],c.id ,rd.activity_start_date [1],(case when rd.created_by = ? then false else true end) as is_owner,rd.hcp_cost_list,rd.hcp_type_of_expense,rd.product_owner_name,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,42,values,0,value}' as requesting_hco,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,43,values,0,value}' as recipient
	,(select (extract(epoch from max(ut.accessed_datetime))::integer)::text from user_tracking ut where ut.event_id =fa.id::text and ut."action"= 'Approve')  
	,(select (extract(epoch from max(ut.accessed_datetime))::integer)::text from user_tracking ut where ut.event_id =fa.id::text and ut."action"= 'Approve Cancel'),
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,23,values,0,value}' as meeting_mode,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,24,values,0,value}' as virtual_event_details,
	rd.board_of_directors_for_hco_sponsorship,rd.level_of_influence,rd.type_of_engagements,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,46,values,0,value}' as multi_client_product
	FROM reporting_db rd
	inner join code c on c.value =status_value
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false 
	WHERE rd.country_value = ? 
	and (rd.created_by = ? or rd.activity_start_date [1]=(select CONCAT(u.first_name ,' ', u.last_name) from "user" u where u.id = ?)) `
	var inputargs []interface{}

	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, userID)
	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}
	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}
	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}
	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}

	if sortElements != nil {
		sort := strings.Join(sortElements, ",")
		querystring = querystring + ` ORDER BY ` + sort
	} else {
		querystring = querystring + ` ORDER BY rd.date_created desc`
	}

	if limit > 0 {
		if Page > 0 {
			querystring = querystring + ` limit ? offset ?`

			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, Page)
		} else {
			querystring = querystring + ` limit ? offset ?`
			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, 0)
		}
	} else if isExcel {
		querystring = querystring + ` limit 500`
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputargs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		var answerEntity entity.FormAnswer
		var clientProductRawStr sql.NullString
		var clientProductList []map[string]string
		err := rows.Scan(&answerEntity.ID, &answerEntity.IsExceptionalApprover,
			&answerEntity.CompletionDate, &answerEntity.RequestorName,
			&answerEntity.RequestorActiveDirectory, &answerEntity.CreatedDate,
			&answerEntity.Status, &answerEntity.StatusValue, &answerEntity.CountryTitle,
			&answerEntity.CountryValue, &answerEntity.EventCode, &answerEntity.EventSeq,
			&answerEntity.CurrencyTitle, &answerEntity.CurrencyValue, &answerEntity.TotalCost,
			&answerEntity.StartDate, &answerEntity.EndDate, &answerEntity.Team, &answerEntity.ActivityName,
			&answerEntity.ProductName, &answerEntity.Venue, &answerEntity.GovtNonGovtHcp, &answerEntity.EventOrganizer,
			&answerEntity.EventExpenses,
			&answerEntity.ExpenseCost, &answerEntity.ActivityTypes, &answerEntity.EventTypes, &answerEntity.StatusCheck,
			&answerEntity.EventOwner, &answerEntity.IsOwner, &answerEntity.HcpCostList, &answerEntity.HcpTypeOfExpense, &answerEntity.ProductOwner, &answerEntity.RequestingHco, &answerEntity.Recipient, &answerEntity.ApproveAccessTime, &answerEntity.ApproveCancelTime, &answerEntity.MeetingMode, &answerEntity.VirtualEventDetails, &answerEntity.BoardOfDirectory, &answerEntity.LevelOfInfluence, &answerEntity.TypeOfEngagements,
			&clientProductRawStr)
		if err != nil {
			panic(err)
		}

		if clientProductRawStr.Valid {
			// purposely ignore the error as it should cause no harm in this context.
			json.Unmarshal([]byte(clientProductRawStr.String), &clientProductList)
			// will reassign client and product when multi-products exist
			_, productList := readProductList(clientProductList)
			if len(productList) != 0 {
				answerEntity.ProductName = sql.NullString{
					String: strings.Join(productList, `, `),
					Valid:  true,
				}
			}
		}

		result = append(result, answerEntity)
	}
	var maxTotalCost float64
	maxTotalCost, err = FetchMaxTotalCostForRequestor(userID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, 0, err
	}
	return result, maxTotalCost, nil
}

func FetchMaxTotalCostForRequestor(userId string) (float64, error) {
	functionName := "FetchMaxTotalCostForRequestor()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result sql.NullFloat64
	querystring := `select max(fa.total_cost) from form_answers fa 
	where fa.created_by = $1 and fa.is_deleted =false `

	err := pool.QueryRow(context.Background(), querystring, userId).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}

	return result.Float64, nil
}

func GetFinanceFormAnswerListCount(country int,
	Page int, limit int, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string,
	totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool,
	product string, productOwner string, team string, requestor string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) (int, error) {

	pool = GetPool()

	codes := codeController.GetValueKeyCodes()["formanswertype"]
	completedStatusID := codes["completed"].Value
	codes1 := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedStatusID := codes1["approved"].Value
	codes2 := codeController.GetIdKeyCodes()["country"]
	countryValue := codes2[country].Value
	var result int
	querystring := `SELECT count(rd.form_answer_id)
    FROM reporting_db rd
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false 
	WHERE rd.country_value = ? 
	and rd.status_value in (?,?) `

	var inputargs []interface{}
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, completedStatusID)
	inputargs = append(inputargs, approvedStatusID)

	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}
	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}

	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}

	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}

	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}

	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	err := pool.QueryRow(context.Background(), querystring, inputargs...).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}

	return result, nil
}

func GetFinanceFormAnswerList(country int, Page int, limit int, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string,
	dateFilterEnd string, sortElements []string, isExcel bool, product string, productOwner string, team string, requestor string,
	dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) ([]entity.FormAnswerForAdmins, float64, error) {
	functionName := "GetFinanceFormAnswerList()"
	log.Println(functionName)
	var result []entity.FormAnswerForAdmins
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["formanswertype"]
	completedStatusID := codes["completed"].Value
	codes1 := codeController.GetValueKeyCodes()["approvedstatus"]
	approvedStatusID := codes1["approved"].Value
	codes2 := codeController.GetIdKeyCodes()["country"]
	countryValue := codes2[country].Value

	querystring := `SELECT rd.form_answer_id, rd.completion_date, rd.requestor_name, 
	rd.active_directory, rd.date_created, rd.event_code, rd.event_seq, rd.status_title, 
	rd.status_value, rd.country_title, rd.country_value, rd.currency_title, rd.currency_value, 
	rd.total_cost, rd.activity_start_date, rd.govt_non_govt_hcp, rd.event_expenses, 
	rd.expense_cost, rd.event_type, rd.team, rd.exceptionalapprover,rd.hcp_cost_list,rd.hcp_type_of_expense,rd.product_owner_name,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,42,values,0,value}' as requesting_hco,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,43,values,0,value}' as recipient,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,23,values,0,value}' as meeting_mode,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,24,values,0,value}' as virtual_event_details,
	rd.board_of_directors_for_hco_sponsorship,rd.level_of_influence,rd.type_of_engagements,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,46,values,0,value}' as multi_client_product

    FROM reporting_db rd
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false 
	WHERE rd.country_value = ? 
	and rd.status_value in (?,?)
	`
	var inputargs []interface{}
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, completedStatusID)
	inputargs = append(inputargs, approvedStatusID)

	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}
	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}

	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}

	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}

	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}

	if sortElements != nil {
		sort := strings.Join(sortElements, ",")
		querystring = querystring + ` ORDER BY ` + sort
	} else {
		querystring = querystring + ` ORDER BY rd.date_created desc`
	}

	if limit > 0 {
		if Page > 0 {
			querystring = querystring + fmt.Sprintf(` limit %d offset %d`, limit, Page)
		} else {
			querystring = querystring + fmt.Sprintf(` limit %d`, limit)
		}
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	fmt.Println(querystring)
	fmt.Println("-----------------\n", inputargs)
	rows, err := pool.Query(context.Background(), querystring, inputargs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		var answerEntity entity.FormAnswerForAdmins
		var clientProductRawStr sql.NullString
		var clientProductList []map[string]string
		err := rows.Scan(&answerEntity.ID, &answerEntity.CompletionTime, &answerEntity.RequestorName,
			&answerEntity.RequestorActiveDirectory, &answerEntity.CreatedDate,
			&answerEntity.EventCode, &answerEntity.EventSeq, &answerEntity.Status,
			&answerEntity.StatusValue, &answerEntity.CountryTitle, &answerEntity.CountryValue,
			&answerEntity.CurrencyTitle, &answerEntity.CurrencyValue, &answerEntity.TotalCost,
			&answerEntity.SingleDataArray, &answerEntity.GovtNonGovtHcp,
			&answerEntity.EventExpenses, &answerEntity.ExpenseCost, &answerEntity.EventTypes, &answerEntity.Team, &answerEntity.IsException, &answerEntity.HcpCostList, &answerEntity.HcpTypeOfExpense, &answerEntity.ProductOwner, &answerEntity.RequestingHco, &answerEntity.Recipient, &answerEntity.MeetingMode, &answerEntity.VirtualEventDetails, &answerEntity.BoardOfDirectory, &answerEntity.LevelOfInfluence, &answerEntity.TypeOfEngagements,
			&clientProductRawStr)
		if err != nil {
			panic(err)
		}
		// purposely ignore the error as it should cause no harm in this context.
		if clientProductRawStr.Valid {
			json.Unmarshal([]byte(clientProductRawStr.String), &clientProductList)

			// will reassign client and product when multi-products exist
			clientList, productList := readProductList(clientProductList)
			if len(clientList) != 0 {
				answerEntity.ProductOwner = sql.NullString{
					String: strings.Join(clientList, `, `),
					Valid:  true,
				}
			}

			if len(productList) != 0 {
				answerEntity.Product = sql.NullString{
					String: strings.Join(productList, `, `),
					Valid:  true,
				}
			}
		}

		result = append(result, answerEntity)
	}

	fmt.Println("===========================================", len(result))
	var maxTotalCost float64
	maxTotalCost, err = FetchMaxTotalCostForFinance(country)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, 0, err
	}
	return result, maxTotalCost, nil
}

func FetchMaxTotalCostForFinance(country int) (float64, error) {
	functionName := "FetchMaxTotalCostForFinance()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["formanswertype"]
	completedStatusID := codes["completed"].ID
	var result sql.NullFloat64
	querystring := `SELECT max(fa.total_cost) 
	from form_answers fa
	where fa.status = $1 and fa.country = $2 and fa.is_deleted =false`

	err := pool.QueryRow(context.Background(), querystring, completedStatusID, country).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	return result.Float64, nil
}

func GetAdminAnswerListCount(country int, user string, Page int, limit int, userID string, status string, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool, product string, productOwner string, team string, requestor string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) (int, error) {
	functionName := "GetAdminAnswerListCount()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	// codes := codeController.GetValueKeyCodes()["approvedstatus"]
	// draftStatusID := codes["draft"].ID
	codes1 := codeController.GetIdKeyCodes()["country"]
	countryValue := codes1[country].Value
	var result int
	querystring := `SELECT count(rd.form_answer_id)
	FROM reporting_db rd
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false
	WHERE rd.country_value = ? and ((rd.created_by <> ? AND rd.status_value <> ?) OR rd.created_by = ?) `

	var inputargs []interface{}
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, user)
	inputargs = append(inputargs, status)
	inputargs = append(inputargs, userID)
	log.Println(inputargs)

	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}

	if status != "" {
		querystring += ` and rd.status_value = ?`
		inputargs = append(inputargs, status)
	}

	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}

	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	err := pool.QueryRow(context.Background(), querystring, inputargs...).Scan(&result)
	if err != nil {
		return 0, err
	}
	return result, nil
}

func GetAdminAnswerList(country int, Page int, limit int, userID string, status string, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool, product string, team string, requestor string) ([]entity.FormAnswerForAdmins, float64, error) {
	functionName := "GetAdminFormAnswerList()"
	log.Println(functionName)
	var result []entity.FormAnswerForAdmins
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	draftStatusID := codes["draft"].ID
	codes2 := codeController.GetValueKeyCodes()["typeofemail"]
	completedMailStatusID := codes2["formcompleted"].ID
	queryString := `WITH fa AS 
	(	
	SELECT fa.id, CONCAT(u.first_name ,' ', u.last_name) as "name", u.id as userId,
	u.active_directory,
	fa.date_created,
	fa.event_code as eventCode,
	fa.event_seq as eventSeq,
	c.title as title,
	c.value as value,
	c1.title as countrytitle,c1.value as countryvalue,
	c2.title as currencytitle, 
	c2.value as currencyvalue,
	fa.total_cost, 
	fa.answers,
	fa.status,
	(select max (date_created) from email_log where event_id = fa.id and type_of_email = ?) as completion_date
	from form_answers fa
	inner join "user" u on fa.created_by = u.id
	inner join code c on fa.status  = c.id
	inner join code c1 on fa.country = c1.id 
	inner join code c2 on fa.currency = c2.id 
	and fa.country = ?
	and ((fa.created_by <> ? AND fa.status <> ?) OR fa.created_by = ?)
	), groups AS 
	(
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
		arr(item_object)
	), tab_section AS (
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
	), group_questions AS (
		SELECT id, arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT id, arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS 
	(
		SELECT id, arr.item_object->>'id' as qn_id, arr.item_object->>'title' as qn_title, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'id' IN ('type-of-ihcp-activity', 'type-of-event', 'activity-name', 'activity-start-date', 'activity-end-date', 'team', 'product','venue','govt-or-non-govt-hcp',
		'event-organizer','speaker-name','specialty','hco-institute-name','type-of-event-expenses','event-owner','proposed-honorarium') or arr.item_object->>'title' IN ('Expenses')
	),cost_answer as
	(
		SELECT id, arr.item_object as value FROM questions, jsonb_array_elements(qn_values) with ordinality
		arr(item_object) 
		WHERE arr.item_object->>'description' IN ('Total Cost')
	) SELECT fa.id, fa.completion_date, fa.name, fa.active_directory, fa.date_created,fa.eventcode, fa.eventseq, fa.title, fa.value, fa.countrytitle, fa.countryvalue, fa.currencytitle, fa.currencyvalue, fa.total_cost,`
	if isExcel {
		queryString += ` (select array(SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('activity-start-date','activity-end-date','activity-name','product','venue','event-organizer','event-owner') AND questions.id = fa.id)) as activity_start_date,`
	} else {
		queryString += ` (select array(SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('activity-start-date','activity-end-date','team','activity-name','product','venue','event-organizer','event-owner') AND questions.id = fa.id)) as activity_start_date,`
	}
	queryString += `(select array (SELECT (qn_values#>>'{0,value}') FROM  questions WHERE qn_id in ('govt-or-non-govt-hcp','speaker-name','specialty','hco-institute-name','proposed-honorarium') AND questions.id = fa.id)) as govtnongovthcp,
 	(select array(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'type-of-event-expenses' AND questions.id = fa.id)) as eventExpenses,
	(select array(SELECT value#>>'{value}' FROM  cost_answer WHERE cost_answer.id = fa.id)) as expenseCost,
	(select array (SELECT jsonb_array_elements(qn_values)->>'description'::text FROM  questions WHERE qn_id in ('type-of-event','type-of-ihcp-activity') AND questions.id = fa.id)) as event_type
	FROM fa`
	var inputargs []interface{}
	inputargs = append(inputargs, completedMailStatusID, country, userID, draftStatusID, userID)
	where := ` WHERE`
	and := ` AND`
	if activityFilter != nil || eventFilter != nil || status != "" || searchItem != "" || statusFilter != nil || totalCostRange != nil || dateFilterStart != "" || dateFilterEnd != "" || product != "" || team != "" || requestor != "" {
		queryString = queryString + where
	}

	if searchItem != "" {

		queryString += ` (fa.name ilike ? or fa.eventcode ilike ? or replace((to_char(fa.eventseq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (fa.eventcode,'-', replace((to_char(fa.eventseq, '99990999')), ' ', ''))) ilike ? or
			fa.title ilike ? or fa.value ilike ? or 
			(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-name' AND questions.id = fa.id) ilike ? or 
			(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'product' AND questions.id = fa.id) ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}

	if status != "" {
		if searchItem != "" {
			queryString = queryString + and
		}
		queryString += ` fa.status = (select id from code  where value = ?)`
		inputargs = append(inputargs, status)
	}

	if activityFilter != nil {
		if searchItem != "" || status != "" {
			queryString = queryString + and
		}
		queryString = queryString + ` (SELECT array (SELECT jsonb_array_elements(qn_values)->>'value'::text 
		FROM  questions WHERE qn_id = 'type-of-ihcp-activity' AND questions.id = fa.id)) && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		if activityFilter != nil || searchItem != "" || status != "" {
			queryString = queryString + and
		}
		queryString = queryString + ` (SELECT array (SELECT jsonb_array_elements(qn_values)->>'value'::text 
		FROM  questions WHERE qn_id = 'type-of-event' AND questions.id = fa.id)) && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil {
			queryString = queryString + and
		}
		queryString = queryString + ` fa.value = any( ? )
		`
		inputargs = append(inputargs, statusFilter)
	}
	if totalCostRange != nil {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil || statusFilter != nil {
			queryString = queryString + and
		}
		queryString = queryString + ` (fa.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil || statusFilter != nil || totalCostRange != nil {
			queryString = queryString + and
		}
		if dateFilterStart != "" && dateFilterEnd != "" {
			queryString = queryString + `((( case when
                (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-start-date' AND questions.id = fa.id) ~ '^[0-9\.]+$'
                then (to_timestamp((SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-start-date' AND questions.id = fa.id):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-end-date' AND questions.id = fa.id) ~ '^[0-9\.]+$'
                then (to_timestamp((SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-end-date' AND questions.id = fa.id):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			queryString = queryString + ` (( case when
                (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-start-date' AND questions.id = fa.id) ~ '^[0-9\.]+$'
                then (to_timestamp((SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-start-date' AND questions.id = fa.id):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			queryString = queryString + ` (( case when
                (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-end-date' AND questions.id = fa.id) ~ '^[0-9\.]+$'
                then (to_timestamp((SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-end-date' AND questions.id = fa.id):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if product != "" {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil || statusFilter != nil || totalCostRange != nil || dateFilterStart != "" || dateFilterEnd != "" {
			queryString = queryString + and
		}
		queryString = queryString + ` (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'product' AND questions.id = fa.id) ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}

	if team != "" {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil || statusFilter != nil || totalCostRange != nil || dateFilterStart != "" || dateFilterEnd != "" || product != "" {
			queryString = queryString + and
		}
		queryString = queryString + ` (SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'team' AND questions.id = fa.id) ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}

	if requestor != "" {
		if activityFilter != nil || searchItem != "" || status != "" || eventFilter != nil || statusFilter != nil || totalCostRange != nil || dateFilterStart != "" || dateFilterEnd != "" || product != "" || team != "" {
			queryString = queryString + and
		}

		queryString = queryString + ` fa.userId = ?
		`
		inputargs = append(inputargs, requestor)
	}

	if sortElements != nil {
		sort := strings.Join(sortElements, ",")
		queryString = queryString + ` ORDER BY ` + sort
	} else {
		queryString = queryString + ` ORDER BY fa.date_created desc`
	}
	if limit > 0 {
		if Page > 0 {
			queryString = queryString + ` limit ? offset ?`

			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, Page)
		} else {
			queryString = queryString + ` limit ? offset ?`
			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, 0)
		}
	}
	queryString = sqlx.Rebind(sqlx.DOLLAR, queryString)
	rows, err := pool.Query(context.Background(), queryString, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("GetAdminAnswerList query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, 0, err
	}
	for rows.Next() {
		var answerEntity entity.FormAnswerForAdmins
		rows.Scan(&answerEntity.ID, &answerEntity.CompletionTime, &answerEntity.RequestorName, &answerEntity.RequestorActiveDirectory, &answerEntity.CreatedDate, &answerEntity.EventCode, &answerEntity.EventSeq, &answerEntity.Status, &answerEntity.StatusValue, &answerEntity.CountryTitle, &answerEntity.CountryValue, &answerEntity.CurrencyTitle, &answerEntity.CurrencyValue, &answerEntity.TotalCost, &answerEntity.SingleDataArray, &answerEntity.GovtNonGovtHcp, &answerEntity.EventExpenses, &answerEntity.ExpenseCost, &answerEntity.EventTypes)
		result = append(result, answerEntity)
	}
	var maxTotalCost float64
	maxTotalCost, err = FetchMaxTotalCostForAdmin(userID, country)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, 0, err
	}
	return result, maxTotalCost, nil
}

func FetchAdminDataForReport(country int, Page int, limit int, userID string, status string, searchItem string, activityFilter []string, eventFilter []string, statusFilter []string, totalCostRange []int, dateFilterStart string, dateFilterEnd string, sortElements []string, isExcel bool, product string, productOwner string, team string, requestor string, dateCreatedFilterStart string, dateCreatedFilterEnd string, exceptionalStatusFilter []string) ([]entity.FormAnswerForAdmins, error) {
	pool = GetPool()
	codes := codeController.GetIdKeyCodes()["country"]
	countryValue := codes[country].Value
	var result []entity.FormAnswerForAdmins
	querystring := `
	SELECT
	rd.form_answer_id, rd.completion_date, rd.requestor_name, 
	rd.active_directory, rd.date_created, rd.event_code, rd.event_seq, rd.status_title, 
	rd.status_value, rd.country_title, rd.country_value, rd.currency_title, rd.currency_value, 
	rd.total_cost, rd.activity_start_date, rd.govt_non_govt_hcp, rd.event_expenses, 
	rd.expense_cost, rd.event_type, rd.team, rd.exceptionalapprover,rd.hcp_cost_list,
	rd.hcp_type_of_expense,rd.product_owner_name,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,42,values,0,value}' as requesting_hco,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,43,values,0,value}' as recipient,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,23,values,0,value}' as meeting_mode,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,24,values,0,value}' as virtual_event_details,
	rd.board_of_directors_for_hco_sponsorship,rd.level_of_influence ,rd.type_of_engagements,
	fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,46,values,0,value}' as multi_client_product
	FROM reporting_db rd
	inner join form_answers fa on fa.id =rd.form_answer_id and fa.is_deleted =false
	WHERE rd.country_value = ?
	and ((rd.created_by <> ? AND rd.status_value <> ?) OR rd.created_by = ?)
	`
	var inputargs []interface{}
	inputargs = append(inputargs, countryValue)
	inputargs = append(inputargs, userID)
	inputargs = append(inputargs, status)
	inputargs = append(inputargs, userID)

	if searchItem != "" {

		querystring += ` and (rd.requestor_name ilike ? or rd.event_code ilike ? or replace((to_char(rd.event_seq, '99990999')), ' ', '') ilike ? or 
			(select CONCAT (rd.event_code,'-', replace((to_char(rd.event_seq, '99990999')), ' ', ''))) ilike ? or
			rd.status_title ilike ? or rd.status_value ilike ? or 
			rd.activity_name ilike ? or 
			rd.product ilike ? )`
		inputargs = append(inputargs, "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%", "%"+searchItem+"%",
			"%"+searchItem+"%")
	}

	if status != "" {
		querystring += ` and rd.status_value = ?`
		inputargs = append(inputargs, status)
	}

	if activityFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, activityFilter)
	}

	if eventFilter != nil {
		querystring = querystring + ` and 
		rd.event_type && ?
		`
		inputargs = append(inputargs, eventFilter)
	}
	if statusFilter != nil || exceptionalStatusFilter != nil {
		querystring = querystring + ` and   ((rd.exceptionalapprover =true and rd.status_value =any(?)) or(rd.status_value =any(?) and rd.exceptionalapprover =false))
		`
		inputargs = append(inputargs, exceptionalStatusFilter, statusFilter)
	}
	if totalCostRange != nil {
		querystring = querystring + ` and (rd.total_cost BETWEEN ? and ?)
		`
		inputargs = append(inputargs, totalCostRange[0], totalCostRange[1])
	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			querystring = querystring + ` and ((( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ? ) and 
                (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ? ))`
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[3]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[3]):: integer))::timestamp::date end) >= ?)`
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			querystring = querystring + ` and (( case when
                (rd.activity_start_date[4]) ~ '^[0-9\.]+$'
                then (to_timestamp((rd.activity_start_date[4]):: integer))::timestamp::date end) <= ?)`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	if product != "" {
		querystring = querystring + ` and rd.product ilike ?
		`
		inputargs = append(inputargs, "%"+product+"%")
	}
	if dateCreatedFilterStart != "" || dateCreatedFilterEnd != "" {
		if dateCreatedFilterStart != "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and ((rd.date_created::date >= ? ) and 
                (rd.date_created::date <= ? ))`
			inputargs = append(inputargs, dateCreatedFilterStart, dateCreatedFilterEnd)
		} else if dateCreatedFilterStart != "" && dateCreatedFilterEnd == "" {
			querystring = querystring + ` and (rd.date_created::date >= ?)`
			inputargs = append(inputargs, dateCreatedFilterStart)
		} else if dateCreatedFilterStart == "" && dateCreatedFilterEnd != "" {
			querystring = querystring + ` and (rd.date_created::date <= ?)`
			inputargs = append(inputargs, dateCreatedFilterEnd)
		}
	}

	if productOwner != "" {
		querystring = querystring + ` and rd.product_owner_name ilike ?
		`
		inputargs = append(inputargs, "%"+productOwner+"%")
	}

	if team != "" {
		querystring = querystring + ` and rd.team ilike ?
		`
		inputargs = append(inputargs, "%"+team+"%")
	}

	if requestor != "" {

		querystring = querystring + ` and rd.created_by = ?
		`
		inputargs = append(inputargs, requestor)
	}

	if sortElements != nil {
		sort := strings.Join(sortElements, ",")
		querystring = querystring + ` ORDER BY ` + sort
	} else {
		querystring = querystring + ` ORDER BY rd.date_created desc`
	}

	if limit > 0 {
		if Page > 0 {
			querystring = querystring + ` limit ? offset ?`

			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, Page)
		} else {
			querystring = querystring + ` limit ? offset ?`
			inputargs = append(inputargs, limit)
			inputargs = append(inputargs, 0)
		}
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputargs...)
	if err != nil {
		panic(err)
	}
	defer rows.Close()
	for rows.Next() {
		var answerEntity entity.FormAnswerForAdmins
		var clientProductRawStr sql.NullString
		var clientProductList []map[string]string
		err := rows.Scan(&answerEntity.ID, &answerEntity.CompletionTime, &answerEntity.RequestorName,
			&answerEntity.RequestorActiveDirectory, &answerEntity.CreatedDate,
			&answerEntity.EventCode, &answerEntity.EventSeq, &answerEntity.Status,
			&answerEntity.StatusValue, &answerEntity.CountryTitle, &answerEntity.CountryValue,
			&answerEntity.CurrencyTitle, &answerEntity.CurrencyValue, &answerEntity.TotalCost,
			&answerEntity.SingleDataArray, &answerEntity.GovtNonGovtHcp,
			&answerEntity.EventExpenses, &answerEntity.ExpenseCost, &answerEntity.EventTypes, &answerEntity.Team, &answerEntity.IsException, &answerEntity.HcpCostList, &answerEntity.HcpTypeOfExpense, &answerEntity.ProductOwner, &answerEntity.RequestingHco, &answerEntity.Recipient, &answerEntity.MeetingMode, &answerEntity.VirtualEventDetails, &answerEntity.BoardOfDirectory, &answerEntity.LevelOfInfluence, &answerEntity.TypeOfEngagements,
			&clientProductRawStr)
		if err != nil {
			panic(err)
		}

		if clientProductRawStr.Valid {
			// purposely ignore the error as it should cause no harm in this context.
			json.Unmarshal([]byte(clientProductRawStr.String), &clientProductList)
			// will reassign client and product when multi-products exist
			clientList, productList := readProductList(clientProductList)
			if len(clientList) != 0 {
				answerEntity.ProductOwner = sql.NullString{
					String: strings.Join(clientList, `, `),
					Valid:  true,
				}
			}

			if len(productList) != 0 {
				answerEntity.Product = sql.NullString{
					String: strings.Join(productList, `, `),
					Valid:  true,
				}
			}
		}

		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchMaxTotalCostForAdmin(userId string, country int) (float64, error) {
	functionName := "FetchMaxTotalCostForAdmin()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	draftStatusID := codes["draft"].ID
	var result sql.NullFloat64
	querystring := `SELECT max(fa.total_cost) 
	from form_answers fa
	where fa.country = $3
	and ((fa.created_by <> $1 AND fa.status <> $2) OR fa.created_by = $1) and fa.is_deleted =false
	`
	err := pool.QueryRow(context.Background(), querystring, userId, draftStatusID, country).Scan(&result)
	if err != nil {
		log.Println(err)
		return 0, err
	}
	return result.Float64, nil
}

func CountRemainingPendingApproval(ctx context.Context, formAnswerID string, approverID string, groupID int) int {
	pendingStatusCode := codeController.GetValueKeyCodes()["approvedstatus"]["pending"].ID
	pool = GetPool()

	querystring := `
		SELECT count(id) 
		FROM approvers 
		WHERE 
			form_answer_id = $1 
			AND status = $2 
			AND group_id = $3 
			AND is_active = true
			AND approver_id != $4`
	var count int
	err := pool.QueryRow(ctx, querystring, formAnswerID, pendingStatusCode, groupID, approverID).Scan(&count)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return 0
		}
		panic(err)
	}

	return count
}

func FetchFormApprovalStatus(formAnswerID string) (int, error) {
	functionName := "FetchFormApprovalStatus()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := "SELECT status FROM approvers WHERE form_answer_id = $1 "
	var status int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&status)
	logengine.GetTelemetryClient().TrackEvent("FetchFormApprovalStatus query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return status, errors.New("Invalid type")
	}

	return status, err
}

func GetRequesterIDByFormAnswer(formAnswerID string) (*suuid.UUID, error) {
	querystring := `select created_by from form_answers where id = $1 `
	var uuid pgtype.UUID
	var createdByID suuid.UUID
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&uuid)
	logengine.GetTelemetryClient().TrackEvent("GetRequesterIDByFormAnswer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return &createdByID, err
	} else {
		if uuid.Status == pgtype.Present {
			userIdbyte := uuid.Get().([16]byte)
			createdByID = util.BytesToUUIDV4(userIdbyte)
		}
	}
	return &createdByID, err
}

func AlreadyApprovedByApprover(approverID string, formAnswerID string) bool {
	functionName := "AlreadyApprovedByApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := "SELECT 1 FROM approval_log WHERE actioned_by = $1 AND form_answer = $2 and is_active = true AND (status = 58 OR status = 59)"
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, approverID, formAnswerID).Scan(&hasValue)
	if err == nil && hasValue > 0 {
		result = true
	}

	return result
}

func GetFromFormAnswerByFormID(formID string) ([]entity.FormAnswerIDActivityData, error) {
	functionName := "GetFromFormAnswerByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `SELECT
	answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}' as start_date,
	answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,5,values,0,value}' as end_date,
	answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,9,values,0,value}' as team,
	answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}' as activity_name
	FROM form_answers where id= $1`

	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetFromFormAnswerByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	var result []entity.FormAnswerIDActivityData
	for rows.Next() {
		var dataList entity.FormAnswerIDActivityData
		rows.Scan(&dataList.ActivityStartDate, &dataList.ActivityEndDate, &dataList.ActivityTeam, &dataList.ActivityName)
		result = append(result, dataList)
	}

	return result, nil
}

func GetEventExpensesFormAnswerByFormID(formID string) (string, error) {
	functionName := "GetEventExpensesFormAnswerByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `SELECT
	answers#>'{3,sectionAnswer,0,form,groupAnswer,0,questionAnswers,3,answers,0,values,2,value}' as total_cost
	FROM form_answers where id= $1`

	result := ""
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetEventExpensesFormAnswerByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func GetHcpLength(formID string) int {
	functionName := "GetHcpLength()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var count int
	query := `with temp_tt as (select  unnest (rd.govt_non_govt_hcp) as hcp from reporting_db rd where rd.form_answer_id =$1)
	select count(hcp) from temp_tt`
	err := pool.QueryRow(context.Background(), query, formID).Scan(&count)
	if err != nil {
		log.Println(functionName + " : " + err.Error())
	}
	return count
}

func GetActivityEndDateByFormId(formID string) (string, string, string, error) {
	functionName := "GetActivityEndDateByFormId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `SELECT
	answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,5,values,0,value}' as end_date,
	answers#>>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values,0,description}' as activity_title,
	answers#>>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,1,values,0,description}' as activity_event_title
	FROM form_answers where id= $1`

	var resultEndDate sql.NullString
	var resultActivityTitle sql.NullString
	var resultActivityEventTitle sql.NullString
	err := pool.QueryRow(context.Background(), query, formID).Scan(&resultEndDate, &resultActivityTitle, &resultActivityEventTitle)
	logengine.GetTelemetryClient().TrackEvent("GetEventExpensesFormAnswerByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", "", "", err
	}

	return resultEndDate.String, resultActivityTitle.String, resultActivityEventTitle.String, nil
}

func GetTypeOfActivityByFormID(formID string) ([]string, error) {
	functionName := "GetTypeOfActivityByFormID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select jsonb_array_elements(answers#>'{0,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,0,values}')->>
	'description'
	 as type_of_activity
	FROM form_answers where id= $1`

	rows, err := pool.Query(context.Background(), query, formID)
	logengine.GetTelemetryClient().TrackEvent("GetTypeOfActivityByFormID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	var result []string
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}
	return result, nil
}

func GetCountryCurrencyId(createdBy *uuid.UUID) *entity.CountryCurrency {
	functionName := "GetCountryCurrencyId()"
	var countryCurrency entity.CountryCurrency
	log.Println(functionName)
	pool = GetPool()

	query := `SELECT country.id, currency.id
	FROM "user" 
	INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Country') country
	ON country.id = "user".country
	INNER JOIN (SELECT id, title, value FROM code  WHERE category = 'Currency') currency
	ON LEFT(currency.value,2) = LEFT(country.value,2)
	WHERE "user".id = $1 AND "user".is_active = true AND "user".is_deleted = false`

	var countryId, currencyId int
	err := pool.QueryRow(context.Background(), query, createdBy).Scan(&countryId, &currencyId)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}

		panic(err)
	}

	countryCurrency.Country = countryId
	countryCurrency.Currency = currencyId

	return &countryCurrency
}

func FetchTotalEventForRequestor(userID string) (*entity.GetTotalEventEntity, error) {
	functionName := "FetchTotalEventForRequestor()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result entity.GetTotalEventEntity
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	statusPending := code["pending"].ID
	statusApproved := code["approved"].ID
	statusRejected := code["rejected"].ID
	query := `SELECT
		(
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $2 and
		  created_by = $1
		) As pending,
		(
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $3 and
		  created_by = $1
		) As approved,
		(
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $4 and
		  created_by = $1
		) As rejected`
	err := pool.QueryRow(context.Background(), query, userID, statusPending, statusApproved, statusRejected).Scan(&result.RequestorTotalPending, &result.RequestorTotalApproved, &result.RequestorTotalRejected)
	logengine.GetTelemetryClient().TrackEvent("FetchTotalEventForRequestor query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}
	return &result, nil
}

func FetchTotalEventForApproverandRequestor(userID string) (*entity.GetTotalEventEntity, *entity.GetTotalEventEntity, error) {
	functionName := "FetchTotalEventForApproverandRequestor()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var resultApprover entity.GetTotalEventEntity
	var resultRequestor entity.GetTotalEventEntity
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	statusPending := code["pending"].ID
	statusApproved := code["approved"].ID
	statusRejected := code["rejected"].ID
	query := `SELECT
	(
	SELECT 
	 count(ap.form_answer_id)
	FROM approvers ap 
	inner join form_answers fa on ap.form_answer_id = fa.id and fa.status = $2
	WHERE ap.status = $2 and 
	  ap.approver_id = $1 and ap.is_active = true
	) As pending,
	(
	SELECT 
	  COUNT(form_answer_id)
	FROM approvers
	WHERE status = $3 and 
	  approver_id = $1 and is_active = true
	) As approved,
	(
	SELECT 
	  COUNT(form_answer_id)
	FROM approvers
	WHERE status = $4 and 
	  approver_id = $1 and is_active = true
	) As rejected, (
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $2 and
		  created_by = $1
		) As reqpending,
		(
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $3 and
		  created_by = $1
		) As reqapproved,
		(
		SELECT
		  COUNT(id)
		FROM form_answers
		WHERE status = $4 and
		  created_by = $1
		) As reqrejected`
	// result := ""
	err := pool.QueryRow(context.Background(), query, userID, statusPending, statusApproved, statusRejected).Scan(&resultApprover.ApproverTotalPending, &resultApprover.ApproverTotalApproved, &resultApprover.ApproverTotalRejected, &resultRequestor.RequestorTotalPending, &resultRequestor.RequestorTotalApproved, &resultRequestor.RequestorTotalRejected)
	logengine.GetTelemetryClient().TrackEvent("FetchTotalEventForApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, nil, err
	}

	return &resultApprover, &resultRequestor, nil
}

func FetchTotalEventForApprover(userID string) (*entity.GetTotalEventEntity, error) {
	functionName := "FetchTotalEventForApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result entity.GetTotalEventEntity
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	statusPending := code["pending"].ID
	statusApproved := code["approved"].ID
	statusRejected := code["rejected"].ID
	query := `SELECT
	(
	SELECT 
	 count(ap.form_answer_id)
	FROM approvers ap 
	inner join form_answers fa on ap.form_answer_id = fa.id and fa.status = $2
	WHERE ap.status = $2 and 
	  ap.approver_id = $1 and ap.is_active = true
	) As pending,
	(
	SELECT 
	  COUNT(form_answer_id)
	FROM approvers
	WHERE status = $3 and 
	  approver_id = $1 and is_active = true
	) As approved,
	(
	SELECT 
	  COUNT(form_answer_id)
	FROM approvers
	WHERE status = $4 and 
	  approver_id = $1 and is_active = true
	) As rejected`
	// result := ""
	err := pool.QueryRow(context.Background(), query, userID, statusPending, statusApproved, statusRejected).Scan(&result.ApproverTotalPending, &result.ApproverTotalApproved, &result.ApproverTotalRejected)
	logengine.GetTelemetryClient().TrackEvent("FetchTotalEventForApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	return &result, nil
}

func GetApprovalLogDataForActivity(inputEntity *entity.FetchApprovalTrailEntity) ([]entity.FetchApprovalLogData, error) {
	functionName := "GetApprovalLogDataForActivity()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	response := []entity.FetchApprovalLogData{}
	queryString := `
	select al.id, fa.id, fa.event_code ,fa.event_seq ,al.actioned_by ,
	CONCAT(u.first_name ,' ', u.last_name) as "name" ,
	(select value from code where id = al.status),
	(select title from code where id = al.status),
	al."comments" ,al.date_created ,
	(select value from user_roles where id = al.user_role_id),
	(select title from user_roles where id = al.user_role_id)
	from approval_log al 
	inner join form_answers fa on al.form_answer = fa.id 
	inner join "user" u on al.actioned_by = u.id 
	where form_answer = $1 `

	rows, err := pool.Query(context.Background(), queryString, inputEntity.FormAnswerId)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalLogDataForActivity query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return response, err
	}
	defer rows.Close()
	for rows.Next() {
		values := entity.FetchApprovalLogData{}
		err := rows.Scan(
			&values.Id,
			&values.FormAnswerId,
			&values.EventCode,
			&values.EventSeq,
			&values.ActionedBy,
			&values.ActionedByName,
			&values.StatusValue,
			&values.StatusTitle,
			&values.Comment,
			&values.DateCreated,
			&values.UserRoleValue,
			&values.UserRoleTitle,
		)
		if err != nil {
			return response, err
		}
		response = append(response, values)
	}
	return response, nil
}
func GetApprovalLogDataForActivityForRequester(inputEntity *entity.FetchApprovalTrailEntity) ([]entity.FetchApprovalLogDataForRequester, error) {
	functionName := "GetApprovalLogDataForActivityForRequester()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	response := []entity.FetchApprovalLogDataForRequester{}
	queryString := ` select fal.id,fal.form_answers_id ,fa.event_code ,fa.event_seq,fal.created_by,
	concat(u.first_name,' ',u.last_name),
	(select value from code where id=fal.status),
	(select title from code where id=fal.status),
	fal.date_created , ur.value ,ur.title 
	from form_answer_logs fal
	inner join form_answers fa on fal.form_answers_id  =fa.id 
	inner join "user" u on fal.created_by= u.id
	inner join user_roles ur on u.user_role_id = ur.id where fal.form_answers_id = $1`
	rows, err := pool.Query(context.Background(), queryString, inputEntity.FormAnswerId)
	logengine.GetTelemetryClient().TrackEvent("GetApprovalLogDataForActivityForRequester query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return response, err
	}
	defer rows.Close()
	for rows.Next() {
		values := entity.FetchApprovalLogDataForRequester{}
		err := rows.Scan(
			&values.Id,
			&values.FormAnswerId,
			&values.EventCode,
			&values.EventSeq,
			&values.ActionedBy,
			&values.ActionedByName,
			&values.StatusValue,
			&values.StatusTitle,
			&values.DateCreated,
			&values.UserRoleValue,
			&values.UserRoleTitle,
		)
		if err != nil {
			return response, err
		}
		response = append(response, values)
	}
	return response, nil
}

func GetApprovalTrailDataForANActivity(inputEntity *entity.FetchApprovalTrailEntity) ([]entity.FetchApprovalTrailData, error) {
	log.Println("GetApprovalTrailDataForANActivity()")

	pool = GetPool()

	response := []entity.FetchApprovalTrailData{}
	queryString := `
	select  "status",  "IValue",  "actionedByName" , "position", "ApprovalRole", "ISequenceNum" ,
	 "LatestCreate", "LastComment", "GroupID" from  (
	select t1.* from (select distinct on (ap.group_id,ap.sequence_no) c1.title as "status", c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
	ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
	inner join "user" u on ap.approver_id = u.id 
	inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
	left join user_roles ur on ap.user_role_id = ur.id 
	LEFT JOIN lateral (
	select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
	al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
	where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	 order by ap.group_id,ap.sequence_no, ap.date_created desc) as t1
inner join (
select approver_id ,max("CreatedDate") as MaxDateTime from (
select distinct on (ap.group_id,ap.sequence_no) c1.title as "status", c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
	ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
	inner join "user" u on ap.approver_id = u.id 
	inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
	left join user_roles ur on ap.user_role_id = ur.id 
	LEFT JOIN lateral (
	select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
	al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
	where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	 order by ap.group_id,ap.sequence_no, ap.date_created desc) 
	 as approverTemp group by approver_id ) groupedt1
	on t1.approver_id = groupedt1.approver_id
	AND t1."CreatedDate" = groupedt1.MaxDateTime ) as finalApprover`

	rows, err := pool.Query(context.Background(), queryString, inputEntity.FormAnswerId)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	for rows.Next() {
		values := entity.FetchApprovalTrailData{}
		err := rows.Scan(
			&values.Status,
			&values.IValue,
			&values.ActionedBy,
			&values.ActionedByRole,
			&values.ApprovalRole,
			&values.ISequenceNum,
			&values.DateCreated,
			&values.Comment,
			&values.GroupID,
		)
		if err != nil {
			panic(err)
		}
		response = append(response, values)
	}
	return response, nil
}

func GetFirstApproverFromFromAnswerID(formID string) (string, error) {
	functionName := "GetFirstApproverFromFromAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `with approval as
	(
	select id ,form_answer_id ,approver_id ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers )select approver_id from approval where row_number = 1 and 
	form_answer_id = $1`

	result := ""
	err := pool.QueryRow(context.Background(), query, formID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetFirstApproverFromFromAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func GetFormAnswerIdByEvent(inputEntity *entity.FormAnswerIdByEventIdEntity) (*entity.FormAnswerIdByEventID, error) {
	functionName := "GetFormAnswerIdByEvent()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	query := `select fa.id,fa.event_code ,fa.event_seq from form_answers fa 
	where fa.event_code = $1 and fa.event_seq = $2`

	var entity entity.FormAnswerIdByEventID
	err := pool.QueryRow(context.Background(), query, inputEntity.EventCode, inputEntity.EventSeq).Scan(&entity.FormAnswerID, &entity.EventCode, &entity.EventSeq)
	logengine.GetTelemetryClient().TrackEvent("GetFormAnswerIdByEvent query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, errors.New("Event Id is invalid!")
	}

	return &entity, nil
}

func GetFormAnswerDetails(FormAnswerID string) ([]entity.FormAnswer, error) {
	pool = GetPool()

	queryString := `WITH fa AS 
	(SELECT 
		fa.id, CONCAT(u.first_name ,' ', u.last_name) as "name",fa.event_seq as eventSeq,
		fa.date_created,fa.total_cost,fa.created_by,
		fa.answers,
		fa.is_apollo
	FROM form_answers fa
	inner join "user" u on fa.created_by = u.id 
	and fa.id = $1
	), groups AS 
	(
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section FROM fa, jsonb_array_elements(answers) with ordinality
		arr(item_object)
	), tab_section AS (
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object)
	), group_questions AS (
		SELECT id, arr.item_object->'questionAnswers' as group_questions FROM tab_section, jsonb_array_elements(group_section) with ordinality
		arr(item_object)
	), question_grp AS (
		SELECT id, arr.item_object->'id',arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
		arr(item_object)
	), questions AS 
	(
		SELECT id, arr.item_object->>'id' as qn_id, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
		arr(item_object)
		WHERE arr.item_object->>'id' IN ('type-of-ihcp-activity', 'activity-name', 'activity-start-date', 'activity-end-date', 'team')
	) SELECT fa.id, fa.name, fa.eventseq,fa.date_created,fa.total_cost,fa.created_by,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-start-date' AND questions.id = fa.id) as activity_start_date,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-end-date' AND questions.id = fa.id) as  activity_end_date,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'team' AND questions.id = fa.id) as team,
	(SELECT qn_values#>>'{0,value}' FROM  questions WHERE qn_id = 'activity-name' AND questions.id = fa.id) as activity_name,
		(SELECT array_to_string(ARRAY(SELECT jsonb_array_elements(qn_values)->>
	'description'::text),'|') FROM  questions WHERE qn_id = 'type-of-ihcp-activity' AND questions.id = fa.id) as type_of_activity,
	fa.is_apollo
	FROM fa
	ORDER BY fa.date_created DESC`

	logengine.GetTelemetryClient().TrackEvent("GetFormAnswerDetails query called")

	rows, err := pool.Query(context.Background(), queryString, FormAnswerID)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var result []entity.FormAnswer
	for rows.Next() {
		var answerEntity entity.FormAnswer
		if err := rows.Scan(&answerEntity.ID, &answerEntity.RequestorName, &answerEntity.EventSequence, &answerEntity.CreatedDate, &answerEntity.TotalCost, &answerEntity.RequestorID,
			&answerEntity.StartDate,
			&answerEntity.EndDate,
			&answerEntity.Team,
			&answerEntity.ActivityName,
			&answerEntity.ActivityTypes,
			&answerEntity.IsApollo); err != nil {

			panic(err)
		}

		result = append(result, answerEntity)
	}

	return result, nil
}

func CheckEventApproved(formAnswerID string) bool {
	functionName := "checkEventApproved()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	action := "approved"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	pendingAction := codes[action].ID
	var result bool
	querystring := `select 1 from form_answers where status = $2 and id = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, pendingAction).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func IsFormIDExistsInFormAnswerRelation(ctx context.Context, tx pgx.Tx, formAnswerID *string) bool {
	functionName := "IsFormIDExistsInFormAnswerRelation()"
	log.Println(functionName)

	querystring := `SELECT 1 FROM form_answers_relation WHERE form_answer = $1 and is_active = true and is_deleted = false`
	var hasValue int
	err := tx.QueryRow(ctx, querystring, formAnswerID).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}
		panic(err)
	}
	return true
}

func InsertFormAnswerRelation(ctx context.Context, formAnswerID string, tx pgx.Tx, answer *entity.AnswerEntity, input model.FormAnswerSubmissionRequest) {
	queryString := `INSERT INTO form_answers_relation (form_answer,parent,created_by) VALUES ($1,$2,$3)`
	_, err := tx.Exec(ctx, queryString, formAnswerID, input.ParentEventID, answer.CreatedBy)
	if err != nil {
		panic(err)
	}
}

func UpdateFormAnswerRelation(ctx context.Context, tx pgx.Tx, answer *entity.AnswerEntity, input model.FormAnswerSubmissionRequest) {
	//functionName := "UpdateFormAnswerRelation()"
	querystring := `UPDATE form_answers_relation 
	SET parent = $2, modified_by = $3, last_modified = now()
	WHERE form_answer = $1 and is_active = true and is_deleted = false`
	_, err := tx.Exec(ctx, querystring, input.ID, input.ParentEventID, answer.CreatedBy)
	if err != nil {
		panic(err)
	}
}

func DeleteFormAnswerRelation(ctx context.Context, tx pgx.Tx, answer *entity.AnswerEntity, input model.FormAnswerSubmissionRequest) {
	//functionName := "DeleteFormAnswerRelation()"
	querystring := `UPDATE form_answers_relation 
	SET is_active = false, is_deleted = true, modified_by = $2, last_modified = now()
	WHERE form_answer = $1`
	_, err := tx.Exec(ctx, querystring, input.ID, answer.CreatedBy)
	if err != nil {
		panic(err)
	}
}

func GetAllFormAnsFromApprover() ([]string, error) {
	functionName := "GetAllFormAnsFromApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["pending"].ID
	query := `SELECT form_answer_id FROM (
		SELECT DISTINCT ON (a.form_answer_id) *, a.date_created as "date" FROM approvers a
		inner join form_answers fa on fa.id = a.form_answer_id 
		where a.is_active = true and a.is_deleted = false
		and fa.status = $1
		ORDER BY a.form_answer_id DESC
	  ) t
	  ORDER BY "date" DESC`

	rows, err := pool.Query(context.Background(), query, status)
	logengine.GetTelemetryClient().TrackEvent("GetAllFormAnsFromApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	var result []string
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}
	return result, nil
}

func GetAllApproverIdFromApproverByFormAnsID(formAnsID string) ([]string, error) {
	functionName := "GetAllApproverIdFromApproverByFormAnsID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `select approver_id from approvers where form_answer_id = $1 and is_active = true`

	rows, err := pool.Query(context.Background(), query, formAnsID)
	logengine.GetTelemetryClient().TrackEvent("GetAllApproverIdFromApproverByFormAnsID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return nil, err
	}

	var result []string
	for rows.Next() {
		var value string
		rows.Scan(&value)
		result = append(result, value)
	}
	return result, nil
}

func GetDateDifference(formAnswerID string, approverID string) (int, error) {
	functionName := "GetDateDifference()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := `SELECT DATE_PART('day', now() ::timestamp - date_created ::timestamp) from approvers 
	where form_answer_id = $1 
	and approver_id = $2 and is_active = true`
	var diff int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approverID).Scan(&diff)
	logengine.GetTelemetryClient().TrackEvent("GetDateDifference query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return diff, errors.New("Invalid type")
	}

	return diff, err
}

func GetRoleNameFromId(role string) (string, error) {
	functionName := "GetRoleNameFromId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select value from user_roles ur where id = $1 
	and is_active = true and is_deleted = false`
	var roleName string
	err := pool.QueryRow(context.Background(), querystring, role).Scan(&roleName)
	logengine.GetTelemetryClient().TrackEvent("GetRoleNameFromId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return roleName, errors.New("Invalid type")
	}

	return roleName, err
}

func FetchFormAnswersForCancel(input string) (int, error) {
	functionName := "FetchFormAnswersForCancel()"
	log.Println(functionName)
	var status int
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select status from form_answers where id = $1 `
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&status)
	logengine.GetTelemetryClient().TrackEvent("FetchFormAnswersForCancel query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return status, nil
}

func FetchCountryIdFromFormAnswer(input string) (int, error) {
	functionName := "FetchCountryIdFromFormAnswer()"
	log.Println(functionName)
	var country int
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select country from form_answers where id = $1 `
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&country)
	logengine.GetTelemetryClient().TrackEvent("FetchCountryIdFromFormAnswer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return country, nil
}

func UpdateFormAnswerActivityDate(entity entity.FormSubmissionRequest) error {
	functionName := "UpdateFormAnswerActivityDate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `with groups AS 
	(
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section ,
		index-1 as index, ('{' || index-1 || ',sectionAnswer,') as path
		FROM form_answers fa , jsonb_array_elements(answers) with ordinality
		arr(item_object,index) WHERE fa.id = $1 
	), section_answer AS (
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section ,
		 arr.index-1 as index, ( groups.path || arr.index-1 || ',form,groupAnswer,') as path
		 FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object,index) 
	), question_answers as (
		SELECT id, arr.item_object->'questionAnswers' as group_questions ,
		 arr.index-1 as index, ( section_answer.path || arr.index-1 || ',questionAnswers,') as path
		FROM section_answer,jsonb_array_elements(group_section) with ordinality
		arr(item_object,index) 
	), answers AS (
		SELECT id, questionAnswer->'answers' as data, arr.index-1 as index, ( question_answers.path || arr.index-1 || ',answers,') as path
		FROM question_answers, jsonb_array_elements(group_questions) with ordinality arr(questionAnswer, index)
		WHERE questionAnswer->>'id'= 'basic-info-section-1-form-1-group1-questions-1'
	), answer AS (
		SELECT id, answer as data, arr.index-1 as index, answers.path
		FROM answers, jsonb_array_elements(data) with ordinality arr(answer, index)
		WHERE answer->>'id' IN ('activity-start-date', 'activity-end-date', 'duration-of-the-activity')
	)
	SELECT path || index || ',values,0,value}' as path, data->>'id'
	FROM answer`

	rows, err := pool.Query(context.Background(), query, entity.FormAnsId)
	logengine.GetTelemetryClient().TrackEvent("UpdateFormAnswerActivityDate query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return err
	}

	var resultPath []string
	var resultID []string

	for rows.Next() {
		var value string
		var id string
		rows.Scan(&value, &id)
		resultPath = append(resultPath, value)
		resultID = append(resultID, id)
	}
	err = UpdateFormAnswerActivityStartDateEndDateDuration(resultPath, resultID, entity)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return err
	}
	return nil
}

func FetchFormAnswerActivityDate(entity entity.FormSubmissionRequest, input model.FormSubmissionActivity, userID string) error {
	functionName := "FetchFormAnswerActivityDate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	query := `with groups AS 
	(
		SELECT id, arr.item_object->>'id' as tab_id, arr.item_object->'sectionAnswer' as section ,
		index-1 as index, ('{' || index-1 || ',sectionAnswer,') as path
		FROM form_answers fa , jsonb_array_elements(answers) with ordinality
		arr(item_object,index) WHERE fa.id = $1
	), section_answer AS (
		SELECT id,  arr.item_object->'form'->'groupAnswer' as group_section ,
		 arr.index-1 as index, ( groups.path || arr.index-1 || ',form,groupAnswer,') as path
		 FROM groups, jsonb_array_elements(section) with ordinality
		arr(item_object,index) 
	), question_answers as (
		SELECT id, arr.item_object->'questionAnswers' as group_questions ,
		 arr.index-1 as index, ( section_answer.path || arr.index-1 || ',questionAnswers,') as path
		FROM section_answer,jsonb_array_elements(group_section) with ordinality
		arr(item_object,index) 
	), answers AS (
		SELECT id, questionAnswer->'answers' as data, arr.index-1 as index, ( question_answers.path || arr.index-1 || ',answers,') as path
		FROM question_answers, jsonb_array_elements(group_questions) with ordinality arr(questionAnswer, index)
		WHERE questionAnswer->>'id'= 'basic-info-section-1-form-1-group1-questions-1'
	), answer AS (
		SELECT id, answer as data, arr.index-1 as index, answers.path
		FROM answers, jsonb_array_elements(data) with ordinality arr(answer, index)
		WHERE answer->>'id' IN ('activity-start-date', 'activity-end-date', 'duration-of-the-activity')
	)
	SELECT data#>>'{values,0,value}' as path, data->>'id'
	FROM answer`

	rows, err := pool.Query(context.Background(), query, entity.FormAnsId)
	logengine.GetTelemetryClient().TrackEvent("FetchFormAnswerActivityDate query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return err
	}

	var resultData []string
	var resultDataID []string

	for rows.Next() {
		var value string
		var id string

		rows.Scan(&value, &id)
		resultData = append(resultData, value)
		resultDataID = append(resultDataID, id)
	}
	err = InsertFormSubmissionChanges(resultData, resultDataID, entity, input, userID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return err
	}
	return nil
}

func InsertFormSubmissionChanges(resultData []string, resultDataID []string, entity entity.FormSubmissionRequest, input model.FormSubmissionActivity, userID string) error {
	functionName := "InsertFormSubmissionChanges()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var inputData string
	var questionID string
	defer tx.Rollback(context.Background())
	for i, value := range resultData {
		if resultDataID[i] == "activity-start-date" {
			questionID = resultDataID[i]
			inputData = input.StartDate
		} else if resultDataID[i] == "activity-end-date" {
			questionID = resultDataID[i]
			inputData = input.EndDate
		} else if resultDataID[i] == "duration-of-the-activity" {
			questionID = resultDataID[i]
			inputData = input.Duration
			inputData = fmt.Sprintf("%q", inputData)
		}
		querystring := `insert into form_submission_changes (form_answer_id,question_id,original,"new",created_by)
		values ($1,$2,$3,$4,$5)`
		_, err = tx.Exec(context.Background(), querystring, entity.FormAnsId, questionID, value, inputData, userID)
		logengine.GetTelemetryClient().TrackEvent("InsertFormSubmissionChanges query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func UpdateFormAnswerActivityStartDateEndDateDuration(resultPath []string, resultID []string, entity entity.FormSubmissionRequest) error {
	functionName := "UpdateFormAnswerActivityStartDateEndDateDuration()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	for i, value := range resultPath {
		if resultID[i] == "activity-start-date" {
			input = entity.StartDate
		} else if resultID[i] == "activity-end-date" {
			input = entity.EndDate
		} else if resultID[i] == "duration-of-the-activity" {
			input = entity.Duration
		}

		input = fmt.Sprintf("%q", input)
		querystring := `UPDATE form_answers 
		SET answers = jsonb_set(answers, $2 , $3, FALSE)
		WHERE form_answers.id = $1`
		_, err = tx.Exec(context.Background(), querystring, entity.FormAnsId, value, input)
		logengine.GetTelemetryClient().TrackEvent("UpdateFormAnswerActivityStartDateEndDateDuration query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func UpdateEventStatusFromPendingToCompleted(formAnswerId string) error {
	functionName := "UpdateEventStatusFromPendingToCompleted()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	input = fmt.Sprintf("%q", input)
	querystring := `UPDATE form_answers 
	SET status = 67, 
	local_approval_status = 58,
	regional_approval_status = case when regional_approval_status is null then null
	else 58 end
	WHERE form_answers.id = $1 `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateEventStatusFromPendingToCompleted query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	queryString := `UPDATE reporting_db
						SET status_title = 'Completed', status_value = 'completed', updated_date = now() 
						WHERE form_answer_id = $1 `
	_, err = tx.Exec(context.Background(), queryString, formAnswerId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	approverError := UpdateApproversStatusFromPendingToApproved(formAnswerId)
	if approverError != nil {
		logengine.GetTelemetryClient().TrackException(approverError)
		log.Printf("%s - Error: %s ", functionName, approverError.Error())
		return approverError
	}
	return nil
}

func UpdateApproversStatusFromPendingToApproved(formAnswerId string) error {
	functionName := "UpdateApproversStatusFromPendingToApproved()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	input = fmt.Sprintf("%q", input)
	querystring := `UPDATE approvers 
	SET status = 58 
	WHERE form_answer_id = $1 
	and is_active = true and is_deleted = false `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateApproversStatusFromPendingToApproved query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func UpdateEventStatusFromCompletedToApproved(formAnswerId string) error {
	functionName := "UpdateEventStatusFromCompletedToApproved()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	input = fmt.Sprintf("%q", input)
	querystring := `UPDATE form_answers 
	SET status = 58, 
	local_approval_status = 58,
	regional_approval_status = case when regional_approval_status is null then null
	else 58 end
	WHERE form_answers.id = $1 `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateEventStatusFromCompletedToApproved query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	queryString := `UPDATE reporting_db
						SET status_title = 'Approved', status_value = 'approved', updated_date = now() 
						WHERE form_answer_id = $1 `
	_, err = tx.Exec(context.Background(), queryString, formAnswerId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}

	return nil
}

func UpdateEventStatusFromCompletedToPending(formAnswerId string) error {
	functionName := "UpdateEventStatusFromCompletedToPending()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	input = fmt.Sprintf("%q", input)
	querystring := `UPDATE form_answers 
	SET status = 57, 
	local_approval_status = 57,
	regional_approval_status = case when regional_approval_status is null then null
	else 57 end
	WHERE form_answers.id = $1 `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateEventStatusFromCompletedToPending query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	queryString := `UPDATE reporting_db
						SET status_title = 'Pending', status_value = 'pending', updated_date = now() 
						WHERE form_answer_id = $1 `
	_, err = tx.Exec(context.Background(), queryString, formAnswerId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	approverError := UpdateApproversStatusFromCompletedToPending(formAnswerId)
	if approverError != nil {
		logengine.GetTelemetryClient().TrackException(approverError)
		log.Printf("%s - Error: %s ", functionName, approverError.Error())
		return approverError
	}
	approverLogError := UpdateApproversLogStatusFromCompletedToPending(formAnswerId)
	if approverLogError != nil {
		logengine.GetTelemetryClient().TrackException(approverLogError)
		log.Printf("%s - Error: %s ", functionName, approverLogError.Error())
		return approverLogError
	}
	return nil
}

func UpdateApproversStatusFromCompletedToPending(formAnswerId string) error {
	functionName := "UpdateApproversStatusFromCompletedToPending()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var input string
	defer tx.Rollback(context.Background())
	input = fmt.Sprintf("%q", input)
	querystring := `UPDATE approvers 
	SET status = 57 
	WHERE form_answer_id = $1 
	and is_active = true and is_deleted = false `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateApproversStatusFromCompletedToPending query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func UpdateApproversLogStatusFromCompletedToPending(formAnswerId string) error {
	functionName := "UpdateApproversLogStatusFromCompletedToPending()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	defer tx.Rollback(context.Background())
	querystring := `UPDATE approval_log 
	SET is_active = false 
	WHERE form_answer = $1 `
	_, err = tx.Exec(context.Background(), querystring, formAnswerId)
	logengine.GetTelemetryClient().TrackEvent("UpdateApproversLogStatusFromCompletedToPending query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func UpdateFormAnswerException(entity *entity.FormSubmissionRequest) error {
	functionName := "UpdateFormAnswerException()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update form_answers set is_exception = $2, is_exceptional_approval=$3 where id = $1`
	_, err = tx.Exec(context.Background(), querystring, entity.FormAnsId, entity.IsExceptionalApprover, entity.IsExceptionalApprover)
	logengine.GetTelemetryClient().TrackEvent("UpdateFormAnswerException query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit approval log ")
		return txErr
	}
	return nil
}

func UpdateFormAnswerId(input *model.UserformAnsID) error {
	functionName := "UpdateFormAnswerId()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update approvers set is_active = false, is_deleted = true where form_answer_id = $1`
	_, err = tx.Exec(context.Background(), querystring, input.FormAnsID)
	logengine.GetTelemetryClient().TrackEvent("UpdateFormAnswerId query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	cancelStatusID := codes["cancelled"].ID
	cancelStatusValue := codes["cancelled"].Value
	cancelStatusTitle := codes["cancelled"].Title

	querystring = `update form_answers set status = $2 where id = $1`
	_, err = tx.Exec(context.Background(), querystring, input.FormAnsID, cancelStatusID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	querystring1 := `UPDATE reporting_db
	SET status_title = $2, status_value = $3, updated_date = now() 
	WHERE form_answer_id = $1`
	_, err = tx.Exec(context.Background(), querystring1, input.FormAnsID, cancelStatusTitle, cancelStatusValue)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit approval log ")
		return err
	}
	return nil
}

func Checkformidanduserid(formAnswerID string, requestorID string) bool {
	functionName := "Checkformidanduserid()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from form_answers where id = $1 and created_by = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, requestorID).Scan(&hasValue)

	if err == nil {
		result = true
	}
	return result
}

func IsActionTakenInApprovalLog(formAnswerID string, approverID string) bool {
	functionName := "IsActionTakenInApprovalLog()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from approval_log where form_answer = $1
	and actioned_by = $2 and is_active = true`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approverID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func GetNextApproverFromFromAnswerID(formAnswerID string) (string, error) {
	functionName := "GetNextApproverFromFromAnswerID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}

	country, err1 := FetchCountryByFormAnswerID(formAnswerID)
	if err1 != nil {
		logengine.GetTelemetryClient().TrackException(err1)
		log.Printf("%s - err: %s", functionName, err1.Error())
		return "", err1
	}
	var localGroupID int
	codesGroup := codeController.GetValueKeyCodes()
	codeGroup := codesGroup["group"]
	if country == 900001 {
		localGroupID = codeGroup["regional"].ID
	} else {
		localGroupID = codeGroup["local"].ID
	}
	action := "pending"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	pendingAction := codes[action].ID
	query := `with approval as
	(
	select id ,form_answer_id , status ,group_id,approver_id ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers where status = $2 and group_id = $3 and is_active = true)
	select approver_id from approval where row_number = 1 and 
	form_answer_id = $1`

	result := ""
	err := pool.QueryRow(context.Background(), query, formAnswerID, pendingAction, localGroupID).Scan(&result)
	logengine.GetTelemetryClient().TrackEvent("GetNextApproverFromFromAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - err: %s", functionName, err.Error())
		return "", err
	}

	return result, nil
}

func CheckApproverPresentInApproversWithStatusReturn(formAnswerID string) bool {
	functionName := "CheckApproverPresentInApproversWithStatusReturn()"
	log.Println(functionName)
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	returnStatusID := codes["return"].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select distinct 1 from approvers where form_answer_id  = $1 and status = $2
	and is_active = true and is_deleted = false`
	var hasValue int

	err := pool.QueryRow(context.Background(), querystring, formAnswerID, returnStatusID).Scan(&hasValue)
	if err != nil {

	}
	if err == nil {
		result = true
	}
	return result
}

func CheckPreviousApproverAction(formAnswerID string, approverID string, group string) bool {
	functionName := "CheckPreviousApproverAction()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	code := codeController.GetValueKeyCodes()["group"]
	groupID := code[group].ID
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	status := codes["pending"].ID

	var result bool
	querystring := `with approval as
	(
	select id ,form_answer_id , status ,group_id,approver_id ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers where status = $3 and group_id = $4 and is_active= true and is_deleted= false)
	select 1 from approval where row_number = 1 and 
	form_answer_id = $1 and approver_id = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approverID, status, groupID).Scan(&hasValue)
	if err == nil && hasValue == 1 {
		result = true
	}
	return result
}

func FetchUserNameByID(input string) (string, error) {
	functionName := "FetchUserNameByID()"
	log.Println(functionName)
	var name string
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select concat(first_name,' ', last_name) as fName from "user" where id = $1 `
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&name)
	logengine.GetTelemetryClient().TrackEvent("FetchUserNameByID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return "", err
	}
	return name, nil
}

func FetchCountryByFormAnswerID(input string) (int, error) {
	functionName := "FetchCountryByFormAnswerID()"
	log.Println(functionName)
	var name int
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select country from form_answers where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&name)
	logengine.GetTelemetryClient().TrackEvent("FetchCountryByFormAnswerID query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return 0, err
	}
	return name, nil
}
func FetchreceiverFromNotification(input string) (string, error) {
	functionName := "FetchreceiverFromNotification()"
	log.Println(functionName)
	var receiver string
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select receiver from status_change_notification where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&receiver)
	logengine.GetTelemetryClient().TrackEvent("FetchreceiverFromNotification query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		// return " ", err
	}

	return receiver, nil
}

func FetchHasReadFromNotification(input string) (bool, error) {
	functionName := "FetchHasReadFromNotification()"
	log.Println(functionName)
	var status bool
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select has_read from status_change_notification where id = $1`
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&status)
	logengine.GetTelemetryClient().TrackEvent("FetchHasReadFromNotification query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return false, err
	}

	return status, nil
}

func UpdateHasReadInStatusChangeNotification(entity entity.StatusChangeNotificationEntity) error {
	log.Println("UpdateHasReadInStatusChangeNotification()")
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update status_change_notification set has_read = true where id = $1 
				and is_active = true and is_deleted = false`
	_, err = tx.Exec(context.Background(), querystring, entity.NotificationID)
	logengine.GetTelemetryClient().TrackEvent("UpdateHasReadInStatusChangeNotification query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit approval log")
		return err
	}
	return nil
}
func CheckFormAnswerInReturnStage(formAnswerID string) bool {
	functionName := "CheckFormAnswerInReturnStage()"
	log.Println(functionName)
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	returnStatusID := codes["return"].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from form_answers where id = $1 and status = $2`
	var hasValue int

	err := pool.QueryRow(context.Background(), querystring, formAnswerID, returnStatusID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckFormAnswerIDStatusReturnOrDraftOrApproved(ctx context.Context, tx pgx.Tx, formAnswerID *string) bool {
	functionName := "CheckFormAnswerIDStatusReturnOrDraftOrApproved()"
	log.Println(functionName)

	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	returnStatusID := codes["return"].ID
	draftStatus := codes["draft"].ID
	approvedStatus := codes["approved"].ID
	recalledStatus := codes["recalled"].ID
	querystring := `SELECT 1 
	FROM form_answers WHERE id = $1 AND (status = $2 OR status = $3 OR status = $4 OR status = $5)`
	var hasValue int
	err := tx.QueryRow(ctx, querystring, formAnswerID, returnStatusID, draftStatus, approvedStatus, recalledStatus).Scan(&hasValue)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false
		}

		panic(err)
	}

	return true
}

func CheckApproverPresentInApprovers(formAnswerID *string) bool {
	functionName := "CheckApproverPresentInApprovers()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select distinct 1 from approvers where form_answer_id = $1`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID).Scan(&hasValue)
	if err == nil {
		result = true
	}

	return result
}

func SoftDeleteApprovers(userID string, formAnswerID string, answer *entity.ApproversEntity, data []*entity.EmailStruct, changeRequestSummary *string, isApollo *bool) error {
	functionName := "SoftDeleteApprovers()"
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["pending"].ID
	statusValue := code["pending"].Value
	statusTitle := code["pending"].Title
	approvedStatus := code["approved"].ID
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	defer tx.Rollback(context.Background())
	querystring := `update approvers set is_active = false, is_deleted = true ,
	modified_by = $1, last_modified = now()
	where form_answer_id = $2 and is_active = true and is_deleted = false`
	_, err = tx.Exec(context.Background(), querystring, userID, formAnswerID)
	logengine.GetTelemetryClient().TrackEvent("SoftDeleteApprovers query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	query := `update approval_log set is_active = false
	where form_answer = $1 and is_active = true`
	_, err = tx.Exec(context.Background(), query, formAnswerID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	var firstApprover string
	localFirstApprover := ""
	rigonalFirstApprover := ""
	var localTemp = 0
	var rigonalTemp = 0
	localGroupID := 55
	regionalGroupID := 56
	for _, value := range answer.ApproverRoles {
		log.Println(value.DepartmentID)
		approverQueryString := `INSERT INTO approvers (form_answer_id, approver_id, approval_role, user_role_id,created_by, group_id, sequence_no, status,department_id,set_number,is_apollo) VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9,$10,$11) `
		_, err = tx.Exec(context.Background(), approverQueryString, formAnswerID, value.UserID, 0, value.RoleID, answer.CreatedBy, value.GroupID, value.SequenceNo, value.StatusID, value.DepartmentID, value.SetNumber, isApollo)
		logengine.GetTelemetryClient().TrackEvent("CreateApprovalRoles query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
		if localTemp == 0 && value.GroupID == localGroupID {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < localTemp && value.GroupID == 55 {
			localTemp = value.SequenceNo
			localFirstApprover = value.UserID
		}
		if rigonalTemp == 0 && value.GroupID == regionalGroupID {
			rigonalTemp = value.SequenceNo
			rigonalFirstApprover = value.UserID
		} else if value.SequenceNo > 0 && value.SequenceNo < rigonalTemp && value.GroupID == 56 {
			rigonalTemp = value.SequenceNo
			rigonalFirstApprover = value.UserID
		}

	}

	if localTemp > 0 {
		firstApprover = localFirstApprover
	} else {
		firstApprover = rigonalFirstApprover
	}
	var inputargs []interface{}
	if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == false {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?,is_recall=false `
		inputargs = append(inputargs, status, approvedStatus, status)
		if changeRequestSummary != nil {
			querystring += `, change_request_summary = ?`
			inputargs = append(inputargs, changeRequestSummary)
		}
		querystring += ` WHERE id = ?`
		inputargs = append(inputargs, formAnswerID)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	} else if answer.RegionalApproverPresent == true && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?, regional_approval_status = ?,is_recall=false `
		inputargs = append(inputargs, status, status, status)
		if changeRequestSummary != nil {
			querystring += `, change_request_summary = ?`
			inputargs = append(inputargs, changeRequestSummary)
		}
		querystring += ` WHERE id = ?`
		inputargs = append(inputargs, formAnswerID)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	} else if answer.RegionalApproverPresent == false && answer.LocalApproverPresent == true {
		querystring := `UPDATE form_answers SET status = ?,
		local_approval_status = ?,is_recall=false `
		inputargs = append(inputargs, status, status)
		if changeRequestSummary != nil {
			querystring += `, change_request_summary = ?`
			inputargs = append(inputargs, changeRequestSummary)
		}
		querystring += ` WHERE id = ?`
		inputargs = append(inputargs, formAnswerID)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}
	querystring1 := `UPDATE reporting_db
	SET status_title = $2, status_value = $3, updated_date = now() 
	WHERE form_answer_id = $1`
	_, err = tx.Exec(context.Background(), querystring1, formAnswerID, statusTitle, statusValue)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}

	pendingStatus := code["pending"].ID
	notificationErr := InsertStatusNotification(formAnswerID, userID, pendingStatus, firstApprover)
	if notificationErr != nil {
		log.Printf("%s - Error: %s ", functionName, notificationErr.Error())
		return notificationErr
	}
	emailCode := codeController.GetValueKeyCodes()["typeofemail"]
	approverStatus := emailCode["requireapproval"].ID
	requestorStatus := emailCode["formsubmitted"].ID

	requestorEmail, err := GetEmailIDFromUserID(userID)
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
	} else {
		requestorEmailContent := util.SendEmailToRequesterAfterEventSubmission(requestorEmail, data)
		newRequestorEmailContent := strip.StripTags(requestorEmailContent)
		err = InsertEmailLog(formAnswerID, requestorStatus, newRequestorEmailContent, userID, userID)
		if err != nil {
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
		formIdUuid, _ := uuid.FromString(formAnswerID)
		emailOwner, ownerId, err2 := GetOwnersEmailIDFromFormAnswerID(formIdUuid)
		if err2 != nil {
			log.Printf("%s - Error: %s ", functionName, err2.Error())
			return err2
		} else {
			if emailOwner != requestorEmail {
				emailOwnerContent := util.SendEmailToRequesterAfterEventSubmission(emailOwner, data)
				newOwnerEmailContent := strip.StripTags(emailOwnerContent)
				err1 := InsertEmailLog(formAnswerID, requestorStatus, newOwnerEmailContent, userID, ownerId)
				if err1 != nil {
					log.Printf("%s - Error: %s ", functionName, err1.Error())
					return err1
				}
			}
		}
	}

	email, err := GetEmailIDFromUserID(firstApprover)
	if err != nil {
		log.Printf("%s - Error: %s ", functionName, err.Error())
	} else {
		approverEmailContent := util.SendEmailScenarioOne(email, data)
		newApproverEmailContent := strip.StripTags(approverEmailContent)
		err = InsertEmailLog(formAnswerID, approverStatus, newApproverEmailContent, userID, firstApprover)
		if err != nil {
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
	}

	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Printf("%s - Error: %s ", functionName, txErr.Error())
	}
	return nil
}

func CheckFirstApprover(formAnswerID string, approverID string) bool {
	functionName := "CheckFirstApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `with approval as
	(
	select id ,form_answer_id ,approver_id ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers where is_active = true and is_deleted = false)
	select 1 from approval where row_number = 1 and 
	form_answer_id = $1 
	and approver_id = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approverID).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckPreviousApproverActionForAllApprovers(formAnswerID string, approverID string) bool {
	functionName := "CheckPreviousApproverActionForAllApprovers()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	status := codes["pending"].ID

	var result bool
	querystring := `with approval as
	(
	select id ,form_answer_id , status ,approver_id ,row_number ()
	over (partition by form_answer_id order by sequence_no)
	from approvers where status = $3 and is_active = true and is_deleted = false)
	select 1 from approval where row_number = 1 and 
	form_answer_id = $1 
	and approver_id = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, approverID, status).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func FetchCodeDescriptionAndValue(category string, category1 string) ([]entity.Dropdown, error) {
	functionName := "FetchCodeDescriptionAndValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select value, description from code where category in ($1,$2) 
	and value not in ('requestorresponse','finalapproved','pendingresponse')`

	rows, err := pool.Query(context.Background(), querystring, category, category1)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Value, &answerEntity.Description)
		result = append(result, answerEntity)
	}
	return result, nil
}
func FetchCodeDescriptionAndValueForOther(category string) []entity.Dropdown {
	log.Println("FetchCodeDescriptionAndValue() ", category)
	pool = GetPool()

	querystring := `select value, title from code where category in ($1) and is_active=true and is_deleted=false`

	rows, err := pool.Query(context.Background(), querystring, category)
	if err != nil {
		panic(err)
	}
	defer rows.Close()

	var result []entity.Dropdown
	for rows.Next() {
		var answerEntity entity.Dropdown
		if err := rows.Scan(&answerEntity.Value, &answerEntity.Description); err != nil {
			panic(err)
		}
		result = append(result, answerEntity)
	}
	return result
}

func FetchTypeApprovalListForFirstApprover(input *entity.FetchTypeApprovalListEntity) ([]entity.ApproverListByType, error) {
	functionName := "FetchTypeApprovalListForFirstApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var response []entity.ApproverListByType
	var result entity.ApproverListByType

	querystring := `with set_no as(
		select max(set_number) as set_no from approvers where form_answer_id = $1
		),sequence_no as(
		select min(sequence_no) from approvers where form_answer_id = $1 and set_number = (select * from set_no)
		)select ap.approver_id , ap.sequence_no , ap.department_id , ap.user_role_id , concat(u.first_name,' ',u.last_name) , d.department  from approvers ap
		inner join "user" u on u.id = ap.approver_id 
		inner join departments d on d.id = ap.department_id 
		where form_answer_id = $1 and
		set_number = (select * from set_no) and sequence_no = (select * from sequence_no)
		`

	err := pool.QueryRow(context.Background(), querystring, input.FormAnswerId).Scan(&result.ApproverId, &result.SequenceNo, &result.DepartmentID, &result.UserRoleId, &result.UserName, &result.DepartmentName)
	if err != nil {
		log.Println(err)
		return response, err
	}

	response = append(response, result)
	return response, nil
}

func FetchTypeApprovalListForAllApprover(input *entity.FetchTypeApprovalListEntity) ([]entity.ApproverListByType, error) {
	functionName := "FetchTypeApprovalListForFirstApprover()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.ApproverListByType
	querystring := `
	select ap.approver_id , ap.sequence_no , ap.department_id , ap.user_role_id , concat(u.first_name,' ',u.last_name) , d.department  from approvers ap
	inner join "user" u on u.id = ap.approver_id 
	inner join departments d on d.id = ap.department_id 
	where 
	set_number = (select max(set_number) as set_no from approvers where form_answer_id = $1) 
	and form_answer_id = $1 
	
		`

	rows, err := pool.Query(context.Background(), querystring, input.FormAnswerId)
	if err != nil {
		log.Println(err)
		return result, err
	}
	for rows.Next() {
		var answerEntity entity.ApproverListByType
		err = rows.Scan(
			&answerEntity.ApproverId,
			&answerEntity.SequenceNo,
			&answerEntity.DepartmentID,
			&answerEntity.UserRoleId,
			&answerEntity.UserName,
			&answerEntity.DepartmentName,
		)
		result = append(result, answerEntity)
	}
	log.Println(result)
	return result, nil
}

func FetchTeamDescriptionAndValue(country int) ([]entity.Dropdown, error) {
	functionName := "FetchCodeDescriptionAndValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select id, "name" from team where country = $1 
	and is_active = true and is_deleted = false`

	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Value, &answerEntity.Description)
		result = append(result, answerEntity)
	}
	return result, nil
}
func FetchProductDescriptionAndValue(country int) ([]entity.Dropdown, error) {
	functionName := "FetchCodeDescriptionAndValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select id, group_name from material where country = $1 
	and is_active = true and is_deleted = false`

	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Value, &answerEntity.Description)
		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchRequestorDescriptionAndValue(country int, input *model.GetRequestorForCountryInput) ([]entity.Dropdown, error) {
	functionName := "FetchCodeDescriptionAndValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var inputargs []interface{}
	var result []entity.Dropdown
	querystring := `select id, CONCAT(first_name ,' ', last_name) as "name" from "user" where country = ?
	and is_active = true and is_deleted = false`
	inputargs = append(inputargs, country)
	if input != nil {
		if input.SearchItem != nil {
			search := *input.SearchItem
			querystring = querystring + ` and CONCAT(first_name ,' ', last_name) ilike ? `
			inputargs = append(inputargs, "%"+search+"%")
		}
	}
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputargs...)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Value, &answerEntity.Description)
		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchCodeIDAndDescription(category string) ([]entity.DropdownIDAndDescription, error) {
	functionName := "FetchCodeDescriptionAndValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.DropdownIDAndDescription
	querystring := `select description, id from code where category = $1 and is_active =true and is_deleted =false`

	rows, err := pool.Query(context.Background(), querystring, category)
	if err != nil {
		log.Println(err)
		return nil, err
	}

	for rows.Next() {
		var answerEntity entity.DropdownIDAndDescription
		rows.Scan(&answerEntity.Description, &answerEntity.ID)
		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchSiteUrl() (string, error) {
	functionName := "FetchSiteUrl()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result string
	querystring := `select value from code where category = 'SiteUrl'`

	err := pool.QueryRow(context.Background(), querystring).Scan(&result)
	if err != nil {
		log.Println(err)
		return "", err
	}

	return result, nil
}

func FetchApolloUrl() string {
	pool = GetPool()
	var result string
	err := pool.QueryRow(context.Background(), `select value from code where category = 'ApolloUrl'`).Scan(&result)
	if err != nil {
		panic(err)
	}

	return result
}

func FetchCodeTitle(id int) string {
	functionName := "FetchSiteUrl()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result string
	querystring := `select title from code where id = $1`

	err := pool.QueryRow(context.Background(), querystring, id).Scan(&result)
	if err != nil {
		log.Println(err)
		return ""
	}

	return result
}

func InsertexceptiondetailsData(input entity.ExceptionalDetailsInput) error {
	functionName := "InsertexceptiondetailsData()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	defer tx.Rollback(context.Background())

	querystring := `update form_answers set details_of_request = $1, ihcp_other_local_policies = $2, more_comments = $3, exceptional_approval_file_upload = $4, scope_of_exceptional_request=$6 where id = $5`
	_, err1 := tx.Exec(context.Background(), querystring, input.DetailsOfRequest, input.IhcpOtherLocalPolicies, input.MoreComments, input.ExceptionalApprovalFileUpload, input.FormID, input.ScopeOfExceptionalRequest)
	logengine.GetTelemetryClient().TrackEvent("InsertexceptiondetailsData query called")
	if err1 != nil {
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return txErr
	}
	return nil
}

func FormIDCheckWithRespectToEventId(eventId string) (string, error) {
	functionName := "FormIDCheckWithRespectToEventId()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	querystring := "select fa.id::text  from form_answers fa where concat(fa.event_code,'-',fa.event_seq )=$1  "
	var formId string
	err := pool.QueryRow(context.Background(), querystring, eventId).Scan(&formId)
	if err != nil {
		log.Println(functionName, ":", err)
		return formId, err
	}
	return formId, nil
}

func FetchExceptionalDetails(FormId string) (*entity.ExceptionDetailsEntity, error) {
	var ent entity.ExceptionDetailsEntity
	pool = GetPool()

	querystring := `
	SELECT 
		currency,
		is_exceptional_approval,
		details_of_request,
		ihcp_other_local_policies,
		more_comments,
		exceptional_approval_file_upload,
		scope_of_exceptional_request,
		is_change_request,
		change_request_summary,
		change_request_type,
		event_code ,
		event_seq,
		is_recall,
		recall_summary  
	FROM
		form_answers
	WHERE id = $1`
	row := pool.QueryRow(context.Background(), querystring, FormId)

	err := row.Scan(
		&ent.CurrencyCode,
		&ent.IsExceptionalApproval,
		&ent.DetailsOfRequest,
		&ent.IhcpOtherLocalPolicies,
		&ent.MoreComments,
		&ent.ExceptionalApprovalFileUpload,
		&ent.ScopeOfExceptionalRequest,
		&ent.IsChangeRequest,
		&ent.ChangeRequestSummary,
		&ent.ChangeRequestType,
		&ent.EventCode,
		&ent.EventSeq,
		&ent.IsRecall,
		&ent.RecallSummary,
	)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, nil
		}
		panic(err)
	}

	return &ent, nil
}

func FetchFormAnswer2(formID string) *pgtype.JSONB {

	pool = GetPool()

	var answer pgtype.JSONB
	querystring := `select answers from form_answers where id=$1`
	err := pool.QueryRow(context.Background(), querystring, formID).Scan(&answer)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil
		}
		panic(err)
	}
	return &answer
}

func FetchFormAnswer(formID string) (pgtype.JSONB, error) {
	functionName := "FetchFormAnswer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var answer pgtype.JSONB
	querystring := `select answers from form_answers where id=$1`
	err := pool.QueryRow(context.Background(), querystring, formID).Scan(&answer)
	if err != nil {
		return answer, err
	}
	return answer, nil
}

func UpdateReportingDbActivityDate(request entity.FormSubmissionRequest) error {
	functionName := "UpdateReportingDbActivityDate()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	defer tx.Rollback(context.Background())
	queryString := `update reporting_db set activity_start_date[3] = $2 , activity_start_date[4] = $3 where form_answer_id = $1`
	_, err1 := tx.Exec(context.Background(), queryString, request.FormAnsId, request.StartDate, request.EndDate)
	logengine.GetTelemetryClient().TrackEvent("UpdateReportingDbActivityDate query called")
	if err1 != nil {
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		return txErr
	}
	return nil
}

func UpdateReportingDbException(entity *entity.FormSubmissionRequest) error {
	functionName := "UpdateReportingDbException()"
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update reporting_db set exceptionalapprover = $2 where form_answer_id = $1`
	_, err = tx.Exec(context.Background(), querystring, entity.FormAnsId, entity.IsExceptionalApprover)
	logengine.GetTelemetryClient().TrackEvent("UpdateReportingDbException query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit approval log ")
		return txErr
	}
	return nil
}

func CheckTypeOfHcp(formAnswerID uuid.UUID) ([]string, error) {
	functionName := "CheckTypeOfHcp()"
	log.Println(functionName)
	var typeOfhcp string
	var typeOfhcparr []string
	if pool == nil {
		pool = GetPool()
	}
	querystring := `with groupAnswertemp as(
		select 
		fa.answers#>'{2,sectionAnswer,0,form,groupAnswer}' as groupAnswer
		from form_answers fa where fa.id = $1
		),questionAnswerstemp as(
		select arr.item_object ->'questionAnswers' as questionAnswers from groupAnswertemp, jsonb_array_elements(groupAnswertemp.groupAnswer)
		with ordinality arr(item_object)
		)
		,answerstemp as(
		select arr.item_object ->'answers' as answers from questionAnswerstemp , jsonb_array_elements (questionAnswerstemp.questionAnswers)
		with ordinality arr(item_object) 
		),valuestemp as(
		select arr.item_object ->'values' as values from answerstemp , jsonb_array_elements (answerstemp.answers)
		with ordinality arr(item_object)
		)
		select arr.item_object ->>'value' as vaglue from valuestemp , jsonb_array_elements (valuestemp.values)
		with ordinality arr(item_object) where arr.item_object->>'description'='Type of HCP'`
	logengine.GetTelemetryClient().TrackEvent("CheckTypeOfHcp query called")
	rows, err := pool.Query(context.Background(), querystring, formAnswerID)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		err1 := rows.Scan(
			&typeOfhcp,
		)
		if err1 != nil {
			return typeOfhcparr, err
		}
		typeOfhcparr = append(typeOfhcparr, typeOfhcp)
	}
	return typeOfhcparr, nil
}

func UpdateTypeOfHcpInFormAnswer(input entity.ChangeRequestInputEntity, hcpIndex int, roleInString string, draftStatusId int) error {
	functionName := "UpdateTypeOfHcpInFormAnswer()"
	log.Println(functionName)
	var inputargs []interface{}
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update form_answers set answers = 
		jsonb_set(answers,'{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,16,answers,0,values,0,value}',?,false)`
	inputargs = append(inputargs, "\""+roleInString+"\"")
	querystring += `, status = ?`
	inputargs = append(inputargs, draftStatusId)
	querystring += ` where id= ?`
	inputargs = append(inputargs, input.FormAnswerID)
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	_, err = tx.Exec(context.Background(), querystring, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("UpdateTypeOfHcpInFormAnswer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit")
		return txErr
	}

	return nil
}

func UpdateTypeOfHcpForSponsoredInFormAnswer(input entity.ChangeRequestInputEntity, hcpIndex int, draftStatusId int) error {
	functionName := "UpdateTypeOfHcpForSponsoredInFormAnswer()"
	log.Println(functionName)
	var inputargs []interface{}
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `   
	update form_answers set answers = 
	 jsonb_set(jsonb_set(jsonb_set(jsonb_set(jsonb_set(jsonb_set(answers,'{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,7,answers,0,values,0,value}',?,false),
	 '{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,8,answers,0,values,0,value}',?,false),
	 '{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,1,answers,0,values,0,value}',?,false),
	 '{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,2,answers,0,values,0,value}',?,false),
	 '{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,3,answers,0,values,0,value}',?,false),
	 '{2,sectionAnswer,0,form,groupAnswer,`
	querystring += strconv.Itoa(hcpIndex) + `,questionAnswers,4,answers,0,values,0,value}',?,false)
	`
	inputargs = append(inputargs, "\""+input.Speciality+"\"")
	inputargs = append(inputargs, "\""+input.Hco+"\"")
	inputargs = append(inputargs, "\""+input.GovtNonGovtHcp+"\"")
	inputargs = append(inputargs, "\""+input.Remarks+"\"")
	inputargs = append(inputargs, "\""+input.SponsoredHcp+"\"")
	inputargs = append(inputargs, "\""+input.SponsoredHcp+"\"")
	querystring += `, status = ?`
	inputargs = append(inputargs, draftStatusId)
	querystring += ` where id= ?`
	inputargs = append(inputargs, input.FormAnswerID)
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	_, err = tx.Exec(context.Background(), querystring, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("UpdateTypeOfHcpForSponsoredInFormAnswer query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit")
		return txErr
	}

	return nil
}

func GetIdOfJsonbAnswersInFormAnswer(input entity.ChangeRequestInputEntity) ([]string, error) {
	functionName := "GetIdOfJsonbAnswersInFormAnswer()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var answersid []string
	var id string
	querystring := `	 
	with answerstemp as( 
	select 
	   fa.answers#>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers}' as answers from form_answers fa where fa.id=$1
	   )
	   select arr.item_object ->>'id' as answerid from answerstemp , jsonb_array_elements (answerstemp.answers)
	   with ordinality arr(item_object) `
	logengine.GetTelemetryClient().TrackEvent("GetIdOfJsonbAnswersInFormAnswer query called")
	rows, err := pool.Query(context.Background(), querystring, input.FormAnswerID)
	if err != nil {
		return answersid, err
	}
	for rows.Next() {
		err1 := rows.Scan(
			&id,
		)
		if err1 != nil {
			return answersid, err
		}
		answersid = append(answersid, id)
	}
	return answersid, nil
}

func UpdateActivityStartDateEndDateHcpNonHcpInFormAnswer(input entity.ChangeRequestInputEntity, index []int, draftStatusId int) error {
	functionName := "UpdateActivityStartDateEndDateHcpNonHcpInFormAnswer()"
	log.Println(functionName)
	for i, val := range index {
		var inputargs []interface{}
		if pool == nil {
			pool = GetPool()
		}
		tx, err := pool.Begin(context.Background())
		defer tx.Rollback(context.Background())
		querystring := `update form_answers set answers = jsonb_set(answers,'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,`
		querystring += strconv.Itoa(val) + `,values,0,value}',?) `
		if input.StartDate != "" && input.EndDate != "" {
			if i == 0 {
				inputargs = append(inputargs, "\""+input.StartDate+"\"")
			} else if i == 1 {
				inputargs = append(inputargs, "\""+input.EndDate+"\"")
			} else {
				inputargs = append(inputargs, "\""+input.Duration+"\"")
			}
		} else if input.NoOfHcp != "" && input.NoOfNonHCP != "" {
			if i == 0 {
				inputargs = append(inputargs, "\""+input.NoOfHcp+"\"")
			} else {
				inputargs = append(inputargs, "\""+input.NoOfNonHCP+"\"")
			}
		} else if input.Venue != "" {
			inputargs = append(inputargs, input.Venue)
		} else {
			inputargs = append(inputargs, input.VirtualEvent)
		}
		querystring += `,status = ?`
		inputargs = append(inputargs, draftStatusId)
		querystring += `where form_answers.id= ?`
		inputargs = append(inputargs, input.FormAnswerID)
		querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
		_, err = tx.Exec(context.Background(), querystring, inputargs...)
		logengine.GetTelemetryClient().TrackEvent("UpdateActivityStartDateEndDateHcpNonHcpInFormAnswer query called")
		if err != nil {
			logengine.GetTelemetryClient().TrackException(err)
			log.Printf("%s - Error: %s ", functionName, err.Error())
			return err
		}
		txErr := tx.Commit(context.Background())
		if txErr != nil {
			log.Println("Failed to commit")
			return txErr
		}
	}
	return nil
}

func UpdateActivityStartDateEndDateStatusInReportingdb(input entity.ChangeRequestInputEntity, draftStatusTitle sql.NullString, draftStatusValue string) error {
	functionName := "UpdateActivityStartDateEndDateStatusInReportingdb()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update reporting_db set activity_start_date[3] = $1,activity_start_date[4] = $2, status_title= $4, status_value= $5 where form_answer_id =$3`
	_, err = tx.Exec(context.Background(), querystring, input.StartDate, input.EndDate, input.FormAnswerID, draftStatusTitle, draftStatusValue)
	logengine.GetTelemetryClient().TrackEvent("UpdateActivityStartDateEndDateStatusInReportingdb query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit")
		return txErr
	}
	return nil
}

func SoftDeleteApproversInApprovers(input entity.ChangeRequestInputEntity) error {
	functionName := "SoftDeleteApproversInApprovers()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update approvers set is_deleted = true , is_active = false where form_answer_id = $1 and set_number = (select max(set_number)from approvers where form_answer_id = $1)`
	_, err = tx.Exec(context.Background(), querystring, input.FormAnswerID)
	logengine.GetTelemetryClient().TrackEvent("SoftDeleteApproversInApprovers query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit")
		return txErr
	}
	return nil
}

func UpdateStatusInReportingDb(input entity.ChangeRequestInputEntity, draftStatusTitle sql.NullString, draftStatusValue string) error {
	functionName := "UpdateStatusInReportingDb()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	defer tx.Rollback(context.Background())
	querystring := `update reporting_db set status_title= $1, status_value= $2 where form_answer_id =$3`
	_, err = tx.Exec(context.Background(), querystring, draftStatusTitle, draftStatusValue, input.FormAnswerID)
	logengine.GetTelemetryClient().TrackEvent("UpdateStatusInReportingDb query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s ", functionName, err.Error())
		return err
	}
	txErr := tx.Commit(context.Background())
	if txErr != nil {
		log.Println("Failed to commit")
		return txErr
	}
	return nil
}
func GetuserRoleId(userId string) (string, error) {
	functionName := "GetuserRoleId"
	if pool == nil {
		pool = GetPool()
	}
	log.Println(functionName)
	var RoleId string
	queryString := `select  u.user_role_id::text  from "user" u where u.id =$1`
	err := pool.QueryRow(context.Background(), queryString, userId).Scan(&RoleId)
	if err != nil {
		return "", nil
	}
	return RoleId, nil
}
func ChangeRequestUpdateForAllEvent(inputEntity entity.ChangeRequestForAllEvent, userId string, isApollo *bool) error {
	functionName := "ChangeRequestUpdateForAllEvent"
	if pool == nil {
		pool = GetPool()
	}
	log.Println(functionName)
	userRoleId, err := GetuserRoleId(userId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	userRoleIdUUID, err := uuid.FromString(userRoleId)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	code := codeController.GetValueKeyCodes()["approvedstatus"]
	status := code["return"].ID
	statusValue := code["return"].Value
	statusTitle := code["return"].Title
	querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status, comments,is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
	_, err = pool.Exec(context.Background(), querystring, inputEntity.FormAnswerID, userId, 0, userRoleIdUUID, status, inputEntity.Comment, isApollo)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	querystring = `UPDATE approvers SET status = $3 ,modified_by = $2, last_modified = now() WHERE form_answer_id = $1 and is_active = true`
	_, err = pool.Exec(context.Background(), querystring, inputEntity.FormAnswerID, userId, status)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	querystring = `update form_answers set status = $1, local_approval_status = $2,regional_approval_status = (case when regional_approval_status is null then null else ` + strconv.Itoa(status) + ` end), is_exceptional_approval = true where id = $3`
	_, err = pool.Exec(context.Background(), querystring, status, status, inputEntity.FormAnswerID)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error1: {%s}", functionName, err.Error())
		return err
	}
	queryString := `UPDATE reporting_db
					SET status_title = $2, status_value = $3, updated_date = now(),exceptionalapprover = true
					WHERE form_answer_id = $1 `
	_, err = pool.Exec(context.Background(), queryString, inputEntity.FormAnswerID, statusTitle, statusValue)
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return err
	}
	return nil
}
func FetchAnswersJsonForCancel(input string) (string, error) {
	functionName := "FetchAnswersJsonForCancel()"
	log.Println(functionName)
	var answer string
	if pool == nil {
		pool = GetPool()
	}
	querystring := `select answers::text from form_answers where id = $1 `
	err := pool.QueryRow(context.Background(), querystring, input).Scan(&answer)
	logengine.GetTelemetryClient().TrackEvent("FetchFormAnswersForCancel query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Println("%s - Error: %s", functionName, err.Error())
		return "", err
	}
	return answer, nil
}
func FetchRequestorValueAndDescription(country int) ([]entity.Dropdown, error) {
	functionName := "FetchRequestorValueAndDescription()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	query := `select concat(u.first_name ,' ',u.last_name ) as "name",u.id  from roles_permission rp
	inner join "user" u on u.user_role_id =rp.user_role_id 
	where  u.is_active =true and u.is_deleted =false and u.country =$1`
	rows, err := pool.Query(context.Background(), query, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Description, &answerEntity.Value)
		result = append(result, answerEntity)
	}
	return result, nil

}
func FetchProductOwnerValueAndDescription(country int) ([]entity.Dropdown, error) {
	functionName := "FetchProductOwnerValueAndDescription()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.Dropdown
	querystring := `select po.id,po.owner_name  from product_owner po where po.country =$1 and po.is_active =true and po.is_deleted =false`
	rows, err := pool.Query(context.Background(), querystring, country)
	if err != nil {
		log.Println(err)
		return nil, err
	}
	for rows.Next() {
		var answerEntity entity.Dropdown
		rows.Scan(&answerEntity.Value, &answerEntity.Description)
		result = append(result, answerEntity)
	}
	return result, nil
}

func FetchAllProductDetails(input model.FetchAllProductRequest, countryId *int) ([]*model.AllProductDetails, error) {
	functionName := "FetchAllProductDetails()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var response []*model.AllProductDetails
	query := `select m.id,m.group_name from material m 
    inner join product_owner po on po.id =m.product_owner_uid and po.is_active=true and po.is_deleted =false 
	where po.owner_name  =$1 and m.country =$2 and m.is_active =true and m.is_deleted =false`
	rows, err := pool.Query(context.Background(), query, input.ProductOwnerName, countryId)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		res := &model.AllProductDetails{}
		err1 := rows.Scan(
			&res.Value,
			&res.Description,
		)
		if err1 != nil {
			return nil, err
		}
		response = append(response, res)
	}
	return response, nil
}
func GetAllEventIDListForDB(input model.GetAllEventIDListRequest, countryId int, userId string) ([]*model.GetAllEventIDListDetails, error) {
	functionName := "GetAllEventIDListForDB()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []*model.GetAllEventIDListDetails
	var inputargs []interface{}
	querystring := `select concat(event_code,'-',event_seq) as event_id,id  from form_answers fa 
	where  fa.is_deleted =?  `
	inputargs = append(inputargs, "false")
	if input.FormID != nil && strings.TrimSpace(*input.FormID) != "" {
		querystring += `and fa.id <> ?`
		inputargs = append(inputargs, input.FormID)
	}
	if input.SearchItem != nil && strings.TrimSpace(*input.SearchItem) != "" {
		querystring += `and concat(event_code,'-',event_seq) ilike ?`
		inputargs = append(inputargs, "%"+strings.TrimSpace(*input.SearchItem)+"%")
	}
	querystring += ` order by fa.date_created desc `
	querystring = sqlx.Rebind(sqlx.DOLLAR, querystring)
	rows, err := pool.Query(context.Background(), querystring, inputargs...)
	if err != nil {
		return nil, err
	}
	for rows.Next() {
		res := &model.GetAllEventIDListDetails{}
		err := rows.Scan(&res.Description, &res.Value)
		if err != nil {
			return nil, err
		}
		result = append(result, res)
	}
	return result, nil
}
func GetFormAnswerAllDetails(formId *string) (entity.BasciInfoDateValidation, error) {
	functionName := "GetFormAnswerAllDetails()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var res entity.BasciInfoDateValidation
	if formId != nil && strings.TrimSpace(*formId) != "" {
		query := `select 	c.value , date(fa.date_created)::text as date_created,
		date(now())::text as today
	   from form_answers fa   
	   inner join code c on c.id=fa.status   
	where fa.id =$1`
		err := pool.QueryRow(context.Background(), query, formId).Scan(&res.Status, &res.CreatedDate, &res.TodayDate)
		if err != nil {
			log.Println(functionName + ":" + err.Error())
		}
		return res, nil
	} else {
		query := `select 
	 date(now())::text as today`
		err := pool.QueryRow(context.Background(), query).Scan(&res.TodayDate)
		if err != nil {
			log.Println(functionName + ":" + err.Error())
		}
		return res, nil
	}

}
func RecallInsertApprovalLog(inputEntity *entity.RecallSubmissionFormApprovalEntity, isApollo *bool) (error, string) {
	functionName := "RecallInsertApprovalLog()"
	log.Println(functionName)
	message := ""
	timenow := util.GetCurrentTime()
	action := "requestorresponse"
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	requestorResponseAction := codes[action].ID
	returnID := codes["return"].ID
	recallId := codes["recalled"].ID
	if pool == nil {
		pool = GetPool()
	}
	tx, err := pool.Begin(context.Background())
	logengine.GetTelemetryClient().TrackEvent("InsertApprovalLog query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - error: {%s}", functionName, err.Error())
		return errors.New("Failed to begin transaction"), message
	}
	defer tx.Rollback(context.Background())
	if inputEntity.FormAnswerID != uuid.Nil && inputEntity.Action != requestorResponseAction {
		if inputEntity.Action == recallId {
			querystring := `INSERT INTO approval_log (form_answer, actioned_by, approval_role,user_role_id, status,"comments",is_apollo)VALUES($1,$2,$3,$4,$5,$6,$7)`
			commandTag, err := tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, 0, inputEntity.ApprovalRole, inputEntity.Action, inputEntity.RecallSummary, isApollo)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - error: {%s}", functionName, err.Error())
				return err, message
			}
			querystring = `UPDATE approvers SET status = $3 ,modified_by = $2, last_modified = $4 WHERE form_answer_id = $1 and is_active = true`
			_, err = tx.Exec(context.Background(), querystring, inputEntity.FormAnswerID, inputEntity.ApprovedBy, returnID, timenow)
			if err != nil {
				logengine.GetTelemetryClient().TrackException(err)
				log.Printf("%s - error: {%s}", functionName, err.Error())
				return err, message
			}
			if !CheckRegionalApproverPresentInApprovers(inputEntity.FormAnswerID.String()) {
				querystring = `update form_answers set status = $1, local_approval_status = $2,recall_summary=$5,is_recall=$6, is_exceptional_approval = $4 where id = $3`
				_, err = tx.Exec(context.Background(), querystring, inputEntity.Action, returnID, inputEntity.FormAnswerID, inputEntity.IsExceptionalApprover, inputEntity.RecallSummary, inputEntity.IsRecall)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - error1: {%s}", functionName, err.Error())
					return err, message
				}
				queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now(), exceptionalapprover = $4
						WHERE form_answer_id = $1 `
				_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue, inputEntity.IsExceptionalApprover)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - Error: %s ", functionName, err.Error())
					return err, message
				}
			} else {
				querystring = `update form_answers set status = $1, local_approval_status = $2,recall_summary=$6,is_recall=$7, regional_approval_status = $4, is_exceptional_approval = $5 where id = $3`
				_, err = tx.Exec(context.Background(), querystring, inputEntity.Action, returnID, inputEntity.FormAnswerID, returnID, inputEntity.IsExceptionalApprover, inputEntity.RecallSummary, inputEntity.IsRecall)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - error2: {%s}", functionName, err.Error())
					return err, message
				}
				queryString := `UPDATE reporting_db
						SET status_title = $2, status_value = $3, updated_date = now() ,exceptionalapprover = $4
						WHERE form_answer_id = $1 `
				_, err = tx.Exec(context.Background(), queryString, inputEntity.FormAnswerID, inputEntity.ActionTitle, inputEntity.ActionValue, inputEntity.IsExceptionalApprover)
				if err != nil {
					logengine.GetTelemetryClient().TrackException(err)
					log.Printf("%s - Error: %s ", functionName, err.Error())
					return err, message
				}
			}

			if commandTag.RowsAffected() != 1 {
				message = "Invalid approval log data"
				return err, message
			} else {
				message = "Approval action submitted successfully"
			}
			txErr := tx.Commit(context.Background())
			if txErr != nil {

				return err, message
			}
		}
	}
	return err, message
}
func CheckFormAnswerInRecalledStage(formAnswerID string) bool {
	functionName := "CheckFormAnswerInRecalledStage()"
	log.Println(functionName)
	codes := codeController.GetValueKeyCodes()["approvedstatus"]
	recalledStatus := codes["recalled"].ID
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	querystring := `select 1 from form_answers where id = $1 and status = $2`
	var hasValue int
	err := pool.QueryRow(context.Background(), querystring, formAnswerID, recalledStatus).Scan(&hasValue)
	if err == nil {
		result = true
	}
	return result
}

func CheckAnswer(input string) bool {
	if pool == nil {
		pool = GetPool()
	}
	var result bool
	query := `select true from code c where c.category ='QuestionFive' and c.value =$1`

	err := pool.QueryRow(context.Background(), query, input).Scan(&result)
	if err != nil {
		return false
	}
	return true

}
func GetOwnerInfo(input model.OwnerDataRequest) (string, string, error) {
	functionName := "OwnerIdCheck()"
	if pool == nil {
		pool = GetPool()
	}
	var ownerFullname sql.NullString
	var ownerActiveDirectory sql.NullString
	query := `select active_directory  as active_directory,concat_ws( ' ',coalesce(first_name, '') , coalesce(last_name, '')) as full_name from "user" u   where  id=$1 `

	err := pool.QueryRow(context.Background(), query, input.UserID).Scan(&ownerActiveDirectory, &ownerFullname)
	if err != nil {
		log.Println(functionName, ": ", err.Error())
		return "", "", err
	}
	return ownerActiveDirectory.String, ownerFullname.String, nil

}
func GetFmvAuditlogsDB(userCountry *int, input model.GetFmvAuditlogsRequest) ([]entity.FmvAuditlogsEntity, int32, error) {
	functionName := "GetFmvAuditlogsDB()"
	if pool == nil {
		pool = GetPool()
	}
	var response []entity.FmvAuditlogsEntity
	var dataCount sql.NullInt32
	query := `select fcal .description,(extract(epoch from (fcal.date_created))::integer)::text  , upper(split_part(c.value ,'|', 2))::text,
	concat(u.first_name ,' ',u.last_name ) as created_by ,fcal.max_limit::text,
	case when fcal.is_active =true and fcal.is_deleted =false then 'Active' else 'Deactive' end as status,
	count(fcal.id) over()
	from fmv_controls_audit_logs fcal 
	left join "user" u on u.id =fcal.created_by 
	left join code c on c.id =fcal.currency_code_limit  
	inner join code c1 on c1.id =fcal.country 
	where fcal .country =? `
	var inputargs []interface{}
	inputargs = append(inputargs, userCountry)
	query += ` ORDER BY fcal.date_created desc,fcal.id desc  `
	if input.Limit != nil && *input.Limit > 0 {
		if input.PageNo != nil {
			if *input.PageNo > 0 {
				query = query + ` limit ? offset ?`
				inputargs = append(inputargs, input.Limit)
				inputargs = append(inputargs, (*input.Limit * (*input.PageNo - 1)))
			} else {
				query = query + ` limit ? offset ?`
				inputargs = append(inputargs, input.Limit)
				inputargs = append(inputargs, 0)
			}
		}
	}
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, inputargs...)
	if err != nil {
		log.Println(functionName, " : ", err.Error())
		return response, 0, err
	}
	for rows.Next() {
		var res entity.FmvAuditlogsEntity
		err := rows.Scan(&res.Description, &res.Date, &res.Currency, &res.CreatedBy, &res.MaxLimit, &res.Status, &dataCount)
		if err != nil {
			log.Println(functionName, " : ", err.Error())
			return response, 0, err
		}
		response = append(response, res)
	}
	return response, dataCount.Int32, nil
}
func GetApolloApprovalTrailDataForANActivity(inputEntity *entity.FetchApprovalTrailEntity) ([]entity.FetchApolloApprovalTrailData, error) {
	functionName := "GetApolloApprovalTrailDataForANActivity()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	response := []entity.FetchApolloApprovalTrailData{}
	queryString := `
	select  case when ("status"='Return' and rd.status_title='Recalled') then status_title else "status" end,case when ("IValue"='return' and rd.status_value='recalled') then status_value else "IValue" end,  "actionedByName" , "position", "ApprovalRole", "ISequenceNum" ,
	 "LatestCreate", "LastComment", "GroupID",ad_name  from  (
	select t1.* from (select distinct on (ap.group_id,ap.sequence_no) c1.title as "status",u.active_directory as ad_name,ap.form_answer_id , c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
	ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
	inner join "user" u on ap.approver_id = u.id 
	inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
	left join user_roles ur on ap.user_role_id = ur.id 
	LEFT JOIN lateral (
	select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
	al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
	where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	 order by ap.group_id,ap.sequence_no, ap.date_created desc) as t1
inner join (
select approver_id ,max("CreatedDate") as MaxDateTime from (
select distinct on (ap.group_id,ap.sequence_no) c1.title as "status", c1.value as "IValue", concat(u.first_name,' ',u.last_name) as "actionedByName" , u."position", ur.title as "ApprovalRole", 
	ap.sequence_no as "ISequenceNum" , log.date_created as "LatestCreate",  log."comments" as "LastComment", ap.date_created as "CreatedDate", ap.approver_id , ap.group_id as "GroupID" from approvers ap 
	inner join "user" u on ap.approver_id = u.id 
	inner join code c1 on ap.status = c1.id and c1.category = 'ApprovedStatus'
	left join user_roles ur on ap.user_role_id = ur.id 
	LEFT JOIN lateral (
	select al2."comments" ,al2.date_created from approval_log al2 where al2.form_answer = ap.form_answer_id and 
	al2.actioned_by = ap.approver_id and al2.is_active = true order by al2.date_created desc limit 1 ) as "log" on true 
	where ap.form_answer_id = $1 and ap.set_number = (select MAX(set_number) from approvers where form_answer_id = $1)
	 order by ap.group_id,ap.sequence_no, ap.date_created desc) 
	 as approverTemp group by approver_id ) groupedt1
	on t1.approver_id = groupedt1.approver_id
	AND t1."CreatedDate" = groupedt1.MaxDateTime ) as finalApprover
	inner join reporting_db rd on rd.form_answer_id  =$1`

	rows, err := pool.Query(context.Background(), queryString, inputEntity.FormAnswerId)
	logengine.GetTelemetryClient().TrackEvent("FetchTotalEventForApprover query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return response, err
	}
	defer rows.Close()
	for rows.Next() {
		values := entity.FetchApolloApprovalTrailData{}
		err := rows.Scan(
			&values.Status,
			&values.IValue,
			&values.ActionedBy,
			&values.ActionedByRole,
			&values.ApprovalRole,
			&values.ISequenceNum,
			&values.DateCreated,
			&values.Comment,
			&values.GroupID,
			&values.ApproverActiveDirectory,
		)
		if err != nil {
			return response, err
		}
		response = append(response, values)
	}
	return response, nil
}
func CalculateTotalEventExpenseFromEventID(formAnswerId string) ([]string, int, error) {
	functionName := "CalculateTotalExpenseFromEventID()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var CalculateTotalExpense []string
	var country int
	querystring := `with approval as (select fa.id,fa.country,fa.answers  from form_answers fa where fa.id =$1
	)
	, groups AS(
						SELECT id,country, arr.item_object->>'id' as tab_id,arr.item_object->>'title', arr.item_object->'sectionAnswer' as section FROM approval , jsonb_array_elements(answers) with ordinality
						arr(item_object)
						where arr.item_object->>'title' in ('Event Expenses')
				), tab_section AS (
						SELECT id,country,  arr.item_object->'form'->'groupAnswer' as group_section FROM groups, jsonb_array_elements(section) with ordinality
						arr(item_object)
				), group_questions AS (
						SELECT id,country,row_number() over() as row_num_1,arr.item_object->>'sequenceNo' as row_no  , arr.item_object->'questionAnswers' as group_questions  FROM tab_section, jsonb_array_elements(group_section) with ordinality
						arr(item_object)
	 
				), question_grp AS (
						SELECT id,country,row_num_1,row_number() over(partition  by row_num_1) as row_num_2, arr.item_object->'id',row_no,arr.item_object->'answers' as questions FROM group_questions, jsonb_array_elements(group_questions) with ordinality
						arr(item_object)
				), questions AS (
						SELECT id,country,row_no,row_num_1,row_number() over() as row_num, arr.item_object->>'id' as qn_id, arr.item_object->'values' as qn_values FROM question_grp, jsonb_array_elements(questions) with ordinality
						arr(item_object)
						where questions not  IN ('null','[]') and row_num_2=5
				),only_cost as(
				 select id,country,row_num, row_no,arr.item_object->>'id' as qn_id,
				 (case when (arr.item_object->>'value' ~ '^[0-9\.]+$') then (arr.item_object->>'value')
				 else (arr.item_object->>'value')
				 end) as qn_values
				,arr.item_object->>'description' as des FROM questions, jsonb_array_elements(qn_values) with ordinality
						arr(item_object)	
				)
				,all_details as(
				SELECT id,country,row_no::int4,(case
					 when ((array_agg(qn_values))[4]~ '^[0-9\.]+$') then (array_agg(qn_values))[4]
				when ((array_agg(qn_values))[3]~ '^[0-9\.]+$') then (array_agg(qn_values))[3]
				 when ((array_agg(qn_values))[2]~ '^[0-9\.]+$') then (array_agg(qn_values))[2]
				when ((array_agg(qn_values))[1]~ '^[0-9\.]+$') then (array_agg(qn_values))[1]
				end) as total_cost,
				(case when((array_agg(des))[1]='Type of Meal') then
				(array_agg(qn_values))[1] when (((array_agg(des))[1]='No of people' or (array_agg(des))[1]='Cost')) then
				'Meals/Accommodation'
				when (((array_agg(des))[1]='Name of Expense')) then
				'Others'
				else (array_agg(des))[1] end) as type_of_expense
				FROM only_cost
					 where des not in ('Do you want to add any attachment?','Remarks')
					 group by row_num,row_no,id,country
					  )
					 select country,ARRAY_AGG(total_cost) as expense_cost from all_details
						group by id,country
						union all 
						 select country, ARRAY[''] as expense_cost from tab_section
						group by id,country
						`
	err := pool.QueryRow(context.Background(), querystring, formAnswerId).Scan(&country, &CalculateTotalExpense)
	if err != nil {
		return CalculateTotalExpense, country, err
	}

	return CalculateTotalExpense, country, nil
}
func GetUserCountryFromCurrencyValue(currency string) (int, error) {
	functionName := "GetUserCountryFromCurrencyValue()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var value int
	queryString := `select id  AS country from code where split_part(value , '|', 2)=$1 
	and category = 'Currency' and is_active =true and is_deleted =false and id<>133`
	err := pool.QueryRow(context.Background(), queryString, currency).Scan(&value)
	if err != nil {
		return 0, err
	}
	return value, nil
}
func GetUSDvalueFromCurrencyValue(convertCountry int, userCountryID int, value float64) (float64, error) {
	if pool == nil {
		pool = GetPool()
	}
	var rate float64
	var country int
	var targetCountry int
	queryString := `select country ,target_country ,target_rate from currency_exchange where 
	((currency_code  = $1 and target_currency_code  = $2) or (currency_code  = $2 and target_currency_code  = $1))
	order by date_created desc limit 1`
	err := pool.QueryRow(context.Background(), queryString, userCountryID, convertCountry).Scan(&country, &targetCountry, &rate)
	logengine.GetTelemetryClient().TrackEvent("ConvertedCurrencyValue query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		return 0, err
	}
	var finalValue float64
	log.Println(rate)
	if rate != 0 {
		finalValue = value * rate
	}

	return finalValue, nil
}
func CheckSpeakernameArrayForApollo(veevaID string, Speakername string, country int, Specialty string, organization string) ([]entity.SpeakerDetailsForApollo, error) {
	if pool == nil {
		pool = GetPool()
	}
	var Speakerarr []entity.SpeakerDetailsForApollo
	organization = strings.ToLower(strings.ReplaceAll(organization, " ", ""))
	Specialty = strings.ToLower(strings.ReplaceAll(Specialty, " ", ""))
	veevaID = strings.ToLower(strings.ReplaceAll(veevaID, " ", ""))
	query := `
	SELECT 1 AS query_num, c.id::text
	FROM customer c
	WHERE c."name" =$1
		AND country =$2
		AND is_active = true
		AND is_deleted = false
		AND REPLACE(lower(c.organization),' ','') = $3
		AND REPLACE(lower(c.v_sp_desc),' ','') = $4
		AND REPLACE(lower(c.veeva_reference_id),' ','') ilike  $5
	UNION ALL
	SELECT 2 AS query_num, 'SpeakerOrganization'
	FROM customer c
	WHERE c.is_active = true
		AND is_deleted = false
		AND c."name" =$1
		AND country =$2
		AND REPLACE(lower(c.organization),' ','') = $3
	UNION ALL
	SELECT 3 AS query_num, 'Specialty'
	FROM customer c
	WHERE c.is_active = true
		AND is_deleted = false
		AND c."name" =$1
		AND country =$2
		AND REPLACE(lower(c.v_sp_desc),' ','') = $4
	UNION ALL
	SELECT 4 AS query_num, 'Speakername'
	FROM customer c
	WHERE c."name" =$1
		AND country =$2
		AND is_active = true
		AND is_deleted = false
		UNION ALL
	SELECT 5 AS query_num, 'VeevaId'
	FROM customer c
	WHERE c.is_active = true
		AND is_deleted = false
		AND c."name" =$1
		AND country =$2
		AND REPLACE(lower(c.veeva_reference_id),' ','') ilike  $5 `
	rows, err := pool.Query(context.Background(), query, Speakername, country, organization, Specialty, veevaID+"%")
	if err != nil {
		return Speakerarr, err
	}
	for rows.Next() {
		var speakerdetails entity.SpeakerDetailsForApollo
		rows.Scan(&speakerdetails.QueryValue, &speakerdetails.SpeakerId)
		Speakerarr = append(Speakerarr, speakerdetails)
	}
	return Speakerarr, nil
}

func ExcelExtractTrailDataFromDB(userCountry int, formId uuid.UUID, dateFilterStart string, dateFilterEnd string) ([]entity.ExcelAuditTrailData, error) {
	functionName := "ExcelExtractTrailDataFromDB()"
	log.Println(functionName)
	if pool == nil {
		pool = GetPool()
	}
	var result []entity.ExcelAuditTrailData
	var inputargs []interface{}
	query := `with audit_log as(select fa.id,fa.country,fa.date_created  as form_date ,concat(fa.event_code,'-',fa.event_seq) as meeting_id,
	(to_timestamp(((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}')::bigint))::timestamp::date) as start_date,
	(to_timestamp(((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}')::bigint))::timestamp::date) as end_date
	,concat(u.first_name ,' ',u.last_name ) as approver_name,ur.title as user_role,fal.date_created ,'' as "comment",c.title as "Action"  from form_answer_logs fal 
	inner join "user" u on u.id =fal.created_by 
	inner join user_roles ur on ur.id =u.user_role_id 
	inner join code c on c.id =fal.status
	inner join form_answers fa on fa.id =fal.form_answers_id 
	union all 
	select fa.id,fa.country,fa.date_created as form_date ,concat(fa.event_code,'-',fa.event_seq) as meeting_id,
	(to_timestamp(((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,3,values,0,value}')::bigint))::timestamp::date) as start_date,
	(to_timestamp(((fa.answers#>>'{1,sectionAnswer,0,form,groupAnswer,0,questionAnswers,0,answers,4,values,0,value}')::bigint))::timestamp::date) as end_date
	,concat(u1.first_name ,' ',u1.last_name ) as approver_name,ur1.title as user_role,al.date_created ,al."comments" ,c1.title as "Action" from approval_log al 
	inner join "user" u1 on u1.id =al.actioned_by  
	inner join user_roles ur1 on ur1.id =al.user_role_id  
	inner join code c1 on c1.id =al.status 
	inner join form_answers fa on fa.id =al.form_answer  )
	select meeting_id,	approver_name,	user_role,	date_created::text,	"comment",	"Action" from audit_log
	where country=?  `
	inputargs = append(inputargs, userCountry)
	if formId != uuid.Nil {
		query += ` and id=? `
		inputargs = append(inputargs, formId)

	}
	if dateFilterStart != "" || dateFilterEnd != "" {
		if dateFilterStart != "" && dateFilterEnd != "" {
			query = query + ` and start_date ~ '^[0-9\.]+$' and start_date >=? and end_date ~ '^[0-9\.]+$' and  end_date<=?   `
			inputargs = append(inputargs, dateFilterStart, dateFilterEnd)
		} else if dateFilterStart != "" && dateFilterEnd == "" {
			query = query + ` and start_date ~ '^[0-9\.]+$' and start_date >=? `
			inputargs = append(inputargs, dateFilterStart)
		} else if dateFilterStart == "" && dateFilterEnd != "" {
			query = query + ` and end_date ~ '^[0-9\.]+$' and end_date<=?`
			inputargs = append(inputargs, dateFilterEnd)
		}
	}
	query += ` ORDER BY audit_log.form_date desc `
	query = sqlx.Rebind(sqlx.DOLLAR, query)
	rows, err := pool.Query(context.Background(), query, inputargs...)
	logengine.GetTelemetryClient().TrackEvent("GetAdminAnswerList query called")
	if err != nil {
		logengine.GetTelemetryClient().TrackException(err)
		log.Printf("%s - Error: %s", functionName, err.Error())
		return nil, err
	}
	for rows.Next() {
		var answerEntity entity.ExcelAuditTrailData
		rows.Scan(&answerEntity.MeetingId, &answerEntity.ApproverName, &answerEntity.UserRole, &answerEntity.DateCreated, &answerEntity.Comment, &answerEntity.Action)
		result = append(result, answerEntity)
	}
	return result, nil

}
