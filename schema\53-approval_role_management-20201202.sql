ALTER TABLE approval_role_management ADD alternate_role int8 ;
ALTER TABLE approval_role_management ADD alternate_group_type int8 ;

ALTER TABLE approval_role_management ADD CONSTRAINT fk_alternate_role FOREIGN KEY (alternate_role) REFERENCES code(id);
ALTER TABLE approval_role_management ADD CONSTRAINT fk_alternate_group_type FOREIGN KEY (alternate_group_type) REFERENCES code(id);


update approval_role_management set alternate_role = 50 , alternate_group_type = 55 
where activity_id = 98 and approval_role = 48 and 
min_limit = 0 and max_limit = 10000 ;

update approval_role_management set alternate_role = 50 , alternate_group_type = 55 
where activity_id = 98 and approval_role = 48 and 
min_limit = 10001 and max_limit = 50000 ;

update approval_role_management set alternate_role = 50 , alternate_group_type = 55 
where activity_id = 98 and approval_role = 48 and 
min_limit = 50001 ;

update approval_role_management set alternate_role = 50 , alternate_group_type = 55 
where activity_id = 92 and approval_role = 48 ;

update approval_role_management set alternate_role = 50 , alternate_group_type = 55 
where activity_id = 93 and approval_role = 48 ;

update approval_role_management set alternate_role = 53 , alternate_group_type = 56 
where activity_id = 96 and approval_role = 48 ;

update approval_role_management set alternate_role = 50 , alternate_group_type = 55
where activity_id = 100 and approval_role = 48 and min_limit = 0
and max_limit = 50000 ;

update approval_role_management set alternate_role = 53 , alternate_group_type = 56
where activity_id = 100 and approval_role = 48 and min_limit = 50001 ;
